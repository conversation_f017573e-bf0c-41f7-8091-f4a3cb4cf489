{"dataList": [{"hostGroup": [{"env": "dev", "url": "http://localhost:9555"}], "name": "gcdp-dws-etl"}, {"hostGroup": [{"env": "hw-sprint", "url": "http://localhost:5566"}], "name": "gcdp-indexsearch"}], "envList": ["dev", "hw-sprint"], "headerList": [], "maxDescriptionLength": -1, "postScript": "", "preScript": "", "projectList": ["gcdp-dws-etl", "gcdp-indexsearch"], "syncModel": {"branch": "master", "domain": "https://github.com", "enabled": false, "namingPolicy": "byDoc", "owner": "", "repo": "", "repoUrl": "", "syncAfterRun": false, "token": "", "type": "github"}, "urlEncodedKeyValueList": [], "urlParamsKeyValueList": [], "urlSuffix": ""}