package com.glodon.qyqdprojectexpanded.domain.thread;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdp.common.datapush.domain.service.MessagePushService;
import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdp.common.domain.dto.ProjectProgressDto;
import com.glodon.gcdp.common.domain.enums.*;
import com.glodon.gcdp.common.domain.isolation.IsolationEnvironmentContext;
import com.glodon.gcdp.common.domain.model.SynchronizationData;
import com.glodon.gcdp.common.infrastructure.exception.DwsException;
import com.glodon.gcdp.common.infrastructure.exception.ValidityException;
import com.glodon.gcdp.common.projectprogress.service.EditProjectProgressService;
import com.glodon.gcdp.common.utils.KafkaProduceUtils;
import com.glodon.gcdp.common.utils.KeyLock;
import com.glodon.gcdp.common.utils.SpringUtil;
import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject;
import com.glodon.gcdp.dwdservice.domain.expand.DwdExpandHandler;
import com.glodon.gcdp.dwdservice.domain.model.QYQDDwdData;
import com.glodon.gcdp.dwdservice.domain.repository.qyqd.QYQDProjectDataRepository;
import com.glodon.gcdp.dwsindexservice.domain.model.dws.DwsDeleteCondition;
import com.glodon.gcdp.dwsindexservice.domain.model.dws.DwsIndexDto;
import com.glodon.gcdp.dwsindexservice.domain.service.IDwsIndexSummaryService;
import com.glodon.gcdp.dwsindexservice.domain.service.dwd.QYQDDwsTransferService;
import com.glodon.gcdp.dwsindexservice.infrastructure.consts.Constants;
import com.glodon.qyqdprojectexpanded.application.EditProjectExpand;
import com.glodon.qyqdprojectexpanded.domain.utils.MessageUtils;
import com.glodon.qyqdprojectexpanded.infrastructure.QyqdKeyLockConfig;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

/**
 * Created by Chenc on 2022/3/30.
 */
@Slf4j
@Service
public class QYQDDWDExpandProvider extends DwdExpandHandler {
    @Autowired
    private EditProjectExpand editProjectExpand;
    @Autowired
    private EditProjectProgressService progressService;
    @Autowired
    @Qualifier(QyqdKeyLockConfig.EDIT_PROJECT_EXPANDED_LOCK)
    private KeyLock<String> lock;

    private final LongAdder totalTimes = new LongAdder();

    private final LongAdder totalTaskNum = new LongAdder();

    private final LongAdder exceptionTaskNum = new LongAdder();

    @Autowired
    private IDwsIndexSummaryService summaryService;
    @Autowired
    private QYQDDwsTransferService qyqdDwsTransferService;
    @Autowired
    private QYQDProjectDataRepository qyqdProjectDataRepository;


    @Value("${topic-Message.topic_project_sync}")
    private String topicProjectSync;

    public QYQDDWDExpandProvider() {
    }

    @Override
    public boolean canProcess(ProjectProgressDto dto) {
        return ProjectProgressDto.isExpandBySingleService(JSON.toJSONString(dto)) && ProductSource.QYQD.getIndex().equals(dto.getProductSource());
    }

    @Override
    public void process(ProjectProgressDto dto) {
        execute(JSON.toJSONString(dto));
    }

    public void execute(String msg) {
        long start = System.currentTimeMillis();
        ProjectProgressDto expandMessage = JSONObject.parseObject(msg, ProjectProgressDto.class);

        // 提前获取合同工程数据
        DwdContractProject project = qyqdProjectDataRepository.selectContractProjectByOriginalIdentity(expandMessage.getOriginalIdentity());
        expandMessage.setExpandWay(ProjectProgressDto.EXPAND_BY_SINGLE_SERVICE);
        String lockKey = String.format(QyqdKeyLockConfig.QYQD_EXPAND_LOCK, expandMessage.getMongodbId());
        try {
            lock.lock(lockKey);
            // 消息不合法
            if (!MessageUtils.isValidMessage(expandMessage)) {
                log.error("消费消息失败，消息体不合法");
                return;
            }
            log.info("线程ID：{}", Thread.currentThread().getId());

            // 过滤非市场化计价来源消息
            if (!ProductSource.QYQD.getIndex().equals(expandMessage.getProductSource())) {
                return;
            }

            // 每次进来先删除dwd数据，之后再刷新
            if (editProjectExpand.deleteProject(expandMessage.getOriginalIdentity())) {
                log.info("消费消息删除工程成功，ID：[{}]", expandMessage.getId());
            } else {
                log.error("消费消息删除工程失败，ID：[{}]", expandMessage.getId());
                return;
            }
            log.info("删除数据耗时：[{}]ms", System.currentTimeMillis() - start);

            QYQDDwdData qyqdDwdData = new QYQDDwdData();
            qyqdDwdData.setContractProject(project);
            qyqdDwdData.setProjectProgressDto(expandMessage);
            if (expandMessage.getDeleted().equals(ProjectProgressDto.DELETED_NONE)) {
                // 展开dwd
                qyqdDwdData = editProjectExpand.process(expandMessage);
                sendSyncMessage(Lists.newArrayList(qyqdDwdData.getContractProject()), false);
            } else {
                sendSyncMessage(Lists.newArrayList(project), true);
            }
            // 获取最新的工程信息
            project = getCurrentProject(qyqdDwdData.getContractProject());

            if (ProjectProgressDto.DELETED_DONE != expandMessage.getDeleted()) {
//                if (qyqdDwdData.getProject() != null) {
//                    // 模拟清单量数据处理
//                    editProjectExpand.processBQSimuData(qyqdDwdData, project, expandMessage);
//                }
                // dws层数据处理
                processDwsData(qyqdDwdData, project);
            }
            // 保存解析状态
            progressService.expandStateUpdate(expandMessage);
            // 将清单和材料的逻辑修改为使用dws-etl公共模块推送
            List<DwsBizEnum> dwsBizEnums = ListUtil.toList(DwsBizEnum.BQITEM_PUSH, DwsBizEnum.RESOURCE_PUSH, DwsBizEnum.BQITEM_CALCULATE);
            SpringUtil.getBean(MessagePushService.class).pushToEtl(expandMessage, project, dwsBizEnums);
            log.info("市场化计价独立服务展开成功,ID:[{}]，耗时:[{}]ms", expandMessage.getId(), System.currentTimeMillis() - start);
        } catch (ValidityException e) {
            log.error("源工程数据异常，ID[{}],栈帧:[{}]", expandMessage.getId(), e.getMessage());
            expandMessage.setValidityState(ProjectProgressDto.VALIDITY_STATE_FALSE);
            // 保存解析状态
            progressService.expandStateUpdate(expandMessage);
            // 异常时删除dws数据
            deleteData(project);
            exceptionTaskNum.increment();
        } catch (DwsException e) {
            log.error("市场化计价独立服务汇总异常，具体信息:[{}]", e.getMessage());
            // 更新汇总状态
            progressService.updateProgressState(expandMessage);
            // 异常时删除dws数据
            deleteData(project);
            exceptionTaskNum.increment();
        } catch (Exception e) {
            log.error("消费消息发生异常，ID：[{}]，错误信息", expandMessage.getId(), e);
            // 异常时删除dws数据
            deleteData(project);
            exceptionTaskNum.increment();
        } finally {
            lock.unlock(lockKey);
            if (ProductSource.QYQD.getIndex().equals(expandMessage.getProductSource())) {
                long totalTime = System.currentTimeMillis() - start;
                this.totalTaskNum.increment();
                this.totalTimes.add(totalTime);
                log.info("市场化计价独立服务数据处理结束，mongodbId：[{}]，总耗时：[{}] ms", expandMessage.getMongodbId(), totalTime);
                log.info("市场化计价独立服务数据处理当前任务数：[{}]，总耗时：[{}] ms，单个任务平均耗时：[{}] ms",
                        totalTaskNum.intValue(), totalTimes.longValue(), (float) totalTimes.longValue() / totalTaskNum.intValue());
                log.info("市场化计价独立服务数据处理异常任务数：[{}]", exceptionTaskNum.intValue());
            }
        }
    }

    private void processDwsData(QYQDDwdData qyqdDwdData, DwdContractProject oldContractProject) throws DwsException {
        if (qyqdDwdData.getContractProject() == null && oldContractProject == null) {
            return;
        }
        // dws数据转换
        long start2 = System.currentTimeMillis();
        DwsIndexDto dwsIndexDto = qyqdDwsTransferService.qyqdDwdTransferDws(qyqdDwdData, oldContractProject);
        log.info("dws数据转换处理耗时:[{}]ms", System.currentTimeMillis() - start2);
        // dws数据保存
        summaryService.execute(dwsIndexDto);
    }

    /**
     * 解析异常时删除dws数据防止出现脏数据
     *
     * @param contractProject
     */
    private void deleteData(DwdContractProject contractProject) {
        if (contractProject == null) {
            return;
        }
        DwsIndexDto dwsIndexDto = new DwsIndexDto();
        dwsIndexDto.setContractProjectId(contractProject.getId());
        dwsIndexDto.setOriginalIdentity(contractProject.getOriginalIdentity());
        dwsIndexDto.setEnterpriseId(contractProject.getEnterpriseId());
        dwsIndexDto.setProjectCode(contractProject.getProjectCode());
        dwsIndexDto.setProductSource(contractProject.getProductSource());
        dwsIndexDto.setIsDeleted(Constants.ONE);
        DwsDeleteCondition deleteCondition = new DwsDeleteCondition();
        deleteCondition.setDeleteContractProjectId(contractProject.getId());
        deleteCondition.setOriginalIdentity(contractProject.getOriginalIdentity());
        deleteCondition.setPhase(contractProject.getPhase());
        deleteCondition.setProductSource(contractProject.getProductSource());
        dwsIndexDto.setDeleteCondition(deleteCondition);
        try {
            summaryService.execute(dwsIndexDto);
        } catch (DwsException e) {
            log.error("dwd 运行报错，dws 删除数据失败：{}", JSON.toJSONString(contractProject), e);
        }
    }

    private DwdContractProject getCurrentProject(DwdContractProject dwdProject) {
        DwdContractProject newProject = new DwdContractProject();
        if (dwdProject != null) {
            newProject.setId(dwdProject.getId());
            newProject.setOriginalIdentity(dwdProject.getOriginalIdentity());
            newProject.setEnterpriseId(dwdProject.getEnterpriseId());
            newProject.setProductSource(dwdProject.getProductSource());
            newProject.setProjectCode(dwdProject.getProjectCode());
            newProject.setProductSource(dwdProject.getProductSource());
            newProject.setPhase(dwdProject.getPhase());
        }
        return newProject;
    }

    private void sendSyncMessage(List<DwdContractProject> dwdContractProjects, Boolean isDelete) {
        if (dwdContractProjects.isEmpty()) {
            return;
        }
        try {
            SynchronizationData synchronizationData = new SynchronizationData();
            List<String> originalIdentity = dwdContractProjects.stream().map(DwdContractProject::getOriginalIdentity).collect(Collectors.toList());
            List<String> originalIds = dwdContractProjects.stream().map(DwdContractProject::getOriginalId).collect(Collectors.toList());
            synchronizationData.setOriginalIdentitys(Joiner.on(CharConst.COMMA).join(originalIdentity));
            synchronizationData.setOriginalId(Joiner.on(CharConst.COMMA).join(originalIds));
            synchronizationData.setDelete(isDelete);
            synchronizationData.setSyncDimension(SyncDimension.CONTRACT_PROJECT.getIndex());
            synchronizationData.setSyncMode(SyncMode.SINGLE_OR_BATCH.getIndex());
            //noinspection OptionalGetWithoutIsPresent
            DwdContractProject dwdContractProject = dwdContractProjects.stream().findFirst().get();
            IsolationEnvironmentContext context = IsolationEnvironmentContext.fromIsolation(dwdContractProject.getEnterpriseId(), dwdContractProject.getProductSource());
            String topicName = topicProjectSync;
            if (context.isIsolationEnvironmentWithProductSource()) {
                topicName = context.getTopicName(TaskEnum.ES, topicProjectSync);
            }
            KafkaProduceUtils.sendMessage(topicName, JSON.toJSONString(synchronizationData));
            log.info("发送es同步消息成功，{}", JSON.toJSONString(synchronizationData));
        } catch (Exception e) {
            log.error("发送es同步消息失败，{}", JSON.toJSONString(dwdContractProjects), e);
        }
    }
}
