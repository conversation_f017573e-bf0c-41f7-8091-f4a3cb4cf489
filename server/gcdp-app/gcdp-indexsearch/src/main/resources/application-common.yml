MYSQL_HOST: jdbc:mysql://${mysql.url}/${mysql.database}?serverTimezone=Asia/Shanghai&characterEncoding=utf-8&useSSL=false&sessionVariables=group_concat_max_len=204800&zeroDateTimeBehavior=CONVERT_TO_NULL
MYSQL_USERNAME: ${mysql.username}
MYSQL_PASSWORD: ${mysql.password}
ECS_REDIS_HOST: ${redis.host}
ECS_REDIS_PORT: ${redis.port}
ECS_REDIS_PASSWORD: ${redis.password}
ECS_REDIS_DB: ${redis.database}

spring:
 config:
  import:
   - classpath:application-gcdp-common.yml
 mvc:
  pathmatch:
   matching-strategy: ant_path_matcher

swagger:
 enable: ${swaggerEnable}
server:
 servlet:
  context-path: /gcdp-index-search
 # byte
 max-http-header-size: 307200
#日志配置
logging:
 level:
  root: info
  com.glodon: debug
  org.spring: info
#  org.mybatis: info
mybatis:
 log-timeout-sql: false
 timeout: 0
sequence:
 defaultCacheSize: 10000
 defaultLongIdStartValue: 1

# 授权中心服务
apiAuth:
 url: https://api-auth.glodon.com
 appKey: kqIUvqfcH7obwsZRx4U3L3yG5M3sLuRL
 g-signature: F864378123E9BA92E14E6C7862257FCC

config:
 depend:
  accountGlodonServiceKey: MiOWRrW6hYBmW6xQE8OGRH2f9I5wfxUG
  accountGlodonserverSecret: vG23xiwByuRxKdhAehgKaDEK89jyKzsC
object-storage:
 enable: true
 protocol: ${oss.protocol}  #可选项 OSS、S3_HW、S3_COS、S3_AWS、S3_AWS_V4、S3_MINIO、S3_CEPH、S3_QY、S3
 endPoint: ${oss.endPoint}
 externalEndpoint: ${oss.externalEndpoint} #外网地址
 accessKeyId: ${oss.accessKeyId}
 accessKeySecret: ${oss.accessKey}
 bucketName: ${oss.pubBucketName}
 region: cn-north-4
 pathStyleAccess: ${oss.pathStyleAccess} # 是否强制开启路径形式的资源访问
 maxConnections: 5000
 connectionTimeout: 3000
 socketTimeout: 3000
env: ${env.code}

#common中变量：gcdp-common-es，gcdp-common-dimservice
dcost-sub:
  url: ${dcost.sub.domain}

depend:
 elasticsearch:
  hosts: ${elasticsearch.url}
  username: ${elasticsearch.username}
  password: ${elasticsearch.password}
  prod-hosts: ${elasticsearch.url2}
  prod-username: ${elasticsearch.username}
  prod-password: ${elasticsearch.password2}
  connectTimeOut: 200000 #连接超时时间
  socketTimeOut: 300000 #连接超时时间
  connectionRequestTimeOut: 60000 #获取连接的超时时间
  maxConnectNum: 500 #最大连接数
  maxConnectPerRoute: 200 #最大路由连接数
  maxKeepAliveTime: 600000  #
