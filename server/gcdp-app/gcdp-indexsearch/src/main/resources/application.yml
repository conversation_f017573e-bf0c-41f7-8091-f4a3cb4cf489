server:
 port: 5566

spring:
 profiles:
  active: hw-sprint

 datasource:
  dynamic:
   primary: master #设置默认的数据源或者数据源组,默认值即为master
   strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候会抛出异常,不启动则使用默认数据源.
   datasource:
    master:
     url: ${MYSQL_HOST}
     username: ${MYSQL_USERNAME}
     password: ${MYSQL_PASSWORD}
     driver-class-name: com.mysql.cj.jdbc.Driver # 3.2.0开始支持SPI可省略此配置
     type: com.alibaba.druid.pool.DruidDataSource
 # Redis配置
 redis:
  host: ${ECS_REDIS_HOST}
  port: ${ECS_REDIS_PORT}
  database: ${ECS_REDIS_DB}
  lettuce:
   pool:
    # 最大活跃链接数 默认8
    max-active: 8
    # 最大空闲连接数 默认8
    max-idle: 8
    # 最小空闲连接数 默认0
    min-idle: 0

  password: ${ECS_REDIS_PASSWORD}
  enable: true
mybatis:
 mapper-locations: classpath:mapper/**/*Mapper.xml
 configuration:
  map-underscore-to-camel-case: true

pagehelper:
 helper-dialect: mysql
 reasonable: true
 support-methods-arguments: true