<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsBuildStandardIndexMapper">

    <select id="selectBuildStandardIndex"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexJZZFZBLibDto">
        select
        bd.id,
        bd.pid,
        GROUP_CONCAT(bd.id) as ids,
        bd.name,
        bd.standard_description as stdDescription,
        CONCAT(IFNULL(convert(min(bd.df_index_value_min), decimal(30, 5)), '-'), '~',
        IFNULL(convert(max(bd.df_index_value_max),decimal(30, 5)), '-'))as dfMinAndMax,
        CONCAT(IFNULL(convert(min(bd.df_index_value_tax_min), decimal(30, 5)), '-'), '~',
        IFNULL(convert(max(bd.df_index_value_tax_max),decimal(30, 5)), '-'))as dfMinAndMaxIncludeTax,
        IFNULL(convert(sum(bd.df_index_value_sum)/sum(bd.df_index_value_count), decimal(30, 5)), '-') as dfAvg,
        IFNULL(convert(sum(bd.df_index_value_tax_sum)/sum(bd.df_index_value_tax_count), decimal(30, 5)), '-') as dfAvgIncludeTax,
        CONCAT(IFNULL (convert(min(bd.swl_index_value_min), decimal(30, 5)), '-'), '~',
        IFNULL (convert(max(bd.swl_index_value_max), decimal(30, 5)), '-'))as zhdjMinAndMax,
        CONCAT(IFNULL (convert(min(bd.swl_index_value_tax_min), decimal(30, 5)), '-'), '~',
        IFNULL (convert(max(bd.swl_index_value_tax_max), decimal(30, 5)), '-'))as zhdjMinAndMaxIncludeTax,
        IFNULL (convert((sum(bd.swl_index_value_sum)/sum(bd.swl_index_value_count)), decimal(30, 5)), '-') as zhdjAvg,
        IFNULL (convert((sum(bd.swl_index_value_tax_sum)/sum(bd.swl_index_value_tax_count)), decimal(30, 5)), '-') as zhdjAvgIncludeTax,
        GROUP_CONCAT(bd.index_project_note_id) as sampleIds
        from gcdp_dws_build_standard_index bd where
        bd.index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        group by bd.item_hash order by code
    </select>
</mapper>