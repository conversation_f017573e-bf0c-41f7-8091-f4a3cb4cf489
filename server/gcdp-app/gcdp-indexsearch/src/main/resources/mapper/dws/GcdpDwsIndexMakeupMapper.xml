<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMakeupMapper">


    <select id="selectCostMakeupByNoteIdAndSubjectId" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMakeup">
        SELECT
        cost.index_project_note_id, makeup.type, makeup.dwd_id, makeup.ld_quantity
        FROM
        gcdp_dws_index_cost cost
        JOIN
        gcdp_dws_index_cost_makeup makeup
        ON cost.id = makeup.`index_cost_id`
        WHERE
        cost.`index_project_note_id` IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND cost.id IN
        <foreach collection="itemIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectUsageMakeupByNoteIdAndSubjectId" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMakeup">
        SELECT
        `usage`.index_project_note_id, makeup.type, makeup.dwd_id, makeup.ld_quantity
        FROM
        gcdp_dws_index_usage `usage`
        JOIN
        gcdp_dws_index_usage_makeup makeup
        ON `usage`.id = makeup.`index_usage_id`
        WHERE
        `usage`.`index_project_note_id` IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND `usage`.id IN
        <foreach collection="itemIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectBqItemByIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MakeupDetailDto">
        SELECT
        `id`, `code`, `name`, spec, unit, quantity, rate_include_tax, amount_include_tax,
        bq_item_type, rate, amount
        FROM
        gcdp_dwd_bqitem
        WHERE
        id IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectNormItemByIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MakeupDetailDto">
        SELECT
        `norm`.`id`, `norm`.`code`, `norm`.`name`, `norm`.unit, `norm`.quantity,
        `norm`.rate as fqdRate, `norm`.amount as fqdAmount,
        `norm`.rate_include_tax as fqdRateIncludeTax, `norm`.amount_include_tax as fqdAmountIncludeTax,
        `norm`.norm_rate as glRate, `norm`.norm_amount as glAmount,
        `contract`.esti_type, norm_item_type
        FROM
            gcdp_dwd_norm_item `norm`
        join
            gcdp_dwd_contract_project `contract`
        on
            norm.contract_project_id = `contract`.`id`
        WHERE
            `norm`.id IN
            <foreach collection="ids" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
    </select>

    <select id="selectResourceItemById" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MakeupDetailDto">
        SELECT `id`, `code`, `name`, `unit`, `spec`,`quantity`, `type` as resourceType, `market_rate`, `market_amount`,
        `market_tax_rate` as marketRateIncludeTax, `market_tax_amount` as marketAmountIncludeTax
        FROM
        gcdp_dwd_resource
        WHERE
        id IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectMakeupByNoteIdAndSubjectId" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMakeup">
        SELECT
        idx.index_project_note_id,
        makeup.type,
        makeup.dwd_id,
        makeup.ld_quantity,
        makeup.is_calc_quantity,
        makeup.is_calc_amount,
        makeup.factor,
        makeup.dws_index_id,
        gn.name,
        gn.product_source,
        gn.total as fullCostAmount,
        gn.total_include_tax as fullCostAmountIncludeTax,
        gn.non_full_cost_total as nonFullCostAmount,
        gn.non_full_cost_total_include_tax as nonFullCostAmountIncludeTax,
        gn.contract_project_id,
        gn.archive_date
        FROM
        gcdp_dws_index idx
        JOIN
        gcdp_dws_index_makeup makeup
        ON idx.id = makeup.`dws_index_id`
        JOIN
        gcdp_dws_index_project_note gn
        ON makeup.index_project_note_id = gn.id
        WHERE
        idx.`index_project_note_id` IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND idx.id IN
        <foreach collection="itemIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectContractProjectInfo" resultType="com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject">
        SELECT gdcp.id,
        gdcp.name,
        gdcp.product_source,
        gdcp.extend_data_json
        FROM gcdp_dwd_contract_project gdcp
        WHERE
        gdcp.id IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectExpendSubBqItemById" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.CBZBKMakeupDetailDto">
        SELECT id, `code`, contract_type_code AS `type`, `name`, `unit`, quantity, characteristic, work_scope,
               rate, rate_include_tax, total AS amount, total_include_tax AS amountIncludeTax, tax_ratio, brand, cost_detail, owner_supply, rule,
               'gcdp_dwd_expend_sub_bqitem' as tableName
        FROM gcdp_dwd_expend_sub_bqitem
        WHERE
        id IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectResourceById" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.CBZBKMakeupDetailDto">
        SELECT id, `code`, `type`, `name`, `unit`, quantity, spec, market_rate as rate, market_tax_rate as rateIncludeTax,
               market_amount as amount, market_tax_amount as amountIncludeTax, tax_ratio, brand, 'gcdp_dwd_resource' as tableName
        FROM `gcdp_dwd_resource`
        WHERE
        id IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectBqItemById" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.CBZBKMakeupDetailDto">
        SELECT id, `code`, item_type AS `type`, `name`, `unit`, quantity, work_scope, rate, rate_include_tax, amount, amount_include_tax, 'gcdp_dwd_bqitem' as tableName
        FROM gcdp_dwd_bqitem
        WHERE
        id IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectMainResMakeupByNoteIdAndSubjectId" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMakeup">
        SELECT
        idx.index_project_note_id,
        makeup.type,
        makeup.dwd_id,
        makeup.is_calc_quantity,
        makeup.is_calc_amount,
        makeup.factor,
        makeup.dws_index_id,
        gn.name,
        gn.product_source,
        gn.contract_project_id,
        gn.archive_date
        FROM
        gcdp_dws_index_main_res idx
        JOIN
        gcdp_dws_index_main_res_makeup makeup
        ON idx.id = makeup.`dws_index_id`
        JOIN
        gcdp_dws_index_project_note gn
        ON makeup.index_project_note_id = gn.id
        WHERE
        idx.`index_project_note_id` IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND idx.id IN
        <foreach collection="itemIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>