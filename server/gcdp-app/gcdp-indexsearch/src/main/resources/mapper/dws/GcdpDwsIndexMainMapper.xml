<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMainMapper">
    <resultMap id="BaseResultMap" type="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndexMainRes">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="ids" jdbcType="VARCHAR" property="ids"/>
        <result column="pid" jdbcType="BIGINT" property="pid"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="name_path" jdbcType="VARCHAR" property="namePath"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="djTotalTax" jdbcType="DECIMAL" property="djTotalTax"/>
        <result column="djTotal" jdbcType="DECIMAL" property="djTotal"/>
        <result column="hlTotal" jdbcType="DECIMAL" property="hlTotal"/>
        <result column="dj_calculate_value" jdbcType="DECIMAL" property="djCalculateValue"/>
        <result column="hl_calculate_value" jdbcType="DECIMAL" property="hlCalculateValue"/>
        <result column="project_un_ld_merge_hash" jdbcType="VARCHAR" property="projectUnLdMergeHash"/>
        <result column="calculate_name" jdbcType="VARCHAR" property="calculateName"/>
        <result column="hl_index_unit" jdbcType="VARCHAR" property="hlIndexUnit"/>
    </resultMap>
    <select id="getIndex" resultMap="BaseResultMap">
        select
        a.id,
        group_concat(a.ids) as ids,
        group_concat(a.sampleIds) as tempNoteIds,
        a.pid,
        a.name,
        a.name_path,
        a.unit,
        a.calculate_name,
        a.hl_index_unit,
        sum(a.djTotalTax) as djTotalTax, -- 含税合价
        sum(a.djTotal) as djTotal, -- 不含税合价
        sum(a.hlTotal) as hlTotal, -- 主要工料消耗量
        sum(a.dj_calculate_value) as dj_calculate_value,
        sum(a.hl_calculate_value) as hl_calculate_value,
        a.project_un_ld_merge_hash
        from
        (
        select
        mainres.id,
        mainres.code,
        group_concat(mainres.id) as ids,
        group_concat(mainres.index_project_note_id) as sampleIds,
        mainres.pid,
        mainres.name,
        mainres.name_path,
        mainres.unit,
        mainres.hl_calculate_name as calculate_name,
        mainres.hl_index_unit,
        -- 对于相同单体合并,主要工料指标单价, 计算口径为消耗量, 取汇总值  含量指标 计算口径取最新值
        sum(mainres.dj_index_value_include_tax * mainres.dj_calculate_value) as djTotalTax, -- 含税合价
        sum(mainres.dj_index_value * mainres.dj_calculate_value) as djTotal, -- 不含税合价
        sum(mainres.hl_index_value * mainres.hl_calculate_value) as hlTotal, -- 主要工料消耗量
        sum(IF((mainres.dj_index_value_include_tax IS NOT NULL AND mainres.dj_index_value_include_tax != 0) or
                (mainres.dj_index_value IS NOT NULL AND mainres.dj_index_value != 0), mainres.dj_calculate_value, 0)) as dj_calculate_value,
        IF(sum(mainres.hl_index_value) != 0, SUBSTRING_INDEX(GROUP_CONCAT(mainres.hl_calculate_value ORDER BY note.archive_date DESC), ',',1), 0) as hl_calculate_value,
        mainres.project_un_ld_merge_hash
        from
        gcdp_dws_index_main_res mainres
        left join gcdp_dws_index_project_note note on mainres.index_project_note_id =note.id
        where
        note.`type` in (4,5,6)
        and index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        group by
        project_ld_merge_hash
        union
        select
        mainres.id,
        mainres.code,
        group_concat(mainres.id) as ids,
        group_concat(mainres.index_project_note_id) as sampleIds,
        mainres.pid,
        mainres.name,
        mainres.name_path,
        mainres.unit,
        mainres.hl_calculate_name as calculate_name,
        mainres.hl_index_unit,
        sum(mainres.dj_index_value_include_tax*mainres.dj_calculate_value) as djTotalTax, -- 含税单价
        sum(mainres.dj_index_value*mainres.dj_calculate_value) as djTotal, -- 不含税单价
        sum(mainres.hl_index_value*mainres.hl_calculate_value) as hlTotalTax, -- 主要工料消耗量
        sum(IF((mainres.dj_index_value_include_tax IS NOT NULL AND mainres.dj_index_value_include_tax != 0) or
               (mainres.dj_index_value IS NOT NULL AND mainres.dj_index_value != 0), mainres.dj_calculate_value, 0)) as dj_calculate_value,
        sum(IF(mainres.hl_index_value IS NOT NULL AND mainres.hl_index_value != 0, mainres.hl_calculate_value, 0)) as hl_calculate_value,
        mainres.project_un_ld_merge_hash
        from
        gcdp_dws_index_main_res mainres
        left join gcdp_dws_index_project_note note on mainres.index_project_note_id =note.id
        where
        note.`type` not in (4,5,6)
        and index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        group by
        project_un_ld_merge_hash
        ) as a
        group by
        a.project_un_ld_merge_hash order by code,id
--         楼栋hash如果一样，非楼栋肯定也一样
    </select>

    <select id="getItemIndex" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexMainRes">
        select
            a.id,
            a.pid,
            a.name,
            a.unit,
            GROUP_CONCAT(a.hl_calculate_name) as hlCalculateName,
            GROUP_CONCAT(a.ids) as ids,
            GROUP_CONCAT(a.djSampleIds) as djSampleIds,
            GROUP_CONCAT(a.djSampleIds) as djTaxSampleIds,
            GROUP_CONCAT(a.djSampleIds) as glhlSampleIds,
            GROUP_CONCAT(a.djSampleIds) as tempNoteIds,
            CONCAT(IFNULL (convert(min(dj_index_value),decimal(30,5)),'-'),'~',
            IFNULL (convert(max(dj_index_value),decimal(30,5)),'-'))as djMaxAndMin,
            IFNULL (convert(sum(dj_index_value)/count(dj_index_value!=0),decimal(30,5)),'-') as djAvg,
            IFNULL (convert(sum(a.market_amount)/sum(a.dj_calculate_value),decimal(30,5)),'-') as djWeightedAvg,
            count(dj_index_value!=0) as djSampleCount,
            CONCAT(IFNULL (convert(min(dj_index_value_include_tax),decimal(30,5)),'-'),'~',
            IFNULL (convert(max(dj_index_value_include_tax),decimal(30,5)),'-'))as djTaxMaxAndMin,
            IFNULL (convert(sum(dj_index_value_include_tax)/count(dj_index_value_include_tax!=0),decimal(30,5)),'-') as djTaxAvg,
            IFNULL (convert(sum(a.tax_market_amount)/sum(a.dj_calculate_value),decimal(30,5)),'-') as djTaxWeightedAvg,
            count(dj_index_value_include_tax!=0) as djTaxSampleCount,
            CONCAT(IFNULL (convert(min(hl_index_value),decimal(30,5)),'-'),'~',
            IFNULL (convert(max(hl_index_value),decimal(30,5)),'-')) as glhlMaxAndMin,
            IFNULL (convert(sum(hl_index_value)/count(hl_index_value!=0),decimal(30,5)),'-') as glhlAvg,
            count(hl_index_value!=0) as glhlSampleCount,
            IFNULL (convert(sum(a.hl_amount)/sum(a.hl_calculate_value),decimal(30,5)),'-') as glhlWeightedAvg
        from (<include refid="mainResLdSumData"> </include>) a
        group by a.subject_un_ld_merge_hash order by a.code
    </select>
    <select id="selectByItemIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMainRes">
        select res.*, note.archive_date from gcdp_dws_index_main_res res
        inner join gcdp_dws_index_project_note note on res.index_project_note_id = note.id
        where
        res.id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
    </select>

    <sql id="mainResLdSumData">
        SELECT
            b.id,
            b.pid,
            b.code,
            b.name,
            b.unit,
            b.ids,
            b.djSampleIds,
            b.dj_calculate_value,
            b.hl_calculate_value,
            b.market_amount,
            b.tax_market_amount,
            b.hl_amount,
            b.market_amount/b.dj_calculate_value AS dj_index_value,
            b.tax_market_amount/b.dj_calculate_value AS dj_index_value_include_tax,
            b.hl_amount/b.hl_calculate_value AS hl_index_value,
            b.hl_calculate_name,
            b.subject_un_ld_merge_hash
        FROM (
            SELECT
                cost.id,
                cost.pid,
                cost.code,
                cost.name,
                cost.unit,
                GROUP_CONCAT(cost.id) as ids,
                GROUP_CONCAT( cost.index_project_note_id) as djSampleIds, -- 不含税样本量id列表
                sum(cost.dj_index_value*cost.dj_calculate_value) as market_amount,
                sum(IF((cost.dj_index_value_include_tax IS NOT NULL AND cost.dj_index_value_include_tax != 0) or
                (cost.dj_index_value IS NOT NULL AND cost.dj_index_value != 0), cost.dj_calculate_value, 0)) as dj_calculate_value,
                IF(sum(cost.hl_index_value) != 0, SUBSTRING_INDEX(GROUP_CONCAT(cost.hl_calculate_value ORDER BY note.archive_date DESC), ',',1), 0) as hl_calculate_value,
                --             sum(cost.dj_index_value*cost.dj_calculate_value)/ sum(cost.dj_calculate_value) as dj_index_value,
                sum(cost.dj_index_value_include_tax* cost.dj_calculate_value) as tax_market_amount,
                --             sum(cost.dj_index_value_include_tax* cost.dj_calculate_value)/ sum(cost.dj_calculate_value) as dj_index_value_include_tax,
                sum(cost.hl_calculate_value * cost.hl_index_value) as hl_amount,
                --             sum(cost.hl_calculate_value* cost.hl_index_value)/cost.hl_calculate_value as hl_index_value,
                cost.hl_calculate_name,
                cost.subject_un_ld_merge_hash
            FROM gcdp_dws_index_main_res cost
            LEFT JOIN gcdp_dws_index_project_note note
            ON cost.index_project_note_id = note.id
            WHERE
            cost.index_project_note_id IN
            <foreach collection="ids" open="(" close=")" item="id" separator=",">
                #{id,jdbcType = BIGINT}
            </foreach>
            <if test="showAll == null or showAll == 0">
                AND (cost.dj_index_value>0 OR cost.dj_index_value_include_tax>0 OR cost.hl_index_value>0)
            </if>
            GROUP BY subject_ld_merge_hash ORDER BY code
        )b

    </sql>
</mapper>
