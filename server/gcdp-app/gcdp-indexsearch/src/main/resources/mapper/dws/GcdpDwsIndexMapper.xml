<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMapper">


    <sql id="CostAndUsageIndexField">
        c.id,
        c.pid,
        c.index_project_note_id,
        c.contract_project_id,
        c.code,
        c.name,
        c.name_path,
        c.unit,
        c.item_cost_type,
        c.swl_calculate_name,
        c.swl_calculate_value,
        c.swl_index_value,
        c.swl_index_value_include_tax,
        c.swl_index_unit,
        c.amount,
        c.amount_include_tax,
        c.df_calculate_name,
        c.df_calculate_value,
        c.df_index_value,
        c.df_index_value_include_tax,
        c.df_index_unit,
        c.item_hash,
        u.zyl_calculate_name,
        u.zyl_calculate_value,
        u.zyl_index_unit,
        u.zyl_index_value
    </sql>

    <sql id="CostAndUsageIndexCondtion">
        on c.index_project_note_id = u.index_project_note_id
        and c.item_cost_type = u.item_cost_type
        and c.item_hash = u.item_hash
        and c.code = u.code
        where
        c.index_project_note_id in
        <foreach collection="noteIds" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        <if test="itemCostType != null">
            and c.item_cost_type = #{itemCostType}
        </if>
        and c.item_hash is not null
        and c.item_cost_type is not null
    </sql>

    <select id="selectCostAndUsageIndexByNoteId" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.SubjectIndexDto">
        select *from ((select
        <include refid="CostAndUsageIndexField"/>
        from gcdp_dws_index_cost c
        left join gcdp_dws_index_usage u
        <include refid="CostAndUsageIndexCondtion"/>
        )
        union
        (select
        <include refid="CostAndUsageIndexField"/>
        from gcdp_dws_index_cost c
        right join gcdp_dws_index_usage u
        <include refid="CostAndUsageIndexCondtion"/>
        )) as t order by t.code;
    </select>

    <!--suppress SqlResolve -->
    <select id="selectItemIndexByNoteId" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndex">
        select
        indx.id,
        indx.pid,
        indx.code,
        indx.name,
        indx.name_path as namePath,
        indx.unit,
        indx.item_cost_type,
        indx.${indexTypePrefix}_index_unit as indexUnit,
        GROUP_CONCAT(indx.id) as itemIds,
        GROUP_CONCAT(indx.index_project_note_id) as tempNoteIds,
        sum(indx.${indexTypePrefix}_index_value*indx.${calcType}_calculate_value) as amount,
        <if test="indexTypePrefix == 'jmdf' or indexTypePrefix == 'df'  or indexTypePrefix == 'swl'">
            sum(indx.${indexTypePrefix}_index_value_include_tax*indx.${calcType}_calculate_value) as amountIncludeTax,
        </if>
        <choose>
            <when test="indexTypePrefix == 'swl'">
                sum(IF(<include refid="costIndexValid"/> OR (note.product_source = 'mbcb' AND indx.zyl_index_value = 1), indx.quantity, 0)) as quantity,
                IF(sum(indx.amount) != 0, sum(indx.amount), null) as total,
                IF(sum(indx.amount_include_tax) != 0, sum(indx.amount_include_tax), null) as total_include_tax,
                sum(IF(<include refid="costIndexValid"/>, indx.${calcType}_calculate_value, 0)) as calcValue,
            </when>
            <otherwise>
                <if test="indexTypePrefix == 'jmdf' or indexTypePrefix == 'df' or indexTypePrefix == 'xmdf'">
                    IF((sum(indx.${indexTypePrefix}_index_value) != 0) or (sum(indx.${indexTypePrefix}_index_value_include_tax) != 0), <include refid="calcValueNotEmptyLatest"/>, 0) as calcValue,
                </if>
                <if test="indexTypePrefix == 'jmhl' or indexTypePrefix == 'zyl'">
                    IF((sum(indx.${indexTypePrefix}_index_value) != 0), <include refid="calcValueNotEmptyLatest"/>, 0) as calcValue,
                </if>
            </otherwise>
        </choose>
        SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN indx.${calcType}_calculate_value IS NOT NULL THEN note.archive_date END ORDER BY note.archive_date DESC), ',',1) as archive_date,
        indx.${calcType}_calculate_name as calcName,
        indx.${calcType}_calculate_unit as calcUnit,
        indx.${indexTypePrefix}_index_merge_hash as indexMergeHash,
        indx.${indexTypePrefix}_index_with_calc_merge_hash as indexWithCalcMergeHash
        from gcdp_dws_index indx inner join gcdp_dws_index_project_note note on indx.index_project_note_id = note.id
        where
        indx.index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        <if test="itemCostType != null">
            AND indx.item_cost_type = #{itemCostType}
        </if>
        group by indx.${indexTypePrefix}_index_with_calc_merge_hash
    </select>

    <sql id="costIndexValid">
        ((indx.${indexTypePrefix}_index_value IS NOT NULL AND indx.${indexTypePrefix}_index_value != 0)
            OR (indx.${indexTypePrefix}_index_value_include_tax IS NOT NULL AND indx.${indexTypePrefix}_index_value_include_tax != 0))
    </sql>

    <sql id="usageIndexValid">
        (indx.${indexTypePrefix}_index_value IS NOT NULL AND indx.${indexTypePrefix}_index_value != 0)
    </sql>

    <sql id="calcValueNotEmptyLatest">
        SUBSTRING_INDEX(GROUP_CONCAT(indx.${calcType}_calculate_value ORDER BY note.archive_date DESC), ',',1)
    </sql>

    <select id="selectDwsIndexByNoteId" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.SingleProjectDbIndex">
        select
        SUBSTRING_INDEX(GROUP_CONCAT(a.id ORDER BY a.id), ',', 1) as id,
        a.pid,
        a.code,
        a.name,
        a.name_path as namePath,
        a.unit,
        a.item_cost_type,
        a.is_share_cost,
        a.calcName as calcName,
        a.calcUnit as calcUnit,
        a.indexUnit as indexUnit,
        group_concat(a.ids) as itemIds,
        group_concat(a.sampleIds) as tempNoteIds,
        <choose>
            <when test="indexTypePrefix == 'jmdf' or indexTypePrefix == 'df'  or indexTypePrefix == 'swl' or indexTypePrefix == 'xmdf'">
                sum(a.indexAmount) as indexFZ,
                sum(a.indexAmountIncludeTax) as indexFZIncludeTax,
            </when>
            <when test="indexTypePrefix == 'jmhl' or indexTypePrefix == 'zyl'">
                sum(a.indexQuantity) as indexFZ,
            </when>
        </choose>
        IF(sum(a.amount) != 0, sum(a.amount), null) as amount,
        IF(sum(a.amountIncludeTax) != 0, sum(a.amountIncludeTax), null) as amountIncludeTax,
        IF(sum(a.quantity) != 0, sum(a.quantity), null) as quantity,
        IF(sum(a.calculateValue) != 0, sum(a.calculateValue), null) as calcValue,
        IF(sum(a.labor_amount) != 0, sum(a.labor_amount), null) as laborAmount,
        IF(sum(a.material_amount) != 0, sum(a.material_amount), null) as materialAmount,
        IF(sum(a.machine_amount) != 0, sum(a.machine_amount), null) as machineAmount,
        IF(sum(a.other_amount) != 0, sum(a.other_amount), null) as otherAmount,
        -- tradeNames是虚拟楼栋对应的专业名称集合；xnldCalcValues是对应专业对应的计算口径值；baseTradeName是归档最晚结构第一个的专业名称；
        -- 代码中处理计算口径取值时，优先从真实楼栋的calcValue里获取，如果获取不到，再结合上述3个字段，获取基准专业对应专业对应计算口径值的累加值作为口径值。
        GROUP_CONCAT(IFNULL(a.tradeName, '')) AS tradeNames,
        GROUP_CONCAT(IFNULL(a.xnldCalculateValue, '0')) AS xnldCalcValues,
        SUBSTRING_INDEX(GROUP_CONCAT(a.tradeName ORDER BY a.archive_date DESC,firstNoteId ASC), ',', 1) AS baseTradeName
        from
        (
        select
        SUBSTRING_INDEX(GROUP_CONCAT(indx.id ORDER BY indx.id), ',', 1) as id,
        indx.pid,
        indx.code,
        indx.name,
        indx.name_path,
        indx.unit,
        indx.item_cost_type,
        indx.is_share_cost,
        sum(indx.labor_amount) as labor_amount,
        sum(indx.material_amount) as material_amount,
        sum(indx.machine_amount) as machine_amount,
        sum(indx.other_amount) as other_amount,
        indx.${calcType}_calculate_name as calcName,
        indx.${calcType}_calculate_unit as calcUnit,
        indx.${indexTypePrefix}_index_unit as indexUnit,
        group_concat(indx.id ORDER BY indx.id) as ids,
        group_concat(indx.index_project_note_id) as sampleIds,
        sum(indx.amount) as amount,
        sum(indx.amount_include_tax) as amountIncludeTax,
        <choose>
            <when test="indexTypePrefix == 'swl'">
                sum(IF(<include refid="costIndexValid"/> OR (note.product_source = 'mbcb' AND indx.zyl_index_value = 1), indx.quantity, 0)) as quantity,
                sum(IF(<include refid="costIndexValid"/>, indx.${calcType}_calculate_value, 0)) as calculateValue,
                NULL as xnldCalculateValue,
                sum(IF(<include refid="costIndexValid"/> AND indx.amount IS NOT NULL AND indx.amount != 0, indx.amount, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value)) as indexAmount,
                sum(IF(<include refid="costIndexValid"/> AND indx.amount_include_tax IS NOT NULL AND indx.amount_include_tax != 0, indx.amount_include_tax, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value_include_tax)) as indexAmountIncludeTax,
            </when>
            <when test="indexTypePrefix == 'jmdf' or indexTypePrefix == 'df' or indexTypePrefix == 'xmdf'">
                sum(IF(<include refid="costIndexValid"/>, indx.quantity, 0)) as quantity,
                IF((sum(indx.${indexTypePrefix}_index_value) != 0) or (sum(indx.${indexTypePrefix}_index_value_include_tax) != 0), <include refid="calcValueNotEmptyLatest"/>, 0) as calculateValue,
                NULL as xnldCalculateValue,
                sum(IF(<include refid="costIndexValid"/> AND indx.amount IS NOT NULL AND indx.amount != 0, indx.amount, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value)) as indexAmount,
                sum(IF(<include refid="costIndexValid"/> AND indx.amount_include_tax IS NOT NULL AND indx.amount_include_tax != 0, indx.amount_include_tax, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value_include_tax)) as indexAmountIncludeTax,
            </when>
            <when test="indexTypePrefix == 'jmhl' or indexTypePrefix == 'zyl'">
                sum(IF(<include refid="usageIndexValid"/>, indx.quantity, 0)) as quantity,
                IF(sum(indx.${indexTypePrefix}_index_value) != 0, <include refid="calcValueNotEmptyLatest"/>, 0) as calculateValue,
                NULL as xnldCalculateValue,
                sum(IF(<include refid="usageIndexValid"/> AND indx.quantity IS NOT NULL AND indx.quantity != 0, indx.quantity, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value)) as indexQuantity,
            </when>
        </choose>
        NULL AS tradeName,
        NULL AS archive_date,
        NULL AS firstNoteId,
        indx.${indexTypePrefix}_index_merge_hash
        from
        gcdp_dws_index indx
        left join gcdp_dws_index_project_note note on indx.index_project_note_id = note.id
        where
        note.`type` in (2,4,5,0)
        <if test="itemCostType != null">
            and indx.item_cost_type = #{itemCostType}
        </if>
        and index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        group by
        ${indexTypePrefix}_index_with_calc_merge_hash
        union
        select
        SUBSTRING_INDEX(GROUP_CONCAT(indx.id ORDER BY indx.id), ',', 1) as id,
        indx.pid,
        indx.code,
        indx.name,
        indx.name_path,
        indx.unit,
        indx.item_cost_type,
        indx.is_share_cost,
        sum(indx.labor_amount) as labor_amount,
        sum(indx.material_amount) as material_amount,
        sum(indx.machine_amount) as machine_amount,
        sum(indx.other_amount) as other_amount,
        indx.${calcType}_calculate_name as calcName,
        indx.${calcType}_calculate_unit as calcUnit,
        indx.${indexTypePrefix}_index_unit as indexUnit,
        group_concat(indx.id ORDER BY indx.id) as ids,
        group_concat(indx.index_project_note_id) as sampleIds,
        sum(indx.amount) as amount,
        sum(indx.amount_include_tax) as amountIncludeTax,
        <choose>
            <when test="indexTypePrefix == 'swl'">
                sum(IF(<include refid="costIndexValid"/> OR (note.product_source = 'mbcb' AND indx.zyl_index_value = 1), indx.quantity, 0)) as quantity,
                sum(IF(<include refid="costIndexValid"/>, indx.${calcType}_calculate_value, 0)) as calculateValue,
                NULL as xnldCalculateValue,
                sum(IF(<include refid="costIndexValid"/> AND indx.amount IS NOT NULL AND indx.amount != 0, indx.amount, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value)) as indexAmount,
                sum(IF(<include refid="costIndexValid"/> AND indx.amount_include_tax IS NOT NULL AND indx.amount_include_tax != 0, indx.amount_include_tax, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value_include_tax)) as indexAmountIncludeTax,
            </when>
            <when test="indexTypePrefix == 'jmdf' or indexTypePrefix == 'df'">
                sum(IF(<include refid="costIndexValid"/>, indx.quantity, 0)) as quantity,
                NULL as calculateValue,
                IF((SUM(indx.${indexTypePrefix}_index_value) != 0) OR (SUM(indx.${indexTypePrefix}_index_value_include_tax) != 0), <include refid="calcValueNotEmptyLatest"/>, 0) AS xnldCalculateValue,
                sum(IF(<include refid="costIndexValid"/> AND indx.amount IS NOT NULL AND indx.amount != 0, indx.amount, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value)) as indexAmount,
                sum(IF(<include refid="costIndexValid"/> AND indx.amount_include_tax IS NOT NULL AND indx.amount_include_tax != 0, indx.amount_include_tax, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value_include_tax)) as indexAmountIncludeTax,
            </when>
            <when test="indexTypePrefix == 'jmhl' or indexTypePrefix == 'zyl'">
                sum(IF(<include refid="usageIndexValid"/>, indx.quantity, 0)) as quantity,
                NULL as calculateValue,
                IF((SUM(indx.${indexTypePrefix}_index_value) != 0), <include refid="calcValueNotEmptyLatest"/>, 0) AS xnldCalculateValue,
                sum(IF(<include refid="usageIndexValid"/> AND indx.quantity IS NOT NULL AND indx.quantity != 0, indx.quantity, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value)) as indexQuantity,
            </when>
        </choose>
        note.trade_name AS tradeName,
        SUBSTRING_INDEX(GROUP_CONCAT(note.archive_date ORDER BY note.archive_date DESC), ',',1) AS archive_date,
        SUBSTRING_INDEX(GROUP_CONCAT(indx.index_project_note_id ORDER BY indx.index_project_note_id ASC), ',', 1) AS firstNoteId,
        indx.${indexTypePrefix}_index_merge_hash
        from
        gcdp_dws_index indx
        left join gcdp_dws_index_project_note note on indx.index_project_note_id = note.id
        where
        note.`type` in (6)
        <if test="itemCostType != null">
            and indx.item_cost_type = #{itemCostType}
        </if>
        and index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        group by
        ${indexTypePrefix}_index_with_calc_merge_hash
        union
        select
        SUBSTRING_INDEX(GROUP_CONCAT(indx.id ORDER BY indx.id), ',', 1) as id,
        indx.pid,
        indx.code,
        indx.name,
        indx.name_path,
        indx.unit,
        indx.item_cost_type,
        indx.is_share_cost,
        sum(indx.labor_amount) as labor_amount,
        sum(indx.material_amount) as material_amount,
        sum(indx.machine_amount) as machine_amount,
        sum(indx.other_amount) as other_amount,
        indx.${calcType}_calculate_name as calcName,
        indx.${calcType}_calculate_unit as calcUnit,
        indx.${indexTypePrefix}_index_unit as indexUnit,
        group_concat(indx.id ORDER BY indx.id) as ids,
        group_concat(indx.index_project_note_id) as sampleIds,
        sum(indx.amount) as amount,
        sum(indx.amount_include_tax) as amountIncludeTax,
        <choose>
            <when test="indexTypePrefix == 'jmdf' or indexTypePrefix == 'df'  or indexTypePrefix == 'swl'">
                sum(IF(<include refid="costIndexValid"/> OR (note.product_source = 'mbcb' AND indx.zyl_index_value = 1), indx.quantity, 0)) as quantity,
                sum(IF(<include refid="costIndexValid"/>, indx.${calcType}_calculate_value, 0)) as calculateValue,
                NULL as xnldCalculateValue,
                sum(IF(<include refid="costIndexValid"/> AND indx.amount IS NOT NULL AND indx.amount != 0, indx.amount, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value)) as indexAmount,
                sum(IF(<include refid="costIndexValid"/> AND indx.amount_include_tax IS NOT NULL AND indx.amount_include_tax != 0, indx.amount_include_tax, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value_include_tax)) as indexAmountIncludeTax,
            </when>
            <when test="indexTypePrefix == 'jmhl' or indexTypePrefix == 'zyl'">
                sum(IF(<include refid="usageIndexValid"/>, indx.quantity, 0)) as quantity,
                sum(IF(<include refid="usageIndexValid"/>, indx.${calcType}_calculate_value, 0)) as calculateValue,
                NULL as xnldCalculateValue,
                sum(IF(<include refid="usageIndexValid"/> AND indx.quantity IS NOT NULL AND indx.quantity != 0, indx.quantity, indx.${calcType}_calculate_value*indx.${indexTypePrefix}_index_value)) as indexQuantity,
            </when>
        </choose>
        NULL AS tradeName,
        NULL AS archive_date,
        NULL AS firstNoteId,
        indx.${indexTypePrefix}_index_merge_hash
        from
        gcdp_dws_index indx
        left join gcdp_dws_index_project_note note on indx.index_project_note_id = note.id
        where
        note.`type` in (1,3)
        <if test="itemCostType != null">
            and indx.item_cost_type = #{itemCostType}
        </if>
        and index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        group by
        ${indexTypePrefix}_index_merge_hash
        ) as a
        group by
        a.${indexTypePrefix}_index_merge_hash order by code,id
    </select>

    <select id="selectByItemIds" resultType="com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsIndex">
        select idx.*, note.archive_date, standard.standard_description from gcdp_dws_index idx
        inner join gcdp_dws_index_project_note note on idx.index_project_note_id = note.id
        left join gcdp_dws_index_build_standards_relationship relationship on idx.id = relationship.dws_index_id
        left join gcdp_dws_build_standard_index standard on relationship.build_standard_id = standard.id
        where idx.id in
        <foreach collection="itemIdList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>

    </select>
</mapper>
