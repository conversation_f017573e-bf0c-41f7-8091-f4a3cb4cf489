<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwsIndexProjectNoteMapper">
    <resultMap id="BaseResultMap" type="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId"/>
        <result column="contract_project_id" jdbcType="BIGINT" property="contractProjectId"/>
        <result column="bidnode_id" jdbcType="BIGINT" property="bidnodeId"/>
        <result column="phase" jdbcType="VARCHAR" property="phase"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="project_category_code" jdbcType="VARCHAR" property="projectCategoryCode"/>
        <result column="project_category_name" jdbcType="VARCHAR" property="projectCategoryName"/>
        <result column="product_source" jdbcType="VARCHAR" property="productSource"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="build_area" jdbcType="DECIMAL" property="buildArea"/>
        <result column="total" jdbcType="DECIMAL" property="total"/>
        <result column="total_include_tax" jdbcType="DECIMAL" property="totalIncludeTax"/>
        <result column="ld_name_identify" jdbcType="VARCHAR" property="ldNameIdentify"/>
        <result column="archive_date" jdbcType="TIMESTAMP" property="archiveDate"/>
        <result column="tbl_create_date" jdbcType="TIMESTAMP" property="tblCreateDate"/>
        <result column="non_construction" jdbcType="BIT" property="nonConstruction"/>
        <result column="include_project_attr" jdbcType="BIT" property="includeProjectAttr"/>
        <result column="include_index_cost" jdbcType="BIT" property="includeIndexCost"/>
        <result column="include_index_usage" jdbcType="BIT" property="includeIndexUsage"/>
        <result column="include_index_main_res" jdbcType="BIT" property="includeIndexMainRes"/>
        <result column="include_index_economics" jdbcType="BIT" property="includeIndexEconomics"/>
        <result column="is_temp" jdbcType="BIT" property="isTemp"/>
        <result column="project_info_json" jdbcType="LONGVARCHAR" property="projectInfoJson"/>
        <result column="project_attr_json" jdbcType="LONGVARCHAR" property="projectAttrJson"/>
        <result column="data_json" jdbcType="LONGVARCHAR" property="dataJson"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="contract_project_total" jdbcType="DECIMAL" property="contractProjectTotal"/>
        <result column="original_template_uuid" jdbcType="VARCHAR" property="originalTemplateUuid"/>
        <result column="item_cost_type" jdbcType="TINYINT" property="itemCostType"/>
        <result column="full_cost_total" jdbcType="DECIMAL" property="fullCostTotal"/>
        <result column="full_cost_total_include_tax" jdbcType="DECIMAL" property="fullCostTotalIncludeTax"/>
        <result column="non_full_cost_total" jdbcType="DECIMAL" property="nonFullCostTotal"/>
        <result column="non_full_cost_total_include_tax" jdbcType="DECIMAL" property="nonFullCostTotalIncludeTax"/>
        <result column="trade_code" jdbcType="VARCHAR" property="tradeCode"/>
        <result column="trade_name" jdbcType="VARCHAR" property="tradeName"/>
        <result column="functional_domain" jdbcType="VARCHAR" property="functionalDomain"/>
        <result column="extend_data_json" jdbcType="LONGVARCHAR" property="extendDataJson"/>
        <result column="project_hash" jdbcType="VARCHAR" property="projectHash"/>
        <result column="dt_hash" jdbcType="VARCHAR" property="dtHash"/>
    </resultMap>
    <select id="selectCondition" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ConditionResultDto">
        SELECT
        ifnull(note.project_category_code, info.project_category_code) as project_category_code,
        ifnull(note.project_category_name, info.project_category_name) as project_category_name,
        note.`phase`,note.product_source,note.trade_name,
        info.province_id,info.province_code,province_name,
        info.city_id,info.city_code,info.city_name,
        info.district_id,info.district_code,info.district_name
        FROM
        gcdp_dws_index_project_note note
        LEFT JOIN gcdp_dwd_project_info info
        ON note.project_code = info.project_code and note.enterprise_id = info.enterprise_id
        WHERE
        (
        (note.enterprise_id = #{enterpriseId,jdbcType=VARCHAR}
        <include refid="com.glodon.gcdp.dwdservice.domain.dao.mapper.DwdProjectQueryCommonMapper.authorityControl"/>
        )
        <if test="sharedEnterpriseIds != null and sharedEnterpriseIds.size >0 ">
            or ( note.enterprise_id in
            <foreach collection="sharedEnterpriseIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        )
        <if test="typeList != null and typeList.size > 0">
            AND note.`type` IN
            <foreach collection="typeList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="productSource != null and productSource.size > 0">
            AND note.`product_source` IN
            <foreach collection="productSource" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        AND note.is_temp = 0
        AND info.is_delete = 0
        AND info.recycle_flag = 0
        AND info.status = 1
        order by note.project_category_code
    </select>

    <select id="selectCategoryCodeAndPhaseAndPosition" resultMap="BaseResultMap">
        SELECT
        DISTINCT note.project_category_code,note.project_category_name,note.`phase`,note.product_position
        FROM
        gcdp_dws_index_project_note note
        LEFT JOIN `gcdp_dwd_project_info` info
        ON note.project_code = info.project_code and note.enterprise_id = info.enterprise_id
        WHERE note.enterprise_id = #{enterpriseId,jdbcType=VARCHAR}
        AND note.is_temp = 0
        AND info.is_delete = 0
        AND info.status = 1
        AND info.recycle_flag = 0
        <include refid="com.glodon.gcdp.dwdservice.domain.dao.mapper.DwdProjectQueryCommonMapper.authorityControl"/>
        <if test="productSource != null and productSource.size() > 0">
            AND  product_source in
            <foreach collection="productSource" open="(" close=")" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by note.project_category_code
    </select>
    <select id="selectNoteByCondition" resultMap="BaseResultMap">
        SELECT
        note.*
        FROM
        gcdp_dws_index_project_note note
        LEFT JOIN `gcdp_dwd_project_info` info
        ON note.project_code = info.project_code and note.enterprise_id = info.enterprise_id
        WHERE note.enterprise_id = #{enterpriseId} AND info.recycle_flag = 0
        <if test="projectNameList != null">
            AND info.project_name in
            <foreach collection="projectNameList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="areaId != null">
            AND (info.province_id = #{areaId,jdbcType=INTEGER} or city_id = #{areaId,jdbcType=INTEGER} or district_id = #{areaId,jdbcType=INTEGER})
        </if>
        <if test="projectScaleStart!=null">
            AND info.project_scale &gt;= #{projectScaleStart}
        </if>
        <if test="projectScaleEnd!=null">
            AND info.project_scale &lt;= #{projectScaleEnd}
        </if>
        AND note.is_temp = 0
        AND info.is_delete = 0
        AND info.status = 1
        <include refid="com.glodon.gcdp.dwdservice.domain.dao.mapper.DwdProjectQueryCommonMapper.authorityControl"/>
        <if test="productSource != null and productSource.size() > 0">
            AND ( product_source in
            <foreach collection="productSource" open="(" close=")" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="phase != null">
            AND note.phase = #{phase}
        </if>
        <if test="position != null">
            AND note.product_position = #{position}
        </if>
        <if test="templateId!=null">
            AND note.original_template_uuid = #{templateId}
        </if>
        <if test="categoryName != null and categoryName.size > 0">
            AND note.project_category_name in
            <foreach collection="categoryName" separator="," close=")" open="(" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectSharedListWithBlobField" resultMap="BaseResultMap">
        SELECT
        <include refid="note_contract_column"/>
        <if test="isNeedContractInfo">, note.project_info_json</if>
        <if test="isNeedFeatureInfo">, note.project_attr_json</if>
        FROM
        gcdp_dws_index_project_note note INNER JOIN gcdp_dwd_contract_project contract ON note.contract_project_id = contract.id
        WHERE
        <foreach collection="sharedEnterpriseList" item="item" open="(" close=")" separator="or">
            ( note.enterprise_id = #{item.enterpriseId,jdbcType=VARCHAR}
            <if test="item.projectCodeList != null and item.projectCodeList.size > 0">
                AND note.project_code IN
                <foreach collection="item.projectCodeList" open="(" close=")" item="subItem" separator=",">
                    #{subItem}
                </foreach>
            </if>
            )
        </foreach>
        <include refid="selectNoteCondition"/>
    </select>
    <select id="selectNoteList" resultMap="BaseResultMap">
        SELECT
        note.id, note.project_code,note.project_category_code,note.project_category_name, note.name, note.ld_name_identify, note.build_area, note.total, note.`phase`, note.`type`,
        note.archive_date, note.project_info_json, note.project_attr_json, note.non_construction, note.contract_project_total, note.product_source,
        note.contract_project_id, note.original_template_uuid,note.product_source, note.project_hash, note.dt_hash
        FROM
        gcdp_dws_index_project_note note
        WHERE note.enterprise_id = #{enterpriseId,jdbcType=VARCHAR}
        <if test="condition.validProjectCodes != null and condition.validProjectCodes.size > 0">
            AND note.project_code IN
            <foreach collection="condition.validProjectCodes" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <include refid="selectNoteCondition"/>
    </select>
    <sql id="selectNoteCondition">
        <if test="typeList != null and typeList.size > 0">
            AND note.`type` IN
            <foreach collection="typeList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.templateUuid != null and condition.templateUuid.size > 0">
            AND note.`original_template_uuid` IN
            <foreach collection="condition.templateUuid" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.categoryCode != null and condition.categoryCode.size > 0">
            AND (
            <foreach collection="condition.categoryCode" open="(" close=")" item="item" separator="OR">
                note.project_category_name = #{item,jdbcType=VARCHAR} or note.project_category_name is null
            </foreach>
            )
            AND note.non_construction = false
        </if>
        <if test="condition.productSource != null and condition.productSource.size() > 0">
            AND ( note.product_source in
            <foreach collection="condition.productSource" open="(" close=")" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="condition.ldNameIdentify != null and condition.ldNameIdentify.trim.length() > 0">
            AND note.ld_name_identify Like concat('%',#{condition.ldNameIdentify},'%')
        </if>
        <if test="condition.startArchiveDate != null">
            AND note.archive_date &gt;= #{condition.startArchiveDate}
        </if>
        <if test="condition.endArchiveDate != null">
            AND note.archive_date &lt;= #{condition.endArchiveDate}
        </if>
        <if test="condition.itemCostType != null">
            <if test="condition.itemCostType == 1">
                AND note.item_cost_type in (1,3)
            </if>
            <if test="condition.itemCostType == 2">
                AND note.item_cost_type in (2,3)
            </if>
        </if>
        <if test="condition.tradeName != null and condition.tradeName.size > 0">
            AND note.`trade_name` IN
            <foreach collection="condition.tradeName" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="phaseList != null and phaseList.size > 0">
            AND note.`phase` IN
            <foreach collection="phaseList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        AND note.is_temp = 0
        order by note.archive_date desc, note.id
    </sql>
    <select id="selectNoteBySingleProjectReqVO" resultMap="BaseResultMap">
        select
            id,
            project_code,
            enterprise_id,
            contract_project_id,
            original_identity,
            original_unique_id,
            bidnode_id,
            phase,
            `name`,
            project_category_code,
            project_category_name,
            trade_code,
            trade_name,
            product_source,
            `type`,
            item_cost_type,
            build_area,
            ifnull(total, non_full_cost_total) as total,
            ifnull(total_include_tax, non_full_cost_total_include_tax) as total_include_tax,
            total as full_cost_total,
            total_include_tax as full_cost_total_include_tax,
            non_full_cost_total,non_full_cost_total_include_tax,
            ld_name_identify,
            archive_date,
            non_construction,
            include_project_attr,
            include_index_jmdf,
            include_index_swldf,
            include_index_dfzb,
            include_index_zylzb,
            include_index_jmhlzb,
            include_index_main_res,
            include_index_economics,
            is_temp,
            contract_project_total,
            contract_project_total_include_tax,
            product_position,
            original_template_uuid,
            extend_data_json,
            project_hash, dt_hash
        from gcdp_dws_index_project_note
        where
            id
            <foreach collection="tempNodeIds" item="id" index="index" open="in(" close=")" separator=",">
                #{id}
            </foreach>
            and product_source
            <foreach collection="productSource" item="item" index="index" open="in(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="typeList != null and typeList.size() != 0">
                and `type`
                <foreach collection="typeList" item="item" index="index" open="in(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="phase != null and phase != ''">
                and `phase` = #{phase}
            </if>
            and enterprise_id = #{enterpriseId,jdbcType=VARCHAR}
            and project_code = #{projectCode,jdbcType=VARCHAR}
        order by archive_date desc
    </select>
    <select id="selectProjectAttrByIds" resultMap="BaseResultMap">
        select project_attr_json,archive_date,`type`,`name`,ld_name_identify,build_area,product_source from gcdp_dws_index_project_note
        <where>
            <if test="ids != null and ids.size() > 0">
                id in
                <foreach collection="ids" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="contractProjectIds != null and contractProjectIds.size() > 0">
                and contract_project_id in
                <foreach collection="contractProjectIds" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
                and type in (4, 6)
            </if>
            and enterprise_id = #{enterpriseId,jdbcType=VARCHAR}
        </where>
        order by archive_date desc
    </select>
    <select id="selectNoteAndProjByIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.SampleNoteDto">
        SELECT
        note.id,
        note.project_code,
        note.`name`,
        note.build_area,
        note.item_cost_type,
        ifnull(note.total, note.non_full_cost_total) amount,
        ifnull(note.total_include_tax, note.non_full_cost_total_include_tax) amountIncludeTax,
        note.ld_name_identify as ld_name_identity,
        note.total as fullCostAmount,
        note.total_include_tax as fullCostAmount,
        note.non_full_cost_total as nonFullCostDfIndexValue,
        note.non_full_cost_total_include_tax as nonFullCostDfIndexValueIncludeTax,
        note.archive_date,
        note.phase as phaseName,
        note.project_category_name as categoryName,
        proj.project_name,
        proj.create_date,
        contract.name as fileName,
        note.product_source, note.project_hash, note.dt_hash
        FROM
        gcdp_dws_index_project_note note
        LEFT JOIN gcdp_dwd_project_info proj ON note.project_code = proj.project_code and note.enterprise_id = proj.enterprise_id
        LEFT JOIN gcdp_dwd_contract_project contract ON note.contract_project_id = contract.id
        WHERE note.id IN
        <foreach collection="noteIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND proj.enterprise_id = #{enterpriseId,jdbcType=VARCHAR}
        AND proj.recycle_flag = 0
        ORDER BY proj.create_date DESC,note.project_code,note.archive_date DESC
    </select>
    <select id="selectNoteByIdsGroupByPhaseAndName" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData">
        SELECT
        GROUP_CONCAT(distinct note.id) as ids,
        note.phase AS phase,
        note.product_position as position,
        note.type,
        GROUP_CONCAT(note.name  ORDER BY note.archive_date DESC) as noteName,
        GROUP_CONCAT(note.ld_name_identify  ORDER BY note.archive_date DESC) as ldNameIdentify,
        SUBSTRING_INDEX(GROUP_CONCAT(note.archive_date ORDER BY note.archive_date DESC), ',',1) as archiveDate,
        SUBSTRING_INDEX(GROUP_CONCAT(note.project_category_name ORDER BY note.archive_date DESC), ',',1) as categoryName,
        proj.project_code as projectCode,
        proj.project_name as projectName,
        proj.province_name as provinceName,
        proj.city_name as cityName,
        proj.district_name as districtName,
        GROUP_CONCAT(contract.name ORDER BY note.archive_date DESC) as fileName
        FROM
        gcdp_dws_index_project_note note
        LEFT JOIN gcdp_dwd_project_info proj ON note.project_code = proj.project_code and note.enterprise_id = proj.enterprise_id
        LEFT JOIN gcdp_dwd_contract_project contract on note.contract_project_id = contract.id
        WHERE note.id IN
        <foreach collection="ids" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND proj.recycle_flag = 0
        group by note.phase,COALESCE(note.ld_name_identify, note.name),note.project_code,note.build_area
        ORDER BY proj.create_date DESC,note.project_code,note.archive_date DESC,note.id
    </select>

    <select id="selectNoteInfoByIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData">
        SELECT
        note.id as ids,
        note.name as noteName,
        note.product_position as position,
        note.ld_name_identify as ldNameIdentify,
        note.archive_date as archiveDate,
        note.project_category_name as categoryName,
        proj.project_name as projectName,
        proj.province_name as provinceName,
        proj.city_name as cityName,
        proj.district_name as districtName,
        note.phase,
        note.project_code as projectCode,
        note.build_area as buildArea,
        contract.name as fileName
        FROM
        gcdp_dws_index_project_note note
        LEFT JOIN gcdp_dwd_project_info proj ON note.project_code = proj.project_code and note.enterprise_id = proj.enterprise_id
        LEFT JOIN gcdp_dwd_contract_project contract ON note.contract_project_id = contract.id
        WHERE note.id IN
        <foreach collection="ids" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND proj.recycle_flag = 0
        ORDER BY proj.create_date DESC,note.project_code,note.archive_date DESC,note.id
    </select>
    <select id="selectNoteByIds" resultMap="BaseResultMap">
        select
        id,
        project_code ,
        enterprise_id ,
        contract_project_id ,
        bidnode_id,
        `phase` as phase,
        name,
        project_category_code ,
        project_category_name ,
        product_source ,
        type ,
        build_area ,
        total ,
        ld_name_identify,
        archive_date,
        tbl_create_date ,
        non_construction ,
        include_project_attr ,
        include_index_cost,
        include_index_usage ,
        include_index_main_res,
        include_index_economics,
        is_temp ,
        contract_project_total,
        original_template_uuid
        from
        gcdp_dws_index_project_note where id in
        <foreach collection="noteIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getTotal"  resultMap="BaseResultMap">
        SELECT
            id,contract_project_id,name, product_source, total, total_include_tax,
            contract_project_total, contract_project_total_include_tax, project_code, enterprise_id
        FROM
        gcdp_dws_index_project_note
        WHERE id IN
        <foreach collection="ids" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectNoteByContractProjectIds" resultType="java.lang.Long">
        select id from gcdp_dws_index_project_note
        where contract_project_id in
        <foreach collection="contractProjectId" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectNoteByBidNodeIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote">
        select * from gcdp_dws_index_project_note
        where enterprise_id = #{enterpriseId} and
        bidnode_id in
        <foreach collection="bidNodeIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        order by archive_date desc;
    </select>
    <select id="queryAllSimple" resultType="com.glodon.gcdpindexsearch.dynamic.domain.entity.SimpleProjectNode">
        select
        proj.project_name as projectName, proj.id as projectId, note.name as nodeName, note.contract_project_id as nodeId,
        0 as selectFlag, proj.id as oldProjectId, note.contract_project_id as oldNodeId, note.enterprise_id as enterpriseId,
        note.item_cost_type
        from
        gcdp_dws_index_project_note note
        left join
        gcdp_dwd_project_info proj
        on
        note.project_code = proj.project_code and note.enterprise_id = proj.enterprise_id
        where
        note.enterprise_id in (
        <foreach collection="sharedEnterpriseIds" item="item" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        )
        <if test="productSource != null and productSource.size() != 0">
            and note.product_source IN (
            <foreach collection="productSource" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        and note.is_temp = 0
        AND proj.recycle_flag = 0
        <if test="projectCodes != null">
            and note.project_code in
            <foreach collection="projectCodes" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY proj.create_date DESC,note.project_code,note.archive_date DESC
    </select>

<!--    <select id="selectNotesByIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote">-->
<!--        select-->
<!--        id,-->
<!--        contract_project_id,-->
<!--        bidnode_id,-->
<!--        archive_date,-->
<!--        original_template_uuid-->
<!--        from gcdp_dws_index_project_note-->
<!--        where-->
<!--            id in-->
<!--        <foreach collection="ids" item="item" close=")" open="(" separator=",">-->
<!--            #{item}-->
<!--        </foreach>-->
<!--    </select>-->

    <select id="selectByContractProjectIds" resultMap="BaseResultMap">
        select
            *
        from
            gcdp_dws_index_project_note
        where
            enterprise_id = #{enterpriseId}
        <if test="contractProjectIds != null and contractProjectIds.size() != 0">
            and contract_project_id in
            <foreach collection="contractProjectIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="bidnodeIds != null and bidnodeIds.size() != 0">
            and bidnode_id in
            <foreach collection="bidnodeIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectNotesByIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote">
        select
        id,
        contract_project_id,
        bidnode_id,
        archive_date,
        total_include_tax,
        product_source,
        project_hash,
        dt_hash,
        enterprise_id,
        project_code,
        phase
        from gcdp_dws_index_project_note
        where
            id in
        <foreach collection="ids" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectCountByEnterpriseIdAndProductSource" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM gcdp_dws_index_project_note
        WHERE enterprise_id = #{enterpriseId,jdbcType=VARCHAR}
        AND product_source
        IN <foreach collection="set" item="item" open="(" close=")" separator=",">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectNotesByContractProjectIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote">
        select id,
               project_code,
               enterprise_id,
               contract_project_id,
               original_identity,
               original_unique_id,
               bidnode_id,
               phase,
               `name`,
               project_category_code,
               project_category_name,
               product_source,
               `type`,
               build_area,
               IFNULL(total, non_full_cost_total) as total,
               IFNULL(total_include_tax, non_full_cost_total_include_tax) as total_include_tax,
               total as full_cost_total,
               total_include_tax as full_cost_total_include_tax,
               non_full_cost_total,
               non_full_cost_total_include_tax,
               item_cost_type,
               ld_name_identify,
               archive_date,
               non_construction,
               include_project_attr,
               include_index_jmdf,
               include_index_swldf,
               include_index_dfzb,
               include_index_zylzb,
               include_index_jmhlzb,
               include_index_main_res,
               include_index_economics,
               project_info_json,
               project_attr_json,
               is_temp,
               contract_project_total,
               contract_project_total_include_tax,
               product_position,
               original_template_uuid
        from gcdp_dws_index_project_note
        WHERE contract_project_id in
        <foreach collection="contractProjectId" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectZbgxNotesByIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ZbgxDwsIndexProjectNote">
        SELECT
        note.id as indexProjectNoteId,
        contract.project_name as projectName,
        note.name as bidnodeName,
        note.contract_project_id as contractProjectId,
        note.bidnode_id as bidnodeId,
        note.product_source as productSource
        FROM gcdp_dws_index_project_note note
        LEFT JOIN gcdp_dwd_contract_project contract ON note.contract_project_id = contract.id
        WHERE
        note.id IN
        <foreach collection="ids" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectAllNotesByIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote">
        select
        id,
        contract_project_id,
        bidnode_id,
        enterprise_id,
        `name`,
        `type`,
        project_code,
        phase,
        project_category_name,
        ld_name_identify,
        build_area,
        product_source,
        archive_date
        from gcdp_dws_index_project_note
        where
        id in
        <foreach collection="ids" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectWholeNoteByHash" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote">
        SELECT
        <include refid="note_contract_column"/>
        <if test="isNeedBlobField">
            ,note.project_info_json, note.project_attr_json
        </if>
        FROM
        gcdp_dws_index_project_note note INNER JOIN gcdp_dwd_contract_project contract ON note.contract_project_id =
        contract.id
        WHERE
        (note.${hashFieldName} in
        <foreach collection="hashSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        or note.id in
        <foreach collection="idSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        AND note.is_temp = 0
        <if test="productSources != null and productSources.size() != 0">
            AND note.product_source
            IN
            <foreach collection="productSources" item="item" open="(" close=")" separator=",">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        order by note.archive_date desc, note.id
    </select>

    <sql id="note_contract_column">
        note.id, note.project_code, note.project_category_code, note.project_category_name, note.name, note.ld_name_identify, note.build_area,
        ifnull(note.total, note.non_full_cost_total) as total, ifnull(note.total_include_tax, note.non_full_cost_total_include_tax) as total_include_tax,
        note.total as full_cost_total,note.total_include_tax as full_cost_total_include_tax,note.non_full_cost_total,note.non_full_cost_total_include_tax,
        note.`phase`, note.`type`, note.trade_code, note.trade_name,
        note.archive_date, note.non_construction, note.contract_project_total, note.contract_project_total_include_tax,
        note.product_source, note.contract_project_id, note.original_template_uuid, note.enterprise_id,note.item_cost_type,
        note.non_full_cost_total,note.non_full_cost_total_include_tax,
        contract.name as fileName, note.project_hash, note.dt_hash
    </sql>

    <update id="batchUpdateJson">
        <foreach collection="list" item="item" separator=";">
            update
            `gcdp_dws_index_project_note`
            set
            `project_info_json` = #{item.projectInfoJson,jdbcType=LONGVARCHAR},
            `project_attr_json` = #{item.projectAttrJson,jdbcType=LONGVARCHAR},
            `refresh_flag` = 1
            where id = #{item.id}
        </foreach>
    </update>
    <select id="selectRefreshEnterprise" resultType="com.glodon.gcdp.dwdservice.domain.model.refresh.RefreshEnterprise">
        SELECT enterprise_id, COUNT(*) AS noteCount
        FROM gcdp_dws_index_project_note
        <where>
            AND refresh_flag = 0
            <if test="enterpriseIds != null and !enterpriseIds.isEmpty()">
                AND enterprise_id IN (
                <foreach collection="enterpriseIds" item="item" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
                )
            </if>
        </where>
        GROUP BY enterprise_id
        ORDER BY noteCount
    </select>
</mapper>
