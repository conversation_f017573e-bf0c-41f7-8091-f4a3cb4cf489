<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwsNewArchiveDataMapper">
    <resultMap id="BaseResultMap" type="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsNewArchiveData">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="archive_date" jdbcType="TIMESTAMP" property="archiveDate"/>
        <result column="product_source" jdbcType="VARCHAR" property="productSource"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId"/>
        <result column="contract_project_id" jdbcType="BIGINT" property="contractProjectId"/>
        <result column="tbl_create_date" jdbcType="TIMESTAMP" property="tblCreateDate"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsNewArchiveData">
        <result column="data_json" jdbcType="LONGVARCHAR" property="dataJson"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, type, archive_date, product_source, project_code, enterprise_id, contract_project_id,
        tbl_create_date
    </sql>
    <select id="getDataJsonByProjectCode" resultMap="ResultMapWithBLOBs">
        select data_json from gcdp_dws_new_archive_data
        where
        enterprise_id =#{enterpriseId}
        and project_code = #{projectCode}
        <if test="phase != null and phase.length > 0 ">
            and phase = #{phase}
        </if>
        and type = 1
    </select>
    <select id="getIncludeByProjectCode" resultMap="ResultMapWithBLOBs">
        select data_json,type from gcdp_dws_new_archive_data
        where
        enterprise_id =#{enterpriseId}
        and project_code = #{projectCode}
        <if test="phase != null and phase.length > 0 ">
            and phase = #{phase}
        </if>
        and type = 1
    </select>

</mapper>
