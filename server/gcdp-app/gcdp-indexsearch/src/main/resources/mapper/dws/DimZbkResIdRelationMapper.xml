<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DimZbkResIdRelationMapper">

    <select id="getResIdAndOriginalProjectIdRelation" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DimZbkProjectNoteRes">
        SELECT zbk.zb_project_note_res_id, zbk.original_project_id, contract.original_identity
        FROM gcdp_dwd_contract_project as contract
        JOIN gcdp_dwd_project_info as project
            ON contract.project_id = project.id
        JOIN gcdp_dim_zbk_project_note_res as zbk
            ON zbk.original_project_id = contract.original_project_id AND zbk.project_code = project.project_code AND zbk.customer_code = project.customer_code
        WHERE zbk.zb_project_note_res_id IN
        <foreach collection="resIdList" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>