<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwsCostDataSearchMapper">
    <select id="selectQCBZBLibJmdfCostIndexByIds"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupQCBZBLibJMDFDto">
        select
            gdic.id,
            gdic.index_project_note_id as indexProjectNoteId,
            gdic.name,
            gdic.unit,
            gdic.jm_index_value as jmIndexValue,
            gdic.jm_index_value_include_tax as jmIndexValueIncludeTax,
            gdic.jm_calculate_value as buildArea,
            convert(gdic.jm_calculate_value * gdic.jm_index_value, decimal(30,5)) as amount,
            convert(gdic.jm_calculate_value * gdic.jm_index_value_include_tax, decimal(30,5)) as amountIncludeTax,
            gdbsi.standard_description as standardValue
        from gcdp_dws_index_cost gdic
        left join gcdp_dws_index_cost_build_standards_relationship gdicm on gdic.id = gdicm.index_cost_id
        left join gcdp_dws_build_standard_index gdbsi on gdicm.build_standard_id = gdbsi.id
        where gdic.id in
        <foreach collection="itemIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and gdic.jm_index_value_include_tax > 0;
    </select>
    <select id="selectQCBZBLibDfhlCostIndexByIds"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupQCBZBLibDFHLDto">
        select
            gdiu.id,
            gdiu.index_project_note_id as indexProjectNoteId,
            gdiu.name,
            gdiu.unit,
            gdiu.zyl_index_value as zylIndexValue,
            gdiu.zyl_calculate_name as calculateName,
            gdiu.zyl_calculate_value as calculateValue,
            convert(gdiu.zyl_calculate_value * gdiu.zyl_index_value, decimal(30,5)) as quantity,
            gdbsi.standard_description as standardValue
        from gcdp_dws_index_usage gdiu
        left join gcdp_dws_index_usage_build_standards_relationship gdicm on gdiu.id = gdicm.index_usage_id
        left join gcdp_dws_build_standard_index gdbsi on gdicm.build_standard_id = gdbsi.id
        where gdiu.id in
        <foreach collection="itemIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and gdiu.zyl_index_value > 0;
    </select>
    <select id="selectQCBZBlibDfzjCostIndexByIds"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupQCBZBLibDFZJDto">
        select
            gdic.id,
            gdic.index_project_note_id as indexProjectNoteId,
            gdic.name,
            gdic.unit,
            gdic.df_index_value as dfIndexValue,
            gdic.df_index_value_include_tax as dfIndexValueIncludeTax,
            gdic.df_calculate_name as calculateName,
            gdic.df_calculate_value as calculateValue,
            convert(gdic.df_calculate_value * gdic.df_index_value, decimal(30,5)) as amount,
            convert(gdic.df_calculate_value * gdic.df_index_value_include_tax, decimal(30,5)) as amountIncludeTax,
            gdbsi.standard_description as standardValue
        from gcdp_dws_index_cost gdic
        left join gcdp_dws_index_cost_build_standards_relationship gdicm on gdic.id = gdicm.index_cost_id
        left join gcdp_dws_build_standard_index gdbsi on gdicm.build_standard_id = gdbsi.id
        where gdic.id in
        <foreach collection="itemIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and gdic.df_index_value_include_tax > 0;
    </select>
    <select id="selectQCBZBlibZhdjCostIndexByIds"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupQCBZBLibZHDJDto">
        select
            gdic.id,
            gdic.index_project_note_id as indexProjectNoteId,
            gdic.name,
            gdic.unit,
            gdic.swl_index_value as swlIndexValue,
            gdic.swl_index_value_include_tax as swlIndexValueIncludeTax,
            gdic.swl_calculate_value as quantity,
            convert(gdic.swl_calculate_value * gdic.swl_index_value, decimal(30,5)) as amount,
            convert(gdic.swl_calculate_value * gdic.swl_index_value_include_tax, decimal(30,5)) as amountIncludeTax,
            gdbsi.standard_description as standardValue
        from gcdp_dws_index_cost gdic
        left join gcdp_dws_index_cost_build_standards_relationship gdicm on gdic.id = gdicm.index_cost_id
        left join gcdp_dws_build_standard_index gdbsi on gdicm.build_standard_id = gdbsi.id
        where gdic.id in
        <foreach collection="itemIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and gdic.swl_index_value_include_tax > 0;
    </select>
    <select id="selectProjectInfoByNoteIds"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupBaseDto">
        select
            gdipn.id as indexProjectNoteId,
            gdipn.project_category_name as categoryName,
            gdipn.product_position as position,
            gdipn.phase,
            gdpi.project_code as projectCode,
            gdpi.project_name as projectName,
            gdpi.province_name as provinceName,
            gdpi.province_id as provinceId,
            gdpi.city_name as cityName,
            gdpi.city_id as cityId,
            gdpi.district_name as districtName,
            gdpi.district_id as districtId,
            gdcp.name as fileName
        from gcdp_dws_index_project_note gdipn
        left join gcdp_dwd_project_info gdpi on gdipn.enterprise_id = gdpi.enterprise_id and gdipn.project_code = gdpi.project_code
        left join gcdp_dwd_contract_project gdcp on gdipn.contract_project_id = gdcp.id
        where gdipn.id in
        <foreach collection="indexProjectNoteIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND gdpi.recycle_flag = 0
    </select>

    <select id="selectJZZFZBLibDfzjCostIndexByIds"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupJZZFZBLibDFZJDto">

        select
        gdic.id,
        gdic.index_project_note_id as indexProjectNoteId,
        gdic.name,
        gdic.df_index_unit as indexUnit,
        gdic.df_index_value as dfIndexValue,
        gdic.df_index_value_include_tax as dfIndexValueIncludeTax,
        gdic.df_calculate_name as calculateName,
        gdic.df_calculate_value as calculateValue,
        convert(gdic.df_calculate_value * gdic.df_index_value, decimal(30,5)) as amount,
        convert(gdic.df_calculate_value * gdic.df_index_value_include_tax, decimal(30,5)) as amountIncludeTax,
        gdbsi.name as standardName,
        gdbsi.standard_description as standardValue
        from gcdp_dws_index_cost_build_standards_relationship gdicm
        left join gcdp_dws_index_cost gdic on gdic.id = gdicm.index_cost_id
        left join gcdp_dws_build_standard_index gdbsi on gdicm.build_standard_id = gdbsi.id and gdbsi.contract_project_id = gdicm.contract_project_id
        where
        gdbsi.id in
        <foreach collection="itemIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and gdic.df_index_value_include_tax > 0;
    </select>

    <select id="selectJZZFZBLibZhdjCostIndexByIds"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupJZZFZBLibZHDJDto">
        select
        gdic.id,
        gdic.index_project_note_id as indexProjectNoteId,
        gdic.name,
        gdic.swl_index_value as swlIndexValue,
        gdic.swl_index_value_include_tax as swlIndexValueIncludeTax,
        gdic.swl_index_unit as indexUnit,
        gdic.swl_calculate_value as quantity,
        gdbsi.name as standardName,
        gdbsi.standard_description as standardValue,
        convert(gdic.swl_calculate_value * gdic.swl_index_value, decimal(30,5)) as amount,
        convert(gdic.swl_calculate_value * gdic.swl_index_value_include_tax, decimal(30,5)) as amountIncludeTax
        from gcdp_dws_index_cost_build_standards_relationship gdicm
        left join gcdp_dws_index_cost gdic on gdic.id = gdicm.index_cost_id
        left join gcdp_dws_build_standard_index gdbsi on gdicm.build_standard_id = gdbsi.id and gdbsi.contract_project_id = gdicm.contract_project_id
        where
        gdbsi.id in
        <foreach collection="itemIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and gdic.swl_index_value_include_tax > 0;
    </select>
</mapper>