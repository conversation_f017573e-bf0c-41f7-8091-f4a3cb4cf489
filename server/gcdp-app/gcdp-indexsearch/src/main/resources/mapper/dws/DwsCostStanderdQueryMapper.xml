<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwsCostStanderdQueryMapper">
    <select id="selectReferenceDataToQYXEZBLib"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYXEZBDto">
        select
        gdie.id,
        gdie.unit,
        gdie.name,
        gdie.index_project_note_id as projectNoteId,
        gdie.contract_project_id as contractProjectId,
        gdie.index_value_include_tax as indexValueIncludeTax,
        gdie.formula
        from gcdp_dws_index_economics gdie
        where
        gdie.name = #{name} and
        gdie.index_value_include_tax > 0 and
        gdie.index_project_note_id in
        <foreach collection="projectNoteIdsMap.entrySet()" index="noteId" close=")" open="(" separator=",">
            #{noteId}
        </foreach>
    </select>

    <select id="selectReferenceDataToQYJZBZLib"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYJZBZDto">
        select
        gdbsi.id,
        gdbsi.name,
        gdic.unit,
        gdbsi.standard_description as standardValue,
        gdic.contract_project_id as contractProjectId,
        gdic.index_project_note_id as projectNoteId,
        gdiu.zyl_calculate_name as zylCalculateName,
        IFNULL(gdic.df_calculate_name, gdic.swl_calculate_name) as caculateName,
        IFNULL(gdic.swl_index_value, gdic.df_index_value) as rate,
        IFNULL(gdic.swl_index_value_include_tax, gdic.df_index_value_include_tax) as rateIncludeTax
        from gcdp_dws_index_cost gdic
        left join gcdp_dws_index_cost_build_standards_relationship gdicm on gdicm.index_cost_id = gdic.id
        left join gcdp_dws_build_standard_index gdbsi on gdicm.build_standard_id = gdbsi.id
        left join gcdp_dws_index_usage gdiu on gdiu.item_hash = gdic.item_hash and gdiu.index_project_note_id = gdic.index_project_note_id
        where
        gdbsi.name = #{name} and
        gdbsi.standard_description is not null and
        (gdic.df_calculate_name is not null || gdic.swl_calculate_name is not null) and
        (gdic.df_index_value is not null || gdic.df_index_value_include_tax is not null || gdic.swl_index_value || gdic.swl_index_value_include_tax is not null) and
        gdic.index_project_note_id in
        <foreach collection="projectNoteIdsMap.entrySet()" index="noteId" close=")" open="(" separator=",">
            #{noteId}
        </foreach>
    </select>

    <select id="selectReferenceDataToQYJZCBLib"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYJZCBDto">
        <!--单方含量 或 （单方造价或综合单价） 为空， 不显示该记录-->
        select
        gdic.id,
        gdic.name,
        gdbsi.name as standardName,
        gdbsi.standard_description as standardValue,
        gdic.unit,
        gdic.contract_project_id as contractProjectId,
        gdic.index_project_note_id as projectNoteId,
        gdic.item_cost_type,
        IFNULL(gdic.df_calculate_name, gdic.swl_calculate_name) as caculateName,
        gdiu.zyl_calculate_name as zylCalculateName,
        gdic.swl_index_value as swlIndexValue,
        gdic.swl_index_value_include_tax as swlIndexValueIncludeTax,
        gdic.df_index_value as dfIndexValue,
        gdic.df_index_value_include_tax as dfIndexValueIncludeTax,
        gdiu.zyl_index_value as zylIndexValue
        from gcdp_dws_index_cost gdic
        left join gcdp_dws_index_cost_build_standards_relationship gdicm on gdicm.index_cost_id = gdic.id
        left join gcdp_dws_build_standard_index gdbsi on gdicm.build_standard_id = gdbsi.id
        left join gcdp_dws_index_usage gdiu on gdiu.item_hash = gdic.item_hash and gdiu.index_project_note_id = gdic.index_project_note_id
        where
        gdic.name = #{name} and
        (gdiu.zyl_index_value > 0 and (gdic.df_index_value > 0 or gdic.df_index_value_include_tax > 0
        or gdic.swl_index_value > 0 or gdic.swl_index_value_include_tax > 0)) and
        gdic.index_project_note_id in
        <foreach collection="projectNoteIdsMap.entrySet()" index="noteId" close=")" open="(" separator=",">
            #{noteId}
        </foreach>
        <if test="itemCostType != null">
            and gdic.item_cost_type = #{itemCostType}
        </if>
    </select>

    <select id="selectProjectInfoByOrgIdsAndEnterprisedId"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto">
        select
        gdcp.id as contractProjectId,
        gdipn.id as projectNoteId,
        gdipn.archive_date as archiveDate,
        gdipn.project_category_name as projectCategoryName,
        gdipn.name as nodeName,
        gdipn.phase,
        info.project_name as projectName,
        info.project_code as projectCode,
        info.province_name as provinceName,
        info.province_id as provinceId,
        info.city_name as cityName,
        info.city_id as cityId,
        info.district_name as districtName,
        info.district_id as districtId,
        gdcp.product_source as dataSoure,
        gdcp.stage,
        gdipn.non_construction as nonConstruction,
        IF(gdcp.product_source = 'zbsq', info.product_positioning, gdipn.product_position) as productPosition,
        IF(gdcp.product_source = 'zbsq', gdcp.old_project_name, gdcp.name) as fileName
        from gcdp_dws_index_project_note gdipn
        left join gcdp_dwd_contract_project gdcp on gdipn.contract_project_id = gdcp.id
        left join gcdp_dwd_project_info info on info.enterprise_id = gdcp.enterprise_id and info.project_code = gdcp.project_code
        where
        gdipn.enterprise_id = #{enterpriseId} and
        gdipn.is_temp=0 AND info.recycle_flag = 0
        <include refid="com.glodon.gcdp.dwdservice.domain.dao.mapper.DwdProjectQueryCommonMapper.authorityControl"/>
        and gdcp.product_source in
        <foreach collection="dataSourceList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        <if test="projectNameList != null and projectNameList.size > 0">
            and info.project_name in
            <foreach collection="projectNameList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>