<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexEconomicsMapper">
    <resultMap id="BaseResultMap" type="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndexEconomics">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="ids" jdbcType="VARCHAR" property="ids"/>
        <result column="pid" jdbcType="BIGINT" property="pid"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="name_path" jdbcType="VARCHAR" property="namePath"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="formula" jdbcType="VARCHAR" property="formula"/>
        <result column="fm_value_include_tax" jdbcType="VARCHAR" property="fmValue"/>
        <result column="fz_value_include_tax" jdbcType="VARCHAR" property="fzValue"/>
        <result column="project_un_ld_merge_hash" jdbcType="VARCHAR" property="projectUnLdMergeHash"/>
    </resultMap>
    <sql id="getProjectIndex">
        SELECT
        economics.id,
        economics.pid,
        economics.code,
        GROUP_CONCAT(economics.id) as ids,
        economics.name,
        economics.name_path,
        economics.unit,
        SUBSTRING_INDEX(GROUP_CONCAT(economics.formula ORDER BY note.archive_date DESC), ',',1) as formula,
        SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN economics.index_value_include_tax IS NOT NULL THEN note.archive_date END ORDER BY note.archive_date DESC), ',', 1) as archive_date,
        IF(economics.fm_type = 0 || economics.fm_type = 3,
        IF(SUM(economics.index_value_include_tax) IS NOT NULL AND SUM(economics.index_value_include_tax) !=0, SUBSTRING_INDEX(GROUP_CONCAT(economics.fm_value_include_tax ORDER BY note.archive_date DESC), ',',1), IF(SUM(economics.index_value) IS NOT NULL AND SUM(economics.index_value) !=0, SUBSTRING_INDEX(GROUP_CONCAT(economics.fm_value ORDER BY note.archive_date DESC), ',',1), 0)),
        SUM(IF(economics.index_value_include_tax IS NOT NULL AND economics.index_value_include_tax != 0, economics.fm_value_include_tax, IF(economics.index_value IS NOT NULL AND economics.index_value != 0, economics.fm_value, 0)))) AS fm_value_include_tax,
        IF(economics.fz_type = 0 || economics.fz_type = 3,
        IF(SUM(economics.index_value_include_tax) IS NOT NULL AND SUM(economics.index_value_include_tax) !=0, SUBSTRING_INDEX(GROUP_CONCAT(economics.fz_value_include_tax ORDER BY note.archive_date DESC), ',',1), IF(SUM(economics.index_value) IS NOT NULL AND SUM(economics.index_value) !=0, SUBSTRING_INDEX(GROUP_CONCAT(economics.fz_value ORDER BY note.archive_date DESC), ',',1), 0)),
        SUM(IF(economics.index_value_include_tax IS NOT NULL AND economics.index_value_include_tax != 0, economics.fz_value_include_tax, IF(economics.index_value IS NOT NULL AND economics.index_value != 0, economics.fz_value, 0)))) AS fz_value_include_tax,
        economics.project_un_ld_merge_hash
        FROM gcdp_dws_index_economics economics
        LEFT JOIN gcdp_dws_index_project_note note ON economics.index_project_note_id = note.id
        WHERE  note.`type` IN (4,5,6)
        AND economics.index_project_note_id IN
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        GROUP BY economics.project_ld_merge_hash
        UNION
        SELECT
        economics.id,
        economics.pid,
        economics.code,
        GROUP_CONCAT(economics.id) as ids,
        economics.name,
        economics.name_path,
        economics.unit,
        SUBSTRING_INDEX(GROUP_CONCAT(economics.formula ORDER BY note.archive_date DESC), ',',1) as formula,
        SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN economics.index_value_include_tax IS NOT NULL THEN note.archive_date END ORDER BY note.archive_date DESC), ',',1) as archive_date,
        SUM(IF(economics.index_value_include_tax IS NOT NULL AND economics.index_value_include_tax != 0, economics.fm_value_include_tax, IF(economics.index_value IS NOT NULL AND economics.index_value != 0, economics.fm_value, 0))) AS fm_value_include_tax,
        SUM(IF(economics.index_value_include_tax IS NOT NULL AND economics.index_value_include_tax != 0, economics.fz_value_include_tax, IF(economics.index_value IS NOT NULL AND economics.index_value != 0, economics.fz_value, 0))) AS fz_value_include_tax,
        economics.project_un_ld_merge_hash
        FROM gcdp_dws_index_economics economics
        LEFT JOIN gcdp_dws_index_project_note note ON economics.index_project_note_id =note.id
        WHERE
        note.`type` NOT IN (4,5,6)
        AND economics.index_project_note_id IN
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        GROUP BY
        economics.project_un_ld_merge_hash
    </sql>
    <select id="getIndex" resultMap="BaseResultMap">
        SELECT
        a.id,
        a.pid,
        GROUP_CONCAT(a.ids) as ids,
        a.name,
        a.name_path,
        a.unit,
        SUBSTRING_INDEX(GROUP_CONCAT(a.formula ORDER BY a.archive_date DESC), ',',1) as formula,
        SUM(a.fm_value_include_tax) AS fm_value_include_tax,
        SUM(a.fz_value_include_tax) AS fz_value_include_tax,
        a.project_un_ld_merge_hash
        FROM (<include refid="getProjectIndex"></include>) a
        GROUP BY a.project_un_ld_merge_hash
        ORDER BY a.code, a.id
        --         经济技术指标，没有计算口径，三个hash都是一样的
    </select>
    <select id="getItemIndex" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexEconomic">
        SELECT
        a.id,
        a.pid,
        GROUP_CONCAT(a.ids) AS ids,
        a.name,
        IFNULL(a.unit,'') AS unit,
        GROUP_CONCAT(a.sampleIds) AS sampleIds,
        CONCAT(IFNULL(CONVERT(MIN(index_value_include_tax),DECIMAL(30,5)),'-'),'~',
        IFNULL(CONVERT(MAX(index_value_include_tax),DECIMAL(30,5)),'-')) AS maxAndMin,
        IFNULL(CONVERT(SUM(index_value_include_tax)/COUNT(index_value_include_tax!=0),DECIMAL(30,5)),'-') AS `avg`,
        IFNULL(CONVERT(SUM(a.fz_value_include_tax)/SUM(a.fm_value_include_tax),DECIMAL(30,5)),'-') AS weightedAvg,
        COUNT(index_value_include_tax!=0) AS sampleCount
        FROM (<include refid="economicLdSumData"/>) a
        GROUP BY a.subject_un_ld_merge_hash
        ORDER BY code
    </select>

    <sql id="economicLdSumData">
        SELECT
        b.id,
        b.code,
        b.pid,
        b.ids,
        b.`name`,
        b.unit,
        b.sampleIds,
        b.index_value_include_tax_str,
        b.fm_value_include_tax,
        b.fz_value_include_tax,
        CONVERT(b.fz_value_include_tax/b.fm_value_include_tax,DECIMAL(30,5)) AS index_value_include_tax,
        b.subject_un_ld_merge_hash
        FROM (
        SELECT
        economics.id,
        economics.code,
        economics.pid,
        GROUP_CONCAT(economics.id) AS ids,
        economics.name,
        IFNULL(economics.unit,'') AS unit,
        GROUP_CONCAT(economics.index_project_note_id) AS sampleIds,
        GROUP_CONCAT(economics.index_value_include_tax) AS index_value_include_tax_str,
        IF(economics.fm_type = 0 || economics.fm_type = 3,
        IF(SUM(IFNULL(economics.index_value_include_tax, economics.index_value)) IS NOT NULL AND SUM(IFNULL(economics.index_value_include_tax, economics.index_value)) !=0,
        SUBSTRING_INDEX(GROUP_CONCAT(IFNULL(economics.fm_value_include_tax, economics.fm_value) ORDER BY note.archive_date DESC), ',',1), 0),
        SUM(IF(economics.index_value_include_tax IS NOT NULL AND economics.index_value_include_tax != 0, economics.fm_value_include_tax, IF(economics.index_value IS NOT NULL AND economics.index_value != 0, economics.fm_value, 0)))) AS fm_value_include_tax,
        IF(economics.fz_type = 0 || economics.fz_type = 3,
        IF(SUM(IFNULL(economics.index_value_include_tax, economics.index_value)) IS NOT NULL AND SUM(IFNULL(economics.index_value_include_tax, economics.index_value)) !=0,
        SUBSTRING_INDEX(GROUP_CONCAT(IFNULL(economics.fz_value_include_tax, economics.fz_value) ORDER BY note.archive_date DESC), ',',1), 0),
        SUM(IF(economics.index_value_include_tax IS NOT NULL AND economics.index_value_include_tax != 0, economics.fz_value_include_tax, IF(economics.index_value IS NOT NULL AND economics.index_value != 0, economics.fz_value, 0)))) AS fz_value_include_tax,
        economics.subject_un_ld_merge_hash
        FROM gcdp_dws_index_economics economics
        LEFT JOIN gcdp_dws_index_project_note note ON economics.index_project_note_id = note.id
        WHERE economics.index_project_note_id IN
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        <if test="showAll == null or showAll == 0">
            AND (economics.index_value_include_tax > 0 or economics.index_value > 0)
        </if>
        GROUP BY economics.subject_ld_merge_hash) b
    </sql>

    <select id="selectByItemIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexEconomics">
        select * from gcdp_dws_index_economics economics
        inner join gcdp_dws_index_project_note note on economics.index_project_note_id = note.id
        where
        economics.id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
    </select>
</mapper>
