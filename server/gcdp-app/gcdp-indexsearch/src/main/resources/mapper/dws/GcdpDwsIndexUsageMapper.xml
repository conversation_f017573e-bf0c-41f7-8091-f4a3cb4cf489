<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexUsageMapper">
    <resultMap id="BaseResultMap" type="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndexUsage">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="ids" jdbcType="VARCHAR" property="ids"/>
        <result column="pid" jdbcType="BIGINT" property="pid"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="zyl_calculate_value" jdbcType="DECIMAL" property="zylCalculateValue"/>
        <result column="jmhl_calculate_value" jdbcType="DECIMAL" property="jmhlCalculateValue"/>
    </resultMap>
    <select id="getIndex" resultMap="BaseResultMap">
        select
        a.id,
        a.code,
        group_concat(a.ids) as ids,
        group_concat(a.zylSampleIds) AS zylSampleIds,
        group_concat(a.jmhlSampleIds) AS jmhlSampleIds,
        a.pid,
        a.name,
        a.unit,
        sum(a.zyl_index_total) as zyl_index_total,
        sum(a.jmhl_index_total) as jmhl_index_total,
        sum(a.zyl_calculate_value) as zyl_calculate_value,
        sum(a.jmhl_calculate_value) as jmhl_calculate_value
        from
        (
        select
        indexusage.id,
        indexusage.code,
        group_concat(indexusage.id) as ids,
        group_concat( indexusage.index_project_note_id) AS zylSampleIds, -- 主要量本量列表
        group_concat( indexusage.index_project_note_id) AS jmhlSampleIds, -- 建面含量样本量列表
        indexusage.pid,
        indexusage.name,
        indexusage.unit,
        sum(indexusage.zyl_index_value*indexusage.zyl_calculate_value) as zyl_index_total,
        sum(indexusage.jmhl_index_value*indexusage.jmhl_calculate_value) as jmhl_index_total,
        IF(sum(indexusage.zyl_index_value) != 0, indexusage.zyl_calculate_value, 0) as zyl_calculate_value,
        IF(sum(indexusage.jmhl_index_value) != 0, indexusage.jmhl_calculate_value, 0) as jmhl_calculate_value,

        indexusage.project_un_ld_merge_hash
        from
        gcdp_dws_index_usage indexusage
        left join gcdp_dws_index_project_note note on indexusage.index_project_note_id =note.id
        where
        note.`type` = 4
        and index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        group by
        project_ld_merge_hash
        union
        select
        indexusage.id,
        indexusage.code,
        group_concat(indexusage.id,',') as ids,
        group_concat( indexusage.index_project_note_id) AS zylSampleIds, -- 主要量本量列表
        group_concat( indexusage.index_project_note_id) AS jmhlSampleIds, -- 建面含量样本量列表
        indexusage.pid,
        indexusage.name,
        indexusage.unit,
        sum(indexusage.zyl_index_value*indexusage.zyl_calculate_value) as zyl_index_total,
        sum(indexusage.jmhl_index_value*indexusage.jmhl_calculate_value) as jmhl_index_total,
        sum(IF(indexusage.zyl_index_value IS NOT NULL AND indexusage.zyl_index_value != 0, indexusage.zyl_calculate_value, 0)) as zyl_calculate_value,
        sum(IF(indexusage.jmhl_index_value IS NOT NULL AND indexusage.jmhl_index_value != 0, indexusage.jmhl_calculate_value, 0)) as jmhl_calculate_value,
        indexusage.project_un_ld_merge_hash
        from
        gcdp_dws_index_usage indexusage
        left join gcdp_dws_index_project_note note on indexusage.index_project_note_id =note.id
        where
        note.`type` != 4
        and index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        group by
        project_un_ld_merge_hash
        ) as a
        group by
        a.project_un_ld_merge_hash order by code,id
    </select>

    <select id="getItemIndex" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexUsage">
        select
        u.id,
        u.pid,
        u.name,
        u.code,
        u.unit,
        u.zyl_calculate_name as zyl_calculate_name,
        GROUP_CONCAT(u.id) as ids,
        GROUP_CONCAT( u.index_project_note_id) as zylSampleIds, -- 主要量本量列表
        GROUP_CONCAT( u.index_project_note_id) as jmhlSampleIds, -- 建面含量样本量列表
        sum(u.zyl_index_value*zyl_calculate_value) as zyl_quantity,
        sum(u.jmhl_index_value*jmhl_calculate_value) as jmhl_quantity,
        sum(u.zyl_index_value*zyl_calculate_value)/zyl_calculate_value as zyl_index_value,
        sum(u.jmhl_index_value*jmhl_calculate_value)/jmhl_calculate_value as jmhl_index_value,
        u.zyl_calculate_value,
        u.jmhl_calculate_value,
        u.zyl_index_unit as zylUnit,
        u.jmhl_index_unit as jmhlUnit,
        u.subject_un_ld_merge_hash,
        u.subject_ld_merge_hash
        from gcdp_dws_index_usage u
        where
        u.index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        <if test="showAll == null or showAll == 0">
            and (u.zyl_index_value>0 or u.jmhl_index_value>0)
        </if>
        group by u.subject_ld_merge_hash
    </select>
    <select id="selectByItemIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexUsage">
        select * from gcdp_dws_index_usage where id in
        <foreach collection="itemIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectUsageIndexMergeByItemHash"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexQCBZBLibDto">
        select * from (
        select
        u.id,
        u.pid,
        trim(u.name) name,
        trim(u.name_path) namePath,
        trim(u.unit) unit,
        u.item_hash,
        GROUP_CONCAT(u.id) as usageIds,
        CONCAT(IFNULL (convert(min(if(u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-'))as dfhlMinAndMax,
        IFNULL (convert(sum(u.zyl_index_value)/count(if(u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-') as dfhlAvg
        from gcdp_dws_index_usage u left join gcdp_dws_index_project_note note on u.index_project_note_id = note.id
        where
        u.index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        and u.item_hash is not null and note.non_construction = 0
        group by  u.item_hash order by u.code) t1
        union
        select * from (
        select
        u.id,
        u.pid,
        trim(u.name) name,
        trim(u.name_path) namePath,
        trim(u.unit) unit,
        u.item_hash,
        GROUP_CONCAT(u.id) as usageIds,
        CONCAT(IFNULL (convert(min(if(u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-'))as dfhlMinAndMax,
        IFNULL (convert(sum(u.zyl_index_value)/count(if(u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-') as dfhlAvg
        from gcdp_dws_index_usage u left join gcdp_dws_index_project_note note on u.index_project_note_id = note.id
        where
        u.index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        and u.item_hash is not null and note.non_construction = 1
        group by u.item_hash order by u.code) t2
    </select>
</mapper>
