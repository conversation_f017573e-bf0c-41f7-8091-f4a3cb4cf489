<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexCostMapper">
    <resultMap id="BaseResultMap" type="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndexCost">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="ids" jdbcType="VARCHAR" property="ids"/>
    </resultMap>
    <select id="getIndex" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndexCost">
        select
        a.name,
        a.unit,
        a.id,
--         a.df_calculate_name,
        group_concat(a.ids) as ids,
        group_concat(a.swlSampleIds) as swlSampleIds,
        group_concat(a.jmSampleIds) as jmSampleIds,
--         group_concat(a.dfSampleIds) as dfSampleIds,
        a.pid,
        sum(a.amount) as amount,                           -- 科目合价（不含税）
        sum(a.amountIncludeTax) as amountIncludeTax,     -- 科目合价（含税）
        sum(a.jm_amount) as jmAmount,
        sum(a.jm_amountIncludeTax) as jmAmountIncludeTax,
        sum(a.swl_amount) as swlAmount,
        sum(a.swl_amountIncludeTax) as swlAmountIncludeTax,
--         sum(a.df_amount) as dfAmount,
        sum(a.jm_calculate_value) as jmValue,   -- 建筑面积 建面单方-计算口径值
        sum(a.swl_calculate_value) as swlValue -- 科目工程量 实物量单方-计算口径值
--         sum(a.df_calculate_value) as dfValue  -- 科目工程量 实物量单方-计算口径值
        from
        (
        select
        cost.code,
        cost.name,
        cost.unit,
        cost.id,
        cost.df_calculate_name,
        group_concat(cost.id) as ids,
        group_concat( cost.index_project_note_id) as swlSampleIds, -- 实物量样本量列表
        group_concat( cost.index_project_note_id) as jmSampleIds, -- 建面样本量列表
        group_concat( cost.index_project_note_id) as dfSampleIds, -- 单方样本量列表
        cost.pid,
        sum(cost.amount) as amount,                               -- 科目合价（不含税）
        sum(cost.amount_include_tax) as amountIncludeTax,         -- 科目合价（含税）
        sum(cost.jm_calculate_value*cost.jm_index_value) as jm_amount,                          -- 科目合价（不含税）
        sum(cost.jm_calculate_value*cost.jm_index_value_include_tax) as jm_amountIncludeTax,    -- 科目合价（含税）
        sum(cost.swl_calculate_value*cost.swl_index_value) as swl_amount,                       -- 科目合价（不含税）
        sum(cost.swl_calculate_value*cost.swl_index_value_include_tax) as swl_amountIncludeTax, -- 科目合价（含税）
        sum(cost.df_calculate_value*cost.df_index_value) as df_amount,                          -- 科目合价（不含税）
        sum(cost.df_calculate_value*cost.df_index_value_include_tax) as df_amountIncludeTax,    -- 科目合价（含税）
        IF((sum(cost.jm_index_value) != 0) or (sum(cost.jm_index_value_include_tax) != 0), cost.jm_calculate_value, 0) as jm_calculate_value,        -- 建筑面积 建面单方-计算口径值
        IF((sum(cost.swl_index_value) != 0) or (sum(cost.swl_index_value_include_tax) != 0), cost.swl_calculate_value, 0) as swl_calculate_value,    -- 科目工程量 实物量单方-计算口径值
        IF((sum(cost.df_index_value) != 0) or (sum(cost.df_index_value_include_tax) != 0), cost.df_calculate_value, 0) as df_calculate_value,        -- 单方指标(其他单方)-计算口径值
        cost.project_un_ld_merge_hash
        from
        gcdp_dws_index_cost cost
        left join gcdp_dws_index_project_note note on cost.index_project_note_id = note.id
        where
        note.`type` = 4
        and index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        group by
        project_ld_merge_hash
        union
        select
        cost.code,
        cost.name,
        cost.unit,
        cost.id,
        cost.df_calculate_name,
        group_concat(cost.id) as ids,
        group_concat( cost.index_project_note_id) as swlSampleIds,  -- 实物量样本量列表
        group_concat( cost.index_project_note_id) as jmSampleIds,   -- 建面样本量列表
        group_concat( cost.index_project_note_id) as dfSampleIds,   -- 单方样本量列表
        cost.pid,
        sum(cost.amount) as amount,                                 -- 科目合价（不含税）
        sum(cost.amount_include_tax) as amountIncludeTax,           -- 科目合价（含税）
        sum(cost.jm_calculate_value*cost.jm_index_value) as jm_amount,                          -- 科目合价（不含税）
        sum(cost.jm_calculate_value*cost.jm_index_value_include_tax) as jm_amountIncludeTax,    -- 科目合价（含税）
        sum(cost.swl_calculate_value*cost.swl_index_value) as swl_amount,                       -- 科目合价（不含税）
        sum(cost.swl_calculate_value*cost.swl_index_value_include_tax) as swl_amountIncludeTax, -- 科目合价（含税）
        sum(cost.df_calculate_value*cost.df_index_value) as df_amount,                          -- 科目合价（不含税）
        sum(cost.df_calculate_value*cost.df_index_value_include_tax) as df_amountIncludeTax,    -- 科目合价（含税）
        sum(IF((cost.jm_index_value IS NOT NULL AND cost.jm_index_value != 0) or (cost.jm_index_value_include_tax IS NOT NULL AND cost.jm_index_value_include_tax != 0), cost.jm_calculate_value, 0)) as jm_calculate_value,        -- 建筑面积 建面单方-计算口径值
        sum(IF((cost.swl_index_value IS NOT NULL AND cost.swl_index_value != 0) or (cost.swl_index_value_include_tax IS NOT NULL AND cost.swl_index_value_include_tax != 0), cost.swl_calculate_value, 0)) as swl_calculate_value,    -- 科目工程量 实物量单方-计算口径值
        sum(IF((cost.df_index_value IS NOT NULL AND cost.df_index_value != 0) or (cost.df_index_value_include_tax IS NOT NULL AND cost.df_index_value_include_tax != 0), cost.df_calculate_value, 0)) as df_calculate_value,        -- 单方指标(其他单方)-计算口径值
        cost.project_un_ld_merge_hash
        from
        gcdp_dws_index_cost cost
        left join gcdp_dws_index_project_note note on cost.index_project_note_id = note.id
        where
        note.`type` != 4
        and index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        group by
        project_un_ld_merge_hash
        ) as a
        group by
        a.project_un_ld_merge_hash order by code,id
    </select>

    <select id="getItemIndex" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexCost">
        select
        cost.id,
        cost.pid,
        cost.code,
        cost.name,
        cost.unit,
        GROUP_CONCAT(cost.id) as ids,
        GROUP_CONCAT( cost.index_project_note_id) as swlSampleIds, -- 实物量样本量列表
        GROUP_CONCAT( cost.index_project_note_id) as jmSampleIds, -- 建面样本量列表
        <!--GROUP_CONCAT( cost.index_project_note_id) as dfSampleIds, &#45;&#45; 单方样本量列表-->
        sum(cost.swl_index_value*swl_calculate_value)/swl_calculate_value as swl_index_value,
        sum(cost.swl_index_value_include_tax*swl_calculate_value)/swl_calculate_value as swl_index_value_include_tax,
        sum(cost.swl_index_value*swl_calculate_value) as swlAmount,
        sum(cost.swl_index_value_include_tax*swl_calculate_value) as swlAmountIncludeTax,
        sum(cost.jm_index_value*jm_calculate_value)/jm_calculate_value as jm_index_value,
        sum(cost.jm_index_value_include_tax*jm_calculate_value)/jm_calculate_value as jm_index_value_include_tax,
        sum(cost.jm_index_value*jm_calculate_value) as jmAmount,
        sum(cost.jm_index_value_include_tax*jm_calculate_value) as jmAmountIncludeTax,
        <!--sum(cost.df_index_value_include_tax*df_calculate_value)/df_calculate_value as df_index_value_include_tax,-->
        <!--sum(cost.df_index_value_include_tax*df_calculate_value) as dfAmount,-->
        cost.jm_calculate_value,
        cost.swl_calculate_value,
        <!--cost.df_calculate_value,-->
        cost.jm_index_unit as jmUnit,
        cost.swl_index_unit as swlUnit,
        <!--cost.df_index_unit as dfUnit,-->
        cost.subject_un_ld_merge_hash,
        cost.subject_ld_merge_hash
        from gcdp_dws_index_cost cost
        where
        index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
       <if test="showAll == null or showAll == 0">
           and (cost.jm_index_value>0 or cost.swl_index_value>0 or cost.df_index_value>0
           or cost.jm_index_value_include_tax>0 or cost.swl_index_value_include_tax>0 or cost.df_index_value_include_tax>0)
       </if>
        group by subject_ld_merge_hash
        <!--select
        a.id,
        a.pid,
        a.name,
        a.unit,
        GROUP_CONCAT(a.ids) as ids,
        GROUP_CONCAT( a.swlSampleIds) as swlSampleIds,
        GROUP_CONCAT(a.jmSampleIds) as jmSampleIds,
        GROUP_CONCAT(a.swl_index_value_include_tax_str) as swlIndexValueIncludeTaxStr,
        GROUP_CONCAT(a.jm_index_value_include_tax_str) as jmIndexValueIncludeTaxStr,
        IFNULL (convert(sum(a.swlAmount)/sum(a.swl_calculate_value),decimal(30,5)),'-') as swlWeightedAvg,
        IFNULL (convert(sum(a.jmAmount)/sum(a.jm_calculate_value),decimal(30,5)),'-') as jmWeightedAvg,
        IFNULL(a.jmUnit,'') as jmUnit,
        IFNULL(a.swlUnit,'' )as swlUnit,
        GROUP_CONCAT(a.swl_calculate_value_str) as swlCalculateValueStr,
        GROUP_CONCAT(a.jm_calculate_value_str) as jmCalculateValueStr
        from
        (
        select
        cost.id,
        cost.pid,
        cost.code,
        cost.name,
        cost.unit,
        GROUP_CONCAT(cost.id) as ids,
        GROUP_CONCAT( cost.index_project_note_id) as swlSampleIds, &#45;&#45; 实物量样本量列表
        GROUP_CONCAT( cost.index_project_note_id) as jmSampleIds, &#45;&#45; 建面样本量列表
        GROUP_CONCAT( IFNULL(cost.swl_index_value_include_tax,0)) as swl_index_value_include_tax_str,
        GROUP_CONCAT( IFNULL(cost.jm_index_value_include_tax,0)) as jm_index_value_include_tax_str,
        GROUP_CONCAT(IFNULL(cost.jm_calculate_value,0)) as jm_calculate_value_str,
        GROUP_CONCAT(IFNULL(cost.swl_calculate_value,0)) as swl_calculate_value_str,
        sum(cost.swl_index_value_include_tax*swl_calculate_value) as swlAmount,
        sum(cost.jm_index_value_include_tax*jm_calculate_value) as jmAmount,
        cost.swl_calculate_value,
        cost.jm_calculate_value,
        cost.jm_index_unit as jmUnit,
        cost.swl_index_unit as swlUnit,
        cost.subject_un_ld_merge_hash
        from gcdp_dws_index_cost cost
        where
        index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        and (cost.jm_index_value_include_tax>0 or cost.swl_index_value_include_tax>0)
        group by subject_ld_merge_hash
        ) a
        group by a.subject_un_ld_merge_hash-->
    </select>
    <select id="selectByItemIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexCost">
        select * from gcdp_dws_index_cost where id in
        <foreach collection="itemIdList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectCostIndexMergeByItemHash" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexQCBZBLibDto">
        select * from (
        select
        cost.id,
        cost.pid,
        trim(cost.name) name,
        trim(cost.name_path) namePath,
        trim(cost.unit) unit,
        cost.item_hash,
        -- GROUP_CONCAT(cost.id) as ids,
        GROUP_CONCAT(cost.id) as costIds,
        GROUP_CONCAT(if((cost.jm_index_value_include_tax is null and  cost.swl_index_value_include_tax is null and cost.df_index_value_include_tax is null ) or u.zyl_index_value &lt;= 0,null,u.id)) as usageIds,
        CONCAT(IFNULL (convert(min(if(cost.jm_index_value &lt;= 0,null,cost.jm_index_value)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.jm_index_value &lt;= 0,null,cost.jm_index_value)),decimal(30,5)),'-'))as jmMinAndMax,
        CONCAT(IFNULL (convert(min(if(cost.jm_index_value_include_tax &lt;= 0,null,cost.jm_index_value_include_tax)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.jm_index_value_include_tax &lt;= 0,null,cost.jm_index_value_include_tax)),decimal(30,5)),'-'))as jmMinAndMaxIncludeTax,
        IFNULL (convert(sum(cost.jm_index_value)/count(if(cost.jm_index_value &lt;= 0,null,cost.jm_index_value)),decimal(30,5)),'-') as jmAvg,
        IFNULL (convert(sum(cost.jm_index_value_include_tax)/count(if(cost.jm_index_value_include_tax &lt;= 0,null,cost.jm_index_value_include_tax)),decimal(30,5)),'-') as jmAvgIncludeTax,
        CONCAT(IFNULL (convert(min(if(cost.swl_index_value &lt;= 0,null,cost.swl_index_value)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.swl_index_value &lt;= 0,null,cost.swl_index_value)),decimal(30,5)),'-'))as zhdjMinAndMax,
        CONCAT(IFNULL (convert(min(if(cost.swl_index_value_include_tax &lt;= 0,null,cost.swl_index_value_include_tax)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.swl_index_value_include_tax &lt;= 0,null,cost.swl_index_value_include_tax)),decimal(30,5)),'-'))as zhdjMinAndMaxIncludeTax,
        IFNULL (convert(sum(cost.swl_index_value)/count(if(cost.swl_index_value &lt;= 0,null,cost.swl_index_value)),decimal(30,5)),'-') as zhdjAvg,
        IFNULL (convert(sum(cost.swl_index_value_include_tax)/count(if(cost.swl_index_value_include_tax &lt;= 0,null,cost.swl_index_value_include_tax)),decimal(30,5)),'-') as zhdjAvgIncludeTax,
        CONCAT(IFNULL (convert(min(if(cost.df_index_value &lt;= 0,null,cost.df_index_value)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.df_index_value &lt;= 0,null,cost.df_index_value)),decimal(30,5)),'-'))as dfMinAndMax,
        CONCAT(IFNULL (convert(min(if(cost.df_index_value_include_tax &lt;= 0,null,cost.df_index_value_include_tax)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.df_index_value_include_tax &lt;= 0,null,cost.df_index_value_include_tax)),decimal(30,5)),'-'))as dfMinAndMaxIncludeTax,
        IFNULL (convert(sum(cost.df_index_value)/count(if(cost.df_index_value &lt;= 0,null,cost.df_index_value)),decimal(30,5)),'-') as dfAvg,
        IFNULL (convert(sum(cost.df_index_value_include_tax)/count(if(cost.df_index_value_include_tax &lt;= 0,null,cost.df_index_value_include_tax)),decimal(30,5)),'-') as dfAvgIncludeTax,
        CONCAT(IFNULL (convert(min(if((cost.jm_index_value_include_tax is null and cost.swl_index_value_include_tax is null and cost.df_index_value_include_tax  is null ) or u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if((cost.jm_index_value_include_tax is null and  cost.swl_index_value_include_tax is null and cost.df_index_value_include_tax is null ) or u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-'))as dfhlMinAndMax,
        IFNULL (convert(sum(if((cost.jm_index_value_include_tax is null and  cost.swl_index_value_include_tax is null and cost.df_index_value_include_tax is null ) or u.zyl_index_value &lt;= 0,null,u.zyl_index_value))/count(if((cost.jm_index_value_include_tax is null and  cost.swl_index_value_include_tax is null and cost.df_index_value_include_tax is null ) or u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-') as dfhlAvg
        from gcdp_dws_index_cost cost left join gcdp_dws_index_project_note note on cost.index_project_note_id = note.id
        left join gcdp_dws_index_usage u on cost.index_project_note_id = u.index_project_note_id and cost.item_hash = u.item_hash
        where
        cost.index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        and cost.item_hash is not null and note.non_construction = 0
        group by cost.item_hash order by cost.code) t1
        union
        select * from (
        select
        cost.id,
        cost.pid,
        trim(cost.name) name,
        trim(cost.name_path) namePath,
        trim(cost.unit) unit,
        cost.item_hash,
        -- GROUP_CONCAT(cost.id) as ids,
        GROUP_CONCAT(cost.id) as costIds,
        GROUP_CONCAT(if((cost.jm_index_value_include_tax is null and  cost.swl_index_value_include_tax is null and cost.df_index_value_include_tax is null ) or u.zyl_index_value &lt;= 0,null,u.id)) as usageIds,
        CONCAT(IFNULL (convert(min(if(cost.jm_index_value &lt;= 0,null,cost.jm_index_value)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.jm_index_value &lt;= 0,null,cost.jm_index_value)),decimal(30,5)),'-'))as jmMinAndMax,
        CONCAT(IFNULL (convert(min(if(cost.jm_index_value_include_tax &lt;= 0,null,cost.jm_index_value_include_tax)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.jm_index_value_include_tax &lt;= 0,null,cost.jm_index_value_include_tax)),decimal(30,5)),'-'))as jmMinAndMaxIncludeTax,
        IFNULL (convert(sum(cost.jm_index_value)/count(if(cost.jm_index_value &lt;= 0,null,cost.jm_index_value)),decimal(30,5)),'-') as jmAvg,
        IFNULL (convert(sum(cost.jm_index_value_include_tax)/count(if(cost.jm_index_value_include_tax &lt;= 0,null,cost.jm_index_value_include_tax)),decimal(30,5)),'-') as jmAvgIncludeTax,
        CONCAT(IFNULL (convert(min(if(cost.swl_index_value &lt;= 0,null,cost.swl_index_value)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.swl_index_value &lt;= 0,null,cost.swl_index_value)),decimal(30,5)),'-'))as zhdjMinAndMax,
        CONCAT(IFNULL (convert(min(if(cost.swl_index_value_include_tax &lt;= 0,null,cost.swl_index_value_include_tax)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.swl_index_value_include_tax &lt;= 0,null,cost.swl_index_value_include_tax)),decimal(30,5)),'-'))as zhdjMinAndMaxIncludeTax,
        IFNULL (convert(sum(cost.swl_index_value)/count(if(cost.swl_index_value &lt;= 0,null,cost.swl_index_value)),decimal(30,5)),'-') as zhdjAvg,
        IFNULL (convert(sum(cost.swl_index_value_include_tax)/count(if(cost.swl_index_value_include_tax &lt;= 0,null,cost.swl_index_value_include_tax)),decimal(30,5)),'-') as zhdjAvgIncludeTax,
        CONCAT(IFNULL (convert(min(if(cost.df_index_value &lt;= 0,null,cost.df_index_value)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.df_index_value &lt;= 0,null,cost.df_index_value)),decimal(30,5)),'-'))as dfMinAndMax,
        CONCAT(IFNULL (convert(min(if(cost.df_index_value_include_tax &lt;= 0,null,cost.df_index_value_include_tax)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if(cost.df_index_value_include_tax &lt;= 0,null,cost.df_index_value_include_tax)),decimal(30,5)),'-'))as dfMinAndMaxIncludeTax,
        IFNULL (convert(sum(cost.df_index_value)/count(if(cost.df_index_value &lt;= 0,null,cost.df_index_value)),decimal(30,5)),'-') as dfAvg,
        IFNULL (convert(sum(cost.df_index_value_include_tax)/count(if(cost.df_index_value_include_tax &lt;= 0,null,cost.df_index_value_include_tax)),decimal(30,5)),'-') as dfAvgIncludeTax,
        CONCAT(IFNULL (convert(min(if((cost.jm_index_value_include_tax is null and cost.swl_index_value_include_tax is null and cost.df_index_value_include_tax  is null ) or  u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-'),'~',
        IFNULL (convert(max(if((cost.jm_index_value_include_tax is null and  cost.swl_index_value_include_tax is null and cost.df_index_value_include_tax is null ) or u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-'))as dfhlMinAndMax,
        IFNULL (convert(sum(if((cost.jm_index_value_include_tax is null and  cost.swl_index_value_include_tax is null and cost.df_index_value_include_tax is null ) or u.zyl_index_value &lt;= 0,null,u.zyl_index_value))/count(if((cost.jm_index_value_include_tax is null and  cost.swl_index_value_include_tax is null and cost.df_index_value_include_tax is null ) or u.zyl_index_value &lt;= 0,null,u.zyl_index_value)),decimal(30,5)),'-') as dfhlAvg
        from gcdp_dws_index_cost cost left join gcdp_dws_index_project_note note on cost.index_project_note_id = note.id
        left join gcdp_dws_index_usage u on cost.index_project_note_id = u.index_project_note_id and cost.item_hash = u.item_hash
        where
        cost.index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        and cost.item_hash is not null and note.non_construction = 1
        group by cost.item_hash order by cost.code) t2
        <!--select
        cost.id,
        cost.pid,
        trim(cost.name) name,
        trim(cost.name_path) namePath,
        trim(cost.unit) unit,
        note.non_construction,
        cost.item_hash,
        GROUP_CONCAT(cost.id) as costIds,
        GROUP_CONCAT(u.id) as usageIds,
        min(cost.jm_index_value_include_tax) as jmMin,
        max(cost.jm_index_value_include_tax) as jmMax,
        sum(cost.jm_index_value_include_tax)/count(cost.jm_index_value_include_tax) as jmAvg,
        min(cost.swl_index_value_include_tax) as zhdjMin,
        max(cost.swl_index_value_include_tax) as zhdjMax,
        sum(cost.swl_index_value_include_tax)/count(cost.swl_index_value_include_tax) as zhdjAvg,
        min(cost.df_index_value_include_tax) as dfMin,
        max(cost.df_index_value_include_tax) as dfMax,
        sum(cost.df_index_value_include_tax)/count(cost.df_index_value_include_tax) as dfAvg,
        min(u.zyl_index_value)as dfhlMin,
        max(u.zyl_index_value)as dfhlMax,
        sum(u.zyl_index_value)/count(u.zyl_index_value) as dfhlAvg
        from gcdp_dws_index_cost cost
        left join gcdp_dws_index_project_note note on cost.index_project_note_id = note.id
        left join gcdp_dws_index_usage u on cost.index_project_note_id = u.index_project_note_id and cost.item_hash = u.item_hash
        where
        cost.index_project_note_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType = BIGINT}
        </foreach>
        group by cost.item_hash,note.non_construction order by cost.code-->
    </select>
</mapper>
