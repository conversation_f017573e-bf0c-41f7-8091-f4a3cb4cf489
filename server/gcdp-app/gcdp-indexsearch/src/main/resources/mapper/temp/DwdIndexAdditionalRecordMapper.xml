<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.temp.domain.dao.mapper.DwdIndexAdditionalRecordMapper">

    <select id="selectSuccessRecords" resultType="com.glodon.gcdpindexsearch.temp.domain.dao.entity.IndexAdditionalRecord">
        select r.original_unique_id, r.contract_id, r.enterprise_id, p.product_source
        from gcdp_dwd_index_additional_record r
        join gcdp_dwd_contract_project p
        on r.contract_id = p.id
        where r.status = 1
    </select>
</mapper>
