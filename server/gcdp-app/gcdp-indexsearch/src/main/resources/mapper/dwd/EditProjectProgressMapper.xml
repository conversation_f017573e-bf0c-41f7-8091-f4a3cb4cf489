<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwddatasearch.domain.dao.mapper.EditProjectProgressMapper">

    <select id="selectConditionList" resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.EditProjectDTO">
        select gp.id,
               gp.tbl_create_date,
               concat(gp.enterprise_id)   as enterprise_id,
               ep.mongodb_id,
               ep.product_source,
               gp.submit_audit_date       as archive_date,
               gp.project_code,
               gp.user_id,
               gp.user_name,
               gp.original_project_id as project_id,
               gp.project_name
        from gcdp_dwd_contract_project gp
                 inner join edit_project_progress ep on gp.original_id = ep.mongodb_id
        where ep.product_source in
        <foreach collection="condition.productSourceList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="condition.archiveBeginDate != null and condition.archiveBeginDate != ''">
            and gp.submit_audit_date &gt;= #{condition.archiveBeginDate}
        </if>
        <if test="condition.archiveEndDate != null and condition.archiveEndDate != ''">
            and gp.submit_audit_date &lt;= #{condition.archiveEndDate}
        </if>
        <if test="condition.enterpriseIds != null and !condition.enterpriseIds.isEmpty">
            and ep.enterprise_id in
            <foreach collection="condition.enterpriseIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.projectCodes != null and !condition.projectCodes.isEmpty">
            and gp.project_code in
            <foreach collection="condition.projectCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.archiveIdList != null and !condition.archiveIdList.isEmpty">
            and ep.mongodb_id in
            <foreach collection="condition.archiveIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and deleted = 1
        order by gp.submit_audit_date desc;
    </select>
</mapper>
