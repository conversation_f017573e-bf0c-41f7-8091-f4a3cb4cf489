<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwdProjectInfoQueryMapper">
    <resultMap id="BaseResultMap" type="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwdProjectInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_global_id" jdbcType="VARCHAR" property="userGlobalId"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="project_serial_num" jdbcType="INTEGER" property="projectSerialNum"/>
        <result column="district_name" jdbcType="VARCHAR" property="districtName"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="project_category_name" jdbcType="VARCHAR" property="projectCategoryName"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
        <result column="project_type" jdbcType="VARCHAR" property="projectType"/>
        <result column="unique_md5code" jdbcType="VARCHAR" property="uniqueMd5code"/>
        <result column="project_scale" jdbcType="DECIMAL" property="projectScale"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="construction_unit" jdbcType="VARCHAR" property="constructionUnit"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="project_scale_unit" jdbcType="VARCHAR" property="projectScaleUnit"/>
        <result column="province_id" jdbcType="VARCHAR" property="provinceId"/>
        <result column="district_code" jdbcType="VARCHAR" property="districtCode"/>
        <result column="district_id" jdbcType="VARCHAR" property="districtId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_display" jdbcType="TINYINT" property="isDisplay"/>
        <result column="project_category_code" jdbcType="VARCHAR" property="projectCategoryCode"/>
        <result column="city_id" jdbcType="VARCHAR" property="cityId"/>
        <result column="product_positioning" jdbcType="VARCHAR" property="productPositioning"/>
    </resultMap>
    <resultMap id="ProjectInfoAndDetail" type="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwdProjectInfo">
        <id column="proj_id" property="id"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="province_id" property="provinceId"/>
        <result column="province_name" property="provinceName"/>
        <result column="city_id" property="cityId"/>
        <result column="city_name" property="cityName"/>
        <result column="district_id" property="districtId"/>
        <result column="district_name" property="districtName"/>
        <result column="project_area" property="projectArea"/>
        <result column="project_category_code" property="projectCategoryCode"/>
        <result column="project_category_name" property="projectCategoryName"/>
        <result column="project_scale" property="projectScale"/>
        <result column="project_scale_unit" property="projectScaleUnit"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="construction_unit" property="constructionUnit"/>
        <result column="product_positioning" property="productPositioning"/>
        <result column="jianshexingzhi" property="jianshexingzhi"/>
        <result column="create_date" property="createDate"/>
        <result column="enterprise_id" property="enterpriseId"/>
        <collection property="projectDetail" ofType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwdProjectDetail">
            <id column="detail_id" property="id"/>
            <result column="field_key" property="fieldKey"/>
            <result column="field_name" property="fieldName"/>
            <result column="field_value" property="fieldValue"/>
            <result column="field_type" property="fieldType"/>
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_global_id, modify_time, city_code, remark, province_code, project_name,
        project_serial_num, district_name, city_name, project_category_name, create_date,
        customer_code, project_type, unique_md5code, project_scale, project_scale_unit, end_time, construction_unit,
        project_code, enterprise_id, update_date, data_source, is_delete, province_name,
        start_time, project_scale_unit, province_id, district_code, district_id, status,
        is_display, project_category_code, city_id,product_positioning
    </sql>
    <sql id="Column_List">
        id, project_name
        ,product_positioning
        ,city_id
        ,province_id
        ,district_id
        ,province_name
        ,city_name
        ,district_name
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from gcdp_dwd_project_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listDetailProjectInfo" resultMap="ProjectInfoAndDetail">
        SELECT info.id AS proj_id, info.project_code, project_name, province_id, province_name, city_id, city_name, district_id, district_name,
        project_category_code, project_category_name, project_scale, project_scale_unit, start_time, end_time, construction_unit, product_positioning, jianshexingzhi,
        detail.id AS detail_id, field_key, field_name, field_value, field_type, create_date,info.enterprise_id
        FROM gcdp_dwd_project_info info
        LEFT JOIN gcdp_dwd_project_detail detail
        ON info.project_code = detail.project_code and info.enterprise_id = detail.enterprise_id
        and detail.is_delete = 0
        <where>
            <choose>
                <when test="includeSelfFlg == null || includeSelfFlg == '' || 1 == includeSelfFlg ">
                    ( (info.enterprise_id = #{enterpriseId}
                    <include refid="com.glodon.gcdp.dwdservice.domain.dao.mapper.DwdProjectQueryCommonMapper.authorityControl"/>
                    )
                    <if test="sharedEnterpriseId !=null and sharedEnterpriseId.size > 0 ">
                        or (info.enterprise_id in
                        <foreach collection="sharedEnterpriseId" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <otherwise>
                    <if test="sharedEnterpriseId !=null and sharedEnterpriseId.size > 0 ">
                        info.enterprise_id in
                        <foreach collection="sharedEnterpriseId" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>

            <if test="projectCodes != null and projectCodes.size > 0">
                AND info.project_code IN
                <foreach collection="projectCodes" open="('', " close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="projectName != null and projectName != ''">
                AND info.project_name Like concat('%',#{projectName},'%')
            </if>
            <if test="areaId != null and areaId.size > 0">
                AND (
                info.province_id IN
                <foreach collection="areaId" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or info.city_id IN
                <foreach collection="areaId" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or info.district_id IN
                <foreach collection="areaId" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            AND info.is_delete = 0
            AND info.status = 1
            AND info.recycle_flag = 0
        </where>
    </select>

    <select id="selectListByEnterpriseId"
            resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.ZbwProjectDto">
        SELECT id AS project_id, project_code, project_name as name,user_global_id as global_id
        FROM gcdp_dwd_project_info
        WHERE
        enterprise_id = #{enterpriseId}
        AND is_delete = 0
        AND recycle_flag = 0
        AND status = 1
        ORDER BY create_date DESC
    </select>

    <select id="selectByProjectCode"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwdProjectInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM gcdp_dwd_project_info
        WHERE
        enterprise_id = #{enterpriseId}
        AND project_code = #{projectCode}
    </select>


    <select id="selectByProjectCodes"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwdProjectInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM gcdp_dwd_project_info
        WHERE
        enterprise_id = #{enterpriseId}
        <if test="projectCodes != null and projectCodes.size > 0">
            AND project_code IN
            <foreach collection="projectCodes" open="('', " close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>