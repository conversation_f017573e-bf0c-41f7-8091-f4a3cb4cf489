<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwddatasearch.domain.dao.mapper.DwdZbsqContractProjectMapper">


    <select id="getProjectList" resultType="com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject">
        SELECT id,
               name,
               phase,
               bid_node_uuid
        FROM gcdp_dwd_contract_project
        WHERE enterprise_id = #{enterpriseId}
          AND project_code = #{projectCode}
          AND product_source = #{productSource}
    </select>

    <select id="getProjectAttrList" resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.ProjectAttrDto">
        SELECT a.*,b.value,b.value_id as valueId
        FROM
        gcdp_dwd_contract_project_attr a LEFT JOIN gcdp_dwd_contract_project_attr_detail b
        ON a.id = b.contract_project_attr_id
        WHERE b.contract_project_id IN
        <foreach collection="contractProjectIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>