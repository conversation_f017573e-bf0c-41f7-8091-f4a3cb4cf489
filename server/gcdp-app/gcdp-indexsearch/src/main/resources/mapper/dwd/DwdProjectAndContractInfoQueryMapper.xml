<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwddatasearch.domain.dao.mapper.DwdProjectAndContractInfoMapper">

    <select id="selectProjectAndContractInfo"
            resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.DwdProjectAndContractInfoDto"
            parameterType="com.glodon.gcdpindexsearch.dwddatasearch.domain.vo.ProjectAndContractSearchConditionVO">
        select
        info.province_id as projectProvinceId, info.province_name as projectProvinceName,
        info.city_id as projectCityId, info.city_name as projectCityName, info.district_id as projectDistrictId,
        info.district_name as projectDistrictName,
        info.project_category_code, info.project_category_name, info.start_time, info.end_time, info.create_date as
        projectCreateDate, info.update_date, info.customer_code,
        info.project_type, info.user_global_id, info.project_scale, info.product_positioning,info.enterprise_id,
        contract.project_id, contract.project_code, contract.project_name, contract.original_identity,
        contract.category_type, contract.product_source, contract.id, contract.name as bidNodeName,
        contract.province_id, contract.province_name, contract.city_id,
        contract.city_name, contract.district_id, contract.district_name, contract.phase,
        contract.create_date, contract.audit_date, contract.submit_audit_date, contract.audit_id, contract.audit_name,
        contract.user_id, contract.user_name, contract.bid_node_uuid, contract.main_uuid,
        contract.total, contract.total_include_tax, contract.build_area, contract.original_project_id,
        ifnull(group_concat(distinct if (bidnode.project_category_name = '',null,bidnode.project_category_name)),'') as
        categoryName,
        ifnull(group_concat(distinct if (bidnode.project_category_code = '',null,bidnode.project_category_code)),'') as
        categoryCode,
        bidnode.selected_template, bidnode.id as nodeId
        from
        gcdp_dwd_project_info info
        join gcdp_dwd_contract_project contract on info.project_code = contract.project_code
        and info.enterprise_id = contract.enterprise_id
        join gcdp_dwd_contract_project_bidnode bidnode on bidnode.contract_project_id = contract.id
        and (bidnode.node_type = 1 or bidnode.node_type is null)
        where
        info.enterprise_id = #{enterpriseId}
        and info.is_delete = 0
        and info.recycle_flag = 0
        and info.status = 1
        <if test="projectNameKeyWord != null and projectNameKeyWord  != ''">
            AND info.project_name Like concat('%',#{projectNameKeyWord},'%')
        </if>
        <if test="projectOrContractName != null and projectOrContractName != ''">
            AND
            (info.project_name Like concat('%',#{projectOrContractName},'%')
            OR
            contract.name Like concat('%',#{projectOrContractName},'%'))
        </if>
        <if test="startTime != null and startTime  != ''">
            AND info.start_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime  != ''">
            AND info.end_time &lt;= #{endTime}
        </if>
        <if test="keyWord != null and keyWord  != ''">
            AND contract.name Like concat('%',#{keyWord},'%')
        </if>
        <if test="provinceId != null and provinceId  != ''">
            AND contract.province_id = #{provinceId}
        </if>
        <if test="cityId != null and cityId  != ''">
            AND contract.city_id = #{cityId}
        </if>
        <if test="districtId != null and districtId  != ''">
            AND contract.district_id = #{districtId}
        </if>
        <if test="phase != null and phase  != ''">
            AND contract.phase = #{phase}
        </if>
        <if test="productSource != null and productSource.size() > 0">
            and contract.product_source in
            <foreach collection="productSource" open="(" close=")" separator="," index="index" item="item">
                #{item}
            </foreach>
        </if>
        <if test="contractIds != null and contractIds.size() > 0">
            and contract.id in
            <foreach collection="contractIds" open="(" close=")" separator="," index="index" item="item">
                #{item}
            </foreach>
        </if>
        <if test="categoryCode != null and categoryCode.size() > 0">
            AND (
            <foreach collection="categoryCode" open="(" close=")" item="item" separator="OR">
                bidnode.project_category_code LIKE concat(#{item,jdbcType=VARCHAR},'%')
            </foreach>
            )
        </if>
        group by contract.id
        order by contract.create_date DESC
    </select>

    <select id="selectStructDtoListByContractIds"
            resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.DwdStructDto">
       select
        bid_node_uuid as structId, main_uuid,node_type,name as structName,id,pid
        from
        gcdp_dwd_contract_project_bidnode
        where contract_project_id  in
        <foreach collection="contractIds" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
    </select>

    <select id="listProjectInfos"
            resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.vo.mbcb.MbcbProjectVO">
        SELECT
        gdcp.project_id,
        gdcp.id as contract_project_id,
        gdcp.original_identity,
        gdcp.project_code,
        info.project_name,
        gdcp.name as contract_project_name,
        gdcpb.project_category_code as category_code,
        gdcpb.project_category_name as category_name,
        gdcp.submit_audit_date as archive_time,
        info.province_name,
        info.city_name,
        info.district_name,
        info.province_id,
        info.city_id,
        info.district_id,
        IF(gdcp.product_source = 'mbcb', gdcp.stage, gdcp.phase) as phase,
        IF(gdcp.product_source = 'mbcb', gdcp.product_positioning, info.product_positioning) as product_position,
        gdcp.is_temp,
        gdcpb.building_area as area_sum,
        gdcp.product_source,
        gdcpb.id,
        ifnull(gdcpb.amount, gdcpb.non_full_cost_amount) as amountSum,
        ifnull(gdcpb.amount_include_tax, gdcpb.non_full_cost_amount_include_tax) as amountSumIncludeTax,
        gdcpb.amount as fullCostAmount,
        gdcpb.amount_include_tax as fullCostAmountIncludeTax,
        gdcpb.non_full_cost_amount as nonFullCostAmount,
        gdcpb.non_full_cost_amount_include_tax as nonFullCostAmountIncludeTax,
        gdcpb.item_cost_type,
        gdcp.extend_data_json
        FROM gcdp_dwd_contract_project_bidnode gdcpb
        LEFT JOIN gcdp_dwd_contract_project gdcp
        ON gdcpb.contract_project_id = gdcp.id
        LEFT JOIN gcdp_dwd_project_info info
        ON info.project_code = gdcp.project_code AND info.enterprise_id = gdcp.enterprise_id
        where gdcp.enterprise_id = #{enterpriseId,jdbcType=VARCHAR}
        and gdcp.product_source in
        <foreach collection="productSource" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        -- and (gdcpb.node_type = 1 or gdcpb.node_type = 0 or gdcpb.node_type is null)
        and CASE
            WHEN gdcp.product_source IN ('zbsq-web', 'gbqd-zbsq-web', 'hgPlatform') THEN (gdcpb.node_type = 2 or gdcpb.node_type = 3)
            WHEN gdcp.product_source IN ('zbgx') and gdcpb.choose_type is not null THEN gdcpb.node_type = 2
            WHEN gdcp.product_source IN ('zbgx') and gdcpb.choose_type is null THEN gdcpb.node_type = 1
            -- 市场化计价是null，目标成本是0
            ELSE gdcpb.node_type = 1 OR gdcpb.node_type = 0 OR gdcpb.node_type IS NULL
        END
        and gdcpb.type = 2
        and gdcp.is_temp = 0
        and gdcp.project_id is not null
        AND info.is_delete = 0
        AND info.recycle_flag = 0
        <include refid="com.glodon.gcdp.dwdservice.domain.dao.mapper.DwdProjectQueryCommonMapper.authorityControl"/>
        <if test="keyWord != null and keyWord.length > 0">
            and (
            info.project_name like concat('%',#{keyWord},'%')
            or gdcp.name like concat('%',#{keyWord},'%')
            )
        </if>
        <if test="categoryCode != null and  categoryCode.length > 0">
            and gdcpb.project_category_code like concat(#{categoryCode},'%')
        </if>
        <if test="provinceId != null and provinceId.length > 0">
            and info.province_id = #{provinceId, jdbcType=VARCHAR}
        </if>
        <if test="cityId != null and cityId.length>0">
            and info.city_id = #{cityId,jdbcType=VARCHAR}
        </if>
        <if test="districtId != null and districtId.length>0">
            and info.district_id = #{districtId,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null and startTime.length > 0">
            and gdcp.submit_audit_date >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null and endTime.length > 0">
            and gdcp.submit_audit_date &lt;= #{endTime, jdbcType=TIMESTAMP}
        </if>
        <if test="phase != null and phase.length > 0 ">
            and (gdcp.phase =#{phase}
                 <if test="stage != null">
                     or gdcp.stage = #{stage}
                 </if>)
        </if>
        <if test="productPosition != null and productPosition.length > 0">
            and case gdcp.product_source
            when 'mbcb'
            then
            gdcp.product_positioning = #{productPosition,jdbcType=VARCHAR}
            else
            info.product_positioning = #{productPosition,jdbcType=VARCHAR}
            end
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            and gdcp.project_id in
            <foreach collection="projectIds" open="(" close=")" separator="," index="index" item="item">
                #{item}
            </foreach>
        </if>
        <if test="categoryCodes != null and categoryCodes.size() > 0">
            and gdcpb.project_category_code in
            <foreach collection="categoryCodes" open="(" close=")" separator="," index="index" item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="listContractProjectId" resultType="java.lang.Long">
        select id
        from gcdp_dwd_contract_project
        where original_identity in
        <foreach collection="set" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        and is_newest_stage = 1
    </select>



</mapper>