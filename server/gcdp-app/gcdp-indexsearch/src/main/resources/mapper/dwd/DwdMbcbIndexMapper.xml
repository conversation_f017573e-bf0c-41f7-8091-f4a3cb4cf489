<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwddatasearch.domain.dao.mapper.DwdMbcbIndexMapper">
    <select id="getMbcbJAIndexDetail"
            resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.vo.mbcb.MbcbProjectIndexVO">
        select
        gdi.bidnode_id,
        ifnull(gdid.index_value_include_tax, 0) as unit_cost,
        ifnull(gdid.amount_value_include_tax, 0) as amount,
        ifnull(gdid.calculate_value, 0) as build_area
        from gcdp_dwd_index_data gdid
        left join gcdp_dwd_index gdi on gdi.id =gdid.index_id
        where
            <if test="contractProjectIds != null and contractProjectIds.size>0">
            gdid.contract_project_id in
            <foreach collection="contractProjectIds" close=")" open="(" separator="," item="item">#{item}</foreach>
         </if>
        and gdid.enterprise_id =#{enterpriseId,jdbcType=VARCHAR}
        <if test="bidnodeIds != null and bidnodeIds.size > 0">
            and gdi.bidnode_id in
            <foreach collection="bidnodeIds" close=")" open="(" separator="," item="item">#{item}</foreach>
        </if>
        and gdid.new_index_type_ID = 1001
        and gdi.name ='合计'
    </select>

    <select id="getMbcbJAIndexData"
            resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.vo.mbcb.MbcbIndexDataVO">
        select
        gdi.id as index_id,
        gdid.new_index_type_ID as index_type,
        gdid.index_value,
        gdid.index_value_include_tax
        from gcdp_dwd_index_data gdid
        inner join gcdp_dwd_index gdi on gdi.id =gdid.index_id
        where gdid.contract_project_id = #{contractProjectId,jdbcType=BIGINT}
        and gdi.bidnode_id =#{bidnodeId,jdbcType=VARCHAR}
        <if test="indexTypeSet != null and indexTypeSet.size > 0">
            and gdid.new_index_type_ID in
            <foreach collection="indexTypeSet" close=")" open="(" separator="," item="item">#{item,jdbcType=INTEGER}</foreach>
        </if>
    </select>
    <select id="getVirtualBidNodeIndexList" resultType="com.glodon.gcdp.dwdservice.domain.dao.entity.DwdIndex">
        SELECT gdi.code,
               gdi.index_data_json
        FROM gcdp_dwd_index gdi
        WHERE gdi.contract_project_id = #{contractProjectId,jdbcType=BIGINT}
          AND gdi.bidnode_id = (
            SELECT id
            FROM gcdp_dwd_contract_project_bidnode
            WHERE contract_project_id = #{contractProjectId,jdbcType=BIGINT}
              AND original_id = -999
        );
    </select>
</mapper>