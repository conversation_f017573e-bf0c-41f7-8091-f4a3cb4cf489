<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwddatasearch.domain.dao.mapper.JsfMapper">
    <select id="selectQyqdStructList"
            resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.ContractStructDto">
        select gp.id,
               gp.original_id       as contract_id,
               gp.name,
               gp.project_code,
               gp.project_info_json,
               gp.project_attr_json,
               gbb.project_category_name,
               gbb.project_category_name_path,
               gbb.project_category_code,
               gbb.id               as bidnode_id,
               gp.phase,
               gp.project_name,
               gp.submit_audit_date as archive_date,
               gp.name              as bidnode_name,
               gbb.pid              as bidnode_pid,
               gbb.id               as bidnode_id,
               gbb.trade_name,
               IF(gbb.type=2 and gbb.exist_LY != 1, 4, gbb.type) AS type,
               gbb.building_area    as bidnode_build_area,
               gbb.name             as level_name,
               gll.bidNode_id       as ld_re_bidnode_id,
                IF(gbb.type=2 and gbb.exist_LY != 1, gbb.name, gll.name) AS ld_name,
                IF(gbb.type=2 and gbb.exist_LY != 1, ROUND(gbb.building_area, 6), gll.building_area) AS ld_build_area,
               gp.product_source
        from gcdp_dwd_contract_project gp
                 inner join gcdp_dwd_contract_project_bidnode gbb
                            on gp.id = gbb.contract_project_id and gbb.type in (1, 2)
                 left join gcdp_dwd_contract_project_lyhf gll on gbb.id = gll.bidNode_id and gll.category = 1
        where gp.product_source in
        <foreach collection="qps" item="ps" open="(" close=")" separator=",">
            #{ps}
        </foreach>
          and gp.enterprise_id = #{enterpriseId,jdbcType=VARCHAR}
        <!--市场化计价模式为科目计价模式-->
        and gp.economic_calc_mode in (0, 2)
        <if test="projectCodes != null and projectCodes.size() != 0">
            and gp.project_code in (
            <foreach collection="projectCodes" item="code" separator=",">
                #{code,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="condition.startTime != null">
            and gp.submit_audit_date &gt;= #{condition.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="condition.endTime != null">
            and gp.submit_audit_date &lt;= #{condition.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="condition.contractName != null and condition.contractName != ''">
            and gp.name like concat('%', #{condition.contractName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="condition.itemCostType != null">
            and gp.item_cost_type in (#{condition.itemCostType}, 3)
        </if>
    </select>

    <select id="selectContractList"
            resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote">
        select
        ifnull(gn.total, gn.non_full_cost_total) as total,
        ifnull(gn.total_include_tax, gn.non_full_cost_total_include_tax) as total_include_tax,
        gn.total as full_cost_total,
        gn.total_include_tax as full_cost_total_include_tax,
        gn.non_full_cost_total,
        gn.non_full_cost_total_include_tax,
        gn.*
        from gcdp_dws_index_project_note gn
                 inner join gcdp_dwd_contract_project gp on gn.contract_project_id = gp.id
        <where>
            gn.is_temp = false
            <if test="projectCode != null and projectCode != ''">
                and gn.project_code = #{projectCode,jdbcType=VARCHAR}
            </if>
            <if test="source != null and (source == 'zbsq' || source == 'gbqd' || source == 'zbgx' || source == 'zbsq-web' || source == 'gbqd-zbsq-web' || source == 'hgPlatform' || source == 'zbw')">
                and gp.main_uuid = #{contractId,jdbcType=VARCHAR}
            </if>
            <if test="source != null and (source == 'qyqd' || source == 'ystz' || source == 'jstz')">
                and gp.original_id = #{contractId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectZbsqStructList"
            resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.ContractStructDto">
        select gp.id,
               gp.build_area,
               gp.total,
               gp.total_include_tax,
               gp.project_info_json,
               gp.project_attr_json,
               gp.extend_data_json,
               gb.main_uuid         as contract_id,
               gp.old_project_name  as name,
               gb.project_category_name,
               gb.project_category_code,
               gb.project_category_name_path,
               gp.project_name,
               gb.id                as bidnode_id,
               gp.phase,
               gp.submit_audit_date as archive_date,
               gb.name              as bidnode_name,
               gb.pid               as bidnode_pid,
               gb.node_type         as type,
               gb.building_area     as bidnode_build_area,
               gp.trade_name,
               gp.main_uuid,
               gp.project_code,
               gp.product_source
        from gcdp_dwd_contract_project gp
                 inner join gcdp_dwd_contract_project_bidnode gb on gp.id = gb.contract_project_id and node_type = 1
        where gp.product_source in
        <foreach collection="zps" item="ps" open="(" close=")" separator=",">
            #{ps}
        </foreach>
          and gp.enterprise_id = #{enterpriseId,jdbcType=VARCHAR}
          and gp.is_temp = false
        <if test="projectCodes != null and projectCodes.size() != 0">
            and gp.project_code in (
            <foreach collection="projectCodes" item="code" separator=",">
                #{code,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
<!--        <if test="condition.contractCategory != null and condition.contractCategory != ''">-->
<!--            and gb.project_category_name like concat('%', #{condition.contractCategory,jdbcType=VARCHAR}, '%')-->
<!--        </if>-->
        <if test="condition.startTime != null">
            and gp.submit_audit_date &gt;= #{condition.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="condition.endTime != null">
            and gp.submit_audit_date &lt;= #{condition.endTime,jdbcType=TIMESTAMP}
        </if>
<!--        <if test="condition.contractName != null and condition.contractName != ''">-->
<!--            and gp.old_project_name like concat('%', #{condition.contractName,jdbcType=VARCHAR}, '%')-->
<!--        </if>-->
        <if test="condition.itemCostType != null">
            and gp.item_cost_type in (#{condition.itemCostType}, 3)
        </if>
    </select>

    <select id="selectZbCostList" resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.ZbStructDto">
        select a.name,
               a.unit,
               a.id,
               group_concat(a.ids, ',')   as item_ids,
               a.name_path,
               a.item_cost_type,
               a.pid,
               a.code,
               sum(a.amount)              as amount,
               a.jm_calc_name,
               a.swl_calc_name,
               a.df_calc_name,
               a.jm_index_unit            as jm_calc_unit,
               a.swl_index_unit           as swl_calc_unit,
               a.df_index_unit            as df_calc_unit,
               sum(a.jm_amount)           as jm_amount,
               sum(a.swl_amount)          as swl_amount,
               sum(a.df_amount)           as df_amount,
               sum(a.jm_amount_no_tax)    as jm_amount_no_tax,
               sum(a.swl_amount_no_tax)   as swl_amount_no_tax,
               sum(a.df_amount_no_tax)    as df_amount_no_tax,
               sum(a.jm_calculate_value)  as jm_value,
               sum(a.swl_calculate_value) as swl_value,
               sum(a.df_calculate_value)  as df_value
        from (
        select cost.code,
               cost.name,
               cost.unit,
               cost.id,
               cost.item_cost_type,
               cost.name_path,
               group_concat(cost.id, ',')                                       as ids,
               cost.pid,
               sum(cost.amount)                                                 as amount,
               cost.jm_calculate_name                                           as jm_calc_name,
               cost.swl_calculate_name                                          as swl_calc_name,
               cost.df_calculate_name                                           as df_calc_name,
               cost.jm_index_unit,
               cost.swl_index_unit,
               cost.df_index_unit,
               sum(cost.jm_calculate_value * cost.jm_index_value_include_tax)   as jm_amount,
               sum(cost.swl_calculate_value * cost.swl_index_value_include_tax) as swl_amount,
               sum(cost.df_calculate_value * cost.df_index_value_include_tax)   as df_amount,
               sum(cost.jm_calculate_value * cost.jm_index_value)               as jm_amount_no_tax,
               sum(cost.swl_calculate_value * cost.swl_index_value)             as swl_amount_no_tax,
               sum(cost.df_calculate_value * cost.df_index_value)               as df_amount_no_tax,
               cost.jm_calculate_value,
               cost.swl_calculate_value,
               cost.df_calculate_value,
               cost.without_calc_merge_hash                                     as project_un_ld_merge_hash
        from gcdp_dws_index_cost cost
                 left join gcdp_dws_index_project_note note on cost.index_project_note_id = note.id
        where note.`type` = 4 # 楼栋
          and index_project_note_id in
        (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
        <if test="itemCostType != null">
            and cost.item_cost_type in (#{itemCostType}, 3)
        </if>
        group by project_ld_merge_hash
        union all
        select cost.code,
               cost.name,
               cost.unit,
               cost.id,
               cost.item_cost_type,
               cost.name_path,
               group_concat(cost.id, ',')                                       as ids,
               cost.pid,
               sum(cost.amount)                                                 as amount,
               cost.jm_calculate_name                                           as jm_calc_name,
               cost.swl_calculate_name                                          as swl_calc_name,
               cost.df_calculate_name                                           as df_calc_name,
               cost.jm_index_unit,
               cost.swl_index_unit,
               cost.df_index_unit,
               sum(cost.jm_calculate_value * cost.jm_index_value_include_tax)   as jm_amount,
               sum(cost.swl_calculate_value * cost.swl_index_value_include_tax) as swl_amount,
               sum(cost.df_calculate_value * cost.df_index_value_include_tax)   as df_amount,
               sum(cost.jm_calculate_value * cost.jm_index_value)               as jm_amount_no_tax,
               sum(cost.swl_calculate_value * cost.swl_index_value)             as swl_amount_no_tax,
               sum(cost.df_calculate_value * cost.df_index_value)               as df_amount_no_tax,
               cost.jm_calculate_value,
               cost.swl_calculate_value,
               cost.df_calculate_value,
               cost.without_calc_merge_hash                                     as project_un_ld_merge_hash
        from gcdp_dws_index_cost cost
                 left join gcdp_dws_index_project_note note on cost.index_project_note_id = note.id
        where note.`type` = 2 # 业态
          and index_project_note_id in
        (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
        <if test="itemCostType != null">
            and cost.item_cost_type in (#{itemCostType}, 3)
        </if>
        group by with_calc_merge_hash
        union all
        select cost.code,
               cost.name,
               cost.unit,
               cost.id,
               cost.item_cost_type,
               cost.name_path,
               group_concat(cost.id, ',')                                       as ids,
               cost.pid,
               sum(cost.amount)                                                 as amount,
               cost.jm_calculate_name                                           as jm_calc_name,
               cost.swl_calculate_name                                          as swl_calc_name,
               cost.df_calculate_name                                           as df_calc_name,
               cost.jm_index_unit,
               cost.swl_index_unit,
               cost.df_index_unit,
               sum(cost.jm_calculate_value * cost.jm_index_value_include_tax)   as jm_amount,
               sum(cost.swl_calculate_value * cost.swl_index_value_include_tax) as swl_amount,
               sum(cost.df_calculate_value * cost.df_index_value_include_tax)   as df_amount,
               sum(cost.jm_calculate_value * cost.jm_index_value)               as jm_amount_no_tax,
               sum(cost.swl_calculate_value * cost.swl_index_value)             as swl_amount_no_tax,
               sum(cost.df_calculate_value * cost.df_index_value)               as df_amount_no_tax,
               sum(IF(jm_index_value_include_tax IS NOT NULL AND jm_index_value_include_tax != 0, jm_calculate_value, 0)) as jm_calculate_value,
               sum(IF(swl_index_value_include_tax IS NOT NULL AND swl_index_value_include_tax != 0, swl_calculate_value, 0)) as swl_calculate_value,
               sum(IF(df_index_value_include_tax IS NOT NULL AND df_index_value_include_tax != 0, df_calculate_value, 0)) as df_calculate_value,
               cost.without_calc_merge_hash                                     as project_un_ld_merge_hash
        from gcdp_dws_index_cost cost
                 left join gcdp_dws_index_project_note note on cost.index_project_note_id = note.id
        where note.`type` = 1 # (甲方不需要分类相关数据)
          and index_project_note_id in
        (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
        <if test="itemCostType != null">
            and cost.item_cost_type in (#{itemCostType}, 3)
        </if>
        group by project_un_ld_merge_hash
        ) as a
        group by a.project_un_ld_merge_hash
        order by null;
    </select>

    <select id="selectZbUsageList" resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.ZbStructDto">
        select a.name,
               a.unit,
               a.id,
               a.item_cost_type,
               group_concat(a.ids, ',')   as item_ids,
               a.name_path,
               a.pid,
               a.code,
               a.zyl_calc_name,
               a.zyl_index_unit           as zyl_calc_unit,
               sum(a.zyl_amount)          as zyl_amount,
               sum(a.zyl_calculate_value) as zyl_value
        from (
        select us.code,
               us.name,
               us.unit,
               us.id,
               us.item_cost_type,
               us.name_path,
               group_concat(us.id, ',')                                   as ids,
               us.pid,
               us.zyl_calculate_name                                      as zyl_calc_name,
               us.zyl_index_unit,
               IFNULL(sum(us.zyl_index_value * us.zyl_calculate_value),
                      sum(us.jmhl_index_value * us.jmhl_calculate_value)) as zyl_amount,
               us.zyl_calculate_value,
               us.without_calc_merge_hash                                 as project_un_ld_merge_hash
        from gcdp_dws_index_usage us
                 left join gcdp_dws_index_project_note note on us.index_project_note_id = note.id
        where note.`type` = 4 # 楼栋
          and index_project_note_id in
        (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
        <if test="itemCostType != null">
            and us.item_cost_type in (#{itemCostType}, 3)
        </if>
        group by project_ld_merge_hash
        union all
        select us.code,
               us.name,
               us.unit,
               us.id,
               us.item_cost_type,
               us.name_path,
               group_concat(us.id, ',')                                   as ids,
               us.pid,
               us.zyl_calculate_name                                      as zyl_calc_name,
               us.zyl_index_unit,
               IFNULL(sum(us.zyl_index_value * us.zyl_calculate_value),
                      sum(us.jmhl_index_value * us.jmhl_calculate_value)) as
                                                                             zyl_amount,
               us.zyl_calculate_value,
               us.without_calc_merge_hash                                 as project_un_ld_merge_hash
        from gcdp_dws_index_usage us
                 left join gcdp_dws_index_project_note note on us.index_project_note_id = note.id
        where note.`type` = 2 # 业态
          and index_project_note_id in
        (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
        <if test="itemCostType != null">
            and us.item_cost_type in (#{itemCostType}, 3)
        </if>
        group by with_calc_merge_hash
        union all
        select us.code,
               us.name,
               us.unit,
               us.id,
               us.item_cost_type,
               us.name_path,
               group_concat(us.id, ',')                                   as ids,
               us.pid,
               us.zyl_calculate_name                                      as zyl_calc_name,
               us.zyl_index_unit,
               IFNULL(sum(us.zyl_index_value * us.zyl_calculate_value),
                      sum(us.jmhl_index_value * us.jmhl_calculate_value)) as
                                                                             zyl_amount,
               sum(IF(zyl_index_value IS NOT NULL AND zyl_index_value != 0, zyl_calculate_value, 0)) as zyl_calculate_value,
               us.without_calc_merge_hash                                 as project_un_ld_merge_hash
        from gcdp_dws_index_usage us
                 left join gcdp_dws_index_project_note note on us.index_project_note_id = note.id
        where note.`type` = 1 # (甲方不需要分类相关数据)
          and index_project_note_id in
        (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
        <if test="itemCostType != null">
            and us.item_cost_type in (#{itemCostType}, 3)
        </if>
        group by project_un_ld_merge_hash
        ) as a
        group by a.project_un_ld_merge_hash
        order by null;
    </select>


    <select id="selectCostMakeupItemId" resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.DwsBqItemDto">
        SELECT gn.id,
               gn.name,
               gn.product_source,
               gn.total as fullCostAmount,
               gn.total_include_tax as fullCostAmountIncludeTax,
               gn.non_full_cost_total as nonFullCostAmount,
               gn.non_full_cost_total_include_tax as nonFullCostAmountIncludeTax,
               gn.contract_project_id,
               gm.type,
               gm.dwd_id,
               gm.ld_quantity
        FROM gcdp_dws_index_project_note gn
                 INNER JOIN gcdp_dws_index_cost gc ON gn.id = gc.index_project_note_id
                 INNER JOIN gcdp_dws_index_cost_makeup gm ON gc.id = gm.`index_cost_id`
        WHERE
            gc.id IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectUsageMakeupItemId" resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.DwsBqItemDto">
        SELECT gn.id,
               gn.name,
               gn.product_source,
               gn.contract_project_id,
               gn.total as fullCostAmount,
               gn.total_include_tax as fullCostAmountIncludeTax,
               gn.non_full_cost_total as nonFullCostAmount,
               gn.non_full_cost_total_include_tax as nonFullCostAmountIncludeTax,
               gm.type,
               gm.dwd_id,
               gm.ld_quantity
        FROM gcdp_dws_index_project_note gn
                 INNER JOIN gcdp_dws_index_usage gu ON gn.id = gu.index_project_note_id
                 INNER JOIN gcdp_dws_index_usage_makeup gm ON gu.id = gm.index_usage_id
        WHERE
            gu.id IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectContractProjectInfo" resultType="com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject">
        SELECT gdcp.id,
        gdcp.name,
        gdcp.product_source,
        gdcp.extend_data_json
        FROM gcdp_dwd_contract_project gdcp
        WHERE
        gdcp.id IN
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>