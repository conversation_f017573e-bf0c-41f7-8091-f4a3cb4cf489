<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwdBqitemCostMapper">

    <select id="selectCostDetailByBqItemId" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.CostDetailDTO">
        select bqitem.code as 'item_code', bqitem.name as 'item_name', bqitem.remark, temp.name as 'cost_name',
        temp.base_amount, temp.base_amount_remark, detail.rate, detail.`cost_identity`
        from gcdp_dwd_bqitem bqitem
        join gcdp_dwd_bqitem_cost_detail detail
             on bqitem.id = detail.`bqitem_id`
        join gcdp_dwd_ratedetail_template temp
             on bqitem.`gross_rate_id` = temp.`ratedetail_dict_id`
             and detail.`cost_identity` = temp.code
        where bqitem.id = #{id}
          and (
            temp.`original_cost_category_name` not like '%综合单价%'
            or temp.`original_cost_category_name` is null
            )
    </select>

    <select id="selectMaterialByBqItemId" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MaterialDTO">
        select res.code, res.name, res.type, res.spec, res.unit, res.market_rate, res.market_tax_rate,
               res.supply_type,
               res.brand,
               lmm.id,
               lmm.pid,
               lmm.quantity,
               lmm.`usage_value` as 'usage',
               lmm.extend_data_json as lmmExtendDataJson,
                res.extend_data_json as resource_extend_data_json
        from gcdp_dwd_bqitem_lmmdetail lmm
        join gcdp_dwd_resource res
            on res.id = lmm.`res_id`
        where lmm.bqitem_id = #{id}
    </select>

    <select id="selectMaterialByBqItemIds" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MaterialDTO">
        select res.code, res.name, res.type, res.spec, res.unit, res.market_rate, res.market_tax_rate,
               res.supply_type,
               res.brand,
               lmm.id,
               lmm.pid,
               lmm.bqitem_id as bqItemId,
               lmm.quantity,
               lmm.`usage_value` as 'usage',
                lmm.extend_data_json as lmmExtendDataJson,
               res.extend_data_json as resource_extend_data_json
        from gcdp_dwd_bqitem_lmmdetail lmm
                 join gcdp_dwd_resource res
                      on res.id = lmm.`res_id`
        <where>
            lmm.`bqitem_id` in
            <foreach collection="ids" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectMaterialByNormItemId" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MaterialDTO">
        select res.code, res.name, res.type, res.spec, res.unit, res.market_rate, res.market_tax_rate,
        res.budget_rate, res.budget_pre_tax_rate,
        res.supply_type,
        res.brand,
        lmm.norm_item_id,
        lmm.id,
        lmm.pid,
        lmm.quantity,
        lmm.`usage_value` as 'usage',
        lmm.extend_data_json as lmmExtendDataJson,
        res.extend_data_json as resource_extend_data_json
        from gcdp_dwd_norm_item_lmmdetail lmm
        join gcdp_dwd_resource res
        on res.id = lmm.`res_id`
        <where>
            lmm.`norm_item_id` in
            <foreach collection="ids" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectNormItemIdsByBqItemId" resultType="java.lang.Long">
        select id
        from gcdp_dwd_norm_item
        where bqtem_id = #{bqItemId}
    </select>

    <select id="selectNormItemByBqItemId" resultType="com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MakeupDetailDto">
        select *
        from gcdp_dwd_norm_item
        where bqtem_id in
        <foreach collection="ids" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>


</mapper>