<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.gcdpindexsearch.dwddatasearch.domain.dao.mapper.DwdStatisticDataMapper">
    <select id="selectBidNodeDataListAll" resultType="com.glodon.gcdpindexsearch.dwddatasearch.domain.dao.entity.BidNodeFilter" parameterType="java.lang.String">
        SELECT
        contract.province_id as provinceId,
        contract.province_name as provinceName,
        contract.city_id as cityId,
        contract.city_name as cityName,
        contract.district_id as districtId,
        contract.district_name as districtName,
        contract.phase as phaseName,
        bidnode.project_category_code as categoryCode,
        bidnode.project_category_name_path as categoryName
        FROM gcdp_dwd_contract_project contract
        LEFT JOIN gcdp_dwd_project_info project ON contract.project_id = project.id
        LEFT JOIN gcdp_dwd_contract_project_bidnode bidnode ON contract.id = bidnode.contract_project_id
        WHERE contract.enterprise_id = #{enterpriseId}
        <choose>
            <when test="productSource == null">
                AND contract.product_source IN ('zbsq', 'qyqd','gbqd')
            </when>
            <otherwise>
                AND contract.product_source in
                <foreach collection="productSource.split(',')" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        AND contract.is_temp = 0
        AND project.enterprise_id = #{enterpriseId}
        AND project.is_delete = 0
        AND project.recycle_flag = 0
        AND bidnode.type = 2;
    </select>

    <select id="projectContractStatistics" resultType="com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject">
        SELECT
        contract.project_id,
        project.project_category_code,
        project.project_category_name,
        project.province_id,
        project.province_name,
        contract.id,
        contract.`phase`,
        contract.`stage`,
        contract.`product_source`,
        contract.`total`,
        contract.`total_include_tax`
        FROM
        gcdp_dwd_contract_project contract
        LEFT JOIN gcdp_dwd_project_info project
        ON contract.`project_id` = project.`id`
        WHERE contract.enterprise_id = #{enterpriseId}
        <choose>
            <when test="productSource == null">
                AND contract.product_source IN ('zbsq', 'qyqd', 'mbcb', 'gbqd', 'zbw', 'hg')
            </when>
            <otherwise>
                AND contract.product_source in
                <foreach collection="productSource.split(',')" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        AND contract.is_temp = 0
        AND project.is_delete = 0
        AND project.recycle_flag = 0;
    </select>
</mapper>
