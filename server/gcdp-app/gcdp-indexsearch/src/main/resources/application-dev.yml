MYSQL_HOST: *************************************************************************************************************************************************************
MYSQL_USERNAME: gcdp_admin
MYSQL_PASSWORD: T97^TmZGgy9fQOE
ECS_REDIS_HOST: ***********
ECS_REDIS_PORT: 6379
ECS_REDIS_PASSWORD: '!yJ0Gx!+mxRq5'
ECS_REDIS_DB: 11
ECS_REDIS_PREFIX: gcdp

spring:
 config:
  import:
   - classpath:application-gcdp-common.yml
 mvc:
  pathmatch:
   matching-strategy: ant_path_matcher

swagger:
 enable: true
server:
 servlet:
  context-path: /gcdp-index-search
 # byte
 max-http-header-size: 307200

#日志配置
logging:
 level:
  root: info
  com.glodon: debug
  org.spring: info
  org.mybatis: info
mybatis:
 log-timeout-sql: true
 timeout: 0
sequence:
 defaultCacheSize: 10000
 defaultLongIdStartValue: 1

# 授权中心服务
apiAuth:
 url: https://api-auth-aetest.glodon.com
 appKey: kqIUvqfcH7obwsZRx4U3L3yG5M3sLuRL
 g-signature: F864378123E9BA92E14E6C7862257FCC

# 部件服务
dcost-sub:
 url: https://dcost-sub-test-sprint.glodon.com

config:
 depend:
  accountGlodonServiceKey: MiOWRrW6hYBmW6xQE8OGRH2f9I5wfxUG
  accountGlodonserverSecret: vG23xiwByuRxKdhAehgKaDEK89jyKzsC

object-storage:
 enable: true
 protocol: S3_HW  #可选项 OSS、S3_HW、S3_COS、S3_AWS、S3_AWS_V4、S3_MINIO、S3_CEPH、S3_QY、S3
 endPoint: https://obs.cn-north-4.myhuaweicloud.com
 #endPoint: https://oss-cn-beijing-internal.aliyuncs.com #内网地址
 externalEndpoint: https://obs.cn-north-4.myhuaweicloud.com #外网地址
 accessKeyId: LIAVC36ROIM0YUIUYUXX
 accessKeySecret: pEtQS6XVokEz1aBB67IOuBPhseE1sI8wjeWucRfD
 bucketName: gldzb-test
 region: cn-north-4
 pathStyleAccess: false # 是否强制开启路径形式的资源访问
 maxConnections: 5000
 connectionTimeout: 3000
 socketTimeout: 3000

# es配置
depend:
 elasticsearch:
  hosts: ************:9200
  username: elastic
  password: 123456
  connectTimeOut: 200000 #连接超时时间
  socketTimeOut: 300000 #连接超时时间
  connectionRequestTimeOut: 60000 #获取连接的超时时间
  maxConnectNum: 500 #最大连接数
  maxConnectPerRoute: 200 #最大路由连接数
  maxKeepAliveTime: 600000  # 链接最大存活时间