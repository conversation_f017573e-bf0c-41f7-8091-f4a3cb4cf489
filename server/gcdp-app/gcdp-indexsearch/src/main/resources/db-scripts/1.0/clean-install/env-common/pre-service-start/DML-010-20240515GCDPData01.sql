USE `db_cost_data_platform_pro`;
REPLACE INTO `db_cost_data_platform_pro`.gcdp_func_dws_biz_config (`id`, `product_source`, `biz_code`, `biz_name`, `biz_type`, `state`)
VALUES ('810', 'hgPlatform', 'biz-ja', '建安', 'gb', '1'),
       ('811', 'hgPlatform', 'biz-qd-push', '清单推送清单库', 'gb', '1'),
       ('812', 'hgPlatform', 'biz-rcj-push', '人材机推送材料库', 'gb', '1');

REPLACE INTO `db_cost_data_platform_pro`.gcdp_func_dws_biz_config (`id`, `product_source`, `biz_code`, `biz_name`, `biz_type`, `state`)
VALUES ('813', 'sgcbhs2', 'biz-rcj-push', '人材机推送', 'sgcbhs2', '1'),
       ('814', 'sgcbhs2', 'biz-fbqd', '分包清单', 'sgcbhs2', '1');

REPLACE INTO `db_cost_data_platform_pro`.gcdp_func_dws_biz_config (`id`, `product_source`, `biz_code`, `biz_name`, `biz_type`, `state`)
VALUES ('815', 'zbw-zbsq-web', 'biz-ja', '建安', 'gb', '1'),
       ('816', 'zbw-zbsq-web', 'biz-qd-push', '清单推送清单库', 'gb', '1'),
       ('817', 'zbw-zbsq-web', 'biz-rcj-push', '人材机推送材料库', 'gb', '1');
