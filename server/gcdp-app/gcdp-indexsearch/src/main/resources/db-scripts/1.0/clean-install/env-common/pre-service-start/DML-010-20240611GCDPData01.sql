use `db_cost_data_platform_pro`;
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_contract_project
    ADD COLUMN item_cost_type TINYINT DEFAULT NULL COMMENT '科目费用类型，1全费 2非全费 3 非全费->全费' AFTER bid_type;

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_contract_project_bidnode
    ADD COLUMN item_cost_type INT DEFAULT null comment '科目费用类型，1全费 2非全费 3 非全费->全费' after bid_node_uuid,
    ADD COLUMN non_full_cost_amount DECIMAL(25, 6) DEFAULT NULL COMMENT '非全费合价' AFTER amount,
    ADD COLUMN non_full_cost_amount_include_tax DECIMAL(25, 6) DEFAULT NULL COMMENT '非全费含税合价' AFTER amount_include_tax;

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_index
    ADD COLUMN item_cost_type TINYINT DEFAULT NULL COMMENT '科目费用类型，1全费 2非全费' AFTER status;

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index
    ADD COLUMN item_cost_type TINYINT DEFAULT NULL COMMENT '科目费用类型，1全费 2非全费' AFTER code;

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_project_note
    ADD COLUMN item_cost_type TINYINT DEFAULT NULL COMMENT '科目费用类型，1全费 2非全费 3 非全费->全费' AFTER type,
    ADD COLUMN non_full_cost_total DECIMAL(25, 6) DEFAULT NULL COMMENT '非全费合价' AFTER total,
    ADD COLUMN non_full_cost_total_include_tax DECIMAL(25, 6) DEFAULT NULL COMMENT '非全费含税合价' AFTER total_include_tax;


ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index
    ADD COLUMN item_cost_type TINYINT DEFAULT NULL COMMENT '科目费用类型，1全费 2非全费' AFTER quantity,
    add labor_amount decimal(25, 6) null comment '人工非合价' after item_cost_type,
    add material_amount decimal(25, 6) null comment '材料费合价' after labor_amount,
    add machine_amount decimal(25, 6) null comment '机械费合价' after material_amount,
    add other_amount decimal(25, 6) null comment '综合费合价（管理费+利润+地区特性(人工费价差/风险费) + 分摊的费用）' after machine_amount;


ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_cost
    ADD COLUMN item_cost_type TINYINT DEFAULT NULL COMMENT '科目费用类型，1全费 2非全费' AFTER unit,
    add labor_amount decimal(25, 6) null comment '人工非合价' after amount_include_tax,
    add material_amount decimal(25, 6) null comment '材料费合价' after labor_amount,
    add machine_amount decimal(25, 6) null comment '机械费合价' after material_amount,
    add other_amount decimal(25, 6) null comment '综合费合价（管理费+利润+地区特性(人工费价差/风险费) + 分摊的费用）' after machine_amount;


ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_usage
    ADD COLUMN item_cost_type TINYINT DEFAULT NULL COMMENT '科目费用类型，1全费 2非全费' AFTER unit,
    add labor_amount decimal(25, 6) null comment '人工非合价' after jmhl_index_unit,
    add material_amount decimal(25, 6) null comment '材料费合价' after labor_amount,
    add machine_amount decimal(25, 6) null comment '机械费合价' after material_amount,
    add other_amount decimal(25, 6) null comment '综合费合价（管理费+利润+地区特性(人工费价差/风险费) + 分摊的费用）' after machine_amount;
