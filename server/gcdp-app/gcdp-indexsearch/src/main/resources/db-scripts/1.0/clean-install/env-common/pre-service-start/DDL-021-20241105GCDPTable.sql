use `db_cost_data_platform_pro`;
CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_index_template` (
  `id` bigint NOT NULL DEFAULT '0',
  `pid` bigint DEFAULT NULL COMMENT '父ID',
  `contract_project_id` bigint DEFAULT NULL COMMENT 'DWD_contract_project主键',
  `enterprise_id` varchar(32)  DEFAULT NULL COMMENT '企业ID',
  `ord` int DEFAULT NULL COMMENT '排序字段',
  `template_items_custom_uuid` varchar(50)  DEFAULT NULL COMMENT '模版项uuid',
  `code` varchar(255) DEFAULT NULL COMMENT '编码',
  `name` varchar(255) DEFAULT NULL COMMENT '科目名称或结构名称',
  `unit` varchar(50) DEFAULT NULL COMMENT '单位',
  `functional_domain` varchar(255) DEFAULT NULL COMMENT '功能区 多个更能区使用，隔开',
  `template_uuid` varchar(50) DEFAULT NULL COMMENT '应用模版uuid',
  `name_path` varchar(500) DEFAULT NULL COMMENT '科目或结构的全路径名称',
  `index_type` smallint DEFAULT NULL COMMENT '指标分类 1=经济指标；2=主要量指标',
  `index_detail_type` smallint DEFAULT NULL COMMENT '指标明细分类 \r\n指标神器成本指标单位区分 专业和部位\r\n1=经济指标_专业，2=经济指标_部位',
  `remark` varchar(1000) DEFAULT NULL COMMENT '其他说明',
  `calculate_pk_code` varchar(96) DEFAULT NULL COMMENT '计算口径内部编码',
  `calculate_name` varchar(255) DEFAULT NULL COMMENT '口径名称',
  `property` tinyint DEFAULT NULL COMMENT '科目属性:1固定值 2 非标项 3 敏感性 4 功能性',
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'yyyy-MM-dd HH:mm:ss',
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`) USING BTREE,
  KEY `enterprise_id` (`enterprise_id`,`contract_project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET = utf8 COMMENT='工程引用科目模板数据';

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_norm_item` CHANGE COLUMN `code` `code` VARCHAR(200) NULL DEFAULT NULL COMMENT '编码';
