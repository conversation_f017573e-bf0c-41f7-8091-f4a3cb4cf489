
use `db_cost_data_platform_pro`;

alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project
    modify total decimal(25, 6) null comment '总造价(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project
    modify total_include_tax decimal(25, 6) null comment '总造价(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project
    modify unit_index_structure_area decimal(25, 6) null comment '建面单方';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project
    modify unit_index_sale_area decimal(25, 6) null comment '销面单方';
# gcdp_dwd_bqitem
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify quantity decimal(25, 6) null comment '工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify limiting_rate decimal(25, 6) null comment '最高限价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify rate decimal(25, 6) null comment '综合单价税前';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify rate_include_tax decimal(25, 6) null comment '综合单价含税';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify amount decimal(25, 6) null comment '综合合价税前';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify amount_include_tax decimal(25, 6) null comment '综合合价含税';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify labor_rate decimal(25, 6) null comment '人工费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify labor_amount decimal(25, 6) null comment '人工费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify primary_material_rate decimal(25, 6) null comment '主材费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify primary_material_amount decimal(25, 6) null comment '主材费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify material_Rate decimal(25, 6) null comment '材料费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify material_amount decimal(25, 6) null comment '材料费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify machine_rate decimal(25, 6) null comment '机械费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify machine_amount decimal(25, 6) null comment '机械费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify equipment_rate decimal(25, 6) null comment '设备费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify equipment_amount decimal(25, 6) null comment '设备费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify overhead_rate decimal(25, 6) null comment '管理费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify overhead_amount decimal(25, 6) null comment '管理费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify profit_rate decimal(25, 6) null comment '利润单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify profit_amount decimal(25, 6) null comment '利润合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify tax_rate decimal(25, 6) null comment '增值税单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify tax_amount decimal(25, 6) null comment '增值税合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify levy_fee_Rate decimal(25, 6) null comment '规费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify levy_fee_amount decimal(25, 6) null comment '规费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify hseaw_rate decimal(25, 6) null comment '安全文明施工费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify hseaw_amount decimal(25, 6) null comment '安全文明施工费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify risk_rate decimal(25, 6) null comment '综合合价风险单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify risk_amount decimal(25, 6) null comment '综合合价风险合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify rest_rate decimal(25, 6) null comment '其他费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify rest_amount decimal(25, 6) null comment '其他费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_labor_rate decimal(25, 6) null comment '取费前人工费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_material_rate decimal(25, 6) null comment '取费前材料费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_primary_material_rate decimal(25, 6) null comment '取费前主材费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_machine_rate decimal(25, 6) null comment '取费前机械费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_equipment_rate decimal(25, 6) null comment '取费前设备费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_overhead_rate decimal(25, 6) null comment '取费前管理费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_rest_rate decimal(25, 6) null comment '取费前其他费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_rate decimal(25, 6) null comment '取费前单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify provisional_material_rate decimal(25, 6) null comment '暂估材料费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_labor_amount decimal(25, 6) null comment '取费前人工费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_material_amount decimal(25, 6) null comment '取费前材料费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_primary_material_amount decimal(25, 6) null comment '取费前主材费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_machine_amount decimal(25, 6) null comment '取费前机械费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_equipment_amount decimal(25, 6) null comment '取费前设备费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_overhead_amount decimal(25, 6) null comment '取费前管理费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_rest_amount decimal(25, 6) null comment '取费前其他费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify norm_amount decimal(25, 6) null comment '取费前合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify provisional_material_amount decimal(25, 6) null comment '暂估材料费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_rate decimal(25, 6) null comment '组织措施单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify tech_amount decimal(25, 6) null comment '技术措施合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_Amount decimal(25, 6) null comment '组织措施合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_labor_rate decimal(25, 6) null comment '组织人工费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_material_rate decimal(25, 6) null comment '组织材料费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_machine_rate decimal(25, 6) null comment '组织机械费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_equipment_rate decimal(25, 6) null comment '组织设备费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_primary_material_rate decimal(25, 6) null comment '组织主材费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_labor_amount decimal(25, 6) null comment '组织人工合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_material_amount decimal(25, 6) null comment '组织材料合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_machine_amount decimal(25, 6) null comment '组织机械合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_equipment_amount decimal(25, 6) null comment '组织设备合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify org_primary_material_amount decimal(25, 6) null comment '组织主材合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify tax_value decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify expression_value decimal(25, 6) null comment '计算口径值:业务特性,市场化计价主要量指标的计算口径值';
# gcdp_dwd_bqitem_cost_detail
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem_cost_detail
    modify rate decimal(25, 6) null comment '单价（税前）';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem_cost_detail
    modify amount decimal(25, 6) null comment '合价（税前）';
# gcdp_dwd_bqitem_lmmdetail
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem_lmmdetail
    modify quantity decimal(25, 6) null comment '工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem_lmmdetail
    modify usage_value decimal(25, 6) null comment '含量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem_lmmdetail
    modify original_usage decimal(25, 6) null comment '原始含量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem_lmmdetail
    modify amount decimal(25, 6) null comment '合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem_lmmdetail
    modify waste_rate decimal(25, 6) null comment '损耗率';
#gcdp_dwd_bqitem_quantity_detail
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem_quantity_detail
    modify quantity decimal(25, 6) null comment '明细工程量';
#gcdp_dwd_contract_project_bidnode    25,8 跟建筑面积保持一致
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_bidnode
    modify building_area decimal(25, 8) null comment '建筑面积';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_bidnode
    modify unit_index decimal(25, 6) null comment '单方造价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_bidnode
    modify amount decimal(25, 6) null comment '造价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_bidnode
    modify percent decimal(25, 6) null comment '占项目总造价比例';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_bidnode
    modify amount_include_tax decimal(25, 6) null comment '含税合价';
#gcdp_dwd_contract_project_build_standard_detail
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_build_standard_detail
    modify reference_price decimal(25, 6) null comment '参考价';
#gcdp_dwd_contract_project_contract_plan
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_contract_plan
    modify amount decimal(25, 6) null comment '合约规划金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_contract_plan
    modify change_ratio decimal(25, 6) null comment '预留变更率%';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_contract_plan
    modify control_amount decimal(25, 6) null comment '控制金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_contract_plan
    modify calculate_value_structure_area decimal(25, 6) null comment '建筑面积口径值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_contract_plan
    modify unit_index_structure_area decimal(25, 6) null comment '建面单方';
# gcdp_dwd_contract_project_lyhf
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_lyhf
    modify building_area decimal(25, 6) null comment '建筑面积';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_lyhf
    modify amount decimal(25, 6) null comment '总造价(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_lyhf
    modify amount_include_tax decimal(25, 6) null comment '总造价(含税)';
#gcdp_dwd_contract_project_lyhf_jzx
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_lyhf_jzx
    modify value decimal(25, 6) null comment '拆分值';
#gcdp_dwd_cost_summary_index
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index
    modify amount decimal(25, 6) null comment '金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index
    modify amount_include_tax decimal(26, 6) null comment '金额（含税）';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index
    modify amount_no_device_fee decimal(25, 6) null comment '合价-不含设备费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index
    modify quantity decimal(25, 6) null comment '工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index
    modify calculate_value decimal(25, 6) null comment '口径值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index
    modify amount_include_tax_no_device_fee decimal(25, 6) null comment '合价不含设备费（含税）';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index
    modify tax_ratio decimal(25, 6) null comment '税率';
# gcdp_dwd_cost_summary_index_data
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    modify amount_value decimal(25, 6) null comment '指标计算合计值_分母';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    modify amount_value_include_tax decimal(25, 6) null comment '指标计算合计值含税_分母';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    modify amount_value_no_device_fee decimal(25, 6) null comment '指标计算合计值不含设备费_分母';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    modify calculate_value decimal(25, 6) null comment '口径值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    modify calculate_value_include_tax decimal(25, 6) null comment '口径值含税';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    modify index_value decimal(25, 6) null comment '指标值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    modify index_value_include_tax decimal(25, 6) null comment '指标值含税';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    modify index_value_no_device_fee decimal(25, 5) null comment '指标值-不含设备费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    modify factor decimal(25, 6) null comment '系数';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    modify amount_value_include_tax_no_device_fee decimal(25, 6) null comment '合价不含设备费（含税）';
alter table `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    modify index_value_include_tax_no_device_fee decimal(25, 6) null comment '指标值不含设备费（含税）';
#gcdp_dwd_dynamic_resource_temp
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify quantity decimal(25, 6) null comment '工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify cur_res_quantity decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify market_rate decimal(25, 6) null comment '市场价单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify market_amount decimal(25, 6) null comment '市场价合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify market_pre_tax_rate decimal(25, 6) null comment '不含税市场价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify old_market_pre_tax_rate decimal(25, 6) null comment '旧不含税市场价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify market_pre_tax_amount decimal(25, 6) null comment '不含税市场合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify old_market_pre_tax_amount decimal(25, 6) null comment '旧不含税市场合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify budget_tax_rate decimal(25, 6) null comment '含税预算单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify budget_tax_amount decimal(25, 6) null comment '含税预算合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify tax_ratio decimal(25, 6) null comment '税率';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify market_tax_amount decimal(25, 6) null comment '含税市场价合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify old_market_tax_amount decimal(25, 6) null comment '旧含税市场价合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify market_tax_rate decimal(25, 6) null comment '含税市场价单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify old_market_tax_rate decimal(25, 6) null comment '旧含税市场价单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify percent decimal(25, 6) null comment '费用占比';
alter table `db_cost_data_platform_pro`.gcdp_dwd_dynamic_resource_temp
    modify old_tax_ratio decimal(25, 6) null comment '税率【除税率】原始值';
# gcdp_dwd_expend_purchase_project
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    modify contract_total decimal(25, 6) null comment '合同金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    modify contract_total_include_tax decimal(25, 6) null comment '含税合同金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    modify cost_amount decimal(25, 6) null comment '计成本金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    modify credit_tax_amount decimal(25, 6) null comment '抵扣税额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    modify tax_radio decimal(25, 6) null comment '税率';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    modify close_total_include_tax decimal(25, 6) null comment '含税结算金额';
# gcdp_dwd_expend_sub_bqitem
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem
    modify quantity decimal(25, 6) null comment '数量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem
    modify rate decimal(25, 6) null comment '不含税单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem
    modify total decimal(25, 6) null comment '合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem
    modify tax_ratio decimal(25, 6) null comment '税率';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem
    modify rate_include_tax decimal(25, 6) null comment '含税单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem
    modify total_include_tax decimal(25, 6) null comment '含税合价';
# gcdp_dwd_expend_sub_farmers_pay
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_farmers_pay
    modify amount decimal(25, 6) null comment '代发金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_farmers_pay
    modify total_amount decimal(25, 6) null comment '累计代发金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_farmers_pay
    modify pay_total_amount decimal(25, 6) null comment '累计应付金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_farmers_pay
    modify percent decimal(25, 6) null comment '占应付款';
#gcdp_dwd_expend_sub_progress_pay
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_progress_pay
    modify pay_amount decimal(25, 6) null comment '支付金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_progress_pay
    modify total_amount decimal(25, 6) null comment '累计支付金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_progress_pay
    modify total_Pay_ment_Amount decimal(25, 6) null comment '累计计算金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_progress_pay
    modify percent decimal(25, 6) null comment '占应付款';
#gcdp_dwd_expend_sub_progress_settle
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_progress_settle
    modify bqitem_amount decimal(25, 6) null comment '清单结算金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_progress_settle
    modify total_bqitem_amount decimal(25, 6) null comment '累计清单结算金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_progress_settle
    modify deduction_amount decimal(25, 6) null comment '扣款金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_progress_settle
    modify total_deduction_amount decimal(25, 6) null comment '累计扣款金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_progress_settle
    modify pay_amount decimal(25, 6) null comment '实际结算金额';
#gcdp_dwd_expend_sub_project
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_project
    modify contract_total decimal(25, 6) null comment '合同金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_project
    modify contract_total_tax decimal(25, 6) null comment '含税合同金额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_project
    modify tax_rate decimal(25, 6) null comment '税率';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_project
    modify input_tax decimal(25, 6) null comment '进项税额';
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_project
    modify contract_days decimal(25, 6) null comment '工期';
#gcdp_dwd_index
alter table `db_cost_data_platform_pro`.gcdp_dwd_index
    modify quantity decimal(25, 6) null comment '工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index
    modify rate decimal(25, 6) null comment '单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index
    modify amount decimal(25, 6) null comment '合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index
    modify rate_include_tax decimal(25, 6) null comment '含税单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index
    modify amount_include_tax decimal(25, 6) null comment '含税合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index
    modify calculate_pk_code varchar(96) null comment '计算口径内部编码';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index
    modify calculate_name varchar(255) null comment '口径名称';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index
    modify calculate_value decimal(25, 6) null comment '口径值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index
    modify usage_value decimal(25, 6) null comment '含量';
#gcdp_dwd_index_data
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data
    modify calculate_name varchar(255) null comment '计算口径名称';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data
    modify calculate_value decimal(25, 6) null comment '计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data
    modify index_value decimal(25, 6) null comment '税前值，或不区分税前税后的值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data
    modify index_value_include_tax decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data
    modify factor decimal(25, 6) default 1.00000 null comment '默认值 1 百分比100';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data
    modify calculate_value_include_tax decimal(25, 6) null comment '计算口径值含税';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data
    modify amount_value decimal(25, 6) null comment '指标计算合计值_分母';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data
    modify amount_value_include_tax decimal(25, 6) null comment '指标计算合计值含税_分母';
# gcdp_dwd_index_data_ld
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data_ld
    modify calculate_name varchar(255) null comment '计算口径名称';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data_ld
    modify calculate_value decimal(25, 6) null comment '计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data_ld
    modify index_value decimal(25, 6) null comment '税前值，或不区分税前税后的值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data_ld
    modify index_value_include_tax decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data_ld
    modify factor decimal(25, 6) default 1.00000 null comment '默认值 1 百分比100';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data_ld
    modify calculate_value_include_tax decimal(25, 6) null comment '计算口径值含税';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data_ld
    modify amount_value decimal(25, 6) null comment '指标计算合计值_分母';
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data_ld
    modify amount_value_include_tax decimal(25, 6) null comment '指标计算合计值含税_分母';
#gcdp_dwd_index_data_makeup
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_data_makeup
    modify factor decimal(25, 6) null comment '转换系数';
#gcdp_dwd_index_makeup
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_makeup
    modify factor decimal(25, 6) null comment '转换系数';
#gcdp_dwd_limit_index
alter table `db_cost_data_platform_pro`.gcdp_dwd_limit_index
    modify index_value decimal(25, 6) null comment '指标值';
#gcdp_dwd_non_construction_installation_index
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index
    modify quantity decimal(25, 6) null comment '工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index
    modify rate decimal(25, 6) null comment '单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index
    modify amount decimal(25, 6) null comment '合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index
    modify rate_include_tax decimal(25, 6) null comment '含税单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index
    modify amount_include_tax decimal(25, 6) null comment '含税合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index
    modify calculate_name varchar(255) null comment '口径名称';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index
    modify calculate_value decimal(25, 6) null comment '口径值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index
    modify calculate_pk_code varchar(64) null comment '计算口径内部编码';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index
    modify usage_value decimal(25, 6) null comment '含量';
#gcdp_dwd_non_construction_installation_index_data
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index_data
    modify amount_value decimal(25, 6) null comment '指标计算合计值_分母';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index_data
    modify amount_value_include_tax decimal(25, 6) null comment '指标计算合计值含税_分母';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index_data
    modify calculate_name varchar(255) null comment '口径名称';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index_data
    modify calculate_value decimal(25, 6) null comment '口径值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index_data
    modify calculate_value_include_tax decimal(25, 6) null comment '口径值含税';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index_data
    modify index_value decimal(25, 6) null comment '指标值';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index_data
    modify index_value_include_tax decimal(25, 6) null comment '指标值含税';
alter table `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index_data
    modify factor decimal(25, 6) null comment '系数';
#gcdp_dwd_norm_item
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify quantity decimal(25, 6) null comment '工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify wastage_ratio decimal(25, 6) null comment '损耗率';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify usage_value decimal(25, 6) null comment '含量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_labor_rate decimal(25, 6) null comment '取费前人工费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_material_rate decimal(25, 6) null comment '取费前材料费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_primary_material_rate decimal(25, 6) null comment '取费前主材费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_machine_rate decimal(25, 6) null comment '取费前机械费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_equipment_rate decimal(25, 6) null comment '取费前设备费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_overhead_rate decimal(25, 6) null comment '取费前管理费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_rest_rate decimal(25, 6) null comment '取费前其他费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_rate decimal(25, 6) null comment '取费前单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify provisional_material_rate decimal(25, 6) null comment '暂估材料费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_labor_amount decimal(25, 6) null comment '取费前人工费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_material_amount decimal(25, 6) null comment '取费前材料费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_primary_material_amount decimal(25, 6) null comment '取费前主材费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_machine_amount decimal(25, 6) null comment '取费前机械费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_equipment_amount decimal(25, 6) null comment '取费前设备费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_overhead_amount decimal(25, 6) null comment '取费前管理费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_rest_amount decimal(25, 6) null comment '取费前其他费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_amount decimal(25, 6) null comment '取费前合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify provisional_material_amount decimal(25, 6) null comment '暂估材料费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_budget_labor_rate decimal(25, 6) null comment '取费前预算价人工费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_budget_material_rate decimal(25, 6) null comment '取费前预算价材料费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_budget_machine_rate decimal(25, 6) null comment '取费前预算价机械费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_budget_rate decimal(25, 6) null comment '取费前预算价单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_budget_labor_amount decimal(25, 6) null comment '取费前预算价人工费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_budget_material_amount decimal(25, 6) null comment '取费前预算价材料费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_budget_machine_amount decimal(25, 6) null comment '取费前预算价机械费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_budget_primary_material_amount decimal(25, 6) null comment '取费前预算价主材费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_budget_equipment_amount decimal(25, 6) null comment '取费前预算价设备费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_market_labor_rate decimal(25, 6) null comment '取费前市场价人工费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_market_material_rate decimal(25, 6) null comment '取费前市场价材料费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_market_machine_rate decimal(25, 6) null comment '取费前市场价机械费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_market_rate decimal(25, 6) null comment '取费前市场价单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_market_labor_amount decimal(25, 6) null comment '取费前市场价人工费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_market_material_amount decimal(25, 6) null comment '取费前市场价材料费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_market_machine_amount decimal(25, 6) null comment '取费前市场价机械费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_market_primary_material_amount decimal(25, 6) null comment '取费前市场价主材费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify norm_market_equipment_amount decimal(25, 6) null comment '取费前市场价设备费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify manday_amount decimal(25, 6) null comment '工日消耗量合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify rate decimal(25, 6) null comment '综合单价(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify rate_include_tax decimal(25, 6) null comment '综合单价(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify amount decimal(25, 6) null comment '综合合价(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify amount_include_tax decimal(25, 6) null comment '综合合价(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify labor_rate decimal(25, 6) null comment '人工费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify material_Rate decimal(25, 6) null comment '材料费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify machine_rate decimal(25, 6) null comment '机械费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify primary_material_rate decimal(25, 6) null comment '主材费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify equipment_rate decimal(25, 6) null comment '设备费单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify rest_rate decimal(25, 6) null comment '其他费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify labor_amount decimal(25, 6) null comment '人工费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify primary_material_amount decimal(25, 6) null comment '主材费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify material_amount decimal(25, 6) null comment '材料费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify machine_amount decimal(25, 6) null comment '机械费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify equipment_amount decimal(25, 6) null comment '设备费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify rest_amount decimal(25, 6) null comment '其他费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify risk_amount decimal(25, 6) null comment '综合合价风险';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify tax_amount decimal(25, 6) null comment '综合合价税金';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify overhead_amount decimal(25, 6) null comment '综合合价管理费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify profit_amount decimal(25, 6) null comment '综合合价利润';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify hseaw_amount decimal(25, 6) null comment '综合合价安全文明施工费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify qtcsfat decimal(25, 6) null comment '其他措施费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify risk_rate decimal(25, 6) null comment '综合单价风险';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify tax_rate decimal(25, 6) null comment '综合单价税金';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify overhead_rate decimal(25, 6) null comment '综合单价管理费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify profit_rate decimal(25, 6) null comment '综合单价利润';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item
    modify hseaw_rate decimal(25, 6) null comment '综合单价安全文明施工费';
#gcdp_dwd_norm_item_cost_detail
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item_cost_detail
    modify rate decimal(25, 6) null comment '单价（税前）';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item_cost_detail
    modify amount decimal(25, 6) null comment '合价（税前）';
#gcdp_dwd_norm_item_lmmdetail
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item_lmmdetail
    modify quantity decimal(25, 6) null comment '工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item_lmmdetail
    modify original_usage decimal(25, 6) null comment '原始含量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item_lmmdetail
    modify usage_value decimal(25, 6) null comment '含量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item_lmmdetail
    modify amount decimal(25, 6) null comment '合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item_lmmdetail
    modify waste_rate decimal(25, 6) null comment '损耗率';
#gcdp_dwd_project_second_cost
alter table `db_cost_data_platform_pro`.gcdp_dwd_project_second_cost
    modify expression_value decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_project_second_cost
    modify price decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_project_second_cost
    modify unit_cost decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_project_second_cost
    modify total_precent decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_project_second_cost
    modify total_secont_precent decimal(25, 6) null;
#gcdp_dwd_ratedetail_template
alter table `db_cost_data_platform_pro`.gcdp_dwd_ratedetail_template
    modify ratio decimal(25, 6) null comment '费率';
#gcdp_dwd_resource
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify rate decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify rate_include_tax decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify quantity decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify amount decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify supply_rate decimal(25, 6) null comment '供应价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify waste_rate decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify ps_cost_ratio decimal(25, 6) null comment '采保费率';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify tax_ratio decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify balance_rate decimal(25, 6) null comment '价差单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify balance_quantity decimal(25, 6) null comment '价差数量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify balance_amount decimal(25, 6) null comment '价差合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_quantity decimal(25, 6) null comment '甲供数量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify budget_amount decimal(25, 6) null comment '预算价合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify swc_quantity decimal(25, 6) null comment '三材量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify primary_material_quantity decimal(25, 6) null comment '主要材料量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_budget_rate decimal(25, 6) null comment '甲供材料预算价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_market_rate decimal(25, 6) null comment '甲供材料市场价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_budget_amount decimal(25, 6) null comment '甲供材料预算价合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_market_amount decimal(25, 6) null comment '甲供材料市场价合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify provisional_rate decimal(25, 6) null comment '暂估材料单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify provisional_quantity decimal(25, 6) null comment '暂估数量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify provisional_amount decimal(25, 6) null comment '暂估合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify first_specify_rate decimal(25, 6) null comment '甲定材料单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify first_specify_quantity decimal(25, 6) null comment '甲定材料数量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify first_specify_amount decimal(25, 6) null comment '甲定材料合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify close_rate decimal(25, 6) null comment '结算价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify close_balance decimal(25, 6) null comment '结算价价差';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify close_balance_amount decimal(25, 6) null comment '结算价价差合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_close_balance_amount decimal(25, 6) null comment '甲供材料结算价价差合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify budget_supply_rate decimal(25, 6) null comment '预算供应价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify sub_letting_res_quantity decimal(25, 6) null comment '分包材料量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify original_market_rate decimal(25, 6) null comment '原始市场价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify original_tax_market_rate decimal(25, 6) null comment '原始含税市场价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_balance_amount decimal(25, 6) null comment '甲供价差合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_balance_quantity decimal(25, 6) null comment '甲供材料价差数量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify market_amount decimal(25, 6) null comment '市场价合计';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify market_rate decimal(25, 6) null comment '市场价单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify budget_tax_rate decimal(25, 6) null comment '含税预算价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify budget_tax_amount decimal(25, 6) null comment '含税预算价合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify budget_pre_tax_rate decimal(25, 6) null comment '不含税预算价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify budget_pre_tax_amount decimal(25, 6) null comment '不含税预算价合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify budget_rate decimal(25, 6) null comment '预算价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_budget_tax_rate decimal(25, 6) null comment '甲供含税预算价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_market_tax_rate decimal(25, 6) null comment '甲供含税市场价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_budget_tax_amount decimal(25, 6) null comment '甲供含税预算价合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify osm_market_tax_amount decimal(25, 6) null comment '甲供含税市场价合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify market_tax_rate decimal(25, 6) null comment '含税市场价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify market_tax_amount decimal(25, 6) null comment '含税市场价合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify amount_include_tax decimal(25, 6) null comment '含税合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    modify tax_amount decimal(25, 6) null comment '税额';
#gcdp_dwd_sub_bqitem
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify quantity decimal(25, 6) null comment '工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify limit_rate decimal(25, 6) null comment '限价单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify limit_labor_rate decimal(25, 6) null comment '限价人工费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify limit_material_rate decimal(25, 6) null comment '限价辅材费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify limit_machine_rate decimal(25, 6) null comment '限价机械费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify limit_zjf_rate decimal(25, 6) null comment '限价直接费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify limit_jjf_rate decimal(25, 6) null comment '限价间接费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify rate decimal(25, 6) null comment '上报单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify total decimal(25, 6) null comment '上报合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify labor_rate decimal(25, 6) null comment '上报人工费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify labor_amount decimal(25, 6) null comment '上报人工费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify material_rate decimal(25, 6) null comment '上报辅材费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify material_amount decimal(25, 6) null comment '上报辅材费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify machine_rate decimal(25, 6) null comment '上报机械费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify machine_amount decimal(25, 6) null comment '上报机械费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify zjf_rate decimal(25, 6) null comment '上报直接费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify zjf_amount decimal(25, 6) null comment '上报直接费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify jjf_rate decimal(25, 6) null comment '上报间接费';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem
    modify jjf_amount decimal(25, 6) null comment '上报间接费合价';
#gcdp_dwd_sub_bqitem_bid
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_bid
    modify quantity decimal(25, 6) null comment '工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_bid
    modify total decimal(25, 6) null comment '上报合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_bid
    modify labor_amount decimal(25, 6) null comment '上报人工费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_bid
    modify material_amount decimal(25, 6) null comment '上报辅材费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_bid
    modify machine_amount decimal(25, 6) null comment '上报机械费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_bid
    modify zjf_amount decimal(25, 6) null comment '上报直接费合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_bid
    modify jjf_amount decimal(25, 6) null comment '上报间接费合价';
#gcdp_dwd_sub_bqitem_bid_cost_detail
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_bid_cost_detail
    modify amount decimal(25, 6) null comment '上报合价';
#gcdp_dwd_sub_bqitem_cost_detail
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_cost_detail
    modify rate decimal(25, 6) null comment '上报单价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_cost_detail
    modify amount decimal(25, 6) null comment '上报合价';
#gcdp_dwd_sub_bqitem_link_contractor_bqitem
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_link_contractor_bqitem
    modify usage_value decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_link_contractor_bqitem
    modify quantity decimal(25, 6) null comment '数量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_link_contractor_bqitem
    modify limit_total decimal(25, 6) null comment '指导价合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_link_contractor_bqitem
    modify total decimal(25, 6) null comment '上报价';
#gcdp_dwd_sub_bqitem_lmmdetail
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_lmmdetail
    modify usage_value decimal(25, 6) null;
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_lmmdetail
    modify usage_amount decimal(25, 6) null comment '合价';
#gcdp_dwd_sub_contractor_bqitem
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem
    modify contract_quantity decimal(25, 6) null comment '合同工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem
    modify labour_quantity decimal(25, 6) null comment '分包工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem
    modify contract_rate decimal(25, 6) null comment '合同人工单价（税前）';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem
    modify limit_rate decimal(25, 6) null comment '指导价人工单价（税前)';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem
    modify control_rate decimal(25, 6) null comment '项目上报人工单价（税前）';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem
    modify contract_total decimal(25, 6) null comment '合同人工合价（税前）';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem
    modify limit_total decimal(25, 6) null comment '指导价人工合价（税前）';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem
    modify control_total decimal(25, 6) null comment '项目上报人工合价（税前）';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem
    modify diff_total decimal(25, 6) null comment '收支差价（税前）';
#gcdp_dwd_sub_contractor_bqitem_bid
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem_bid
    modify quantity decimal(25, 6) null comment '工程量';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem_bid
    modify total decimal(25, 6) null comment '合价';
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_contractor_bqitem_bid
    modify percentage decimal(25, 6) null comment '占比';
#gcdp_dwd_sub_multi_bid
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_multi_bid
    modify percentage decimal(25, 6) null comment '比重';
#gcdp_dwd_sub_ratedetail_template
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_ratedetail_template
    modify ratio decimal(25, 6) null comment '费率';
#gcdp_dwd_third_project_info
alter table `db_cost_data_platform_pro`.gcdp_dwd_third_project_info
    modify longitude decimal(25, 6) null comment '经度';
alter table `db_cost_data_platform_pro`.gcdp_dwd_third_project_info
    modify latitude decimal(25, 6) null comment '纬度';
alter table `db_cost_data_platform_pro`.gcdp_dwd_third_project_info
    modify area decimal(25, 6) null comment '项目规模';
#gcdp_dws_build_standard_index
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify df_index_value_sum decimal(25, 6) null comment '单方造价之和(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify df_index_value_tax_sum decimal(25, 6) null comment '单方造价之和(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify df_index_value_tax_count decimal(25, 6) null comment '单方造价数量(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify df_index_value_max decimal(25, 6) null comment '单方造价最大值(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify df_index_value_tax_max decimal(25, 6) null comment '单方造价最大值(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify df_index_value_min decimal(25, 6) null comment '单方造价最小值(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify df_index_value_tax_min decimal(25, 6) null comment '单方造价最小值(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify swl_index_value_sum decimal(25, 6) null comment '实物量指标结果之和(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify swl_index_value_tax_sum decimal(25, 6) null comment '实物量指标结果之和(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify swl_index_value_max decimal(25, 6) null comment '实物量单方指标结果最大值(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify swl_index_value_tax_max decimal(25, 6) null comment '实物量单方指标结果最大值(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify swl_index_value_min decimal(25, 6) null comment '实物量指标结果最小值(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_build_standard_index
    modify swl_index_value_tax_min decimal(25, 6) null comment '实物量指标结果最小值(含税)';
#gcdp_dws_business_summary
alter table `db_cost_data_platform_pro`.gcdp_dws_business_summary
    modify building_area decimal(25, 6) null comment '建筑面积';
#gcdp_dws_business_summary_ld
alter table `db_cost_data_platform_pro`.gcdp_dws_business_summary_ld
    modify building_area decimal(25, 6) null comment '建筑面积';
#gcdp_dws_index_cost
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost
    modify jm_calculate_value decimal(25, 6) null comment '建面单方-计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost
    modify jm_index_value decimal(25, 6) null comment '建面单方-指标值（不含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost
    modify jm_index_value_include_tax decimal(25, 6) null comment '建面单方-指标值（含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost
    modify swl_calculate_value decimal(25, 6) null comment '实物量单方-计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost
    modify swl_index_value decimal(25, 6) null comment '实物量单方-指标值（不含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost
    modify swl_index_value_include_tax decimal(25, 6) null comment '实物量单方-指标值（含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost
    modify amount decimal(25, 6) null comment '科目合价(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost
    modify amount_include_tax decimal(25, 6) null comment '科目合价(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost
    modify df_calculate_value decimal(25, 6) null comment '单方造价-计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost
    modify df_index_value decimal(25, 6) null comment '单方造价-指标值（不含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost
    modify df_index_value_include_tax decimal(25, 6) null comment '单方造价-指标值（含税）';
#gcdp_dws_index_cost_makeup
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost_makeup
    modify ld_quantity decimal(25, 6) null comment '楼栋工程量';
#gcdp_dws_index_economics
alter table `db_cost_data_platform_pro`.gcdp_dws_index_economics
    modify index_value decimal(25, 6) null comment '指标结果';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_economics
    modify index_value_include_tax decimal(25, 6) null comment '指标结果含税';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_economics
    modify fz_value decimal(25, 6) null comment '分子';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_economics
    modify fz_value_include_tax decimal(25, 6) null comment '分子含税';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_economics
    modify fm_value decimal(25, 6) null comment '分母';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_economics
    modify fm_value_include_tax decimal(25, 6) null comment '分母含税';
#gcdp_dws_index_main_res
alter table `db_cost_data_platform_pro`.gcdp_dws_index_main_res
    modify dj_calculate_value decimal(25, 6) null comment '主要工料单价指标-计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_main_res
    modify dj_index_value decimal(25, 6) null comment '主要工料单价指标-指标值（不含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_main_res
    modify dj_index_value_include_tax decimal(25, 6) null comment '主要工料单价指标-指标值（含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_main_res
    modify hl_calculate_value decimal(25, 6) null comment '主要工料含量指标-计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_main_res
    modify hl_index_value decimal(25, 6) null comment '主要工料含量指标-指标值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_main_res
    modify market_amount decimal(25, 6) null comment '不含税市场价合价';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_main_res
    modify tax_market_amount decimal(25, 6) null comment '含税市场价合价';
#gcdp_dws_index_project_note
alter table `db_cost_data_platform_pro`.gcdp_dws_index_project_note
    modify build_area decimal(25, 6) null comment '总建筑面积';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_project_note
    modify total decimal(25, 6) null comment '总造价(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_project_note
    modify total_include_tax decimal(25, 6) null comment '总造价(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_project_note
    modify contract_project_total decimal(25, 6) null comment '所属合同工程的总造价(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_project_note
    modify contract_project_total_include_tax decimal(25, 6) null comment '所属合同工程的总造价(含税)';
#gcdp_dws_index
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify amount decimal(25, 6) null comment '科目合价(不含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify amount_include_tax decimal(25, 6) null comment '科目合价(含税)';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify quantity decimal(25, 6) null comment '科目工程量';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify jm_calculate_value decimal(25, 6) null comment '建面/含量-计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify jmdf_index_value decimal(25, 6) null comment '建面单方-指标值（不含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify jmdf_index_value_include_tax decimal(25, 6) null comment '建面单方-指标值（含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify jmhl_index_value decimal(25, 6) null comment '建面含量-指标值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify swl_calculate_value decimal(25, 6) null comment '实物量单方-计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify swl_index_value decimal(25, 6) null comment '实物量单方-指标值（不含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify swl_index_value_include_tax decimal(25, 6) null comment '实物量单方-指标值（含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify df_calculate_value decimal(25, 6) null comment '单方造价-计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify df_index_value decimal(25, 6) null comment '单方造价-指标值（不含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify df_index_value_include_tax decimal(25, 6) null comment '单方造价-指标值（含税）';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify zyl_calculate_value decimal(25, 6) null comment '主要量指标-计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index
    modify zyl_index_value decimal(25, 6) null comment '主要量指标-指标值';
#gcdp_dws_index_makeup
alter table `db_cost_data_platform_pro`.gcdp_dws_index_makeup
    modify ld_quantity decimal(25, 6) null comment '楼栋工程量';
#gcdp_dws_index_usage
alter table `db_cost_data_platform_pro`.gcdp_dws_index_usage
    modify zyl_calculate_value decimal(25, 6) null comment '主要量指标-计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_usage
    modify zyl_index_value decimal(25, 6) null comment '主要量指标-指标值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_usage
    modify jmhl_calculate_value decimal(25, 6) null comment '建面含量-计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_usage
    modify jmhl_index_value decimal(25, 6) null comment '建面含量-指标值';
#gcdp_dws_index_usage_makeup
alter table `db_cost_data_platform_pro`.gcdp_dws_index_usage_makeup
    modify ld_quantity decimal(25, 6) null comment '楼栋工程量';
#gcdp_dws_market_project_cluster
alter table `db_cost_data_platform_pro`.gcdp_dws_market_project_cluster
    modify build_area decimal(25, 6) null comment '建筑面积';
# gcdp_dws_market_project_cluster_bqitem
alter table `db_cost_data_platform_pro`.gcdp_dws_market_project_cluster_bqitem
    modify expression_name varchar(255) null comment '计算口径名称';
alter table `db_cost_data_platform_pro`.gcdp_dws_market_project_cluster_bqitem
    modify quantity_value decimal(25, 6) null comment '含量指标，或者叫模拟清单系数';
alter table `db_cost_data_platform_pro`.gcdp_dws_market_project_cluster_bqitem
    modify quantity decimal(25, 6) null comment '清单合并后的工程量';
alter table `db_cost_data_platform_pro`.gcdp_dws_market_project_cluster_bqitem
    modify expression_value decimal(25, 6) null comment '清单合并后的计算口径值';
#gcdp_dws_result_for_analysis
alter table `db_cost_data_platform_pro`.gcdp_dws_result_for_analysis
    modify zb_cost decimal(25, 6) null comment '单方造价';
alter table `db_cost_data_platform_pro`.gcdp_dws_result_for_analysis
    modify zb_content decimal(25, 6) null comment '单方含量';
alter table `db_cost_data_platform_pro`.gcdp_dws_result_for_analysis
    modify unit_price decimal(25, 6) null comment '综合单价';
alter table `db_cost_data_platform_pro`.gcdp_dws_result_for_analysis
    modify expression_value decimal(25, 6) null comment '计算口径值';
#gcdp_dws_sub_bqitem_index_detail
alter table `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail
    modify quantity decimal(25, 6) null comment '清单数量';
alter table `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail
    modify rate decimal(25, 6) null comment '清单不含税单价';
alter table `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail
    modify rate_include_tax decimal(25, 6) null comment '含税单价';
alter table `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail
    modify tax_ratio decimal(25, 6) null comment '税率';
#gcdp_dws_subject_summary_index
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index
    modify calculate_name varchar(255) null comment '计算口径名称';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index
    modify calculate_value decimal(25, 6) null comment '计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index
    modify factor decimal(25, 6) null comment '默认值1百分比100';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index
    modify index_weighted_avg decimal(25, 6) null comment '税前值_或不区分税前税后的值';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index
    modify index_weighted_avg_include_tax decimal(25, 6) null comment '加权平均指标值_含税';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index
    modify index_arithmetic_avg decimal(25, 6) null comment '算数平均指标值_税前';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index
    modify index_arithmetic_avg_include_tax decimal(25, 6) null comment '算数平均指标值_含税';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index
    modify calculate_value_include_tax decimal(25, 6) null comment '计算口径值含税';
#gcdp_dws_subject_summary_index_ld
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index_ld
    modify calculate_name varchar(255) null comment '计算口径名称';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index_ld
    modify calculate_value decimal(25, 6) null comment '计算口径值';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index_ld
    modify factor decimal(25, 6) null comment '默认值1百分比100';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index_ld
    modify index_weighted_avg decimal(25, 6) null comment '税前值_或不区分税前税后的值';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index_ld
    modify index_weighted_avg_include_tax decimal(25, 6) null comment '加权平均指标值_含税';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index_ld
    modify index_arithmetic_avg decimal(25, 6) null comment '税前值_或不区分税前税后的值';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index_ld
    modify index_arithmetic_avg_include_tax decimal(25, 6) null comment '算数平均指标值_含税';
alter table `db_cost_data_platform_pro`.gcdp_dws_subject_summary_index_ld
    modify calculate_value_include_tax decimal(25, 6) null comment '计算口径值含税';
