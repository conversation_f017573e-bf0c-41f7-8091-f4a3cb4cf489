use `db_cost_data_platform_pro`;
UPDATE `db_cost_data_platform_pro`.gcdp_dwd_index_makeup SET `is_calc_quantity` = 1 WHERE `is_calc_quantity` IS NULL;
UPDATE `db_cost_data_platform_pro`.gcdp_dwd_index_makeup SET `is_calc_amount` = 1 WHERE `is_calc_amount` IS NULL;
UPDATE `db_cost_data_platform_pro`.gcdp_dwd_index_makeup SET `factor` = 1 WHERE `factor` IS NULL;

UPDATE `db_cost_data_platform_pro`.gcdp_dws_index_makeup SET `is_calc_quantity` = 1 WHERE `is_calc_quantity` IS NULL;
UPDATE `db_cost_data_platform_pro`.gcdp_dws_index_makeup SET `is_calc_amount` = 1 WHERE `is_calc_amount` IS NULL;
UPDATE `db_cost_data_platform_pro`.gcdp_dws_index_makeup SET `factor` = 1 WHERE `factor` IS NULL;

update `db_cost_data_platform_pro`.gcdp_dwd_bqitem set extend_data_json = NULL;