USE `db_cost_data_platform_pro`;

REPLACE INTO `db_cost_data_platform_pro`.gcdp_func_dws_biz_config (id, product_source, biz_code, biz_name, biz_type, state) VALUES (501, 'tzgsbz', 'biz-ja', '建安指标', 'tzgsbz', 1);

update `db_cost_data_platform_pro`.gcdp_dwd_contract_project set cost_ratios_way = 0 where cost_ratios_way is null;

REPLACE INTO `db_cost_data_platform_pro`.`gcdp_func_dws_biz_config` (`id`, `product_source`, `biz_code`, `biz_name`, `biz_type`, `state`)
VALUES ('120', 'jstz', 'biz-ja', '建安', 'qb', '1'),
       ('121', 'jstz', 'biz-qd-push', '清单推送清单库', 'qb', '1'),
       ('122', 'jstz', 'biz-rcj-push', '人材机推送材料库', 'qb-jstz', '1'),
       ('123', 'jstz', 'biz-qd-calculate', '模拟清单量', 'qb', '1');

REPLACE INTO `db_cost_data_platform_pro`.gcdp_func_dws_biz_config
(id, product_source, biz_code, biz_name, biz_type, state)
VALUES(601, 'sgcbcs2', 'biz-rcj-push', '人材机推送', 'sgcbcs2', 1);

REPLACE INTO `db_cost_data_platform_pro`.gcdp_func_dws_biz_config
(id, product_source, biz_code, biz_name, biz_type, state)
VALUES(602, 'sgcbcs2', 'biz-fbqd', '分包清单', 'sgcbcs2', 1);

REPLACE INTO `db_cost_data_platform_pro`.`gcdp_dws_index_category_dynamic_col_dict` (`id`, `index_category_dict_id`, `is_include_tax`, `tbl_create_date`, `dynamic_col_json`) VALUES
 (1, 1001, 0, '2022-11-25 17:57:17', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"jmIndexValue","caption":"不含税建面单方","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"amount","caption":"不含税造价金额","defaultValue":"","type":"number"},{"ord":4,"fieldName":"buildArea","caption":"建筑面积","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
 (2, 1005, 0, '2022-11-25 17:57:17', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"swlIndexValue","caption":"不含税综合单价","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"amount","caption":"不含税造价金额","defaultValue":"","type":"number"},{"ord":4,"fieldName":"quantity","caption":"工程量","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
 (3, 2001, 0, '2022-11-27 15:06:47', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"zylIndexValue","caption":"单方含量","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"quantity","caption":"工程量","defaultValue":"","type":"number"},{"ord":4,"fieldName":"caliber","caption":"计算口径","defaultValue":"","type":"text"},{"ord":5,"fieldName":"caliberValue","caption":"计算口径值","defaultValue":"","type":"number"},{"ord":6,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":7,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":8,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":9,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
 (4, 2002, 0, '2022-11-27 15:28:06', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"jmhlIndexValue","caption":"建面含量指标","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"quantity","caption":"工程量","defaultValue":"","type":"number"},{"ord":4,"fieldName":"buildArea","caption":"建筑面积","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
 (5, 1007, 0, '2022-11-27 16:06:52', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"djTaxIndexValue","caption":"主要工料的单价指标（含税）","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"taxMarketAmount","caption":"含税合价","defaultValue":"","type":"number"},{"ord":4,"fieldName":"quantity","caption":"消耗量","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
 (6, 1010, 0, '2022-11-27 19:00:21', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"djIndexValue","caption":"主要工料的单价指标（不含税）","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"marketAmount","caption":"不含税合价","defaultValue":"","type":"number"},{"ord":4,"fieldName":"quantity","caption":"消耗量","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
 (7, 2004, 0, '2022-11-27 19:45:26', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"glhlIndexValue","caption":"主要工料含量指标","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"quantity","caption":"消耗量","defaultValue":"","type":"number"},{"ord":4,"fieldName":"caliber","caption":"计算口径","defaultValue":"","type":"text"},{"ord":5,"fieldName":"caliberValue","caption":"计算口径值","defaultValue":"","type":"number"},{"ord":6,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":7,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":8,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":9,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
 (8, 2003, 0, '2022-12-02 13:46:01', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"indexValue","caption":"比值指标","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"formula","caption":"计算规则说明","defaultValue":"","type":"text"},{"ord":4,"fieldName":"fzValue","caption":"分子","defaultValue":"","type":"number"},{"ord":5,"fieldName":"fmValue","caption":"分母","defaultValue":"","type":"number"},{"ord":6,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":7,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":8,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":9,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
 (9, 1006, 0, '2023-03-30 16:21:44', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"dfIndexValue","caption":"不含税单方造价","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"amount","caption":"不含税造价金额","defaultValue":"","type":"number"},{"ord":4,"fieldName":"calculateName","caption":"计算口径","defaultValue":"","type":"text"},{"ord":5,"fieldName":"calculateValue","caption":"计算口径值","defaultValue":"","type":"number"},{"ord":6,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":7,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":8,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":9,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
 (10, 1011, 0, '2023-08-30 16:21:44', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"jmIndexValueIncludeTax","caption":"含税建面单方","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"amountIncludeTax","caption":"含税造价金额","defaultValue":"","type":"number"},{"ord":4,"fieldName":"buildArea","caption":"建筑面积","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
 (11, 1012, 0, '2023-08-30 16:21:44', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"swlIndexValueIncludeTax","caption":"含税综合单价","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"amountIncludeTax","caption":"含税造价金额","defaultValue":"","type":"number"},{"ord":4,"fieldName":"quantity","caption":"工程量","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
 (12, 1013, 0, '2023-08-30 16:21:44', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"dfIndexValueIncludeTax","caption":"含税单方造价","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"amountIncludeTax","caption":"含税造价金额","defaultValue":"","type":"number"},{"ord":4,"fieldName":"calculateName","caption":"计算口径","defaultValue":"","type":"text"},{"ord":5,"fieldName":"calculateValue","caption":"计算口径值","defaultValue":"","type":"number"},{"ord":6,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":7,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":8,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":9,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]');

