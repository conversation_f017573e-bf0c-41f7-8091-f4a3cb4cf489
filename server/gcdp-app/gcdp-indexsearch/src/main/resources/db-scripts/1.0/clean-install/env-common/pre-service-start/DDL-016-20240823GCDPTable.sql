use `db_cost_data_platform_pro`;
ALTER TABLE db_cost_data_platform_pro.gcdp_dws_sub_bqitem_index_detail ADD payment_terms varchar(500) NULL COMMENT '付款条件';
ALTER TABLE db_cost_data_platform_pro.gcdp_dws_sub_bqitem_index_detail ADD owner_default_conditions varchar(500) NULL COMMENT '己方违约条件';
ALTER TABLE db_cost_data_platform_pro.gcdp_dws_sub_bqitem_index_detail ADD is_include_management_cost varchar(500) NULL COMMENT '是否含管理费';
ALTER TABLE db_cost_data_platform_pro.gcdp_dws_sub_bqitem_index_detail ADD brand varchar(500) NULL COMMENT '品牌';
ALTER TABLE db_cost_data_platform_pro.gcdp_dws_sub_bqitem_index_detail ADD data_provider varchar(500) NULL COMMENT '数据录入者';

ALTER TABLE db_cost_data_platform_pro.gcdp_dwd_expend_sub_bqitem MODIFY COLUMN cost_detail TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '费用构成';
ALTER TABLE db_cost_data_platform_pro.gcdp_dwd_expend_sub_bqitem MODIFY COLUMN work_scope TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '工作内容';
ALTER TABLE db_cost_data_platform_pro.gcdp_dwd_expend_sub_bqitem ADD payment_terms varchar(500) NULL COMMENT '付款条件';
ALTER TABLE db_cost_data_platform_pro.gcdp_dwd_expend_sub_bqitem ADD owner_default_conditions varchar(500) NULL COMMENT '己方违约条件';
ALTER TABLE db_cost_data_platform_pro.gcdp_dwd_expend_sub_bqitem ADD is_include_management_cost varchar(500) NULL COMMENT '是否含管理费';
ALTER TABLE db_cost_data_platform_pro.gcdp_dwd_expend_sub_bqitem ADD brand varchar(500) NULL COMMENT '品牌';
ALTER TABLE db_cost_data_platform_pro.gcdp_dwd_expend_sub_bqitem ADD data_provider varchar(500) NULL COMMENT '数据录入者';


