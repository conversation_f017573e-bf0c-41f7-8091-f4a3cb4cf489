use `db_cost_data_platform_pro`;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_ods_contract_project` (
  `id` bigint NOT NULL DEFAULT '0' COMMENT '唯一标识',
  `original_id` varchar(50) NOT NULL COMMENT '来源系统id,如来源归档库对应归档库id',
  `original_identity` varchar(50) DEFAULT NULL COMMENT '展开消息的唯一标识id',
  `product_source` varchar(50) DEFAULT NULL COMMENT '产品来源标识',
  `name` varchar(255) DEFAULT NULL COMMENT '合同工程名称',
  `project_id` varchar(50) DEFAULT NULL COMMENT '项目户口薄中的ID',
  `project_code` varchar(255) DEFAULT NULL COMMENT '项目户口薄中的编码',
  `project_name` varchar(255) DEFAULT NULL COMMENT '项目户口薄中的名称',
  `enterprise_id` varchar(50) DEFAULT NULL,
  `deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1=业务库未删除\r\n2、业务库已删除，中台数据未删除\r\n3、中台数据已删除',
  `data_source` tinyint DEFAULT '2' COMMENT '数据来源：1归档库',
  `tax_method` smallint DEFAULT NULL COMMENT '1：增值税模式（一般计税法）；2：营业税模式\r\n5=简易计税法',
  `stage` smallint DEFAULT NULL COMMENT '业务阶段1=可研版2=定位版3=方案版4=施工图版5=结算版',
  `phase` varchar(100) DEFAULT NULL COMMENT '造价类型',
  `province_id` varchar(32) DEFAULT NULL COMMENT '省ID',
  `province_name` varchar(50) DEFAULT NULL COMMENT '省名称',
  `city_id` varchar(32) DEFAULT NULL COMMENT '市ID',
  `city_name` varchar(50) DEFAULT NULL COMMENT '市名称',
  `district_id` varchar(32) DEFAULT NULL COMMENT '区ID',
  `district_name` varchar(50) DEFAULT NULL COMMENT '区名称',
  `product_positioning` varchar(50) DEFAULT NULL COMMENT '产品定位',
  `project_category_code` varchar(20) DEFAULT NULL COMMENT '工程分类编码',
  `project_category_name` varchar(50) DEFAULT NULL COMMENT '工程分类名称（业态）',
  `extend_data_json` text,
  `validity_state` tinyint(1) DEFAULT '1' COMMENT '0=无效；1=有效',
  `archive_date` datetime NOT NULL COMMENT '归档时间',
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tbl_update_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `user_id` varchar(32) DEFAULT NULL,
  `user_name` varchar(255) DEFAULT NULL,
  `organization_id` varchar(32) DEFAULT NULL,
  `organization_name` varchar(255) DEFAULT NULL COMMENT '组织名称,非必须字段,作业中没有不解析',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_data_source_mongodb_id` (`data_source`,`original_id`),
  UNIQUE KEY `unique_mongodb_id_product_source` (`original_id`,`product_source`),
  KEY `product_source` (`product_source`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8  COMMENT='归档文件已解析工程列表';
