USE `db_cost_data_platform_pro`;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_bqitem_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pid` bigint DEFAULT NULL COMMENT '父id',
  `name` varchar(50) DEFAULT NULL COMMENT '名字',
  `attribute` varchar(50) DEFAULT '1' COMMENT '名称',
  `type` varchar(2) DEFAULT NULL COMMENT '标准来源1，其他待定',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `level` bigint DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='分包价格库分类字典表';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem ADD category_id BIGINT NULL COMMENT '分包清单分类id';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail ADD category_id BIGINT NULL COMMENT '分包清单分类id';
