USE `db_cost_data_platform_pro`;

alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem_lmmdetail add index bqItemIndex(bqitem_id);
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item_lmmdetail add index normItemIndex(norm_item_id);
alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item add index bqItemIndex(bqtem_id);

alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem_cost_detail  add index bqItemIndex(bqitem_id);

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project MODIFY COLUMN contract_total decimal(20, 5) DEFAULT NULL COMMENT '合同金额';

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project`
CHANGE `total` `total` DECIMAL(20,5) NULL COMMENT '总造价(不含税)',
ADD COLUMN `total_include_tax` DECIMAL(20,5) NULL COMMENT '总造价(含税)' AFTER `total`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_project_note`
CHANGE `total` `total` DECIMAL(20,5) NULL COMMENT '总造价(不含税)',
ADD COLUMN `total_include_tax` DECIMAL(20,5) NULL COMMENT '总造价(含税)' AFTER `total`,
CHANGE `contract_project_total` `contract_project_total` DECIMAL(20,5) NULL COMMENT '所属合同工程的总造价(不含税)',
ADD COLUMN `contract_project_total_include_tax` DECIMAL(20,5) NULL COMMENT '所属合同工程的总造价(含税)' AFTER `contract_project_total`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dws_build_standard_index`
ADD COLUMN `df_index_value_sum` DECIMAL(20,5) NULL COMMENT '单方造价之和(不含税)' AFTER `standard_description`,
CHANGE `df_index_value_tax_sum` `df_index_value_tax_sum` DECIMAL(20,5) NULL COMMENT '单方造价之和(含税)',
ADD COLUMN `df_index_value_max` DECIMAL(20,5) NULL COMMENT '单方造价最大值(不含税)' AFTER `df_index_value_tax_count`,
CHANGE `df_index_value_tax_max` `df_index_value_tax_max` DECIMAL(20,5) NULL COMMENT '单方造价最大值(含税)',
ADD COLUMN `df_index_value_min` DECIMAL(20,5) NULL COMMENT '单方造价最小值(不含税)' AFTER `df_index_value_tax_max`,
CHANGE `df_index_value_tax_min` `df_index_value_tax_min` DECIMAL(20,5) NULL COMMENT '单方造价最小值(含税)',
ADD COLUMN `df_index_value_count` INT(10) NULL COMMENT '单方造价数量(不含税)' AFTER `df_index_value_tax_sum`,
CHANGE `df_index_value_tax_count` `df_index_value_tax_count` DECIMAL(20,5) NULL COMMENT '单方造价数量(含税)',
ADD COLUMN `swl_index_value_sum` DECIMAL(20,5) NULL COMMENT '实物量指标结果之和(不含税)' AFTER `df_index_value_tax_min`,
CHANGE `swl_index_value_tax_sum` `swl_index_value_tax_sum` DECIMAL(20,5) NULL COMMENT '实物量指标结果之和(含税)',
ADD COLUMN `swl_index_value_max` DECIMAL(20,5) NULL COMMENT '实物量单方指标结果最大值(不含税)' AFTER `swl_index_value_tax_count`,
CHANGE `swl_index_value_tax_max` `swl_index_value_tax_max` DECIMAL(20,5) NULL COMMENT '实物量单方指标结果最大值(含税)',
ADD COLUMN `swl_index_value_min` DECIMAL(20,5) NULL COMMENT '实物量指标结果最小值(不含税)' AFTER `swl_index_value_tax_max`,
CHANGE `swl_index_value_tax_min` `swl_index_value_tax_min` DECIMAL(20,5) NULL COMMENT '实物量指标结果最小值(含税)',
ADD COLUMN `swl_index_value_count` INT(10) NULL COMMENT '实物量单方数量(不含税)' AFTER `swl_index_value_tax_sum`,
CHANGE `swl_index_value_tax_count` `swl_index_value_tax_count` INT(10) NULL COMMENT '实物量单方数量(含税)';

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_lyhf`
CHANGE `amount` `amount` DECIMAL(20,5) NULL COMMENT '总造价(不含税)',
ADD COLUMN `amount_include_tax` DECIMAL(20,5) NULL COMMENT '总造价(含税)' AFTER `amount`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_norm_item`
CHANGE `rate` `rate` DECIMAL(20,5) NULL COMMENT '综合单价(不含税)',
ADD COLUMN `rate_include_tax` DECIMAL(20,5) NULL COMMENT '综合单价(含税)' AFTER `rate`,
CHANGE `amount` `amount` DECIMAL(20,5) NULL COMMENT '综合合价(不含税)',
ADD COLUMN `amount_include_tax` DECIMAL(20,5) NULL COMMENT '综合合价(含税)' AFTER `amount`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_cost`
CHANGE `amount` `amount` DECIMAL(20,5) NULL COMMENT '科目合价(不含税)',
ADD COLUMN `amount_include_tax` DECIMAL(20,5) NULL COMMENT '科目合价(含税)' AFTER `amount`;

CREATE INDEX idx_original_id USING BTREE ON `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_project (original_id);

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem ADD contract_type varchar(100)  NULL COMMENT '分包模式';

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem ADD contract_type_code smallint(4) NULL COMMENT '合同类型编码1 劳务分包 2 专业分包';

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail ADD contract_type varchar(100)  NULL COMMENT '分包模式';

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail ADD contract_type_code smallint(4) NULL COMMENT '合同类型编码1 劳务分包 2 专业分包';



ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index
    ADD extend_data_json VARCHAR(2048) NULL COMMENT '扩展数据JSON字段';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index
    ADD amount_include_tax_no_device_fee DECIMAL(20, 5)   NULL COMMENT '合价不含设备费（含税）';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index
    ADD tax_ratio DECIMAL(10, 5)  NULL COMMENT '税率';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    ADD amount_value_include_tax_no_device_fee DECIMAL(20, 5)  NULL COMMENT '合价不含设备费（含税）';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_cost_summary_index_data
    ADD index_value_include_tax_no_device_fee DECIMAL(20, 5)  NULL COMMENT '指标值不含设备费（含税）';

