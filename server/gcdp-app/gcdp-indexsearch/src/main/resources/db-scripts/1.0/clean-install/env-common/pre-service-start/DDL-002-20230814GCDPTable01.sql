USE `db_cost_data_platform_pro`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dim_tb_area`
  MODIFY COLUMN `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`,
  DROP INDEX `i_20220426143812_1_549049170`,
  DROP INDEX `i_20220426143812_3_284431460`,
  ADD UNIQUE KEY `idx_area_code` (`area_code`,`displayable`),
  ADD UNIQUE KEY `i_20220426141946_3_298144492` (`areaid`);


DROP TABLE IF EXISTS `db_cost_data_platform_pro`.`gcdp_dim_tb_area_rerun`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dim_tb_commonprojcategory_standards`
  ADD COLUMN `qy_code_old` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL AFTER `enterprise_id`,
  ADD COLUMN `qy_flag` tinyint(1) DEFAULT NULL AFTER `qy_code_old`,
  MODIFY COLUMN `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `create_time`,
  DROP INDEX `i_20220426143812_7_878784782`,
  DROP INDEX `i_20220426143812_8_109625350`,
  DROP INDEX `i_20220426143812_1_390552849`,
  DROP INDEX `i_20220426143812_2_627468894`,
  DROP INDEX `i_20220426143812_3_996453350`,
  DROP INDEX `i_20220426143812_4_313260422`,
  DROP INDEX `i_20220426143812_6_877443441`,
  ADD KEY `i_20220426141946_8_188877225` (`commonprojcategoryid`),
  ADD KEY `i_20220426141946_1_855324903` (`categoryname`),
  ADD KEY `i_20220426141946_2_303851395` (`qy_code`),
  ADD KEY `i_20220426141946_3_296226287` (`categorycode1`),
  ADD KEY `i_20220426141946_4_684318167` (`categorycode3`),
  ADD KEY `i_20220426141946_6_188575910` (`categorycode2`),
  ADD KEY `i_20220426141946_7_130966180` (`categorycode4`);

DROP TABLE IF EXISTS `db_cost_data_platform_pro`.`gcdp_dim_tb_commonprojcategory_standards_rerun`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dim_tb_commonprojcategory_standards_used`
  ADD COLUMN `qy_code_old` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL AFTER `tenantglobalId`,
  ADD COLUMN `qy_flag` tinyint(1) DEFAULT NULL AFTER `qy_code_old`;

DROP TABLE IF EXISTS `db_cost_data_platform_pro`.`gcdp_dim_tb_commonprojcategory_standards_used_rerun`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_standards_expression`
  ADD COLUMN `qy_code_old` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL AFTER `enterprise_id`,
  ADD COLUMN `qy_flag` tinyint(1) DEFAULT NULL AFTER `qy_code_old`,
  MODIFY COLUMN `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `create_time`,
  MODIFY COLUMN `expression_create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `expression_create_global_id`,
  DROP INDEX `i_20220426143814_1_595267140`,
  DROP INDEX `i_20220426143814_3_733342947`,
  ADD KEY `i_20220426141948_1_614378444` (`name`),
  ADD KEY `i_20220426141948_3_980359215` (`qy_code`);

DROP TABLE IF EXISTS `db_cost_data_platform_pro`.`gcdp_dim_zb_standards_expression_rerun`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_standards_project_info`
  ADD COLUMN `qy_code_old` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL AFTER `modify_time_sjzt`,
  ADD COLUMN `qy_flag` tinyint(1) DEFAULT NULL AFTER `qy_code_old`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_standards_trade`
  ADD COLUMN `qy_code_old` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL AFTER `modify_time_sjzt`,
  ADD COLUMN `qy_flag` tinyint(1) DEFAULT NULL AFTER `qy_code_old`;


DROP TABLE IF EXISTS `db_cost_data_platform_pro`.`gcdp_dim_zb_standards_trade_rerun`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_unit`
  ADD COLUMN `qy_code_old` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL AFTER `modify_time_sjzt`,
  ADD COLUMN `qy_flag` tinyint(1) DEFAULT NULL AFTER `qy_code_old`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project`
  ADD COLUMN `original_unique_id` varchar(50) GENERATED ALWAYS AS (
if(
(`product_source` = 'gbqd'),
md5(
concat(
`original_id`,
'_',
`enterprise_id`,
'_',
`product_source`,
'_',
`original_project_id`
)
),
`original_identity`
)
) STORED AFTER `original_identity`,
  ADD COLUMN `extend_data_json` json DEFAULT NULL AFTER `original_project_id`,
  MODIFY COLUMN `cost_ratios_way` smallint(3) NOT NULL DEFAULT '0' AFTER `norm_item_calc_cost`,
  ADD KEY `original_unique_id` (`original_unique_id`);

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_attr`
  MODIFY COLUMN `pk_code` varchar(512) DEFAULT NULL AFTER `code`;

CREATE TABLE IF NOT EXISTS `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_build_standard_detail` (
  `id` bigint(20) NOT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `build_standard_id` bigint(20) DEFAULT NULL,
  `standard_description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ord` int(3) DEFAULT '1',
  `select_list` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` int(3) DEFAULT NULL,
  `reference_price` decimal(20,5) DEFAULT NULL,
  `value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_build_standard_id` (`build_standard_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_plan`
  MODIFY COLUMN `formula` varchar(3072) DEFAULT NULL AFTER `unit`,
  MODIFY COLUMN `pk_code` varchar(512) DEFAULT NULL AFTER `value_type`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_expend_sub_project`
  ADD COLUMN `recycle_flag` tinyint(1) DEFAULT '0' AFTER `contract_telephone`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_index`
  ADD COLUMN `extend_data_json` json DEFAULT NULL AFTER `calculate_value`;

CREATE TABLE IF NOT EXISTS `db_cost_data_platform_pro`.`gcdp_dwd_index_additional` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `index_id` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `index_data_json` longtext CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`),
  KEY `idx_bidnode_id` (`bidnode_id`),
  KEY `idx_index_id` (`index_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE IF NOT EXISTS `db_cost_data_platform_pro`.`gcdp_dwd_index_build_standards_relationship` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `index_id` bigint(20) DEFAULT NULL,
  `build_standard_id` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `bid_node_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_bid_node_id` (`index_id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_index` (`bid_node_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_index_data`
  ADD KEY `idx_index_id` (`index_id`);

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_item_index_template`
  MODIFY COLUMN `extend_data_json` json DEFAULT NULL AFTER `tax_flag`,
  ADD KEY `bidnode_id_index` (`bidnode_id`);

CREATE TABLE IF NOT EXISTS `db_cost_data_platform_pro`.`gcdp_dwd_project_auth_ent_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_date` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `i_20230627141747_2_639345911` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_project_info`
  ADD COLUMN `sync_source` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL AFTER `is_add_org_member`,
  ADD COLUMN `sync_orignal_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL AFTER `sync_source`,
  ADD COLUMN `recycle_flag` tinyint(1) DEFAULT '0' AFTER `sync_orignal_id`,
  ADD COLUMN `sync_orignal_org_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL AFTER `recycle_flag`,
  ADD COLUMN `recycle_date` datetime NULL DEFAULT NULL AFTER `sync_orignal_org_id`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_resource`
  MODIFY COLUMN `spec` varchar(2048) DEFAULT NULL AFTER `price_source`;

CREATE TABLE IF NOT EXISTS `db_cost_data_platform_pro`.`gcdp_dws_index_cost_build_standards_relationship` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `index_project_note_id` bigint(20) DEFAULT NULL,
  `index_cost_id` bigint(20) DEFAULT NULL,
  `build_standard_id` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `bid_node_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_bid_node_id` (`index_cost_id`),
  KEY `idx_build_standard_id` (`build_standard_id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_index` (`bid_node_id`),
  KEY `idx_index_project_note_id` (`index_project_note_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_project_note`
  ADD COLUMN `original_unique_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL AFTER `original_identity`;

CREATE TABLE IF NOT EXISTS `db_cost_data_platform_pro`.`gcdp_dws_index_usage_build_standards_relationship` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `index_project_note_id` bigint(20) DEFAULT NULL,
  `index_usage_id` bigint(20) DEFAULT NULL,
  `build_standard_id` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `bid_node_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_bid_node_id` (`index_usage_id`),
  KEY `idx_build_standard_id` (`build_standard_id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_index` (`bid_node_id`),
  KEY `idx_index_project_note_id` (`index_project_note_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dws_market_project_cluster`
  MODIFY COLUMN `building_name` varchar(2000) DEFAULT NULL AFTER `category_name`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dws_sub_bqitem_index`
  MODIFY COLUMN `code` varchar(1000) DEFAULT NULL AFTER `id`,
  MODIFY COLUMN `name` varchar(1000) DEFAULT NULL AFTER `code`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dws_sub_bqitem_index_detail`
  ADD COLUMN `query_count` bigint(20) NOT NULL DEFAULT '0' AFTER `save_at_time`,
  ADD COLUMN `recycle_flag` tinyint(1) DEFAULT '0' AFTER `query_count`,
  ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT '0' AFTER `recycle_flag`,
  MODIFY COLUMN `code` varchar(500) DEFAULT NULL AFTER `project_category_name`;
