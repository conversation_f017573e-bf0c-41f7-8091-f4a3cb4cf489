use `db_cost_data_platform_pro`;
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_bqitem ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_contract_project ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_contract_project_bidnode ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_contract_project_lyhf ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_contract_project_lyhf_jzx ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_contract_project_multi_bid ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_project ADD one_id varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_index ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_index_data_ld ADD one_id varchar(50) NULL COMMENT '楼栋指标数据表';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_non_construction_installation_index ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_norm_item ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_resource ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_sub_project ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_cost ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_economics ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_main_res ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_project_note ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_usage ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project ADD one_id varchar(50) NULL COMMENT '业务实体唯一标识';

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index ADD dwd_id bigint NULL COMMENT 'dws数据对应dwd层的数据id';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_cost ADD dwd_id bigint NULL COMMENT 'dws数据对应dwd层的数据id';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_economics ADD dwd_id bigint NULL COMMENT 'dws数据对应dwd层的数据id';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_main_res ADD dwd_id bigint NULL COMMENT 'dws数据对应dwd层的数据id';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_usage ADD dwd_id bigint NULL COMMENT 'dws数据对应dwd层的数据id';

CREATE INDEX contract_project_id USING BTREE ON `db_cost_data_platform_pro`.gcdp_dws_index_economics (contract_project_id);
CREATE INDEX contract_project_id USING BTREE ON `db_cost_data_platform_pro`.gcdp_dws_index_main_res (contract_project_id);


