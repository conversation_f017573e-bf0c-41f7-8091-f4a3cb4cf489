use `db_cost_data_platform_pro`;
#归档设置
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_resource
ADD general_price_source varchar(20) NULL COMMENT '成本测算归档设置数据范围筛选',
ADD cost_type_id varchar(10) NULL COMMENT '成本测算归档设置分类筛选',
add original_resource_category_id bigint null comment '原始分类id' after resource_category_id,
add measurement_rule varchar(512) null comment '计量规则';

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem ADD general_price_source varchar(20) NULL COMMENT '成本测算归档设置数据范围筛选',
ADD cost_type_id varchar(10) NULL COMMENT '成本测算归档设置分类筛选';

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_project ADD extend_data_json json NULL;

#分包价格库多附件上传
CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_subbqitem_price_file` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `price_id` varchar(90) NOT NULL COMMENT '外键清单ID',
  `file_name` varchar(128) DEFAULT NULL COMMENT '文件名称',
  `file_path` varchar(1024) NOT NULL COMMENT '文件路径',
  `suffix` varchar(32) DEFAULT NULL COMMENT '后缀名',
  `enterprise_id` varchar(32) DEFAULT NULL COMMENT '企业ID',
  `file_id` varchar(32) DEFAULT NULL COMMENT 'gccs文件id',
  `flag` int DEFAULT '0' COMMENT '预留字段',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `index_price_id` (`price_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3
