use `db_cost_data_platform_pro`;
ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_index_makeup`
    CHANGE `is_calc_quantity` `is_calc_quantity` TINYINT(1) NULL COMMENT '工程量是否计入 1 是 0 否',
    ADD COLUMN `is_calc_amount` TINYINT(1) NULL COMMENT '综合合价是否计入 1 是 0 否' AFTER `is_calc_quantity`,
    ADD quantity DECIMAL(25, 6) NULL COMMENT '数量' AFTER is_calc_amount,
    ADD amount DECIMAL(25,6) NULL COMMENT '合价' AFTER quantity,
    ADD amount_include_tax DECIMAL(25,6) NULL COMMENT '含税合价' AFTER amount;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_makeup`
    ADD COLUMN `is_calc_quantity` TINYINT(1) NULL COMMENT '工程量是否计入 1 是 0 否' AFTER `tbl_create_date`,
    ADD COLUMN `is_calc_amount` TINYINT(1) NULL COMMENT '综合合价是否计入 1 是 0 否' AFTER `is_calc_quantity`,
    ADD COLUMN `factor` DECIMAL(25,6) NULL COMMENT '转换系数' AFTER `is_calc_amount`,
    ADD amount DECIMAL(25,6) NULL COMMENT '合价' AFTER ld_quantity,
    ADD amount_include_tax DECIMAL(25,6) NULL COMMENT '含税合价' AFTER amount;

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem MODIFY name VARCHAR(1024) NULL COMMENT '名称';

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_bqitem`
    CHANGE `item_type` `item_type` SMALLINT NULL COMMENT '类型:1=分布;2=清单;3=计算公式组价;4=定额组价;5=实物量组价;6=清单组价;7=子措施组价;8=间接费;9=税金;';

alter table `db_cost_data_platform_pro`.gcdp_dws_index_makeup
    modify type tinyint not null comment '类型 1=清单 2=措施 3=材料 4=定额 5=费用 6=耗量 7=其他 8=分包清单 9=机械/大型机械 10=周转材';

alter table `db_cost_data_platform_pro`.gcdp_dwd_index_makeup
    modify makeup_type smallint null comment '1=分部分项；2=措施项目；3=人材机 4=定额 5=费用 6=耗量 7=其他清单 8=分包 9=机械/大型机械 10=周转材料';

REPLACE INTO `db_cost_data_platform_pro`.gcdp_func_dws_biz_config (id, product_source, biz_code, biz_name, biz_type, state)
VALUES (603, 'sgcbcs2', 'biz-ja', '建安指标', 'sgcbcs2', 1);
