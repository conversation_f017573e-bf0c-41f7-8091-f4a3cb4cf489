use `db_cost_data_platform_pro`;
# ------------------------分包表添加字段---------------------
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem modify owner_default_conditions text;

alter table `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail modify owner_default_conditions text;

# ------------------------资源合同表添加字段---------------------
alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    add compile_org_id bigint null comment '编制机构组织id';

alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    add compile_org_name varchar(128) null comment '编制机构名称';

alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    add settlement_begin_date datetime null comment '结算开始时间，结算阶段才有';

alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    add settlement_end_date datetime null comment '结算结算时间，结算阶段才有';

alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    add owner_default_conditions text null comment '己方违约规则';

alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    add extend_data_json json null comment '扩展字段';

alter table `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project
    add tax_amount decimal(25, 6) null comment '税额' after credit_tax_amount;



# ------------------------材料明细表添加字段（已上线）--------------------
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    add original_resource_category_id bigint null comment '原始分类id' after resource_category_id;

alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    add measurement_rule varchar(512) null comment '计量规则';

# ------------------------新建pmdata同步进度表---------------------
create table `db_cost_data_platform_pro`.gcdp_dim_resource_sync_progress
(
    id             int auto_increment comment 'id'
        primary key,
    product_source varchar(255) null comment '统一标识业务系统标识',
    last_sync_date datetime     null comment '上一次同步时间'
)
    comment '资源数据同步进度表';


# ------------------------新建周转材表---------------------
create table `db_cost_data_platform_pro`.gcdp_dwd_revolving_resource
(
    id                            bigint    default 0                 not null comment 'id'
        primary key,
    project_link_id               bigint                              null comment 'project_source=1 关联 DWD_contract_project主键 project_source=2 关联gcdp_dwd_sub_contractor_project主键',
    product_source                varchar(255)                        null comment '统一标识业务系统标识',
    original_id                   bigint                              null comment '原信息ID',
    code                          varchar(255)                        null comment '编码',
    name                          varchar(2000)                       null comment '名称',
    unit                          varchar(128)                        null comment '单位',
    lease_rate                    decimal(25, 6)                      null comment '不含税租赁单价',
    lease_rate_include_tax        decimal(25, 6)                      null comment '含税租赁单价',
    lease_date                    int                                 null comment '租期',
    quantity                      decimal(25, 6)                      null comment '工程量',
    lease_quantity                decimal(25, 6)                      null comment '租赁数量',
    price_source                  varchar(255)                        null comment '价格来源',
    spec                          varchar(2048)                       null,
    texture                       varchar(128)                        null comment '材质',
    brand                         varchar(255)                        null comment '品牌',
    originate                     varchar(255)                        null comment '产地',
    supplier                      varchar(255)                        null comment '供应商',
    tax_ratio                     decimal(25, 6)                      null comment '税率',
    creator_gid                   varchar(50)                         null comment '创建人ID',
    creator_account               varchar(50)                         null comment '创建账户',
    creator_time                  datetime                            null comment '创建时间 yyyy-MM-dd HH:mm:ss',
    tbl_create_date               timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment 'yyyy-MM-dd HH:mm:ss',
    bidnode_id                    bigint                              null comment 'roject_bidNode主键',
    enterprise_id                 varchar(32)                         null comment '企业ID',
    lease_amount_include_tax      decimal(25, 6)                      null comment '含税合价',
    lease_amount                  decimal(25, 6)                      null comment '不含税合计',
    tax_amount                    decimal(25, 6)                      null comment '税额',
    spec_description              varchar(255)                        null comment '型号说明',
    manufacturer                  varchar(100)                        null comment '生产厂商',
    remark                        text                                null,
    extend_data_json              json                                null comment '扩展json字段',
    contract_detail_original_id   bigint                              null comment '合同价-源id',
    resource_category_id          bigint                              null comment '分类id',
    original_resource_category_id bigint                              null comment '原始分类id',
    original_uuid                 varchar(50)                         null comment '材料唯一标识',
    price_date                    datetime                            null comment '报价时间',
    cost_rule                     tinyint                             null comment '计费方式(1:日租, 2:月租, 3:工程量租, 4:设备安拆, 5:月租（整月）, 6:月租（不足月）, 7:停租扣减, 8:出场扣减)',
    cost_unit                     varchar(128)                        null comment '计费单位(天、月、工程量...)',
    measurement_rule              varchar(512)                        null comment '计量规则'
)
    comment '周转材表';



# ------------------------新建大型机械表---------------------
create table `db_cost_data_platform_pro`.gcdp_dwd_large_machine
(
    id                           bigint    default 0                 not null comment 'id'
        primary key,
    project_link_id              bigint                              null comment 'project_source=1 关联 DWD_contract_project主键 project_source=2 关联gcdp_dwd_sub_contractor_project主键',
    product_source               varchar(255)                        null comment '统一标识业务系统标识',
    original_id                  bigint                              null comment '原信息ID',
    code                         varchar(255)                        null comment '编码',
    name                         varchar(2000)                       null comment '名称',
    unit                         varchar(128)                        null comment '单位',
    lease_rate                   decimal(25, 6)                      null comment '不含税租赁单价',
    lease_rate_include_tax       decimal(25, 6)                      null comment '含税租赁单价',
    lease_date                   int                                 null comment '租期',
    quantity                     decimal(25, 6)                      null comment '工程量',
    lease_quantity               decimal(25, 6)                      null comment '租赁数量',
    price_source                 varchar(255)                        null comment '价格来源',
    spec                         varchar(2048)                       null,
    brand                        varchar(255)                        null comment '品牌',
    originate                    varchar(255)                        null comment '产地',
    supplier                     varchar(255)                        null comment '供应商',
    tax_ratio                    decimal(25, 6)                      null comment '税率',
    creator_gid                  varchar(50)                         null comment '创建人ID',
    creator_account              varchar(50)                         null comment '创建账户',
    creator_time                 datetime                            null comment '创建时间 yyyy-MM-dd HH:mm:ss',
    tbl_create_date              timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment 'yyyy-MM-dd HH:mm:ss',
    bidnode_id                   bigint                              null comment 'roject_bidNode主键',
    enterprise_id                varchar(32)                         null comment '企业ID',
    lease_amount_include_tax     decimal(25, 6)                      null comment '含税合价',
    lease_amount                 decimal(25, 6)                      null comment '不含税合计',
    tax_amount                   decimal(25, 6)                      null comment '税额',
    spec_description             varchar(255)                        null comment '型号说明',
    manufacturer                 varchar(100)                        null comment '生产厂商',
    remark                       text                                null,
    extend_data_json             json                                null comment '扩展json字段',
    contract_detail_original_id  bigint                              null comment '合同价-源id',
    machine_category_id          bigint                              null comment '分类id',
    original_machine_category_id bigint                              null comment '原始分类id',
    original_uuid                varchar(50)                         null comment '唯一标识',
    price_date                   datetime                            null comment '报价时间',
    cost_rule                    tinyint                             null comment '计费方式(1:日租, 2:月租, 3:工程量租, 4:设备安拆, 5:月租（整月）, 6:月租（不足月）, 7:停租扣减, 8:出场扣减)',
    cost_unit                    varchar(128)                        null comment '计费单位(天、月、工程量...)',
    measurement_rule             varchar(512)                        null comment '计量规则',
    labor_quantity               int                                 null comment '配备工人数量（人）'
)
    comment '大型机械表';


# ------------------------新建表添加索引---------------------
create index product_source_idx
    on `db_cost_data_platform_pro`.gcdp_dim_resource_sync_progress (product_source);

create index enterprise_id_idx
    on `db_cost_data_platform_pro`.gcdp_dwd_revolving_resource (enterprise_id);

create index project_link_id_product_source_idx
    on `db_cost_data_platform_pro`.gcdp_dwd_revolving_resource (project_link_id, product_source);


create index enterprise_id_idx
    on `db_cost_data_platform_pro`.gcdp_dwd_large_machine (enterprise_id);

create index project_link_id_product_source_idx
    on `db_cost_data_platform_pro`.gcdp_dwd_large_machine (project_link_id, product_source);

# ------------------------分包dwd表添加索引---------------------
create index original_id_idx
    on `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem (original_id);


# ------------------------字典sql修改---------------------
alter table `db_cost_data_platform_pro`.gcdp_dwd_sub_bqitem_category
    add column platform_original_id varchar(32) null comment '平台源id',
    add column platform_original_pid varchar(32) null comment '平台源父id';

alter table `db_cost_data_platform_pro`.gcdp_dim_resource_category_dict
    add column material_type  tinyint null comment '材料类型 1=普通材料2=周转材，当type=1时有值' after type;

alter table `db_cost_data_platform_pro`.gcdp_dim_resource_category_dict
    add column pid  bigint null  after id;

alter table `db_cost_data_platform_pro`.gcdp_dim_resource_category_dict
    modify remark text null comment '备注';