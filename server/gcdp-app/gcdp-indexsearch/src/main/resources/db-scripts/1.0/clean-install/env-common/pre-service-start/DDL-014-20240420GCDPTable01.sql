use `db_cost_data_platform_pro`;
CREATE TABLE `db_cost_data_platform_pro`.`gcdp_ads_enterprise_status` (
                                              `id` bigint NOT NULL AUTO_INCREMENT,
                                              `enterprise_id` varchar(32) NOT NULL,
                                              `status` tinyint DEFAULT NULL COMMENT '1：未统计 2. 统计完成\r\n3. 统计错误',
                                              `module` varchar(20) NOT NULL,
                                              `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `gcdp_ads_enterprise__status_enterprise_id_IDX` (`enterprise_id`,`module`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_ads_screen_index_intermediate_data` (
                                                           `id` bigint NOT NULL AUTO_INCREMENT,
                                                           `category_name_path` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '业态全路径',
                                                           `phase` varchar(20) DEFAULT NULL COMMENT '造价阶段',
                                                           `project_code` varchar(32) DEFAULT NULL COMMENT '项目编码',
                                                           `index_value` decimal(30,6) DEFAULT NULL COMMENT '建面单方',
                                                           `index_value_include_tax` decimal(30,6) DEFAULT NULL COMMENT '含税建面单方',
                                                           `enterprise_id` varchar(32) DEFAULT NULL COMMENT '企业id',
                                                           `province_id` varchar(6) DEFAULT NULL,
                                                           `province_name` varchar(50) DEFAULT NULL,
                                                           `city_id` varchar(6) DEFAULT NULL,
                                                           `city_name` varchar(50) DEFAULT NULL,
                                                           `district_id` varchar(6) DEFAULT NULL,
                                                           `district_name` varchar(50) DEFAULT NULL,
                                                           `global_id` varchar(32) DEFAULT NULL,
                                                           `item_count` int DEFAULT NULL,
                                                           `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;