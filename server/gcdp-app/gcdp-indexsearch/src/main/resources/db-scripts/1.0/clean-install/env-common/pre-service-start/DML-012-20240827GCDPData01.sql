use `db_cost_data_platform_pro`;
REPLACE INTO `db_cost_data_platform_pro`.gcdp_func_dws_biz_config (id, product_source, biz_code, biz_name, biz_type, state) VALUES (310, 'zbgx', 'biz-ja', '建安指标', 'gb', 1);

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_index
    ADD COLUMN is_share_cost TINYINT(1)  DEFAULT NULL COMMENT '科目是否分摊 0 未分摊 1 分摊' AFTER item_cost_type;

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index
    ADD COLUMN is_share_cost TINYINT(1)  DEFAULT NULL COMMENT '科目是否分摊 0 未分摊 1 分摊' AFTER item_cost_type;

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_cost
    ADD COLUMN is_share_cost TINYINT(1)  DEFAULT NULL COMMENT '科目是否分摊 0 未分摊 1 分摊' AFTER item_cost_type;

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_usage
    ADD COLUMN is_share_cost TINYINT(1)  DEFAULT NULL COMMENT '科目是否分摊 0 未分摊 1 分摊' AFTER item_cost_type;
