use `db_cost_data_platform_pro`;
CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_main_res_makeup` (
                                                  `id` bigint NOT NULL COMMENT 'id',
                                                  `index_project_note_id` bigint NOT NULL COMMENT 'dws单体表id',
                                                  `dws_index_id` bigint NOT NULL COMMENT '科目id',
                                                  `dwd_id` bigint DEFAULT NULL COMMENT '对应typedwd层id',
                                                  `contract_project_id` bigint NOT NULL COMMENT '合约工程id',
                                                  `dwd_bidnode_id` bigint NOT NULL COMMENT '单体、业态id',
                                                  `enterprise_id` varchar(32) NOT NULL COMMENT '企业id',
                                                  `type` tinyint NOT NULL COMMENT '类型 1=清单 2=措施 3=材料 4=定额 5=费用 6=耗量 7=其他',
                                                  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  `is_calc_quantity` tinyint(1) DEFAULT NULL COMMENT '是否计量 1 是 0 否',
                                                  `is_calc_amount` tinyint(1) DEFAULT NULL COMMENT '是否计价 1是 0否',
                                                  `factor` decimal(25, 6) DEFAULT NULL COMMENT '转换系数',
                                                  PRIMARY KEY (`id`),
                                                  KEY `contract_project_id` (`contract_project_id`),
                                                  KEY `dws_index_id` (`dws_index_id`),
                                                  KEY `enterprise_id` (`enterprise_id`, `contract_project_id`),
                                                  KEY `note_id` (`index_project_note_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb3 COMMENT = 'dws主要工料指标组成表';

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_index_makeup` ADD INDEX `idx_enterprise_id` (`enterprise_id`);

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_new_archive_data
ADD COLUMN `phase` varchar(100) null comment '造价类型';

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_build_standards_relationship
MODIFY COLUMN build_standard_id bigint null comment '建造标准id';

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_contract_project_build_standard
    ADD COLUMN ord INT DEFAULT 1 NULL COMMENT '顺序';

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_contract_project
    ADD COLUMN is_newest_stage TINYINT(1) DEFAULT 1 COMMENT '是否是最新造价工程，0：不是，1：是' AFTER stage;

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_contract_project_build_standard_detail
    ADD COLUMN bid_node_id BIGINT NOT NULL COMMENT '工程结构id' AFTER build_standard_id;
;
