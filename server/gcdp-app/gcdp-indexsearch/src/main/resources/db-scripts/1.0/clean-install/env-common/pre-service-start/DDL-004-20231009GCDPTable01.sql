USE `db_cost_data_platform_pro`;

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_project_info add column src_project_id varchar(50) comment '虚拟项目原始id';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_project_info MODIFY COLUMN project_scale_unit varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目规模-单位';

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project` CHANGE `main_uuid` `main_uuid` VARCHAR(50) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '文件uuid';

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_bidnode` CHANGE `main_uuid` `main_uuid` VARCHAR(50) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '文件uuid';
