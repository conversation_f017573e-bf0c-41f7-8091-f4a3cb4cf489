use `db_cost_data_platform_pro`;
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    add extend_data_json json null comment '扩展字段';

alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_bidnode
    add choose_type int null comment '用户选择的类型（0: 合同 1: 楼栋  2:专业）' after node_type,
    add functional_domain varchar(255) null comment '功能区 多个更能区使用，隔开' after choose_type;

alter table `db_cost_data_platform_pro`.gcdp_dws_index_project_note
    add trade_code varchar(255) null comment '专业编码' after project_category_name,
    add trade_name varchar(255) null comment '专业名称' after trade_code,
    add functional_domain varchar(255) null comment '功能区 多个功能区，隔开' after trade_name;

alter table `db_cost_data_platform_pro`.gcdp_dws_index_project_note
    modify type tinyint default 0 not null comment '类别： 0=none 1=专业 2=业态 3=分类 4=楼栋 5=虚拟节点 6=虚拟楼栋';

alter table `db_cost_data_platform_pro`.`gcdp_dws_index_project_note` CHANGE `phase` `phase` VARCHAR(20) CHARSET utf8 COLLATE utf8_general_ci DEFAULT '待复核数据' NOT NULL COMMENT '阶段名';

# 创建特征索引 修复历史数据添加索引,方便数据更新
create index idx_trade_id
    on `db_cost_data_platform_pro`.gcdp_dwd_contract_project_attr (trade_id)
    comment '添加专业id的索引方便数据更新';
