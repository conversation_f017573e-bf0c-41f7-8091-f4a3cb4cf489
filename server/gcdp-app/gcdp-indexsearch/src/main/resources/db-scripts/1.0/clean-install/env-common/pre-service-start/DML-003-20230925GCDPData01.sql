use `db_cost_data_platform_pro`;

-- 删除历史数据
update `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail set is_deleted = 1 where is_deleted = 0 and product_source like '%geps%' and (rate = 0 or rate is null)
and (rate_include_tax = 0 or rate_include_tax is null);

-- 刷新GEPS项目ID到户口簿ID，如果客户有历史数据，ID是GEPS的，但是咱们没刷这个ID到户口簿ID，后面是不是就查不到
UPDATE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_project AS sub
JOIN gcdp_dwd_third_project_info AS third ON sub.third_project_id = third.id
JOIN gcdp_dwd_project_info AS project ON third.third_id = project.sync_orignal_id
SET sub.project_id = project.id
WHERE sub.product_source LIKE '%geps%'
AND sub.enterprise_id = third.enterprise_id
AND third.enterprise_id = project.enterprise_id;

UPDATE `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project AS pur
JOIN gcdp_dwd_third_project_info AS third ON pur.third_project_id = third.id
JOIN gcdp_dwd_project_info AS project ON third.third_id = project.sync_orignal_id
SET pur.project_id = project.id
WHERE pur.product_source LIKE '%geps%'
AND pur.enterprise_id = third.enterprise_id
AND third.enterprise_id = project.enterprise_id;

UPDATE `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail AS detail
JOIN gcdp_dwd_expend_sub_project AS sub ON detail.dwd_sub_project_id = sub.id
SET detail.project_id = sub.project_id
WHERE detail.product_source LIKE '%geps%';
