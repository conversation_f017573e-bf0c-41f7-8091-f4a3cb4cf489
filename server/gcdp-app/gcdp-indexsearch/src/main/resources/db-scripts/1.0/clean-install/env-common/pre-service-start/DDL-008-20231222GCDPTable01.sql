USE `db_cost_data_platform_pro`;
CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_item_component_category_item` (
                                                         `id` varchar(32) NOT NULL COMMENT '主键',
                                                         `pid` varchar(32) DEFAULT NULL COMMENT '父级id',
                                                         `enterprise_id` varchar(32) DEFAULT NULL COMMENT '企业id',
                                                         `category_code` varchar(20) DEFAULT NULL COMMENT '业态编码',
                                                         `category_name` varchar(50) DEFAULT NULL COMMENT '业态名称',
                                                         `status` tinyint DEFAULT NULL COMMENT '基础信息库对应工程分类状态 0 启用未删除  1禁用  2删除',
                                                         `category_ord` int DEFAULT NULL COMMENT '科目顺序',
                                                         `item_name` varchar(255) DEFAULT NULL COMMENT '科目名字',
                                                         `item_name_path` varchar(255) DEFAULT NULL COMMENT '科目全路径',
                                                         `item_level_code` varchar(100) DEFAULT NULL COMMENT '科目层级编码',
                                                         `item_ord` int DEFAULT NULL COMMENT '科目顺序',
                                                         `unit` varchar(50) DEFAULT NULL COMMENT '科目单位',
                                                         `expression_name` varchar(250) DEFAULT NULL COMMENT '计算口径名称',
                                                         `item_hash` varchar(20) DEFAULT NULL COMMENT '科目hash',
                                                         `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'tbl_create_date',
                                                         PRIMARY KEY (`id`),
                                                         KEY `enterprise_id_IDX` (`enterprise_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '项目划分业态科目数据'
