CREATE DATABASE IF NOT EXISTS db_cost_data_platform_pro;
USE `db_cost_data_platform_pro`;

CREATE TABLE `db_cost_data_platform_pro`.`edit_project_progress` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mongodb_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `archive_date` datetime NOT NULL,
  `progress_state` tinyint(1) NOT NULL DEFAULT '0',
  `cover_state` tinyint(1) NOT NULL DEFAULT '0',
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tbl_update_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `validity_state` tinyint(1) NOT NULL DEFAULT '1',
  `product_source` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `deleted` tinyint(3) NOT NULL DEFAULT '1',
  `data_source` tinyint(4) DEFAULT '2',
  `dws_err_biz` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_mongodb_id_product_source` (`mongodb_id`,`product_source`),
  UNIQUE KEY `idx_data_source_mongodb_id` (`data_source`,`mongodb_id`),
  KEY `product_source` (`product_source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`edit_project_pull_state` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `last_pull_date` datetime NULL DEFAULT NULL,
  `project_source` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `only_one_row_state` (`project_source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_cost_category_dict` (
  `id` int(11) NOT NULL,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_cost_subject` (
  `id` bigint(20) NOT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `original_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `deleted` tinyint(4) DEFAULT NULL,
  `full_original_id_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `order_no` int(11) DEFAULT NULL,
  `org_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `parent_original_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit_pid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tbl_update_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `quality` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `product_source` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `unit_id` (`unit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_index_category_dict` (
  `id` int(11) NOT NULL,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `calc_logic_remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `factor` decimal(20,2) NOT NULL DEFAULT '1.00',
  `type` smallint(6) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_resource_category_dict` (
  `id` bigint(20) NOT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `original_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` tinyint(4) NOT NULL,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `deleted` tinyint(4) DEFAULT NULL,
  `full_original_id_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `order_no` int(11) DEFAULT NULL,
  `org_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `parent_original_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit_pid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tbl_update_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `product_source` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`type`),
  KEY `unit_id` (`unit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_target_dictionary` (
  `id` bigint(20) NOT NULL,
  `pk_id` bigint(20) DEFAULT NULL,
  `template_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `target_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `target_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `tax_flag` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_tb_area` (
  `id` int(11) NOT NULL,
  `areaid` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `pid` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `area_code` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `short_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `parent_area_code` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `abbr_spell` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `full_spell` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `level` tinyint(4) DEFAULT NULL,
  `sort` smallint(6) DEFAULT NULL,
  `area` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `longitude` double(22,0) DEFAULT NULL,
  `latitude` double(22,0) DEFAULT NULL,
  `displayable` tinyint(4) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `i_20220426143812_1_549049170` (`area_code`),
  UNIQUE KEY `i_20220426143812_3_284431460` (`areaid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_tb_area_rerun` (
  `id` int(11) NOT NULL,
  `areaid` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `pid` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `area_code` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `short_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `parent_area_code` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `abbr_spell` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `full_spell` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `level` tinyint(4) DEFAULT NULL,
  `sort` smallint(6) DEFAULT NULL,
  `area` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `longitude` double(22,0) DEFAULT NULL,
  `latitude` double(22,0) DEFAULT NULL,
  `displayable` tinyint(4) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `i_20220426141946_1_373250811` (`area_code`),
  UNIQUE KEY `i_20220426141946_3_298144492` (`areaid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_tb_commonprojcategory_standards` (
  `id` int(11) NOT NULL,
  `commonprojcategoryid` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `categoryname` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` int(11) DEFAULT NULL,
  `categorycode1` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `categorycode2` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `categorycode3` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `categorycode4` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category_type_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category_type_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `level` bigint(20) DEFAULT NULL,
  `projcount` int(11) DEFAULT NULL,
  `mainparamname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `province_process` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `qy_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_deleted` tinyint(4) DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_using` tinyint(4) DEFAULT NULL,
  `update_global_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_usable` tinyint(4) DEFAULT NULL,
  `global_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `accountname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `i_20220426143812_1_390552849` (`categoryname`),
  KEY `i_20220426143812_2_627468894` (`qy_code`),
  KEY `i_20220426143812_3_996453350` (`categorycode1`),
  KEY `i_20220426143812_4_313260422` (`categorycode3`),
  KEY `i_20220426143812_6_877443441` (`categorycode2`),
  KEY `i_20220426143812_7_878784782` (`categorycode4`),
  KEY `i_20220426143812_8_109625350` (`commonprojcategoryid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_tb_commonprojcategory_standards_rerun` (
  `id` int(11) NOT NULL,
  `commonprojcategoryid` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `categoryname` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` int(11) DEFAULT NULL,
  `categorycode1` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `categorycode2` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `categorycode3` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `categorycode4` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category_type_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category_type_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `level` bigint(20) DEFAULT NULL,
  `projcount` int(11) DEFAULT NULL,
  `mainparamname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `province_process` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `qy_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_deleted` tinyint(4) DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_using` tinyint(4) DEFAULT NULL,
  `update_global_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_usable` tinyint(4) DEFAULT NULL,
  `global_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `accountname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `i_20220426141946_1_855324903` (`categoryname`),
  KEY `i_20220426141946_2_303851395` (`qy_code`),
  KEY `i_20220426141946_3_296226287` (`categorycode1`),
  KEY `i_20220426141946_4_684318167` (`categorycode3`),
  KEY `i_20220426141946_6_188575910` (`categorycode2`),
  KEY `i_20220426141946_7_130966180` (`categorycode4`),
  KEY `i_20220426141946_8_188877225` (`commonprojcategoryid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_tb_commonprojcategory_standards_used` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `qy_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tenantId` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` tinyint(4) DEFAULT NULL,
  `tenantglobalId` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `CUSTOMERCODE` (`qy_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_tb_commonprojcategory_standards_used_rerun` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `qy_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tenantId` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` tinyint(4) DEFAULT NULL,
  `tenantglobalId` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `CUSTOMERCODE` (`qy_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_standards_expression` (
  `id` bigint(20) NOT NULL,
  `expression_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `data_type` tinyint(4) DEFAULT NULL,
  `rule` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `scope` int(11) DEFAULT NULL,
  `qy_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_usable` tinyint(4) DEFAULT NULL,
  `is_deleted` tinyint(4) DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` int(11) DEFAULT NULL,
  `option` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_global_id` bigint(20) DEFAULT NULL,
  `update_global_id` bigint(20) DEFAULT NULL,
  `is_expression` tinyint(4) DEFAULT NULL,
  `unit` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `expression_is_from_system` tinyint(4) DEFAULT NULL,
  `expression_is_using` tinyint(4) DEFAULT NULL,
  `expression_ord` int(11) DEFAULT NULL,
  `expression_create_global_id` bigint(20) DEFAULT NULL,
  `expression_create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `expression_remark` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `i_20220426143814_1_595267140` (`name`),
  KEY `i_20220426143814_3_733342947` (`qy_code`),
  KEY `enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_standards_expression_rerun` (
  `id` bigint(20) NOT NULL,
  `expression_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `data_type` tinyint(4) DEFAULT NULL,
  `rule` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `scope` int(11) DEFAULT NULL,
  `qy_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_usable` tinyint(4) DEFAULT NULL,
  `is_deleted` tinyint(4) DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` int(11) DEFAULT NULL,
  `option` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_global_id` bigint(20) DEFAULT NULL,
  `update_global_id` bigint(20) DEFAULT NULL,
  `is_expression` tinyint(4) DEFAULT NULL,
  `unit` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `expression_is_from_system` tinyint(4) DEFAULT NULL,
  `expression_is_using` tinyint(4) DEFAULT NULL,
  `expression_ord` int(11) DEFAULT NULL,
  `expression_create_global_id` bigint(20) DEFAULT NULL,
  `expression_create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `expression_remark` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` bigint(20) DEFAULT NULL,
  `old_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `i_20220426141948_1_614378444` (`name`),
  KEY `i_20220426141948_3_980359215` (`qy_code`),
  KEY `enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_standards_project_info` (
  `id` bigint(20) NOT NULL,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `select_list` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_using` tinyint(4) DEFAULT NULL,
  `is_required` tinyint(4) DEFAULT NULL,
  `creator_id` bigint(20) DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `updater_id` bigint(20) DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `remark` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `customer_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_deleted` tinyint(4) DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `standard_data_type` tinyint(4) DEFAULT NULL,
  `enterprise_id` bigint(20) DEFAULT NULL,
  `modify_time_sjzt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_standards_trade` (
  `id` bigint(20) NOT NULL,
  `description` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` tinyint(4) DEFAULT NULL,
  `creator_id` bigint(20) DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `customer_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_deleted` tinyint(4) DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `refer_trade` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `updater_id` bigint(20) DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `modify_time_sjzt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cc_tr_id` (`customer_code`,`trade_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_standards_trade_rerun` (
  `id` bigint(20) NOT NULL,
  `description` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` tinyint(4) DEFAULT NULL,
  `creator_id` bigint(20) DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `customer_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_deleted` tinyint(4) DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `refer_trade` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `updater_id` bigint(20) DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `modify_time_sjzt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cc_tr_id` (`customer_code`,`trade_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_target_dictionary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `target_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `target_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `ord` tinyint(4) DEFAULT NULL,
  `is_show` char(2) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '1',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `fill_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `meaning` int(20) DEFAULT NULL,
  `field_control_flag` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `target_code` (`target_code`),
  KEY `template_uuid_inx` (`template_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_template_item_match` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_uuid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `item_uuid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `global_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `target_uuid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `target_type` tinyint(4) DEFAULT NULL,
  `target_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `target_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `zb_template_item_match_id_uindex` (`id`),
  KEY `zb_template_item_match_template_uuid_IDX` (`template_uuid`,`item_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_unit` (
  `id` bigint(20) NOT NULL,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_deleted` tinyint(4) DEFAULT NULL,
  `customer_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_editable` tinyint(4) DEFAULT NULL,
  `creator_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  `enterprise_id` bigint(20) DEFAULT NULL,
  `modify_time_sjzt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_zbk_project_note_res` (
  `zb_project_note_res_id` bigint(20) NOT NULL DEFAULT '0',
  `original_project_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `customer_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `product_source` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tbl_update_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`zb_project_note_res_id`),
  KEY `INDEX_ORIGINAL_PROJECT_ID` (`original_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_zbsq_template_custom` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `uuid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` int(11) DEFAULT NULL,
  `src_template_uuid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `qy_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `scope` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uuid` (`uuid`),
  KEY `scope` (`scope`),
  KEY `src` (`src_template_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_bqitem` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `item_type` smallint(6) DEFAULT NULL,
  `item_source_type` smallint(6) DEFAULT NULL,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `work_scope` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `gross_rate_id` bigint(20) DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `rate` decimal(20,5) DEFAULT NULL,
  `rate_include_tax` decimal(20,5) DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `amount_include_tax` decimal(20,5) DEFAULT NULL,
  `labor_rate` decimal(20,5) DEFAULT NULL,
  `labor_amount` decimal(20,5) DEFAULT NULL,
  `primary_material_rate` decimal(20,5) DEFAULT NULL,
  `primary_material_amount` decimal(20,5) DEFAULT NULL,
  `material_Rate` decimal(20,5) DEFAULT NULL,
  `material_amount` decimal(20,5) DEFAULT NULL,
  `machine_rate` decimal(20,5) DEFAULT NULL,
  `machine_amount` decimal(20,5) DEFAULT NULL,
  `overhead_rate` decimal(20,5) DEFAULT NULL,
  `overhead_amount` decimal(20,5) DEFAULT NULL,
  `profit_rate` decimal(20,5) DEFAULT NULL,
  `profit_amount` decimal(20,5) DEFAULT NULL,
  `spec` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `creator_account` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `creator_gid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `creator_time` datetime NULL DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `levy_fee_Rate` decimal(20,5) DEFAULT NULL,
  `levy_fee_amount` decimal(20,5) DEFAULT NULL,
  `db_id` int(11) DEFAULT NULL,
  `db_item_id` int(11) DEFAULT NULL,
  `trade_id` int(11) DEFAULT NULL,
  `section_id` int(11) DEFAULT NULL,
  `Index_db_id` int(11) DEFAULT NULL,
  `bq_item_type` smallint(6) DEFAULT NULL,
  `primary_bqItem` tinyint(4) DEFAULT '0',
  `Is_provisional` tinyint(4) DEFAULT '0',
  `serial_number` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `addi_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity_expr` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity_calc_rule` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity_detail` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `limiting_rate` decimal(20,5) DEFAULT NULL,
  `equipment_rate` decimal(20,5) DEFAULT NULL,
  `equipment_amount` decimal(20,5) DEFAULT NULL,
  `tax_rate` decimal(20,5) DEFAULT NULL,
  `tax_amount` decimal(20,5) DEFAULT NULL,
  `hseaw_rate` decimal(20,5) DEFAULT NULL,
  `hseaw_amount` decimal(20,5) DEFAULT NULL,
  `risk_rate` decimal(20,5) DEFAULT NULL,
  `risk_amount` decimal(20,5) DEFAULT NULL,
  `rest_rate` decimal(20,5) DEFAULT NULL,
  `rest_amount` decimal(20,5) DEFAULT NULL,
  `norm_labor_rate` decimal(20,5) DEFAULT NULL,
  `norm_material_rate` decimal(20,5) DEFAULT NULL,
  `norm_primary_material_rate` decimal(20,5) DEFAULT NULL,
  `norm_machine_rate` decimal(20,5) DEFAULT NULL,
  `norm_equipment_rate` decimal(20,5) DEFAULT NULL,
  `norm_overhead_rate` decimal(20,5) DEFAULT NULL,
  `norm_rest_rate` decimal(20,5) DEFAULT NULL,
  `norm_rate` decimal(20,5) DEFAULT NULL,
  `provisional_material_rate` decimal(20,5) DEFAULT NULL,
  `norm_labor_amount` decimal(20,5) DEFAULT NULL,
  `norm_material_amount` decimal(20,5) DEFAULT NULL,
  `norm_primary_material_amount` decimal(20,5) DEFAULT NULL,
  `norm_machine_amount` decimal(20,5) DEFAULT NULL,
  `norm_equipment_amount` decimal(20,5) DEFAULT NULL,
  `norm_overhead_amount` decimal(20,5) DEFAULT NULL,
  `norm_rest_amount` decimal(20,5) DEFAULT NULL,
  `norm_amount` decimal(20,5) DEFAULT NULL,
  `provisional_material_amount` decimal(20,5) DEFAULT NULL,
  `org_rate` decimal(20,5) DEFAULT NULL,
  `tech_amount` decimal(20,5) DEFAULT NULL,
  `org_Amount` decimal(20,5) DEFAULT NULL,
  `org_labor_rate` decimal(20,5) DEFAULT NULL,
  `org_material_rate` decimal(20,5) DEFAULT NULL,
  `org_machine_rate` decimal(20,5) DEFAULT NULL,
  `org_equipment_rate` decimal(20,5) DEFAULT NULL,
  `org_primary_material_rate` decimal(20,5) DEFAULT NULL,
  `org_labor_amount` decimal(20,5) DEFAULT NULL,
  `org_material_amount` decimal(20,5) DEFAULT NULL,
  `org_machine_amount` decimal(20,5) DEFAULT NULL,
  `org_equipment_amount` decimal(20,5) DEFAULT NULL,
  `org_primary_material_amount` decimal(20,5) DEFAULT NULL,
  `formula_esti_Item_json` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `actual_esti_Item_json` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tax_value` decimal(20,5) DEFAULT NULL,
  `expression_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `expression_value` decimal(20,5) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_contractprojid_bidnodeid` (`contract_project_id`,`bidnode_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_bqitem_cost_detail` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `bqitem_id` bigint(20) DEFAULT NULL,
  `cost_identity` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cost_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `rate` decimal(20,5) DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_bqitem_lmmdetail` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `bqitem_id` bigint(20) DEFAULT NULL,
  `res_id` bigint(20) DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `waste_rate` decimal(20,5) DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `usage_value` decimal(20,5) DEFAULT NULL,
  `original_usage` decimal(20,5) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_bqitem_quantity_detail` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `bqitem_id` bigint(20) DEFAULT NULL,
  `category` smallint(6) DEFAULT NULL,
  `link_id` bigint(20) DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_bqitemid` (`bqitem_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `bid_node_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `original_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `original_identity` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `audit_id` bigint(20) DEFAULT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `project_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `old_project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `audit_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_id` bigint(20) DEFAULT NULL,
  `main_uuid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tax_method` smallint(6) DEFAULT NULL,
  `default_tax_flag` tinyint(4) DEFAULT NULL,
  `project_info_json` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_attr_json` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category_type` tinyint(4) DEFAULT NULL,
  `bid_type` tinyint(4) DEFAULT NULL,
  `edit_mode` smallint(6) DEFAULT NULL,
  `create_mode` smallint(6) DEFAULT NULL,
  `create_date` datetime NULL DEFAULT NULL,
  `last_udpate_date` datetime NULL DEFAULT NULL,
  `trade_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `audit_date` datetime NULL DEFAULT NULL,
  `audit_size` int(11) DEFAULT NULL,
  `submit_audit_date` datetime NULL DEFAULT NULL,
  `total` double(20,2) DEFAULT NULL,
  `product_source` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `region_en` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `build_area` decimal(25,8) DEFAULT NULL,
  `sale_area` decimal(25,8) DEFAULT NULL,
  `esti_type` smallint(3) DEFAULT NULL,
  `valuation_specification` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cur_ratio_file_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `product_positioning` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `template_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit_index_structure_area` decimal(20,5) DEFAULT NULL,
  `district_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `job_tpl_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `job_tpl_type_id` int(11) DEFAULT NULL,
  `bid_project_type` smallint(3) DEFAULT NULL,
  `bid_classification_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit_index_sale_area` decimal(20,5) DEFAULT NULL,
  `build_standard_template_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cost_bid_node_data_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `item_type_build_standard_json` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `target_name_dic_str` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_hg_history` bit(1) DEFAULT false,
  `phase` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_temp` tinyint(4) DEFAULT '0',
  `economic_calc_mode` tinyint(1) DEFAULT NULL,
  `province_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `norm_db_series_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `bq_esti_style` smallint(3) DEFAULT NULL,
  `norm_item_calc_cost` tinyint(1) DEFAULT '0',
  `cost_ratios_way` smallint(3) DEFAULT NULL,
  `stage` smallint(6) DEFAULT NULL,
  `original_project_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_original_id` (`original_id`),
  KEY `project_id` (`project_id`),
  KEY `idx_enterprise_id` (`enterprise_id`,`product_source`),
  KEY `idx_main_uuid` (`main_uuid`),
  KEY `idx_original_identity_id` (`original_identity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_attr` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `expression_id` bigint(20) DEFAULT NULL,
  `select_list` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_expression` tinyint(2) DEFAULT NULL,
  `formula` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_ord` int(11) DEFAULT NULL,
  `order_num` int(11) DEFAULT NULL,
  `trade_id` bigint(20) DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `trade_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` smallint(6) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `pk_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `pid` bigint(20) DEFAULT NULL,
  `sum_value` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `sequence` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_attr_detail` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `contract_project_id` bigint(20) DEFAULT NULL,
  `contract_project_attr_id` bigint(20) DEFAULT NULL,
  `value_category` smallint(6) DEFAULT NULL,
  `value` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `value_id` int(11) DEFAULT NULL,
  `classify_link_id` bigint(20) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_bidnode` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT '-1',
  `original_id` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `multi_bid_id` bigint(20) DEFAULT NULL,
  `bid_node_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` smallint(6) DEFAULT NULL,
  `amount_include_tax` decimal(20,5) DEFAULT NULL,
  `building_area` decimal(20,5) DEFAULT NULL,
  `sale_area` decimal(25,8) DEFAULT NULL,
  `exist_LY` tinyint(1) DEFAULT NULL,
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_id` bigint(20) DEFAULT '-1',
  `project_category_code` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_name_path` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `main_uuid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `gsp_file_path` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_main_major` tinyint(4) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `node_type` smallint(3) DEFAULT NULL,
  `selected_template` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit_index` decimal(20,5) DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `percent` decimal(20,5) DEFAULT NULL,
  `bq_esti_style` smallint(3) DEFAULT NULL,
  `esti_type` smallint(3) DEFAULT NULL,
  `bq_trade_id` int(11) DEFAULT NULL,
  `trade_type_id` int(11) DEFAULT NULL,
  `cost_list` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `common_trade_id` int(11) DEFAULT NULL,
  `bq_trade_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `position_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category_positioning` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_lease` bit(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_build_standard` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `standard_description` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `standard_value` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `pk_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `default_standard_value` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `template_level_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `show_type` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `value_type` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_contract_plan` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `contract_type_id` tinyint(4) DEFAULT NULL,
  `contract_type_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `department` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `change_ratio` decimal(20,5) DEFAULT NULL,
  `control_amount` decimal(20,5) DEFAULT NULL,
  `calculate_value_structure_area` decimal(20,5) DEFAULT NULL,
  `unit_index_structure_area` decimal(20,5) DEFAULT NULL,
  `contract_range` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_range_interface` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_contract_plan_relationship` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_id` bigint(20) NOT NULL,
  `contract_plan_id` bigint(20) DEFAULT NULL,
  `tbl_create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `remark` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_subject_id` (`subject_id`),
  KEY `idx_contract_plan_id` (`contract_plan_id`),
  KEY `idx_enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_info` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `contract_project_id` bigint(20) DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `value_id` int(11) DEFAULT NULL,
  `value_type` smallint(6) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `select_list` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_lyhf` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `original_id` bigint(20) DEFAULT NULL,
  `bidNode_id` bigint(20) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_identify` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category` smallint(6) DEFAULT NULL,
  `building_area` decimal(20,5) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `amount` decimal(20,5) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`),
  KEY `idx_bidnode_id` (`bidNode_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_lyhf_jzx` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `original_id` bigint(20) DEFAULT NULL,
  `lyhf_id` bigint(20) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category` smallint(6) DEFAULT NULL,
  `value` decimal(20,5) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_multi_bid` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `contract_project_id` bigint(20) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `building_area` double(20,2) DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_contract_project_plan` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `formula` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `formula_desc` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `value` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `original_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `value_type` tinyint(4) DEFAULT NULL,
  `pk_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_cost_summary_index` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `summart_type` tinyint(4) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_path` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `plan_name` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `property` tinyint(4) DEFAULT NULL,
  `formula` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `share_tenet` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `amount_include_tax` decimal(25,8) DEFAULT NULL,
  `amount_no_device_fee` decimal(20,5) DEFAULT NULL,
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `calculate_value` decimal(20,5) DEFAULT NULL,
  `calculate_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_pk_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cost_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_cost_summary_index_data` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `contract_project_id` bigint(20) DEFAULT NULL,
  `cost_summary_index_id` bigint(20) DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `amount_value` decimal(20,5) DEFAULT NULL,
  `amount_value_include_tax` decimal(20,5) DEFAULT NULL,
  `amount_value_no_device_fee` decimal(20,5) DEFAULT NULL,
  `calculate_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_value` decimal(20,5) DEFAULT NULL,
  `calculate_value_include_tax` decimal(20,5) DEFAULT NULL,
  `calculate_pk_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `index_value` decimal(20,5) DEFAULT NULL,
  `index_value_include_tax` decimal(20,5) DEFAULT NULL,
  `index_value_no_device_fee` decimal(20,5) DEFAULT NULL,
  `new_index_type_id` int(11) DEFAULT NULL,
  `factor` decimal(20,5) DEFAULT NULL,
  `category` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_dynamic_resource_temp` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `project_id` bigint(20) DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `old_project_id` bigint(20) DEFAULT NULL,
  `old_bidnode_id` bigint(20) DEFAULT NULL,
  `main_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `res_id` bigint(20) DEFAULT NULL,
  `job_node_id` bigint(20) DEFAULT NULL,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cost_type` int(11) DEFAULT NULL,
  `description` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `spec` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `cur_res_quantity` decimal(20,5) DEFAULT NULL,
  `unit` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `db_id` int(11) DEFAULT NULL,
  `db_item_id` int(11) DEFAULT NULL,
  `market_rate` decimal(20,6) DEFAULT NULL,
  `market_amount` decimal(20,6) DEFAULT NULL,
  `is_main_metrial` tinyint(2) DEFAULT NULL,
  `market_pre_tax_rate` decimal(20,5) DEFAULT NULL,
  `old_market_pre_tax_rate` decimal(20,5) DEFAULT NULL,
  `market_pre_tax_amount` decimal(20,5) DEFAULT NULL,
  `old_market_pre_tax_amount` decimal(20,5) DEFAULT NULL,
  `budget_tax_rate` decimal(20,5) DEFAULT NULL,
  `budget_tax_amount` decimal(20,5) DEFAULT NULL,
  `tax_ratio` decimal(20,5) DEFAULT NULL,
  `is_provisional` tinyint(2) DEFAULT NULL,
  `price_source` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `market_tax_amount` decimal(20,5) DEFAULT NULL,
  `old_market_tax_amount` decimal(20,5) DEFAULT NULL,
  `market_tax_rate` decimal(20,5) DEFAULT NULL,
  `old_market_tax_rate` decimal(20,5) DEFAULT NULL,
  `rate_by_sum` tinyint(2) DEFAULT NULL,
  `mix_res_type_id` bigint(20) DEFAULT NULL,
  `percent` decimal(20,5) DEFAULT NULL,
  `is_change` tinyint(2) DEFAULT NULL,
  `old_tax_ratio` decimal(20,5) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_expend_purchase_project` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `original_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_original_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `bill_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `project_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `third_project_id` bigint(20) DEFAULT NULL,
  `product_source` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `org_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `org_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_date` datetime NULL DEFAULT NULL,
  `contract_form` smallint(4) DEFAULT NULL,
  `supplier` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_total` decimal(20,5) unsigned zerofill DEFAULT NULL,
  `contract_total_include_tax` decimal(20,5) DEFAULT NULL,
  `cost_amount` decimal(20,5) DEFAULT NULL,
  `credit_tax_amount` decimal(20,5) DEFAULT NULL,
  `tax_radio` decimal(20,5) DEFAULT NULL,
  `contract_area` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_code` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `original_identity` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_type` tinyint(4) DEFAULT NULL,
  `stage` tinyint(4) DEFAULT NULL,
  `tbl_create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `close_total_include_tax` decimal(20,5) DEFAULT NULL,
  `user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`org_id`),
  KEY `original_identity` (`original_identity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_expend_sub_bqitem` (
  `id` bigint(20) NOT NULL,
  `expend_sub_project_id` bigint(20) DEFAULT NULL,
  `original_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `item_type` smallint(6) DEFAULT NULL,
  `code` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `rate` decimal(20,5) DEFAULT NULL,
  `total` decimal(20,5) DEFAULT NULL,
  `cost_detail` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `rule` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `work_scope` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `characteristic` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unique_md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tax_ratio` decimal(20,5) DEFAULT NULL,
  `rate_include_tax` decimal(20,5) DEFAULT NULL,
  `total_include_tax` decimal(20,5) DEFAULT NULL,
  `main_resource` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `price_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `contract_detail_original_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cost_subject_id` bigint(20) DEFAULT NULL,
  `owner_supply` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `second_supply` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `expend_sub_project_id` (`expend_sub_project_id`),
  KEY `gcdp_dwd_expend_sub_bqitem_unique_md5_IDX` (`unique_md5`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_expend_sub_farmers_pay` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `expend_sub_project_id` bigint(20) DEFAULT NULL,
  `stage_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `stage_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `stage_date` date DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `total_amount` decimal(20,5) DEFAULT NULL,
  `pay_total_amount` decimal(20,5) DEFAULT NULL,
  `percent` decimal(20,5) DEFAULT NULL,
  `people_number_this_time` int(11) DEFAULT NULL,
  `people_number_this_site` int(11) DEFAULT NULL,
  `remark` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_expend_sub_progress_pay` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `expend_sub_project_id` bigint(20) DEFAULT NULL,
  `stage_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `stage_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `stage_date` date DEFAULT NULL,
  `pay_amount` decimal(20,5) DEFAULT NULL,
  `total_amount` decimal(20,5) DEFAULT NULL,
  `total_Pay_ment_Amount` decimal(20,5) DEFAULT NULL,
  `percent` decimal(20,5) DEFAULT NULL,
  `remark` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_expend_sub_progress_settle` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `expend_sub_project_id` bigint(20) DEFAULT NULL,
  `stage_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `stage_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `stage_date` date DEFAULT NULL,
  `bqitem_amount` decimal(20,5) DEFAULT NULL,
  `total_bqitem_amount` decimal(20,5) DEFAULT NULL,
  `deduction_amount` decimal(20,5) DEFAULT NULL,
  `total_deduction_amount` decimal(20,5) DEFAULT NULL,
  `pay_amount` decimal(20,5) DEFAULT NULL,
  `pay_total_amount` decimal(20,5) DEFAULT NULL,
  `remark` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_expend_sub_project` (
  `id` bigint(20) NOT NULL,
  `original_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_original_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `project_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `org_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `org_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_date` datetime NULL DEFAULT NULL,
  `contract_company` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_total` decimal(20,5) DEFAULT NULL,
  `contract_area` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `contract_Index` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `sub_project_id` bigint(20) DEFAULT NULL,
  `province_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_code` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `nick_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `org_full_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `branch_company` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `auth_agent_user_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_manager` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `original_identity` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `product_source` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_type_code` smallint(4) DEFAULT NULL,
  `branch_company_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_form` smallint(4) DEFAULT NULL,
  `contract_total_tax` decimal(20,5) DEFAULT NULL,
  `tax_rate` decimal(20,5) DEFAULT NULL,
  `input_tax` decimal(20,5) DEFAULT NULL,
  `begin_date` datetime NULL DEFAULT NULL,
  `end_date` datetime NULL DEFAULT NULL,
  `contract_days` decimal(20,5) DEFAULT NULL,
  `contract_status` smallint(6) DEFAULT NULL,
  `construction_content` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_stage` smallint(6) DEFAULT NULL,
  `unique_md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `third_project_id` bigint(20) DEFAULT NULL,
  `contract_stage_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_contacts` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_telephone` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id_contract_date_project_category_code` (`enterprise_id`,`contract_date`,`project_category_code`),
  KEY `project_id` (`project_id`),
  KEY `contract_type_code` (`contract_type_code`),
  KEY `gcdp_dwd_expend_sub_project_unique_md5_IDX` (`unique_md5`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_index` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `template_items_custom_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `status` tinyint(1) DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `template_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_path` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `index_type` smallint(6) DEFAULT NULL,
  `subject_type` smallint(6) DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `bidnode_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `index_detail_type` smallint(6) DEFAULT NULL,
  `build_standard` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `rate` decimal(20,5) DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `rate_include_tax` decimal(20,5) DEFAULT NULL,
  `usage_value` decimal(20,5) DEFAULT NULL,
  `property` tinyint(4) DEFAULT NULL,
  `amount_include_tax` decimal(20,5) DEFAULT NULL,
  `remark` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `index_data_json` longtext CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_pk_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_value` decimal(20,5) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`),
  KEY `enterprise_id` (`enterprise_id`,`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_index_data` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `index_id` bigint(20) DEFAULT NULL,
  `original_economicindex_type` smallint(6) DEFAULT NULL,
  `original_economicindex_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `new_index_type_ID` int(11) DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_value` decimal(20,5) DEFAULT NULL,
  `calculate_value_include_tax` decimal(20,5) DEFAULT NULL,
  `index_value` decimal(20,5) DEFAULT NULL,
  `index_value_include_tax` decimal(20,5) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `factor` decimal(20,5) DEFAULT '1.00000',
  `amount_value_include_tax` decimal(20,5) DEFAULT NULL,
  `calculate_unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `amount_value` decimal(20,5) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_index_data_ld` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `lyhf_id` bigint(20) DEFAULT NULL,
  `index_value_include_tax` decimal(20,5) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `calculate_value` decimal(20,5) DEFAULT NULL,
  `index_value` decimal(20,5) DEFAULT NULL,
  `name_identify` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_value_include_tax` decimal(20,5) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `index_id` bigint(20) DEFAULT NULL,
  `original_economicindex_type` smallint(6) DEFAULT NULL,
  `original_economicindex_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `new_index_type_ID` int(11) DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `factor` decimal(20,5) DEFAULT '1.00000',
  `amount_value` decimal(20,5) DEFAULT NULL,
  `amount_value_include_tax` decimal(20,5) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`),
  KEY `index_id` (`index_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_index_data_makeup` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `index_data_id` bigint(20) DEFAULT NULL,
  `makeup_type` smallint(6) DEFAULT NULL,
  `makeup_link_id` bigint(20) DEFAULT NULL,
  `validity` tinyint(1) DEFAULT NULL,
  `factor` decimal(20,5) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_indexdataid` (`index_data_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_index_makeup` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `index_id` bigint(20) DEFAULT NULL,
  `makeup_type` smallint(6) DEFAULT NULL,
  `makeup_link_id` bigint(20) DEFAULT NULL,
  `validity` tinyint(1) DEFAULT NULL,
  `factor` decimal(20,5) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `factor_flag` tinyint(1) DEFAULT NULL,
  `is_calc_quantity` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_index_id` (`index_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_item_index_template` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `contract_project_id` bigint(20) DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `template_uuid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `bid_node_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `index_type` tinyint(3) DEFAULT NULL,
  `major_type_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `bid_type` tinyint(4) DEFAULT NULL,
  `type_flag` tinyint(2) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `tax_flag` tinyint(4) DEFAULT NULL,
  `extend_data_json` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`),
  KEY `enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_limit_index` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `formula` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `formula_desc` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `index_value` decimal(20,5) DEFAULT NULL,
  `index_desc` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_enterprise_id` (`enterprise_id`,`contract_project_id`,`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_non_construction_installation_index` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_path` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `share_rule` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `rate` decimal(20,5) DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `rate_include_tax` decimal(20,5) DEFAULT NULL,
  `amount_include_tax` decimal(20,5) DEFAULT NULL,
  `calculate_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_value` decimal(20,5) DEFAULT NULL,
  `calculate_pk_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `usage_value` decimal(20,5) DEFAULT NULL,
  `subject_note` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `template_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `template_item_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_enterprise_Id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_non_construction_installation_index_data` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `contract_project_id` bigint(20) DEFAULT NULL,
  `non_construction_installation_index_id` bigint(20) DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `amount_value` decimal(20,5) DEFAULT NULL,
  `amount_value_include_tax` decimal(20,5) DEFAULT NULL,
  `calculate_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_value` decimal(20,5) DEFAULT NULL,
  `calculate_value_include_tax` decimal(20,5) DEFAULT NULL,
  `calculate_pk_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `index_value` decimal(20,5) DEFAULT NULL,
  `index_value_include_tax` decimal(20,5) DEFAULT NULL,
  `new_index_type_id` int(11) DEFAULT NULL,
  `factor` decimal(20,5) DEFAULT NULL,
  `category` tinyint(4) DEFAULT NULL,
  `unit` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_norm_item` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT '-1',
  `contract_project_id` bigint(20) DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `bqtem_id` bigint(20) DEFAULT '-1',
  `original_id` int(11) DEFAULT NULL,
  `item_type` smallint(3) DEFAULT NULL,
  `item_source_type` smallint(3) DEFAULT NULL,
  `section_id` int(11) DEFAULT NULL,
  `db_id` int(11) DEFAULT NULL,
  `db_item_id` int(11) DEFAULT NULL,
  `res_id` bigint(20) DEFAULT NULL,
  `trade_id` int(11) DEFAULT NULL,
  `norm_item_type` smallint(3) DEFAULT NULL,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity_expr` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `conv_Info` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `work_scope` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `norm_Item_hash_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `wastage_ratio` decimal(20,5) DEFAULT NULL,
  `usage_value` decimal(20,5) DEFAULT NULL,
  `norm_labor_rate` decimal(20,5) DEFAULT NULL,
  `norm_material_rate` decimal(20,5) DEFAULT NULL,
  `norm_primary_material_rate` decimal(20,5) DEFAULT NULL,
  `norm_machine_rate` decimal(20,5) DEFAULT NULL,
  `norm_equipment_rate` decimal(20,5) DEFAULT NULL,
  `norm_overhead_rate` decimal(20,5) DEFAULT NULL,
  `norm_rest_rate` decimal(20,5) DEFAULT NULL,
  `norm_rate` decimal(20,5) DEFAULT NULL,
  `provisional_material_rate` decimal(20,5) DEFAULT NULL,
  `norm_labor_amount` decimal(20,5) DEFAULT NULL,
  `norm_material_amount` decimal(20,5) DEFAULT NULL,
  `norm_primary_material_amount` decimal(20,5) DEFAULT NULL,
  `norm_machine_amount` decimal(20,5) DEFAULT NULL,
  `norm_equipment_amount` decimal(20,5) DEFAULT NULL,
  `norm_overhead_amount` decimal(20,5) DEFAULT NULL,
  `norm_rest_amount` decimal(20,5) DEFAULT NULL,
  `norm_amount` decimal(20,5) DEFAULT NULL,
  `provisional_material_amount` decimal(20,5) DEFAULT NULL,
  `norm_budget_labor_rate` decimal(20,5) DEFAULT NULL,
  `norm_budget_material_rate` decimal(20,5) DEFAULT NULL,
  `norm_budget_machine_rate` decimal(20,5) DEFAULT NULL,
  `norm_budget_rate` decimal(20,5) DEFAULT NULL,
  `norm_budget_labor_amount` decimal(20,5) DEFAULT NULL,
  `norm_budget_material_amount` decimal(20,5) DEFAULT NULL,
  `norm_budget_machine_amount` decimal(20,5) DEFAULT NULL,
  `norm_budget_primary_material_amount` decimal(20,5) DEFAULT NULL,
  `norm_budget_equipment_amount` decimal(20,5) DEFAULT NULL,
  `norm_market_labor_rate` decimal(20,5) DEFAULT NULL,
  `norm_market_material_rate` decimal(20,5) DEFAULT NULL,
  `norm_market_machine_rate` decimal(20,5) DEFAULT NULL,
  `norm_market_rate` decimal(20,5) DEFAULT NULL,
  `norm_market_labor_amount` decimal(20,5) DEFAULT NULL,
  `norm_market_material_amount` decimal(20,5) DEFAULT NULL,
  `norm_market_machine_amount` decimal(20,5) DEFAULT NULL,
  `norm_market_primary_material_amount` decimal(20,5) DEFAULT NULL,
  `norm_market_equipment_amount` decimal(20,5) DEFAULT NULL,
  `manday_amount` decimal(20,5) DEFAULT NULL,
  `rate` decimal(20,5) DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `labor_rate` decimal(20,5) DEFAULT NULL,
  `material_Rate` decimal(20,5) DEFAULT NULL,
  `machine_rate` decimal(20,5) DEFAULT NULL,
  `primary_material_rate` decimal(20,5) DEFAULT NULL,
  `equipment_rate` decimal(20,5) DEFAULT NULL,
  `rest_rate` decimal(20,5) DEFAULT NULL,
  `labor_amount` decimal(20,5) DEFAULT NULL,
  `primary_material_amount` decimal(20,5) DEFAULT NULL,
  `material_amount` decimal(20,5) DEFAULT NULL,
  `machine_amount` decimal(20,5) DEFAULT NULL,
  `equipment_amount` decimal(20,5) DEFAULT NULL,
  `rest_amount` decimal(20,5) DEFAULT NULL,
  `quantity_detail` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `risk_amount` decimal(20,5) DEFAULT NULL,
  `tax_amount` decimal(20,5) DEFAULT NULL,
  `overhead_amount` decimal(20,5) DEFAULT NULL,
  `profit_amount` decimal(20,5) DEFAULT NULL,
  `hseaw_amount` decimal(20,5) DEFAULT NULL,
  `qtcsfat` decimal(20,5) DEFAULT NULL,
  `risk_rate` decimal(20,5) DEFAULT NULL,
  `tax_rate` decimal(20,5) DEFAULT NULL,
  `overhead_rate` decimal(20,5) DEFAULT NULL,
  `profit_rate` decimal(20,5) DEFAULT NULL,
  `hseaw_rate` decimal(20,5) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_norm_item_lmmdetail` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `contract_project_id` bigint(20) NOT NULL DEFAULT '-1',
  `norm_item_id` bigint(20) NOT NULL DEFAULT '-1',
  `res_id` bigint(20) NOT NULL DEFAULT '-1',
  `quantity` decimal(20,5) DEFAULT NULL,
  `original_usage` decimal(20,5) DEFAULT NULL,
  `usage_value` decimal(20,5) DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `waste_rate` decimal(20,5) DEFAULT NULL,
  `original_id` int(11) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_project_build_standard` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) NOT NULL DEFAULT '-1',
  `project_id` bigint(20) NOT NULL,
  `project_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `level_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `archive_date` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`project_id`),
  KEY `project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_project_build_standard_detail` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `project_id` bigint(20) DEFAULT NULL,
  `project_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `project_build_standard_id` bigint(20) NOT NULL DEFAULT '0',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` int(3) DEFAULT NULL,
  `select_list` varchar(3000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`project_id`),
  KEY `project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_project_detail` (
  `id` int(11) NOT NULL,
  `project_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `field_name` varchar(90) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `field_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `field_value` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `field_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `customer_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_global_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_delete` tinyint(4) DEFAULT NULL,
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_customercode_projcode` (`customer_code`,`project_code`),
  KEY `idx_projcode` (`project_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_project_info` (
  `id` bigint(20) NOT NULL,
  `project_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_serial_num` int(11) DEFAULT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_scale` decimal(25,4) DEFAULT NULL,
  `project_scale_unit` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `start_time` datetime NULL DEFAULT NULL,
  `end_time` datetime NULL DEFAULT NULL,
  `construction_unit` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `customer_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_global_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unique_md5code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `data_source` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_display` tinyint(4) DEFAULT NULL,
  `status` tinyint(4) DEFAULT NULL,
  `is_delete` tinyint(4) DEFAULT NULL,
  `create_date` datetime NULL DEFAULT NULL,
  `update_date` datetime NULL DEFAULT NULL,
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `jianshexingzhi` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `product_positioning` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `organization_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `virtual_org_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `virtual_org_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_add_org_member` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `i_20220413134348_3_820537922` (`project_code`),
  KEY `idx_customercode` (`customer_code`),
  KEY `idx_enterpriseid` (`enterprise_id`),
  KEY `gcdp_dwd_project_info_enterprise_id_IDX` (`enterprise_id`,`project_code`,`organization_id`,`project_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_project_organization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) DEFAULT NULL,
  `organization_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `project_organization_FK` (`project_id`),
  KEY `project_organization_organization_id_IDX` (`organization_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_project_second_cost` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) NOT NULL DEFAULT '-1',
  `project_id` bigint(20) NOT NULL,
  `project_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `original_id` int(10) DEFAULT '0',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `level_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `total` decimal(20,5) DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `expression_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `expression_value` decimal(20,5) DEFAULT NULL,
  `price` decimal(20,5) DEFAULT NULL,
  `unit_cost` decimal(20,5) DEFAULT NULL,
  `total_precent` decimal(20,5) DEFAULT NULL,
  `total_secont_precent` decimal(20,5) DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `archive_date` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`project_id`),
  KEY `project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_ratedetail_dict` (
  `id` bigint(20) NOT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `trade_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `gcdp_dwd_ratedetail_dict_id_IDX` (`id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_ratedetail_template` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `ratedetail_dict_id` bigint(20) DEFAULT NULL,
  `sequence_num` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `base_amount` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `base_amount_remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ratio` decimal(20,5) DEFAULT NULL,
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cost_category` int(11) DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `original_cost_category` int(11) DEFAULT NULL,
  `original_cost_category_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `gcdp_dwd_ratedetail_template_contract_project_id_IDX` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_resource` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `project_link_id` bigint(20) DEFAULT NULL,
  `product_source` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` smallint(6) DEFAULT NULL,
  `is_main_resource` tinyint(4) DEFAULT '0',
  `rate` decimal(20,5) DEFAULT NULL,
  `rate_include_tax` decimal(20,5) DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `price_source` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `spec` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `supply_type` smallint(6) DEFAULT NULL,
  `material_texture` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `amount_include_tax` decimal(20,5) DEFAULT NULL,
  `tax_amount` decimal(20,5) DEFAULT NULL,
  `spec_descrption` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `manufacturer` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_detail_original_id` bigint(20) DEFAULT NULL,
  `resource_category_id` bigint(20) DEFAULT NULL,
  `cost_subject_id` bigint(20) DEFAULT NULL,
  `waste_rate` decimal(20,5) DEFAULT NULL,
  `brand` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `originate` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `supplier` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tax_ratio` decimal(20,5) DEFAULT NULL,
  `creator_gid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `creator_account` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `creator_time` datetime NULL DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `mix_res_type_id` bigint(20) DEFAULT NULL,
  `swc_id` smallint(3) DEFAULT NULL,
  `machine_type` smallint(3) DEFAULT NULL,
  `has_detail` tinyint(1) DEFAULT '0',
  `rate_by_sum` tinyint(1) DEFAULT '0',
  `is_provisional` tinyint(1) DEFAULT '0',
  `list_detail` tinyint(1) DEFAULT '0',
  `is_site_mix_res_or_mix_machine` tinyint(1) DEFAULT '0',
  `is_list_detail` tinyint(1) DEFAULT '0',
  `allow_balance` tinyint(1) DEFAULT '0',
  `is_calx` tinyint(1) DEFAULT '0',
  `un_priceable_res` int(11) DEFAULT NULL,
  `db_id` int(11) DEFAULT NULL,
  `db_item_id` int(11) DEFAULT NULL,
  `supply_rate` decimal(20,5) DEFAULT NULL,
  `delivery_mode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `comment` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `mix_res_lmmdetail_json` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ps_cost_ratio` decimal(20,5) DEFAULT NULL,
  `quality_grade` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `balance_rate` decimal(20,5) DEFAULT NULL,
  `balance_quantity` decimal(20,5) DEFAULT NULL,
  `balance_amount` decimal(20,5) DEFAULT NULL,
  `osm_quantity` decimal(20,5) DEFAULT NULL,
  `budget_amount` decimal(20,5) DEFAULT NULL,
  `swc_quantity` decimal(20,5) DEFAULT NULL,
  `primary_material_quantity` decimal(20,5) DEFAULT NULL,
  `osm_budget_rate` decimal(20,5) DEFAULT NULL,
  `osm_market_rate` decimal(20,5) DEFAULT NULL,
  `osm_budget_amount` decimal(20,5) DEFAULT NULL,
  `osm_market_amount` decimal(20,5) DEFAULT NULL,
  `provisional_rate` decimal(20,5) DEFAULT NULL,
  `provisional_quantity` decimal(20,5) DEFAULT NULL,
  `provisional_amount` decimal(20,5) DEFAULT NULL,
  `first_specify_rate` decimal(20,5) DEFAULT NULL,
  `first_specify_quantity` decimal(20,5) DEFAULT NULL,
  `first_specify_amount` decimal(20,5) DEFAULT NULL,
  `close_rate` decimal(20,5) DEFAULT NULL,
  `close_balance` decimal(20,5) DEFAULT NULL,
  `close_balance_amount` decimal(20,5) DEFAULT NULL,
  `osm_close_balance_amount` decimal(20,5) DEFAULT NULL,
  `budget_supply_rate` decimal(20,5) DEFAULT NULL,
  `sub_letting_res_quantity` decimal(20,5) DEFAULT NULL,
  `original_market_rate` decimal(20,5) DEFAULT NULL,
  `original_tax_market_rate` decimal(20,5) DEFAULT NULL,
  `osm_balance_amount` decimal(20,5) DEFAULT NULL,
  `osm_balance_quantity` decimal(20,5) DEFAULT NULL,
  `market_amount` decimal(20,5) DEFAULT NULL,
  `market_rate` decimal(20,5) DEFAULT NULL,
  `budget_tax_rate` decimal(20,5) DEFAULT NULL,
  `budget_tax_amount` decimal(20,5) DEFAULT NULL,
  `budget_pre_tax_rate` decimal(20,5) DEFAULT NULL,
  `budget_pre_tax_amount` decimal(20,5) DEFAULT NULL,
  `budget_rate` decimal(20,5) DEFAULT NULL,
  `osm_budget_tax_rate` decimal(20,5) DEFAULT NULL,
  `osm_market_tax_rate` decimal(20,5) DEFAULT NULL,
  `osm_budget_tax_amount` decimal(20,5) DEFAULT NULL,
  `osm_market_tax_amount` decimal(20,5) DEFAULT NULL,
  `unit` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `market_tax_rate` decimal(20,5) DEFAULT NULL,
  `market_tax_amount` decimal(20,5) DEFAULT NULL,
  `original_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `project_link_id_project_source` (`project_link_id`),
  KEY `project_link_id_product_source` (`project_link_id`,`product_source`),
  KEY `enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_bqitem` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `sub_project_id` bigint(20) DEFAULT NULL,
  `original_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `item_type` smallint(6) DEFAULT NULL,
  `labour_bqid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `labour_dbid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cost_detail` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `rule` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `work_scope` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `gross_rate_id` bigint(20) DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `limit_rate` decimal(20,5) DEFAULT NULL,
  `limit_labor_rate` decimal(20,5) DEFAULT NULL,
  `limit_material_rate` decimal(20,5) DEFAULT NULL,
  `limit_machine_rate` decimal(20,5) DEFAULT NULL,
  `limit_zjf_rate` decimal(20,5) DEFAULT NULL,
  `limit_jjf_rate` decimal(20,5) DEFAULT NULL,
  `rate` decimal(20,5) DEFAULT NULL,
  `total` decimal(20,5) DEFAULT NULL,
  `labor_rate` decimal(20,5) DEFAULT NULL,
  `labor_amount` decimal(20,5) DEFAULT NULL,
  `material_rate` decimal(20,5) DEFAULT NULL,
  `material_amount` decimal(20,5) DEFAULT NULL,
  `machine_rate` decimal(20,5) DEFAULT NULL,
  `machine_amount` decimal(20,5) DEFAULT NULL,
  `zjf_rate` decimal(20,5) DEFAULT NULL,
  `zjf_amount` decimal(20,5) DEFAULT NULL,
  `jjf_rate` decimal(20,5) DEFAULT NULL,
  `jjf_amount` decimal(20,5) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `sub_project_id` (`sub_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_bqitem_bid` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `sub_project_id` bigint(20) DEFAULT NULL,
  `sub_bqItem_id` bigint(20) DEFAULT NULL,
  `sub_multi_bid_id` bigint(20) DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `total` decimal(20,5) DEFAULT NULL,
  `labor_amount` decimal(20,5) DEFAULT NULL,
  `material_amount` decimal(20,5) DEFAULT NULL,
  `machine_amount` decimal(20,5) DEFAULT NULL,
  `zjf_amount` decimal(20,5) DEFAULT NULL,
  `jjf_amount` decimal(20,5) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_bqitem_bid_cost_detail` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `sub_project_id` bigint(20) DEFAULT NULL,
  `sub_bqItem_id` bigint(20) DEFAULT NULL,
  `sub_bqItem_bid_id` bigint(20) DEFAULT NULL,
  `cost_identity` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cost_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_bqitem_cost_detail` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `sub_project_id` bigint(20) DEFAULT NULL,
  `sub_bqItem_id` bigint(20) DEFAULT NULL,
  `cost_identity` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cost_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `rate` decimal(20,5) DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_bqitem_link_contractor_bqitem` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `sub_project_id` bigint(20) DEFAULT NULL,
  `sub_contractor_bqItem_id` bigint(20) DEFAULT NULL,
  `sub_bqItem_id` bigint(20) DEFAULT NULL,
  `bqitem_id` bigint(20) DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `usage_value` decimal(20,5) DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `limit_total` decimal(20,5) DEFAULT NULL,
  `remark` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `total` decimal(20,5) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_bqitem_lmmdetail` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `sub_project_id` bigint(20) DEFAULT NULL,
  `sub_bqItem_id` bigint(20) DEFAULT NULL,
  `res_id` bigint(20) DEFAULT NULL,
  `usage_value` decimal(20,5) DEFAULT NULL,
  `usage_amount` decimal(20,5) DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_contractor_bqitem` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `sub_project_id` bigint(20) DEFAULT NULL,
  `original_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `item_type` smallint(6) DEFAULT NULL,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `scope` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_quantity` decimal(20,5) DEFAULT NULL,
  `labour_quantity` decimal(20,5) DEFAULT NULL,
  `contract_rate` decimal(20,5) DEFAULT NULL,
  `limit_rate` decimal(20,5) DEFAULT NULL,
  `control_rate` decimal(20,5) DEFAULT NULL,
  `contract_total` decimal(20,5) DEFAULT NULL,
  `limit_total` decimal(20,5) DEFAULT NULL,
  `control_total` decimal(20,5) DEFAULT NULL,
  `diff_total` decimal(20,5) DEFAULT NULL,
  `remark` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_contractor_bqitem_bid` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `sub_project_id` bigint(20) DEFAULT NULL,
  `sub_contractor_bqItem_id` bigint(20) DEFAULT NULL,
  `sub_multi_bid_id` bigint(20) DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `total` decimal(20,5) DEFAULT NULL,
  `percentage` decimal(20,5) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_info` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `sub_project_id` bigint(20) DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `value_id` int(11) DEFAULT NULL,
  `value_type` smallint(6) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_multi_bid` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `sub_project_id` bigint(20) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `bid_id` int(11) DEFAULT NULL,
  `construction_scope` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `percentage` decimal(20,5) DEFAULT NULL,
  `remark` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `original_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_project` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `original_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `project_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `bid_type` smallint(6) DEFAULT NULL,
  `create_date` datetime NULL DEFAULT NULL,
  `last_udpate_date` datetime NULL DEFAULT NULL,
  `audit_date` datetime NULL DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `product_source` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_type` smallint(6) DEFAULT NULL,
  `org_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `org_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_code` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `original_identity` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `org_full_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `stage` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id_audit_date_project_category_code` (`enterprise_id`,`audit_date`,`project_category_code`),
  KEY `project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_ratedetail_dict` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `sub_project_id` bigint(20) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_ratedetail_template` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `sub_project_id` bigint(20) DEFAULT NULL,
  `sub_ratedetail_dict_id` bigint(20) DEFAULT NULL,
  `sequence_num` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `base_amount` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `base_amount_remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ratio` decimal(20,5) DEFAULT NULL,
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cost_category` int(11) DEFAULT NULL,
  `original_id` bigint(20) DEFAULT NULL,
  `original_cost_category` int(11) DEFAULT NULL,
  `original_cost_category_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_third_project_info` (
  `id` bigint(20) NOT NULL,
  `third_id` bigint(20) NOT NULL,
  `project_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `third_source` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `organization_id` bigint(20) DEFAULT NULL,
  `start_time` datetime NULL DEFAULT NULL,
  `end_time` datetime NULL DEFAULT NULL,
  `project_category_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `longitude` decimal(20,6) DEFAULT NULL,
  `latitude` decimal(20,6) DEFAULT NULL,
  `province_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `area` decimal(20,5) DEFAULT NULL,
  `construction_unit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_date` datetime NULL DEFAULT NULL,
  `update_date` datetime NULL DEFAULT NULL,
  `tbl_create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `tbl_update_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `third_source` (`third_source`,`third_id`),
  KEY `enterprise_id` (`enterprise_id`,`project_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_build_standard_index` (
  `id` bigint(20) NOT NULL,
  `pid` bigint(20) DEFAULT '-1',
  `index_project_note_id` bigint(20) NOT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `code` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `standard_description` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `df_index_value_tax_sum` decimal(20,5) DEFAULT NULL,
  `df_index_value_tax_count` int(10) DEFAULT NULL,
  `df_index_value_tax_max` decimal(20,5) DEFAULT NULL,
  `df_index_value_tax_min` decimal(20,5) DEFAULT NULL,
  `swl_index_value_tax_sum` decimal(20,5) DEFAULT NULL,
  `swl_index_value_tax_count` int(10) DEFAULT NULL,
  `swl_index_value_tax_max` decimal(20,5) DEFAULT NULL,
  `swl_index_value_tax_min` decimal(20,5) DEFAULT NULL,
  `item_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_contract_project_id` (`contract_project_id`),
  KEY `idx_index_project_note_id` (`index_project_note_id`),
  KEY `idx_item_hash` (`item_hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_business_summary` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `original_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `original_identity` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `project_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_path` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_id` bigint(20) DEFAULT NULL,
  `project_category_code` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `building_area` decimal(20,5) DEFAULT NULL,
  `trade_code` int(11) DEFAULT NULL,
  `trade_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `contract_project_info` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_project_attr` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_enterpriseid_projcateid` (`enterprise_id`,`project_category_id`),
  KEY `original_identity` (`original_identity`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_business_summary_ld` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `business_summary_id` bigint(20) DEFAULT NULL,
  `name_identify` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `building_area` decimal(20,5) DEFAULT NULL,
  `contract_project_attr` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `contract_project_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_businesssumid` (`business_summary_id`),
  KEY `idx_contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_business_summary_ld_makeup` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `business_summary_id` bigint(20) DEFAULT NULL,
  `business_summary_ld_id` bigint(20) DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `lyhf_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `contract_project_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `business_summary_id` (`business_summary_id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `idx_contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_business_summary_makeup` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `business_summary_id` bigint(20) DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `contract_project_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `business_summary_id` (`business_summary_id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `idx_contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_contract_attr_link` (
  `id` bigint(20) NOT NULL,
  `business_summary_id` bigint(20) DEFAULT NULL,
  `contract_project_attr_id` bigint(20) DEFAULT NULL,
  `contract_project_attr_detail_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `contract_project_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `business_summary_id` (`business_summary_id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_contract_ld_attr_link` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `business_summary_id` bigint(20) DEFAULT NULL,
  `business_summary_ld_id` bigint(20) DEFAULT NULL,
  `contract_project_attr_id` bigint(20) DEFAULT NULL,
  `contract_project_attr_detail_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `contract_project_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `business_summary_id` (`business_summary_id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_category_dynamic_col_dict` (
  `id` bigint(20) NOT NULL,
  `index_category_dict_id` int(11) NOT NULL DEFAULT '0',
  `is_include_tax` bit(1) DEFAULT false,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `dynamic_col_json` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `index_category_dict_id` (`index_category_dict_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_cost` (
  `id` bigint(20) NOT NULL,
  `pid` bigint(20) NOT NULL DEFAULT '-1',
  `index_project_note_id` bigint(20) NOT NULL,
  `contract_project_id` bigint(20) NOT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_path` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `jm_calculate_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `jm_calculate_value` decimal(20,5) DEFAULT NULL,
  `jm_index_value` decimal(20,5) DEFAULT NULL,
  `jm_index_value_include_tax` decimal(20,5) DEFAULT NULL,
  `jm_index_unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `swl_calculate_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `swl_calculate_value` decimal(20,5) DEFAULT NULL,
  `swl_index_value` decimal(20,5) DEFAULT NULL,
  `swl_index_value_include_tax` decimal(20,5) DEFAULT NULL,
  `swl_index_unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `amount` decimal(20,5) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `project_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_un_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_un_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `df_calculate_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `df_calculate_value` decimal(20,5) DEFAULT NULL,
  `df_index_value` decimal(20,5) DEFAULT NULL,
  `df_index_value_include_tax` decimal(20,5) DEFAULT NULL,
  `df_index_unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `with_calc_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `without_calc_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `item_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `index_project_note_id` (`index_project_note_id`),
  KEY `enterprise_id` (`enterprise_id`,`contract_project_id`),
  KEY `pid` (`pid`),
  KEY `contract_project_id` (`contract_project_id`),
  KEY `gcdp_dws_index_cost_index_project_note_id_IDX` (`index_project_note_id`,`subject_ld_merge_hash`,`jm_index_value_include_tax`,`swl_index_value_include_tax`),
  KEY `idx_slm` (`subject_ld_merge_hash`),
  KEY `cost_item_hash_idx` (`index_project_note_id`,`item_hash`,`jm_index_value_include_tax`,`swl_index_value_include_tax`,`df_index_value_include_tax`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_cost_makeup` (
  `id` bigint(20) NOT NULL,
  `index_cost_id` bigint(20) NOT NULL,
  `contract_project_id` bigint(20) NOT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` tinyint(4) NOT NULL,
  `dwd_bidnode_id` bigint(20) DEFAULT NULL,
  `dwd_id` bigint(20) DEFAULT NULL,
  `ld_quantity` decimal(20,5) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`contract_project_id`),
  KEY `index_project_note_id` (`index_cost_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_economics` (
  `id` bigint(20) NOT NULL,
  `pid` bigint(20) NOT NULL DEFAULT '-1',
  `index_project_note_id` bigint(20) NOT NULL,
  `contract_project_id` bigint(20) NOT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_path` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `index_value` decimal(20,5) DEFAULT NULL,
  `index_value_include_tax` decimal(20,5) DEFAULT NULL,
  `fz_value` decimal(20,5) DEFAULT NULL,
  `fz_value_include_tax` decimal(20,5) DEFAULT NULL,
  `fm_value` decimal(20,5) DEFAULT NULL,
  `fm_value_include_tax` decimal(20,5) DEFAULT NULL,
  `formula` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `project_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_un_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_un_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`contract_project_id`),
  KEY `index_project_note_id` (`index_project_note_id`),
  KEY `pid` (`pid`),
  KEY `gcdp_dws_index_economics_index_project_note_id_IDX` (`index_project_note_id`,`subject_ld_merge_hash`,`index_value_include_tax`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_main_res` (
  `id` bigint(20) NOT NULL,
  `pid` bigint(20) NOT NULL DEFAULT '-1',
  `index_project_note_id` bigint(20) NOT NULL,
  `contract_project_id` bigint(20) NOT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_path` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `dj_calculate_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `dj_calculate_value` decimal(20,5) DEFAULT NULL,
  `dj_index_value` decimal(20,5) DEFAULT NULL,
  `dj_index_value_include_tax` decimal(20,5) DEFAULT NULL,
  `dj_index_unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `hl_calculate_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `hl_calculate_value` decimal(20,5) DEFAULT NULL,
  `hl_index_value` decimal(20,5) DEFAULT NULL,
  `hl_index_unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `market_amount` decimal(20,5) DEFAULT NULL,
  `tax_market_amount` decimal(20,5) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `project_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_un_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_un_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`contract_project_id`),
  KEY `index_project_note_id` (`index_project_note_id`),
  KEY `pid` (`pid`),
  KEY `gcdp_dws_index_main_res_index_project_note_id_IDX` (`index_project_note_id`,`subject_ld_merge_hash`,`dj_index_value_include_tax`,`dj_index_value`,`hl_index_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_project_note` (
  `id` bigint(20) NOT NULL,
  `project_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `original_identity` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `bidnode_id` bigint(20) DEFAULT NULL,
  `phase` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_info_json` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_attr_json` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `product_source` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` tinyint(4) NOT NULL DEFAULT '0',
  `build_area` decimal(20,5) DEFAULT NULL,
  `total` decimal(20,5) DEFAULT NULL,
  `ld_name_identify` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `archive_date` datetime NOT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `non_construction` bit(1) NOT NULL DEFAULT false,
  `include_project_attr` bit(1) NOT NULL DEFAULT false,
  `include_index_cost` bit(1) NOT NULL DEFAULT false,
  `include_index_usage` bit(1) NOT NULL DEFAULT false,
  `include_index_main_res` bit(1) NOT NULL DEFAULT false,
  `include_index_economics` bit(1) NOT NULL DEFAULT false,
  `is_temp` bit(1) NOT NULL DEFAULT false,
  `contract_project_total` decimal(20,5) DEFAULT NULL,
  `original_template_uuid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `product_position` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`project_code`),
  KEY `idx_projcode` (`project_code`),
  KEY `idx_categoryname` (`project_category_name`),
  KEY `idx_original_identity` (`original_identity`),
  KEY `idx_cpi` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_usage` (
  `id` bigint(20) NOT NULL,
  `pid` bigint(20) NOT NULL DEFAULT '-1',
  `index_project_note_id` bigint(20) NOT NULL,
  `contract_project_id` bigint(20) NOT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_path` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `zyl_calculate_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `zyl_calculate_value` decimal(20,5) DEFAULT NULL,
  `zyl_index_value` decimal(20,5) DEFAULT NULL,
  `zyl_index_unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `jmhl_calculate_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `jmhl_calculate_value` decimal(20,5) DEFAULT NULL,
  `jmhl_index_value` decimal(20,5) DEFAULT NULL,
  `jmhl_index_unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `project_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_un_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_un_ld_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `with_calc_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `without_calc_merge_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `item_hash` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`contract_project_id`),
  KEY `index_project_note_id` (`index_project_note_id`),
  KEY `pid` (`pid`),
  KEY `contract_project_id` (`contract_project_id`),
  KEY `gcdp_dws_index_usage_index_project_note_id_IDX` (`index_project_note_id`,`subject_ld_merge_hash`,`zyl_index_value`,`jmhl_index_value`),
  KEY `usage_item_hash_idx` (`index_project_note_id`,`item_hash`,`zyl_index_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_index_usage_makeup` (
  `id` bigint(20) NOT NULL,
  `index_usage_id` bigint(20) NOT NULL,
  `contract_project_id` bigint(20) NOT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` tinyint(4) NOT NULL,
  `dwd_bidnode_id` bigint(20) DEFAULT NULL,
  `dwd_id` bigint(20) DEFAULT NULL,
  `ld_quantity` decimal(20,5) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`contract_project_id`),
  KEY `index_project_note_id` (`index_usage_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_info_link` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `business_summary_id` bigint(20) DEFAULT NULL,
  `contract_project_info_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `contract_project_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `business_summary_id` (`business_summary_id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_market_project_cluster` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `project_id` bigint(20) DEFAULT NULL,
  `project_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `phase` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `category_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `building_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `build_area` decimal(20,5) DEFAULT NULL,
  `cluster_type` int(11) DEFAULT NULL,
  `project_area_path` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `trade_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_market_project_cluster_bqitem` (
  `id` int(11) NOT NULL DEFAULT '0',
  `cluster_id` bigint(20) DEFAULT NULL,
  `name` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `spec` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `expression_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `hash_id` int(11) DEFAULT NULL,
  `unit` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity_value` decimal(20,5) DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `expression_value` decimal(20,5) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `cluster_id_index` (`cluster_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_new_archive_data` (
  `id` bigint(20) NOT NULL,
  `type` tinyint(4) NOT NULL,
  `data_json` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `archive_date` datetime NOT NULL,
  `product_source` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `project_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `contract_project_id` bigint(20) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`,`project_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_result_for_analysis` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `contract_project_id` bigint(20) DEFAULT NULL,
  `template_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_path` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `code` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `zb_cost` decimal(20,5) DEFAULT NULL,
  `index_type` int(11) DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `zb_content` decimal(20,5) DEFAULT NULL,
  `expression_unit` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit_price` decimal(20,5) DEFAULT NULL,
  `expression_value` decimal(20,5) DEFAULT NULL,
  `target_json_string` varchar(3000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `original_identity` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_sub_bqitem_index` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `code` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `enterprice_id` (`enterprise_id`),
  KEY `code` (`code`),
  KEY `name` (`name`),
  KEY `enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_sub_bqitem_index_detail` (
  `id` bigint(20) NOT NULL,
  `type` smallint(6) DEFAULT NULL,
  `link_id` bigint(20) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `dwd_sub_project_id` bigint(20) DEFAULT NULL,
  `sub_bqitem_index_id` bigint(20) DEFAULT NULL,
  `original_identity` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `product_source` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `project_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `org_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `org_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `org_full_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_company` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `branch_company` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_code` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `project_category_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `code` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cost_detail` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `rule` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `work_scope` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `quantity` decimal(20,5) DEFAULT NULL,
  `rate` decimal(20,5) DEFAULT NULL,
  `contract_date` datetime NULL DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `province_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `city_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `district_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `group_by_md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `item_type` tinyint(2) DEFAULT '1',
  `characteristic` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `owner_supply` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `second_supply` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_stage_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_contacts` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `contract_telephone` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `rate_include_tax` decimal(20,5) DEFAULT NULL,
  `tax_ratio` decimal(20,5) DEFAULT NULL,
  `save_at_time` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `type_link_id` (`type`,`link_id`),
  KEY `enterprise_id` (`enterprise_id`),
  KEY `original_identity` (`original_identity`),
  KEY `sub_bqitem_index_id` (`sub_bqitem_index_id`),
  KEY `dwd_sub_project_id_type` (`dwd_sub_project_id`,`type`),
  KEY `search_list` (`enterprise_id`,`sub_bqitem_index_id`,`type`,`contract_date`,`quantity`),
  KEY `group_by_md5_list` (`enterprise_id`,`code`,`group_by_md5`,`sub_bqitem_index_id`,`project_category_code`,`org_id`,`type`,`contract_date`),
  KEY `project_id` (`project_id`),
  KEY `item_type` (`item_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;


CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_subject_summary_index` (
  `id` bigint(20) NOT NULL,
  `pid` bigint(20) DEFAULT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `project_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `business_summary_id` bigint(20) DEFAULT NULL,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_path` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `new_index_type_id` int(11) DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_value` decimal(20,5) DEFAULT NULL,
  `factor` decimal(20,5) DEFAULT NULL,
  `index_weighted_avg` decimal(20,5) DEFAULT NULL,
  `index_weighted_avg_include_tax` decimal(20,5) DEFAULT NULL,
  `index_arithmetic_avg` decimal(20,5) DEFAULT NULL,
  `index_arithmetic_avg_include_tax` decimal(20,5) DEFAULT NULL,
  `calculate_value_include_tax` decimal(20,5) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `contract_project_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_e_i_b` (`enterprise_id`,`new_index_type_id`,`business_summary_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_subject_summary_index_ld` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `pid` bigint(20) DEFAULT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `project_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `business_summary_id` bigint(20) DEFAULT NULL,
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `subject_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name_path` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `new_index_type_ID` int(11) DEFAULT NULL,
  `calculate_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `business_summary_ld_id` bigint(20) DEFAULT NULL,
  `name_identify` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `calculate_value` decimal(20,5) DEFAULT NULL,
  `factor` decimal(20,5) DEFAULT NULL,
  `index_weighted_avg` decimal(20,5) DEFAULT NULL,
  `index_weighted_avg_include_tax` decimal(20,5) DEFAULT NULL,
  `index_arithmetic_avg` decimal(20,5) DEFAULT NULL,
  `index_arithmetic_avg_include_tax` decimal(20,5) DEFAULT NULL,
  `calculate_value_include_tax` decimal(20,5) DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `contract_project_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_e_i_ld_b` (`enterprise_id`,`new_index_type_ID`,`name_identify`,`business_summary_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_subject_summary_index_ld_makeup` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `summary_index_ld_id` bigint(20) DEFAULT NULL,
  `dwd_index_id` bigint(20) DEFAULT NULL,
  `dwd_index_data_id` bigint(20) DEFAULT NULL,
  `dwd_index_data_ld_id` bigint(20) DEFAULT NULL,
  `business_summary_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `contract_project_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_sumindexid` (`summary_index_ld_id`),
  KEY `idx_dwdindexid` (`dwd_index_id`),
  KEY `idx_dwxindexdataid` (`dwd_index_data_id`),
  KEY `idx_dwxindexdataldid` (`dwd_index_data_ld_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dws_subject_summary_index_makeup` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `summary_index_id` bigint(20) DEFAULT NULL,
  `dwd_index_id` bigint(20) DEFAULT NULL,
  `dwd_index_data_id` bigint(20) DEFAULT NULL,
  `business_summary_id` bigint(20) DEFAULT NULL,
  `enterprise_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `contract_project_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_sumindexid` (`summary_index_id`),
  KEY `idx_dwdindexid` (`dwd_index_id`),
  KEY `idx_dwxindexdataid` (`dwd_index_data_id`),
  KEY `contract_project_id` (`contract_project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_func_dws_biz_config` (
  `id` int(11) NOT NULL,
  `product_source` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `biz_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `biz_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `biz_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `state` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `product_source` (`product_source`),
  KEY `biz_code` (`biz_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_func_zbsq_project_pull_log` (
  `id` bigint(20) NOT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `zb_project_note_res_id` bigint(20) DEFAULT NULL,
  `main_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `bidnode_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `node_type` int(11) DEFAULT NULL,
  `file_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `file_path` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `error_type` int(11) DEFAULT NULL,
  `error_msg` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `enterprise_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `struct_bidnode_uuid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `success` int(11) DEFAULT NULL,
  `has_struct` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `zb_project_note_res_id` (`zb_project_note_res_id`),
  KEY `file_name` (`file_name`),
  KEY `error_type` (`error_type`),
  KEY `enterprise_id` (`enterprise_id`,`success`,`file_name`),
  KEY `has_struct` (`has_struct`),
  KEY `success` (`success`),
  KEY `node_type` (`node_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;


CREATE TABLE `db_cost_data_platform_pro`.`sys_distributed_lock_register` (
  `REGISTER_ID` int(11) NOT NULL AUTO_INCREMENT,
  `LOCK_NAME` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `LOCK_DESC` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`REGISTER_ID`),
  KEY `xxx` (`LOCK_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

CREATE TABLE `db_cost_data_platform_pro`.`sys_sequence` (
  `SEQUENCE_NAME` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `CURRENT_VALUE` decimal(19,0) DEFAULT '1',
  `INCREMENT_BY` decimal(5,0) DEFAULT '1',
  `MAX_VALUE` decimal(19,0) DEFAULT NULL,
  `MIN_VALUE` decimal(19,0) DEFAULT NULL,
  `CYCLE_FLAG` tinyint(1) DEFAULT '0',
  `CREATE_TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `LAST_UPDATE_TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `DESCRIPTION` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`SEQUENCE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;