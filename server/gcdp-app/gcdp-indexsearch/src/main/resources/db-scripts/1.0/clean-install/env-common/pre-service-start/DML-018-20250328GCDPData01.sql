use `db_cost_data_platform_pro`;
ALTER table gcdp_dwd_index ADD COLUMN tax_ratio DECIMAL(25, 6) DEFAULT NULL comment '税率' AFTER amount_include_tax;

ALTER table gcdp_dwd_non_construction_installation_index ADD COLUMN tax_ratio DECIMAL(25, 6) DEFAULT NULL comment '税率' AFTER amount_include_tax;

alter table gcdp_dwd_contract_project
    add column fja_build_area decimal(25, 8) null comment '非建安建筑面积' after build_area;

ALTER TABLE gcdp_dwd_non_construction_installation_index_data ADD COLUMN `calculate_unit` VARCHAR(50) NULL COMMENT '计算口径单位' AFTER `unit`;

INSERT INTO gcdp_func_dws_biz_config (id, product_source, biz_code, biz_name, biz_type, state) VALUES (820, 'zbsq-web', 'biz-fja', '非建安指标', 'gb', 1)


