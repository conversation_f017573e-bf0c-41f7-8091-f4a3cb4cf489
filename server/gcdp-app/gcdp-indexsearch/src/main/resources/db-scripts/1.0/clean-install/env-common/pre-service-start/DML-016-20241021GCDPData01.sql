use `db_cost_data_platform_pro`;
# 按照专业id 进行数据更新 添加专业的索引
update `db_cost_data_platform_pro`.gcdp_dwd_contract_project_attr ga inner join gcdp_dwd_contract_project gp on ga.contract_project_id = gp.id
    inner join gcdp_dim_zb_standards_trade gt on ga.trade_id = gt.id
set ga.trade_code = gt.trade_code
where gp.product_source in ('zbsq', 'gbqd');

# 更新dws_index_project_note表的attr_json和info_json的字段由 'null' => NULL
update `db_cost_data_platform_pro`.gcdp_dws_index_project_note
set project_info_json = null
where project_info_json = 'null';

# 处理单体维度的数据处理
update `db_cost_data_platform_pro`.gcdp_dws_index_project_note
set project_attr_json = null
where project_attr_json = 'null';
