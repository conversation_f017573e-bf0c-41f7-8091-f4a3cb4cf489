USE `db_cost_data_platform_pro`;

REPLACE INTO `db_cost_data_platform_pro`.`gcdp_func_dws_biz_config` (`id`, `product_source`, `biz_code`, `biz_name`, `biz_type`, `state`) VALUES
	(304, 'gbqd', 'biz-rcj-push', '人材机推送', 'gb', 1),
    (305, 'gbqd', 'biz-qd-push', '清单推送', 'gb', 1),
    (306, 'zbw', 'biz-rcj-push', '人材机推送', 'gb', 1),
    (307, 'zbw', 'biz-qd-push', '清单推送', 'gb', 1),
    (308, 'hg', 'biz-rcj-push', '人材机推送', 'gb', 1),
    (309, 'hg', 'biz-qd-push', '清单推送', 'gb', 1),
    (501, 'tzgsbz', 'biz-ja', '建安指标', 'tzgsbz', 1);

update `db_cost_data_platform_pro`.gcdp_dwd_contract_project set phase = '可研版' where phase is null and stage = 1 and product_source = 'mbcb';
update `db_cost_data_platform_pro`.gcdp_dwd_contract_project set phase = '定位版' where phase is null and stage = 2 and product_source = 'mbcb';
update `db_cost_data_platform_pro`.gcdp_dwd_contract_project set phase = '方案版' where phase is null and stage = 3 and product_source = 'mbcb';
update `db_cost_data_platform_pro`.gcdp_dwd_contract_project set phase = '施工图版' where phase is null and stage = 4 and product_source = 'mbcb';
update `db_cost_data_platform_pro`.gcdp_dwd_contract_project set phase = '结算版' where phase is null and stage = 5 and product_source = 'mbcb';
