USE `db_cost_data_platform_pro`;
CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dim_zb_standards_main_quantity` (
                                                       `id` bigint NOT NULL COMMENT '主键',
                                                       `trade_id` bigint DEFAULT NULL COMMENT '专业id',
                                                       `description` varchar(100) DEFAULT NULL COMMENT '科目名称',
                                                       `unit` varchar(100) DEFAULT NULL COMMENT '科目单位',
                                                       `remark` varchar(500) DEFAULT NULL COMMENT '科目备注',
                                                       `type` tinyint DEFAULT NULL COMMENT '科目类型 0 内置科目 1 新增科目',
                                                       `qy_code_old` varchar(100) DEFAULT NULL COMMENT '企业编码（旧）',
                                                       `is_deleted` tinyint DEFAULT '0' COMMENT '是否已被删除，0：未删除，1：已删除',
                                                       `ord` int DEFAULT NULL COMMENT '排序字段',
                                                       `create_global_id` bigint DEFAULT NULL COMMENT '创建人',
                                                       `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                       `update_global_id` bigint DEFAULT NULL COMMENT '更新人',
                                                       `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                       `trade_code` varchar(10) DEFAULT NULL COMMENT '专业编码',
                                                       `trade_name` varchar(100) DEFAULT NULL COMMENT '专业名称',
                                                       `enterprise_id` bigint DEFAULT NULL COMMENT 'gccs企业id',
                                                       `customer_code` varchar(100) DEFAULT NULL COMMENT '企业编码（新）',
                                                       `qy_flag` tinyint DEFAULT NULL COMMENT '企业标识：1-企业id；0-企业编码',
                                                       PRIMARY KEY (`id`),
                                                       KEY `i_20230927135945_1_952035662` (`trade_code`),
                                                       KEY `i_20230927135945_2_752336099` (`qy_flag`),
                                                       KEY `i_20230927135945_3_956503831` (`qy_code_old`),
                                                       KEY `i_20230927135945_4_666855658` (`customer_code`),
                                                       KEY `i_20230927135945_6_198603012` (`trade_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb3 COMMENT = '企业数据标准-主要量指标';
