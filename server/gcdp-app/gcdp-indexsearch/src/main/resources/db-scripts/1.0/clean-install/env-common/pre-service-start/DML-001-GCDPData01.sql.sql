USE `db_cost_data_platform_pro`;

REPLACE INTO `db_cost_data_platform_pro`.`gcdp_dim_cost_category_dict` (`id`, `name`) VALUES
	(100, '人工费'),
	(200, '材料费'),
	(300, '机械费'),
	(400, '设备费'),
	(500, '主材费'),
	(600, '辅材费'),
	(700, '直接费'),
	(800, '间接费');



REPLACE INTO `db_cost_data_platform_pro`.`gcdp_dim_index_category_dict` (`id`, `name`, `calc_logic_remark`, `factor`, `type`) VALUES
	(1001, '建面单方（元/m2）', '科目合价/建筑面积', 1.00, 1),
	(1002, '销面单方（元/m2）', '科目合价/可售面积', 1.00, 1),
	(1003, '父级占比指标', '科目合价/父项合价', 100.00, 1),
	(1004, '总造价占比指标', '科目合价/总造价', 100.00, 1),
	(1005, '实物量单方', '科目合价/科目工程量', 1.00, 1),
	(1006, '单方指标（自定义）', '科目合价/计算口径', 1.00, 1),
	(1007, '主要工料单价指标', '', 1.00, 1),
	(1008, '工料占造价比', '', 100.00, 1),
	(1009, '造价指标', '', 1.00, 1),
	(1010, '主要工料单价指标(不含税)', '', 1.00, 1),
	(2001, '主要量指标', '科目工程量/计算口径', 1.00, 2),
	(2002, '建面含量指标', '总工程量/建筑面积', 1.00, 2),
	(2003, '比值指标', '', 1.00, 1),
	(2004, '主要工料含量指标', '', 1.00, 1);



REPLACE INTO `db_cost_data_platform_pro`.`gcdp_dws_index_category_dynamic_col_dict` (`id`, `index_category_dict_id`, `is_include_tax`, `tbl_create_date`, `dynamic_col_json`) VALUES
	(1, 1001, 0, '2022-11-25 17:57:17', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"jmIndexValue","caption":"建面单方","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"amount","caption":"造价金额","defaultValue":"","type":"number"},{"ord":4,"fieldName":"buildArea","caption":"建筑面积","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
	(2, 1005, 0, '2022-11-25 17:57:17', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"swlIndexValue","caption":"实物量单方","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"amount","caption":"造价金额","defaultValue":"","type":"number"},{"ord":4,"fieldName":"quantity","caption":"工程量","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
	(3, 2001, 0, '2022-11-27 15:06:47', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"zylIndexValue","caption":"主要量指标","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"quantity","caption":"工程量","defaultValue":"","type":"number"},{"ord":4,"fieldName":"caliber","caption":"计算口径","defaultValue":"","type":"text"},{"ord":5,"fieldName":"caliberValue","caption":"计算口径值","defaultValue":"","type":"number"},{"ord":6,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":7,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":8,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":9,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
	(4, 2002, 0, '2022-11-27 15:28:06', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"jmhlIndexValue","caption":"建面含量指标","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"quantity","caption":"工程量","defaultValue":"","type":"number"},{"ord":4,"fieldName":"buildArea","caption":"建筑面积","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
	(5, 1007, 0, '2022-11-27 16:06:52', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"djTaxIndexValue","caption":"主要工料的单价指标（含税）","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"taxMarketAmount","caption":"含税合价","defaultValue":"","type":"number"},{"ord":4,"fieldName":"quantity","caption":"消耗量","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
	(6, 1010, 0, '2022-11-27 19:00:21', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"djIndexValue","caption":"主要工料的单价指标（不含税）","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"marketAmount","caption":"含税合价","defaultValue":"","type":"number"},{"ord":4,"fieldName":"quantity","caption":"消耗量","defaultValue":"","type":"number"},{"ord":5,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":6,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":7,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":8,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
	(7, 2004, 0, '2022-11-27 19:45:26', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"glhlIndexValue","caption":"主要工料含量指标","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"quantity","caption":"消耗量","defaultValue":"","type":"number"},{"ord":4,"fieldName":"caliber","caption":"计算口径","defaultValue":"","type":"text"},{"ord":5,"fieldName":"caliberValue","caption":"计算口径值","defaultValue":"","type":"number"},{"ord":6,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":7,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":8,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":9,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
	(8, 2003, 0, '2022-12-02 13:46:01', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"indexValue","caption":"比值指标","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"formula","caption":"计算规则说明","defaultValue":"","type":"text"},{"ord":4,"fieldName":"fzValue","caption":"分子","defaultValue":"","type":"number"},{"ord":5,"fieldName":"fmValue","caption":"分母","defaultValue":"","type":"number"},{"ord":6,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":7,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":8,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":9,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]'),
	(9, 1006, 0, '2023-03-30 16:21:44', '[{"ord":0,"fieldName":"name","caption":"科目名称","defaultValue":"","type":"text"},{"ord":1,"fieldName":"dfIndexValue","caption":"单方指标","defaultValue":"","type":"number"},{"ord":2,"fieldName":"unit","caption":"单位","defaultValue":"","type":"text"},{"ord":3,"fieldName":"amount","caption":"造价金额","defaultValue":"","type":"number"},{"ord":4,"fieldName":"calculateName","caption":"计算口径","defaultValue":"","type":"text"},{"ord":5,"fieldName":"calculateValue","caption":"计算口径值","defaultValue":"","type":"number"},{"ord":6,"fieldName":"noteName","caption":"所属单项工程","defaultValue":"","type":"text"},{"ord":7,"fieldName":"projectName","caption":"项目名称","defaultValue":"","type":"text"},{"ord":8,"fieldName":"categoryName","caption":"工程分类","defaultValue":"","type":"text"},{"ord":9,"fieldName":"area","caption":"地区","defaultValue":"","type":"text"}]');

REPLACE INTO `db_cost_data_platform_pro`.`gcdp_func_dws_biz_config` (`id`, `product_source`, `biz_code`, `biz_name`, `biz_type`, `state`) VALUES
	(101, 'ystz', 'biz-ja', '建安指标', 'qb', 1),
	(102, 'ystz', 'biz-qd-push', '清单推送', 'qb', 1),
	(103, 'ystz', 'biz-rcj-push', '人材机推送', 'qb', 1),
	(104, 'ystz', 'biz-qd-calculate', '模拟清单量', 'qb', 1),
	(201, 'qyqd', 'biz-ja', '建安指标', 'qb', 1),
	(202, 'qyqd', 'biz-qd-push', '清单推送', 'qb', 1),
	(203, 'qyqd', 'biz-rcj-push', '人材机推送', 'qb', 1),
	(204, 'qyqd', 'biz-qd-calculate', '模拟清单量', 'qb', 1),
	(301, 'zbsq', 'biz-ja', '建安指标', 'gb', 0),
	(302, 'zbsq', 'biz-rcj-push', '人材机推送', 'gb', 1),
	(303, 'zbsq', 'biz-qd-push', '清单推送', 'gb', 1),
	(401, 'mbcb', 'biz-ja', '建安指标', 'mbcb', 1),
	(402, 'mbcb', 'biz-fja', '非建安指标', 'mbcb', 1),
	(403, 'mbcb', 'biz-jzbz', '建造标准', 'mbcb', 1),
	(404, 'mbcb', 'biz-sjzb', '产品设计指标', 'mbcb', 1);

REPLACE INTO `db_cost_data_platform_pro`.`sys_distributed_lock_register` (`REGISTER_ID`, `LOCK_NAME`, `LOCK_DESC`, `CREATE_TIME`, `UPDATE_TIME`) VALUES
	(1, 'com.glodon.cost.bdp.component.sequence.core.LongSequence', 'Long型分布式锁', '2022-03-11 14:45:35', '2023-05-29 17:17:45'),
	(2, 'com.glodon.cost.bdp.component.sequence.core.IntegerSequence', 'Integer型分布式锁', '2022-03-11 14:54:53', '2022-03-11 14:54:53');
