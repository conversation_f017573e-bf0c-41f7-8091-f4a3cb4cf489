use `db_cost_data_platform_pro`;
CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_bqitem_category_initrecord` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `enterprise_id` varchar(32) DEFAULT NULL COMMENT '企业ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `gcdp_dwd_sub_bqitem_category_initrecord_enterprise_id_IDX` (`enterprise_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb3 COMMENT='分包价格库分类初始化记录表'
