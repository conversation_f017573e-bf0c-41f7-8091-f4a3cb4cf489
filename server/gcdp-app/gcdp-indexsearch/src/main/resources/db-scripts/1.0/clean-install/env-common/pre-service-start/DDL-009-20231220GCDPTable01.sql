USE `db_cost_data_platform_pro`;
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_project_note
    ADD COLUMN include_index_jmdf BIT DEFAULT 0 NOT NULL COMMENT '是否包含建面单方：0=否，1=是' AFTER include_index_usage,
    ADD COLUMN include_index_swldf BIT DEFAULT 0 NOT NULL COMMENT '是否包含实物量单方：0=否，1=是' AFTER include_index_jmdf,
    ADD COLUMN include_index_dfzb BIT DEFAULT 0 NOT NULL COMMENT '是否包含单方指标：0=否，1=是' AFTER include_index_swldf,
    ADD COLUMN include_index_zylzb BIT DEFAULT 0 NOT NULL COMMENT '是否包含主要量：0=否，1=是' AFTER include_index_dfzb,
    ADD COLUMN include_index_jmhlzb BIT DEFAULT 0 NOT NULL COMMENT '是否包含建面含量：0=否，1=是' AFTER include_index_zylzb;

CREATE TABLE `db_cost_data_platform_pro`.gcdp_dws_index
(
    id                              BIGINT                              NOT NULL COMMENT 'Id'
        PRIMARY KEY,
    pid                             BIGINT                              NULL COMMENT '父Id',
    index_project_note_id           BIGINT                              NOT NULL COMMENT 'note_id',
    contract_project_id             BIGINT                              NULL COMMENT '合约工程id',
    enterprise_id                   VARCHAR(32)                         NULL COMMENT '企业id',
    code                            VARCHAR(500)                        NULL COMMENT '科目编码',
    name                            VARCHAR(255)                        NULL COMMENT '科目名称',
    name_path                       VARCHAR(2000)                       NULL COMMENT '科目名称全路径',
    unit                            VARCHAR(255)                        NULL COMMENT '科目单位',
    amount                          DECIMAL(20, 5)                      NULL COMMENT '科目合价(不含税)',
    amount_include_tax              DECIMAL(20, 5)                      NULL COMMENT '科目合价(含税)',
    quantity                        DECIMAL(20, 5)                      NULL COMMENT '科目工程量',
    tbl_create_date                 TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    jm_calculate_name               VARCHAR(255)                        NULL COMMENT '建面单方/含量-计算口径名称',
    jm_calculate_unit               VARCHAR(50)                         NULL COMMENT '建面/含量-计算口径单位',
    jm_calculate_value              DECIMAL(20, 5)                      NULL COMMENT '建面/含量-计算口径值',
    jmdf_index_value                DECIMAL(20, 5)                      NULL COMMENT '建面单方-指标值（不含税）',
    jmdf_index_value_include_tax    DECIMAL(20, 5)                      NULL COMMENT '建面单方-指标值（含税）',
    jmdf_index_unit                 VARCHAR(255)                        NULL COMMENT '建面单方-指标单位',
    jmdf_index_merge_hash           VARCHAR(20)                         NULL COMMENT '建面单方-科目合并hash',
    jmdf_index_with_calc_merge_hash VARCHAR(20)                         NULL COMMENT '建面单方-口径合并hash',
    jmhl_index_value                DECIMAL(20, 5)                      NULL COMMENT '建面含量-指标值',
    jmhl_index_unit                 VARCHAR(255)                        NULL COMMENT '建面含量-指标单位',
    jmhl_index_merge_hash           VARCHAR(20)                         NULL COMMENT '建面含量-科目合并hash',
    jmhl_index_with_calc_merge_hash VARCHAR(20)                         NULL COMMENT '建面含量-口径合并hash',
    swl_calculate_name              VARCHAR(255)                        NULL COMMENT '实物量单方-计算口径名称',
    swl_calculate_unit              VARCHAR(50)                         NULL COMMENT '实物量单方-计算口径单位',
    swl_calculate_value             DECIMAL(20, 5)                      NULL COMMENT '实物量单方-计算口径值',
    swl_index_value                 DECIMAL(20, 5)                      NULL COMMENT '实物量单方-指标值（不含税）',
    swl_index_value_include_tax     DECIMAL(20, 5)                      NULL COMMENT '实物量单方-指标值（含税）',
    swl_index_unit                  VARCHAR(255)                        NULL COMMENT '实物量单方-指标单位',
    swl_index_merge_hash            VARCHAR(20)                         NULL COMMENT '实物量单方-科目合并hash',
    swl_index_with_calc_merge_hash  VARCHAR(20)                         NULL COMMENT '实物量单方-口径合并hash',
    df_calculate_name               VARCHAR(255)                        NULL COMMENT '单方造价-计算口径名称',
    df_calculate_unit               VARCHAR(50)                         NULL COMMENT '单方造价-计算口径单位',
    df_calculate_value              DECIMAL(20, 5)                      NULL COMMENT '单方造价-计算口径值',
    df_index_value                  DECIMAL(20, 5)                      NULL COMMENT '单方造价-指标值（不含税）',
    df_index_value_include_tax      DECIMAL(20, 5)                      NULL COMMENT '单方造价-指标值（含税）',
    df_index_unit                   VARCHAR(255)                        NULL COMMENT '单方造价-指标单位',
    df_index_merge_hash             VARCHAR(20)                         NULL COMMENT '单方造价-科目合并hash',
    df_index_with_calc_merge_hash   VARCHAR(20)                         NULL COMMENT '单方造价-口径合并hash',
    zyl_calculate_name              VARCHAR(255)                        NULL COMMENT '主要量指标-计算口径名称',
    zyl_calculate_unit              VARCHAR(50)                         NULL COMMENT '主要量指标-计算口径单位',
    zyl_calculate_value             DECIMAL(20, 5)                      NULL COMMENT '主要量指标-计算口径值',
    zyl_index_value                 DECIMAL(20, 5)                      NULL COMMENT '主要量指标-指标值',
    zyl_index_unit                  VARCHAR(255)                        NULL COMMENT '主要量-指标单位',
    zyl_index_merge_hash            VARCHAR(20)                         NULL COMMENT '主要量指标-科目合并hash',
    zyl_index_with_calc_merge_hash  VARCHAR(20)                         NULL COMMENT '主要量指标-口径合并hash',
    item_hash                       VARCHAR(20)                         NULL COMMENT 'item_hash'
)
    COMMENT 'dws指标表';

CREATE INDEX contract_project_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index (contract_project_id);

CREATE INDEX enterprise_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index (enterprise_id);

CREATE INDEX idx_code_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index (code, id);

CREATE INDEX idx_df_index_merge_hash
    ON `db_cost_data_platform_pro`.gcdp_dws_index (index_project_note_id, df_index_merge_hash);

CREATE INDEX idx_df_index_with_calc_merge_hash
    ON `db_cost_data_platform_pro`.gcdp_dws_index (index_project_note_id, df_index_with_calc_merge_hash);

CREATE INDEX idx_jmdf_index_merge_hash
    ON `db_cost_data_platform_pro`.gcdp_dws_index (index_project_note_id, jmdf_index_merge_hash);

CREATE INDEX idx_jmdf_index_with_calc_merge_hash
    ON `db_cost_data_platform_pro`.gcdp_dws_index (index_project_note_id, jmdf_index_with_calc_merge_hash);

CREATE INDEX idx_jmhl_index_merge_hash
    ON `db_cost_data_platform_pro`.gcdp_dws_index (index_project_note_id, jmhl_index_merge_hash);

CREATE INDEX idx_jmhl_index_with_calc_merge_hash
    ON `db_cost_data_platform_pro`.gcdp_dws_index (index_project_note_id, jmhl_index_with_calc_merge_hash);

CREATE INDEX idx_swl_index_merge_hash
    ON `db_cost_data_platform_pro`.gcdp_dws_index (index_project_note_id, swl_index_merge_hash);

CREATE INDEX idx_swl_index_with_calc_merge_hash
    ON `db_cost_data_platform_pro`.gcdp_dws_index (index_project_note_id, swl_index_with_calc_merge_hash);

CREATE INDEX idx_zyl_index_merge_hash
    ON `db_cost_data_platform_pro`.gcdp_dws_index (index_project_note_id, zyl_index_merge_hash);

CREATE INDEX idx_zyl_index_with_calc_merge_hash
    ON `db_cost_data_platform_pro`.gcdp_dws_index (index_project_note_id, zyl_index_with_calc_merge_hash);

CREATE INDEX note_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index (index_project_note_id);

CREATE TABLE `db_cost_data_platform_pro`.gcdp_dws_index_makeup
(
    id                    BIGINT                              NOT NULL COMMENT 'id'
        PRIMARY KEY,
    index_project_note_id BIGINT                              NOT NULL COMMENT 'dws单体表id',
    dws_index_id          BIGINT                              NOT NULL COMMENT '成本科目id',
    dwd_id                BIGINT                              NULL COMMENT '对应typedwd层id',
    contract_project_id   BIGINT                              NOT NULL COMMENT '合约工程id',
    dwd_bidnode_id        BIGINT                              NOT NULL COMMENT '单体、业态id',
    enterprise_id         VARCHAR(32)                         NOT NULL COMMENT '企业id',
    type                  TINYINT                             NOT NULL COMMENT '类型 1=清单 2=措施 3=材料 4=定额 5=费用 6=耗量 7=其他',
    ld_quantity           DECIMAL(20, 5)                      NULL COMMENT '楼栋工程量',
    tbl_create_date       TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间'
)
    COMMENT 'dws指标组成表';

CREATE INDEX contract_project_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index_makeup (contract_project_id);

CREATE INDEX dws_index_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index_makeup (dws_index_id);

CREATE INDEX enterprise_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index_makeup (enterprise_id, contract_project_id);

CREATE TABLE `db_cost_data_platform_pro`.gcdp_dws_index_build_standards_relationship
(
    id                    BIGINT                              NOT NULL COMMENT '主键'
        PRIMARY KEY,
    dws_index_id          BIGINT                              NOT NULL COMMENT '成本科目id',
    build_standard_id     BIGINT                              NOT NULL COMMENT '建造标准id',
    index_project_note_id BIGINT                              NOT NULL COMMENT 'dws单体表id',
    contract_project_id   BIGINT                              NOT NULL COMMENT '工程id',
    bid_node_id           BIGINT                              NOT NULL COMMENT '关联的业态id',
    enterprise_id         VARCHAR(32)                         NOT NULL COMMENT '企业编号',
    tbl_create_date       TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间'
);

CREATE INDEX bid_node_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index_build_standards_relationship (bid_node_id);

CREATE INDEX build_standard_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index_build_standards_relationship (build_standard_id);

CREATE INDEX contract_project_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index_build_standards_relationship (contract_project_id);

CREATE INDEX dws_index_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index_build_standards_relationship (dws_index_id);

CREATE INDEX index_project_note_id
    ON `db_cost_data_platform_pro`.gcdp_dws_index_build_standards_relationship (index_project_note_id);

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_economics ADD fz_type TINYINT(4) NULL COMMENT '分子的表达式类型 type：0=口径；1=工程量 ；2=合价';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_economics ADD fm_type TINYINT(4) NULL COMMENT '分母的表达式类型 type：0=口径；1=工程量 ；2=合价';
