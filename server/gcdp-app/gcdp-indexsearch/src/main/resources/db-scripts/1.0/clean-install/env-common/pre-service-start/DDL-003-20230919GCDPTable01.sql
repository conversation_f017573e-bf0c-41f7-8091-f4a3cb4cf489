USE `db_cost_data_platform_pro`;

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_project_info ADD sync_trd_org_id varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '第三方原始id';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_project_info ADD extend_field varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '额外字段（geps使用）';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_project_info MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_project_detail MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_contract_project MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目户口薄中的编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_purchase_project MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目户口薄中的编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_project MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目户口薄中的编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_sub_project MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目户口薄中的编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_project_note MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_market_project_cluster MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目户口薄中的编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_new_archive_data MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail MODIFY COLUMN cost_detail text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '费用构成';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目户口薄中的编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_project_build_standard MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目户口薄中的编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_project_build_standard_detail MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目户口薄中的编码';
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_project_second_cost MODIFY COLUMN project_code varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目户口薄中的编码';

CREATE TABLE `db_cost_data_platform_pro`.`gcdp_dwd_project_integration` (
   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
   `enterprise_id` varchar(32) DEFAULT NULL COMMENT '企业ID',
   `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
   `project_source` varchar(32) DEFAULT NULL COMMENT '项目来源',
   `is_init` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否初始化：0-无；1-已初始化',
   `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`),
   UNIQUE KEY `project_integration_un` (`enterprise_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='项目集成企业列表'
