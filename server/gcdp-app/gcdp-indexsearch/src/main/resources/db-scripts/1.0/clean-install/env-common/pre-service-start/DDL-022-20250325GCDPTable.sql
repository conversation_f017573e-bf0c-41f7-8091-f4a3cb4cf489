use `db_cost_data_platform_pro`;
-- 支持AI楼栋提取 以及初始化数据
create table if not exists `db_cost_data_platform_pro`.gcdp_dws_index_project_note_ld_extract_helper
(
    id               bigint                             not null primary key,
    enterprise_id    varchar(32)                        not null comment '企业id',
    project_name     varchar(255)                       null comment '项目名称',
    engineering_name varchar(255)                       null comment '工程名称',
    name             varchar(255)                       null comment '单体名称',
    type             tinyint  default 0                 not null comment '类别： 0=none 1=专业 2=业态 3=分类 4=楼栋 5=虚拟节点 6=虚拟楼栋',
    extract_name     varchar(255)                       null comment '提取后的单体名称',
    extract_type     varchar(16)                        null comment 'ai-extract, rule-extract, pre-rule-extract  AI提取 规则提取 前置规则提取',
    tbl_create_date  datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment 'yyyy-MM-dd HH:mm:ss'
)
    comment 'dws-AI楼栋提取-辅助表' engine = InnoDB;
-- 创建唯一索引（确保无重复数据）
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_index_project_note_ld_extract_helper
    ADD UNIQUE INDEX udx_uniq_combine (enterprise_id, project_name, engineering_name, name);

# 初始化helper数据
REPLACE INTO `db_cost_data_platform_pro`.gcdp_dws_index_project_note_ld_extract_helper
(id, enterprise_id, project_name, engineering_name, name, type, extract_name, extract_type)
SELECT gn.id AS id,
       gn.enterprise_id,
       gp.project_name,
       gp.name,
       gn.name,
       gn.type,
       gn.ld_name_identify,
       'rule-extract'
FROM `db_cost_data_platform_pro`.gcdp_dws_index_project_note gn
         INNER JOIN
     `db_cost_data_platform_pro`.gcdp_dwd_contract_project gp
     ON gn.contract_project_id = gp.id;
# 初始化sys_sequence表
REPLACE INTO `db_cost_data_platform_pro`.sys_sequence (SEQUENCE_NAME, CURRENT_VALUE, INCREMENT_BY, MAX_VALUE, MIN_VALUE, CYCLE_FLAG, CREATE_TIME,
                           LAST_UPDATE_TIME)
values ('gcdp_dws_index_project_note_ld_extract_helper',
        COALESCE(CEIL((SELECT MAX(id) FROM `db_cost_data_platform_pro`.gcdp_dws_index_project_note_ld_extract_helper) / 1000.0) * 1000, 1), 1,
        9223372036854775807, 1, 0, CURRENT_TIME(),
        CURRENT_TIME());