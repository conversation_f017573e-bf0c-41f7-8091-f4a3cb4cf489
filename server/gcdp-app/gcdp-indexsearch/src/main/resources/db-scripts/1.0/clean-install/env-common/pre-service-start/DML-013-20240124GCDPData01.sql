use `db_cost_data_platform_pro`;

ALTER TABLE db_cost_data_platform_pro.gcdp_dwd_expend_sub_project
    ADD COLUMN bidnode_id BIGINT DEFAULT NULL COMMENT '工程结构id' AFTER original_id;

ALTER TABLE db_cost_data_platform_pro.gcdp_dwd_resource
    ADD COLUMN price_date DATETIME DEFAULT NULL COMMENT '报价时间' AFTER brand,
    ADD COLUMN belong_name VARCHAR(256) DEFAULT NULL COMMENT '所属名称' AFTER price_date;

REPLACE INTO db_cost_data_platform_pro.gcdp_func_dws_biz_config (id, product_source, biz_code, biz_name, biz_type, state) VALUES(809, 'cost-zbsq-web', 'biz-rcj-push', '人材机推送材料库', 'gb', 1);
REPLACE INTO db_cost_data_platform_pro.gcdp_func_dws_biz_config (id, product_source, biz_code, biz_name, biz_type, state) VALUES(808, 'cost-zbsq-web', 'biz-fbqd', '分包清单', 'zbsq-cost', 1);
REPLACE INTO db_cost_data_platform_pro.gcdp_func_dws_biz_config (id, product_source, biz_code, biz_name, biz_type, state) VALUES(807, 'cost-zbsq-web', 'biz-ja', '建安', 'zbsq-cost', 1);
ALTER TABLE db_cost_data_platform_pro.gcdp_dwd_contract_project MODIFY COLUMN template_id varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '文件模板ID';
ALTER TABLE db_cost_data_platform_pro.gcdp_dws_index_project_note MODIFY COLUMN original_template_uuid varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '初始模板uuid';


