USE `db_cost_data_platform_pro`;
# 1. 添加定额组价数据
create table `db_cost_data_platform_pro`.gcdp_dwd_norm_item_cost_detail
(
    id                  bigint    default 0                 not null comment '唯一标识'
        primary key,
    norm_id           bigint                              null comment 'gcdp_dwd_norm_item 主键',
    cost_identity       varchar(50)                         null comment '费用明细类别编码',
    cost_name           varchar(50)                         null comment '费用明细名称清单编制',
    rate                decimal(20, 5)                      null comment '单价（税前）',
    amount              decimal(20, 5)                      null comment '合价（税前）',
    contract_project_id bigint                              null comment 'gcdp_dwd_contract_project主键',
    enterprise_id       varchar(32)                         null comment '企业ID',
    tbl_create_date     timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '到该表中的时间'
)
    comment '构建定额的费用组成明细';

create index idx_norm_item_id
    on `db_cost_data_platform_pro`.gcdp_dwd_norm_item_cost_detail (norm_id);

create index contract_project_id
    on `db_cost_data_platform_pro`.gcdp_dwd_norm_item_cost_detail (contract_project_id);

# 2. 人材机添加扩展字段 价格来源 报价范围 供货方式 产地
alter table `db_cost_data_platform_pro`.gcdp_dwd_resource
    add extend_data_json json null comment '扩展json字段' after remark;

# 3. 耗量添加扩展字段 数量和消耗量字段
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem_lmmdetail
    add extend_data_json json null comment '扩展json字段' after enterprise_id;

alter table `db_cost_data_platform_pro`.gcdp_dwd_norm_item_lmmdetail
    add extend_data_json varchar(1024) null comment '扩展数据json字段' after enterprise_id;

#4. makeUp表的注释修改
alter table `db_cost_data_platform_pro`.gcdp_dwd_index_makeup
    modify makeup_type smallint null comment '组成类别 1=分部分项；2=措施项目；3=人材机 4=定额 5=费用 6=耗量 7=其他清单';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_cost_makeup
    modify type tinyint not null comment '类型 1=清单 2=措施 3=材料 4=定额 5=费用 6=耗量 7=其他';
alter table `db_cost_data_platform_pro`.gcdp_dws_index_usage_makeup
    modify type tinyint not null comment '类型 1=清单 2=措施 3=材料 4=定额 5=费用 6=耗量 7=其他';
#5. 清单数据
alter table `db_cost_data_platform_pro`.gcdp_dwd_bqitem
    modify item_source_type smallint null comment '数据来源分类1=分部分项清单；2=措施清单 3=其他清单';
#6. bidNode注释添加
alter table `db_cost_data_platform_pro`.gcdp_dwd_contract_project_bidnode
    modify node_type smallint(3) null comment '结构类型 类型（0：文件，1:单项，2：单位 3: 虚拟节点）';
