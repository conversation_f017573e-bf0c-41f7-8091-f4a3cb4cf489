USE `db_cost_data_platform_pro`;

ALTER TABLE `db_cost_data_platform_pro`.`gcdp_dwd_sub_bqitem_category`
	CHANGE COLUMN `id` `id` BIGINT(19) NOT NULL COMMENT '主键' FIRST,
    <PERSON><PERSON><PERSON> COLUMN `name` 	`name` VARCHAR(100) NULL DEFAULT NULL COMMENT '名字',
	CHANGE COLUMN `type` `type` VARCHAR(2) NULL DEFAULT NULL COMMENT '标准来源1，GEPS 2' COLLATE 'utf8_general_ci' AFTER `attribute`,
	ADD COLUMN `contract_type_code` INT NOT NULL DEFAULT 0 COMMENT '分包类型 1 劳务分包 2 专业分包' AFTER `level`,
	ADD COLUMN `order_no` INT NULL DEFAULT NULL COMMENT '排序号' AFTER `contract_type_code`,
	ADD COLUMN `enterprise_id` VARCHAR(32) NULL DEFAULT NULL COMMENT '企业ID' AFTER `order_no`,
	ADD COLUMN `original_id` VARCHAR(32) NULL DEFAULT NULL COMMENT '源id' AFTER `enterprise_id`,
	ADD COLUMN `original_pid` VARCHAR(32) NULL DEFAULT NULL COMMENT '源父id' AFTER `original_id`,
	ADD COLUMN `deleted` INT NULL DEFAULT '0' COMMENT '删除标识 1已删除 0未删除' AFTER `original_pid`,
	ADD COLUMN `tbl_create_date` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间' AFTER `deleted`,
	ADD COLUMN `tbl_update_date` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间' AFTER `tbl_create_date`,
	ADD INDEX `enterprise_id` (`enterprise_id`),
	ADD INDEX `type` (`type`),
	ADD INDEX `deleted` (`deleted`);

ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem ADD pay_type varchar(100);
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail ADD pay_type varchar(100);
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dwd_expend_sub_bqitem ADD project_scale varchar(100);
ALTER TABLE `db_cost_data_platform_pro`.gcdp_dws_sub_bqitem_index_detail ADD project_scale varchar(100);
