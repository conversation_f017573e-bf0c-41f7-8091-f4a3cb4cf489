use `db_cost_data_platform_pro`;
ALTER TABLE gcdp_dim_item_component_category_item MODIFY COLUMN item_name_path varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '科目全路径';

CREATE TABLE `gcdp_data_sync_progress` (
                                           `id` bigint NOT NULL AUTO_INCREMENT,
                                           `data_type` varchar(50) DEFAULT NULL COMMENT '同步的数据类型',
                                           `last_sync_date` datetime DEFAULT NULL COMMENT '同步完成时间',
                                           `task_status` tinyint DEFAULT '0' COMMENT '任务运行状态,0:未运行,1:运行中',
                                           `remark` varchar(50) DEFAULT NULL COMMENT '说明',
                                           `tbl_create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '添加到该表中的时间yyyy-MM-dd HH:mm:ss',
                                           PRIMARY KEY (`id`)
) COMMENT='数据中台数据同步进度表';