use `db_cost_data_platform_pro`;

UPDATE gcdp_dws_index_project_note SET project_hash = MD5(CONCAT(IFNULL(`enterprise_id`, ''), '@@', IFNULL(`project_code`, ''), '@@', IFNULL(`phase`, '')));

UPDATE gcdp_dws_index_project_note SET dt_hash = MD5(CONCAT(IFNULL(`enterprise_id`, ''), '@@', IFNULL(`project_code`, ''), '@@', IFNULL(`phase`, ''), '@@', IFNULL(`ld_name_identify`, ''), '@@', IFNULL(`build_area`, ''))) WHERE `type` = 4;
UPDATE gcdp_dws_index_project_note SET dt_hash = MD5(CONCAT(IFNULL(`enterprise_id`, ''), '@@', IFNULL(`project_code`, ''), '@@', IFNULL(`phase`, ''), '@@', IFNULL(`ld_name_identify`, ''), '@@', IFNULL(`trade_name`, ''))) WHERE `type` = 6;
UPDATE gcdp_dws_index_project_note SET dt_hash = -1 WHERE `type` = 0 AND non_construction = 1;
UPDATE gcdp_dws_index_project_note SET dt_hash = -1 WHERE `type` = 5;

UPDATE gcdp_dws_index_project_note AS note
    JOIN gcdp_dwd_contract_project_bidnode AS bidnode
    ON note.bidnode_id = bidnode.id
SET note.name = SUBSTRING(
        note.name,
        1,
        CHAR_LENGTH(note.name) - CHAR_LENGTH(bidnode.name) - 2
    )
WHERE note.`type` = 4
  AND note.name LIKE CONCAT('%--', bidnode.name);
#1.创建新表的数据结构 -- by  yanyh
CREATE TABLE gcdp_dws_index_project_note_ld_extract_helper_test_250604 LIKE gcdp_dws_index_project_note_ld_extract_helper;
#2.针对新表进行去重数据插入
INSERT INTO gcdp_dws_index_project_note_ld_extract_helper_test_250604
SELECT id, enterprise_id, project_name, engineering_name, SUBSTRING_INDEX(name, '--', 1)  as name1, type, extract_name, extract_type, tbl_create_date FROM gcdp_dws_index_project_note_ld_extract_helper group by enterprise_id,project_name, engineering_name, name1;
#3. 重命名原表 gcdp_dws_index_project_note_ld_extract_helper
RENAME TABLE gcdp_dws_index_project_note_ld_extract_helper TO back_gcdp_dws_index_project_note_ld_extract_helper_250604;
#4. 重命名新表为原表
RENAME TABLE gcdp_dws_index_project_note_ld_extract_helper_test_250604 TO gcdp_dws_index_project_note_ld_extract_helper;