use `db_cost_data_platform_pro`;

ALTER TABLE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
ADD COLUMN `contract_type_code` INT NOT NULL DEFAULT '0' COMMENT '分包类型 1 劳务分包 2 专业分包',
ADD COLUMN `order_no` INT DEFAULT NULL COMMENT '排序号',
ADD COLUMN `enterprise_id` VARCHAR(32) DEFAULT NULL COMMENT '企业ID',
ADD COLUMN `original_id` VARCHAR(32) DEFAULT NULL COMMENT '源id',
ADD COLUMN `original_pid` VARCHAR(32) DEFAULT NULL COMMENT '源父id',
ADD COLUMN `deleted` INT DEFAULT '0' COMMENT '删除标识 1已删除 0未删除',
ADD COLUMN `tbl_create_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN `tbl_update_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=1
WHERE id=1;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=2
WHERE id=5;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=3
WHERE id=8;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=4
WHERE id=14;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=5
WHERE id=21;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=6
WHERE id=25;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=7
WHERE id=39;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=8
WHERE id=41;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=9
WHERE id=45;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=10
WHERE id=47;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=11
WHERE id=58;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=12
WHERE id=61;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=13
WHERE id=76;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=14
WHERE id=84;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=15
WHERE id=97;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=16
WHERE id=112;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=17
WHERE id=121;
UPDATE db_cost_data_platform_pro.gcdp_dwd_sub_bqitem_category
set order_no=18
WHERE id=133;
