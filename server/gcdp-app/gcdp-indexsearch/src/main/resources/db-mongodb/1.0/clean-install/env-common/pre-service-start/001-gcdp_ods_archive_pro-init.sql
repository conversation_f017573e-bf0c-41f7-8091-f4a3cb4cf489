db.createCollection("gcdp_ods_build_standard_result", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_second_cost_contract", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_project", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_project_booklet", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_project_booklet_attachment", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_second_cost_template", { capped : false, autoIndexId : true });