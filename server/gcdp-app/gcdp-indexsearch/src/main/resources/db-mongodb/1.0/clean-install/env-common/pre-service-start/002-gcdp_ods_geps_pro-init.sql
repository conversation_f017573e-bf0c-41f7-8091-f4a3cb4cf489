
db.createCollection("gcdp_func_module_config", { capped : false, autoIndexId : true });
db.createCollection("gcdp_func_tenant_config", { capped : false, autoIndexId : true });
db.createCollection("gcdp_func_tenant_module_progress", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_cght_clmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_cgjsd_clmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_sphntgyht_stmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_sphntjsd_stmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_zzclzlht", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_zzclzlht_gclmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_zzclzlht_rzmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_zzclzlht_yzmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_zzclzljsd", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_zzclzljsd_gclmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_zzclzljsd_zlmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_dic_cbkm", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_dic_cl", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_dic_dy", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_dic_fyxm", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_dic_jxsb", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_dic_lwfbfl", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_dic_lwfbxm", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_dic_zyfbfl", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_dic_zyfbxm", { capped : false, autoIndexId : true });


db.createCollection("gcdp_ods_fyxm_cbkmgj", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_jx_cbkmgj", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_cl_cbkmgj", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_zyfb_cbkmgj", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_lwfb_cbkmgj", { capped : false, autoIndexId : true });


db.createCollection("gcdp_ods_fyjzd", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_fyjzd_fymx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_gcys", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_gcys_csxm", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_gcys_fbfx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_gcys_fyx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_gcys_jjcx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_gcys_qtxm", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_gcys_rcj", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_hzhb", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_jx_gzht_jxmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_jx_gzjsd_jxmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_jx_zlht", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_jx_zlht_gclmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_jx_zlht_rzmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_jx_zlht_yzmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_jx_zljsd", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_jx_zljsd_zlmx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_lwfb_js_mx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_lwfb_yjs_mx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_lwfb_ysgl", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_ys", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_ys_csxm", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_ys_fbfx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_ys_fyx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_ys_jjcx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_ys_qtxm", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_ys_ratedetail", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_ys_rcj", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_ys_rcjpb", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_zyfb_js_mx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_zyfb_yjs_mx", { capped : false, autoIndexId : true });
db.createCollection("gcdp_ods_zyfb_ysgl", { capped : false, autoIndexId : true });


db.getCollection("gcdp_ods_cl_cght_clmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_cght_clmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_cgjsd_clmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_cgjsd_clmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_sphntgyht_stmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_sphntgyht_stmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_sphntjsd_stmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_sphntjsd_stmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_gclmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_gclmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_rzmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_rzmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_yzmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_yzmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzljsd_zlmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzljsd_zlmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_ysgl").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_zyfb_ysgl").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_yjs_mx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_zyfb_yjs_mx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_js_mx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_zyfb_js_mx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_rcjpb").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_ys_rcjpb").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_rcj").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_ys_rcj").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_ratedetail").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_ys_ratedetail").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});


db.getCollection("gcdp_ods_ys_qtxm").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_ys_qtxm").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_jjcx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_ys_jjcx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_fyx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_ys_fyx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_fbfx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_ys_fbfx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_csxm").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_ys_csxm").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_ys").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_lwfb_ysgl").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_lwfb_ysgl").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_lwfb_yjs_mx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_lwfb_yjs_mx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_lwfb_js_mx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_lwfb_js_mx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zljsd_zlmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_jx_zljsd_zlmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zljsd").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_jx_zljsd").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zlht_yzmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_jx_zlht_yzmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zlht_rzmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_jx_zlht_rzmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zlht_gclmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});

db.getCollection("gcdp_ods_jx_zlht_gclmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zlht").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_jx_zlht").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_gzjsd_jxmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_jx_gzjsd_jxmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_gzht_jxmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_jx_gzht_jxmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_hzhb").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_hzhb").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_rcj").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_gcys_rcj").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_qtxm").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_gcys_qtxm").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_jjcx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_gcys_jjcx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_fyx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_gcys_fyx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_fbfx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_gcys_fbfx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_csxm").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_gcys_csxm").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_gcys").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_fyjzd_fymx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_fyjzd_fymx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_fyjzd").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_fyjzd").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_dic_dy").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_dic_dy").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzljsd_gclmx").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzljsd_gclmx").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});



db.getCollection("gcdp_ods_cl_zzclzljsd").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzljsd").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_project").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_cght").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_cght").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_cgjsd").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_cgjsd").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_sphntgyht").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_sphntgyht").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_sphntjsd").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_sphntjsd").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_gzjsd").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_jx_gzjsd").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_htdj").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_zyfb_htdj").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_yjs").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_zyfb_yjs").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});



db.getCollection("gcdp_ods_lwfb_yjs").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_lwfb_yjs").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_lwfb_js").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_lwfb_js").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_js").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_zyfb_js").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_lwfb_htdj").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_lwfb_htdj").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_gzht").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_jx_gzht").createIndex({"enterpriseId":1,"master_bill_id":1},{"name":'enterpriseId_master_bill_id',background:true});
db.getCollection("gcdp_ods_dic_clfl").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_dic_jxsbfl").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_dic_cbkm").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_dic_cl").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_dic_jxsb").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_dic_lwfbxm").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_dic_zyfbfl").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_dic_zyfbxm").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});





db.getCollection("gcdp_ods_fyxm_cbkmgj").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_jx_cbkmgj").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_cl_cbkmgj").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_zyfb_cbkmgj").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});
db.getCollection("gcdp_ods_lwfb_cbkmgj").createIndex({"enterpriseId":1,"id":1},{"name":'enterpriseId_id',background:true});






db.getCollection("gcdp_func_module_config").insert([{
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Professional.Contract.ZYFBYSBZ",
	"moduleCode": "GEPS.Professional.Contract.ZYFBYSGLModule",
	"entityCode": "GEPS.Professional.Contract.ZYFBYSBZ",
	"children": [{
		"mainEntityKey": "id",
		"mainEntityCode": "GEPS.BasicData.Budget.YS",
		"moduleCode": "GEPS.BasicData.Budget.YS",
		"entityCode": "GEPS.BasicData.Budget.YS",
		"collectionName": "gcdp_ods_ys",
		"children": [{
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.FYX",
			"collectionName": "gcdp_ods_ys_fyx",
			"children": [],
			"name": "预算-费用项",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 1.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.FBFX",
			"collectionName": "gcdp_ods_ys_fbfx",
			"children": [],
			"name": "预算-分部分项",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 2.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.CSXM",
			"collectionName": "gcdp_ods_ys_csxm",
			"children": [],
			"name": "预算-措施项目",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 3.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.RCJ",
			"collectionName": "gcdp_ods_ys_rcj",
			"children": [],
			"name": "预算-人材机",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 4.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.QTXM",
			"collectionName": "gcdp_ods_ys_qtxm",
			"children": [],
			"name": "预算-其他项目",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 5.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.JJCX",
			"collectionName": "gcdp_ods_ys_jjcx",
			"children": [],
			"name": "预算-计价程序",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 6.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.RCJPB",
			"collectionName": "gcdp_ods_ys_rcjpb",
			"children": [],
			"name": "预算-人材机配比",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 7.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.RateDetail",
			"collectionName": "gcdp_ods_ys_ratedetail",
			"children": [],
			"name": "预算-单价构成",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 8.0
		}],
		"name": "预算",
		"priKey": "id",
		"isMainEntity": true,
		"order": 3.0,
		"isDataGet": false,
		"isDeletedIdGet": true,
		"isFromParentDel": true
	}],
	"name": "专业分包-预算管理",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_zyfb_ysgl",
	"order": -8.0,
	"isDataGet": true,
	"isDeletedIdGet": true,
	"specialChildDelModel": {
		"selectKey": "YS.ID"
	}
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Professional.Contract.ZYFBHTDJ",
	"moduleCode": "GEPS.Professional.Contract.ZYFBHTDJModule",
	"entityCode": "GEPS.Professional.Contract.ZYFBHTDJ",
	"children": [],
	"name": "专业分包合同登记",
	"priKey": "id",
	"isMainEntity": true,
	"order": 2.0,
	"isDataGet": true,
	"isDeletedIdGet": true,
	"collectionName": "gcdp_ods_zyfb_htdj"
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.BasicData.Budget.YS",
	"moduleCode": "GEPS.BasicData.Budget.YS",
	"entityCode": "GEPS.BasicData.Budget.YS",
	"collectionName": "gcdp_ods_ys",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.YS",
		"moduleCode": "GEPS.BasicData.Budget.YS",
		"entityCode": "GEPS.BasicData.Budget.FYX",
		"collectionName": "gcdp_ods_ys_fyx",
		"children": [],
		"name": "预算-费用项",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 1.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.YS",
		"moduleCode": "GEPS.BasicData.Budget.YS",
		"entityCode": "GEPS.BasicData.Budget.FBFX",
		"collectionName": "gcdp_ods_ys_fbfx",
		"children": [],
		"name": "预算-分部分项",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 2.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.YS",
		"moduleCode": "GEPS.BasicData.Budget.YS",
		"entityCode": "GEPS.BasicData.Budget.CSXM",
		"collectionName": "gcdp_ods_ys_csxm",
		"children": [],
		"name": "预算-措施项目",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 3.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.YS",
		"moduleCode": "GEPS.BasicData.Budget.YS",
		"entityCode": "GEPS.BasicData.Budget.RCJ",
		"collectionName": "gcdp_ods_ys_rcj",
		"children": [],
		"name": "预算-人材机",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 4.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.YS",
		"moduleCode": "GEPS.BasicData.Budget.YS",
		"entityCode": "GEPS.BasicData.Budget.QTXM",
		"collectionName": "gcdp_ods_ys_qtxm",
		"children": [],
		"name": "预算-其他项目",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 5.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.YS",
		"moduleCode": "GEPS.BasicData.Budget.YS",
		"entityCode": "GEPS.BasicData.Budget.JJCX",
		"collectionName": "gcdp_ods_ys_jjcx",
		"children": [],
		"name": "预算-计价程序",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 6.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.YS",
		"moduleCode": "GEPS.BasicData.Budget.YS",
		"entityCode": "GEPS.BasicData.Budget.RCJPB",
		"collectionName": "gcdp_ods_ys_rcjpb",
		"children": [],
		"name": "预算-人材机配比",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 7.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.YS",
		"moduleCode": "GEPS.BasicData.Budget.YS",
		"entityCode": "GEPS.BasicData.Budget.RateDetail",
		"collectionName": "gcdp_ods_ys_ratedetail",
		"children": [],
		"name": "预算-单价构成",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 8.0
	}],
	"name": "预算",
	"priKey": "id",
	"isMainEntity": true,
	"order": -5.0,
	"isDataGet": true,
	"isDeletedIdGet": false
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Labor.Contract.LWFBHTDJ",
	"moduleCode": "GEPS.Labor.Contract.LWFBHTDJModule",
	"entityCode": "GEPS.Labor.Contract.LWFBHTDJ",
	"children": [],
	"name": "劳务分包-合同登记",
	"priKey": "id",
	"isMainEntity": true,
	"order": 1.0,
	"isDataGet": true,
	"isDeletedIdGet": true,
	"collectionName": "gcdp_ods_lwfb_htdj"
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Labor.Contract.LWFBYSBZ",
	"moduleCode": "GEPS.Labor.Contract.LWFBYSGLModule",
	"entityCode": "GEPS.Labor.Contract.LWFBYSBZ",
	"children": [{
		"mainEntityKey": "id",
		"mainEntityCode": "GEPS.BasicData.Budget.YS",
		"moduleCode": "GEPS.BasicData.Budget.YS",
		"entityCode": "GEPS.BasicData.Budget.YS",
		"collectionName": "gcdp_ods_ys",
		"children": [{
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.FYX",
			"collectionName": "gcdp_ods_ys_fyx",
			"children": [],
			"name": "预算-费用项",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 1.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.FBFX",
			"collectionName": "gcdp_ods_ys_fbfx",
			"children": [],
			"name": "预算-分部分项",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 2.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.CSXM",
			"collectionName": "gcdp_ods_ys_csxm",
			"children": [],
			"name": "预算-措施项目",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 3.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.RCJ",
			"collectionName": "gcdp_ods_ys_rcj",
			"children": [],
			"name": "预算-人材机",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 4.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.QTXM",
			"collectionName": "gcdp_ods_ys_qtxm",
			"children": [],
			"name": "预算-其他项目",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 5.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.JJCX",
			"collectionName": "gcdp_ods_ys_jjcx",
			"children": [],
			"name": "预算-计价程序",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 6.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.RCJPB",
			"collectionName": "gcdp_ods_ys_rcjpb",
			"children": [],
			"name": "预算-人材机配比",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 7.0
		}, {
			"mainEntityKey": "master_bill_id",
			"mainEntityCode": "GEPS.BasicData.Budget.YS",
			"moduleCode": "GEPS.BasicData.Budget.YS",
			"entityCode": "GEPS.BasicData.Budget.RateDetail",
			"collectionName": "gcdp_ods_ys_ratedetail",
			"children": [],
			"name": "预算-单价构成",
			"priKey": "id",
			"isMainEntity": false,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"order": 8.0
		}],
		"name": "预算",
		"priKey": "id",
		"isMainEntity": true,
		"order": 3.0,
		"isDataGet": false,
		"isDeletedIdGet": true,
		"isFromParentDel": true
	}],
	"name": "劳务分包-预算管理",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_lwfb_ysgl",
	"order": -7.0,
	"isDataGet": true,
	"isDeletedIdGet": true,
	"specialChildDelModel": {
		"selectKey": "YS.ID"
	}
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
	"moduleCode": "GEPS.BasicData.Budget.GCYS",
	"entityCode": "GEPS.BasicData.Budget.GCYS",
	"collectionName": "gcdp_ods_gcys",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
		"moduleCode": "GEPS.BasicData.Budget.GCYS",
		"entityCode": "GEPS.BasicData.Budget.GCYS_FYX",
		"collectionName": "gcdp_ods_gcys_fyx",
		"children": [],
		"name": "过程预算-费用项",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 1.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
		"moduleCode": "GEPS.BasicData.Budget.GCYS",
		"entityCode": "GEPS.BasicData.Budget.GCYS_FBFX",
		"collectionName": "gcdp_ods_gcys_fbfx",
		"children": [],
		"name": "过程预算-分部分项",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 2.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
		"moduleCode": "GEPS.BasicData.Budget.GCYS",
		"entityCode": "GEPS.BasicData.Budget.GCYS_CSXM",
		"collectionName": "gcdp_ods_gcys_csxm",
		"children": [],
		"name": "过程预算-措施项目",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 3.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
		"moduleCode": "GEPS.BasicData.Budget.GCYS",
		"entityCode": "GEPS.BasicData.Budget.GCYS_RCJ",
		"collectionName": "gcdp_ods_gcys_rcj",
		"children": [],
		"name": "过程预算-人材机",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 4.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
		"moduleCode": "GEPS.BasicData.Budget.GCYS",
		"entityCode": "GEPS.BasicData.Budget.GCYS_QTXM",
		"collectionName": "gcdp_ods_gcys_qtxm",
		"children": [],
		"name": "过程预算-其他项目",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 5.0
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
		"moduleCode": "GEPS.BasicData.Budget.GCYS",
		"entityCode": "GEPS.BasicData.Budget.GCYS_JJCX",
		"collectionName": "gcdp_ods_gcys_jjcx",
		"children": [],
		"name": "过程预算-计价程序",
		"priKey": "id",
		"isMainEntity": false,
		"isDataGet": true,
		"isDeletedIdGet": false,
		"order": 6.0
	}],
	"name": "过程预算",
	"priKey": "id",
	"isMainEntity": true,
	"order": -6.0,
	"isDataGet": true,
	"isDeletedIdGet": false
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Professional.Contract.ZYFBYJS",
	"moduleCode": "GEPS.Professional.Contract.ZYFBYJSModule",
	"entityCode": "GEPS.Professional.Contract.ZYFBYJS",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Professional.Contract.ZYFBYJS",
		"moduleCode": "GEPS.Professional.Contract.ZYFBYJSModule",
		"entityCode": "GEPS.Professional.Contract.YJSYSMX",
		"children": [{
			"mainEntityKey": "id",
			"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
			"moduleCode": "GEPS.BasicData.Budget.GCYS",
			"entityCode": "GEPS.BasicData.Budget.GCYS",
			"collectionName": "gcdp_ods_gcys",
			"children": [{
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_FYX",
				"collectionName": "gcdp_ods_gcys_fyx",
				"children": [],
				"name": "过程预算-费用项",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 1.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_FBFX",
				"collectionName": "gcdp_ods_gcys_fbfx",
				"children": [],
				"name": "过程预算-分部分项",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 2.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_CSXM",
				"collectionName": "gcdp_ods_gcys_csxm",
				"children": [],
				"name": "过程预算-措施项目",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 3.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_RCJ",
				"collectionName": "gcdp_ods_gcys_rcj",
				"children": [],
				"name": "过程预算-人材机",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 4.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_QTXM",
				"collectionName": "gcdp_ods_gcys_qtxm",
				"children": [],
				"name": "过程预算-其他项目",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 5.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_JJCX",
				"collectionName": "gcdp_ods_gcys_jjcx",
				"children": [],
				"name": "过程预算-计价程序",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 6.0
			}],
			"name": "过程预算",
			"priKey": "id",
			"isMainEntity": true,
			"order": 6.0,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"isFromParentDel": true
		}],
		"name": "专业分包-预结算-明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_zyfb_yjs_mx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true,
		"specialChildDelModel": {
			"selectKey": "GCYS.ID"
		}
	}],
	"name": "专业分包-预结算",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_zyfb_yjs",
	"order": 7.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Labor.Contract.LWFBYJS",
	"moduleCode": "GEPS.Labor.Contract.LWFBYJSModule",
	"entityCode": "GEPS.Labor.Contract.LWFBYJS",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Labor.Contract.LWFBYJS",
		"moduleCode": "GEPS.Labor.Contract.LWFBYJSModule",
		"entityCode": "GEPS.Labor.Contract.YJSYSMX",
		"children": [{
			"mainEntityKey": "id",
			"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
			"moduleCode": "GEPS.BasicData.Budget.GCYS",
			"entityCode": "GEPS.BasicData.Budget.GCYS",
			"collectionName": "gcdp_ods_gcys",
			"children": [{
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_FYX",
				"collectionName": "gcdp_ods_gcys_fyx",
				"children": [],
				"name": "过程预算-费用项",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 1.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_FBFX",
				"collectionName": "gcdp_ods_gcys_fbfx",
				"children": [],
				"name": "过程预算-分部分项",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 2.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_CSXM",
				"collectionName": "gcdp_ods_gcys_csxm",
				"children": [],
				"name": "过程预算-措施项目",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 3.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_RCJ",
				"collectionName": "gcdp_ods_gcys_rcj",
				"children": [],
				"name": "过程预算-人材机",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 4.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_QTXM",
				"collectionName": "gcdp_ods_gcys_qtxm",
				"children": [],
				"name": "过程预算-其他项目",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 5.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_JJCX",
				"collectionName": "gcdp_ods_gcys_jjcx",
				"children": [],
				"name": "过程预算-计价程序",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 6.0
			}],
			"name": "过程预算",
			"priKey": "id",
			"isMainEntity": true,
			"order": 6.0,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"isFromParentDel": true
		}],
		"name": "；劳务分包-预结算-明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_lwfb_yjs_mx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true,
		"specialChildDelModel": {
			"selectKey": "GCYS.ID"
		}
	}],
	"name": "劳务分包-预结算",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_lwfb_yjs",
	"order": 8.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Professional.Contract.ZYFBJS",
	"moduleCode": "GEPS.Professional.Contract.ZYFBJSModule",
	"entityCode": "GEPS.Professional.Contract.ZYFBJS",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Professional.Contract.ZYFBJS",
		"moduleCode": "GEPS.Professional.Contract.ZYFBJSModule",
		"entityCode": "GEPS.Professional.Contract.JSYSMX",
		"children": [{
			"mainEntityKey": "id",
			"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
			"moduleCode": "GEPS.BasicData.Budget.GCYS",
			"entityCode": "GEPS.BasicData.Budget.GCYS",
			"collectionName": "gcdp_ods_gcys",
			"children": [{
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_FYX",
				"collectionName": "gcdp_ods_gcys_fyx",
				"children": [],
				"name": "过程预算-费用项",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 1.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_FBFX",
				"collectionName": "gcdp_ods_gcys_fbfx",
				"children": [],
				"name": "过程预算-分部分项",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 2.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_CSXM",
				"collectionName": "gcdp_ods_gcys_csxm",
				"children": [],
				"name": "过程预算-措施项目",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 3.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_RCJ",
				"collectionName": "gcdp_ods_gcys_rcj",
				"children": [],
				"name": "过程预算-人材机",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 4.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_QTXM",
				"collectionName": "gcdp_ods_gcys_qtxm",
				"children": [],
				"name": "过程预算-其他项目",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 5.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_JJCX",
				"collectionName": "gcdp_ods_gcys_jjcx",
				"children": [],
				"name": "过程预算-计价程序",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 6.0
			}],
			"name": "过程预算",
			"priKey": "id",
			"isMainEntity": true,
			"order": 6.0,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"isFromParentDel": true
		}],
		"name": "专业分包-结算-明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_zyfb_js_mx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true,
		"specialChildDelModel": {
			"selectKey": "GCYS.ID"
		}
	}],
	"name": "专业分包-结算",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_zyfb_js",
	"order": 9.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Labor.Contract.LWFBJS",
	"moduleCode": "GEPS.Labor.Contract.LWFBJSModule",
	"entityCode": "GEPS.Labor.Contract.LWFBJS",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Labor.Contract.LWFBJS",
		"moduleCode": "GEPS.Labor.Contract.LWFBJSModule",
		"entityCode": "GEPS.Labor.Contract.JSYSMX",
		"children": [{
			"mainEntityKey": "id",
			"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
			"moduleCode": "GEPS.BasicData.Budget.GCYS",
			"entityCode": "GEPS.BasicData.Budget.GCYS",
			"collectionName": "gcdp_ods_gcys",
			"children": [{
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_FYX",
				"collectionName": "gcdp_ods_gcys_fyx",
				"children": [],
				"name": "过程预算-费用项",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 1.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_FBFX",
				"collectionName": "gcdp_ods_gcys_fbfx",
				"children": [],
				"name": "过程预算-分部分项",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 2.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_CSXM",
				"collectionName": "gcdp_ods_gcys_csxm",
				"children": [],
				"name": "过程预算-措施项目",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 3.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_RCJ",
				"collectionName": "gcdp_ods_gcys_rcj",
				"children": [],
				"name": "过程预算-人材机",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 4.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_QTXM",
				"collectionName": "gcdp_ods_gcys_qtxm",
				"children": [],
				"name": "过程预算-其他项目",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 5.0
			}, {
				"mainEntityKey": "master_bill_id",
				"mainEntityCode": "GEPS.BasicData.Budget.GCYS",
				"moduleCode": "GEPS.BasicData.Budget.GCYS",
				"entityCode": "GEPS.BasicData.Budget.GCYS_JJCX",
				"collectionName": "gcdp_ods_gcys_jjcx",
				"children": [],
				"name": "过程预算-计价程序",
				"priKey": "id",
				"isMainEntity": false,
				"isDataGet": false,
				"isDeletedIdGet": true,
				"order": 6.0
			}],
			"name": "过程预算",
			"priKey": "id",
			"isMainEntity": true,
			"order": 6.0,
			"isDataGet": false,
			"isDeletedIdGet": true,
			"isFromParentDel": true
		}],
		"name": "劳务分包-结算-明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_lwfb_js_mx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true,
		"specialChildDelModel": {
			"selectKey": "GCYS.ID"
		}
	}],
	"name": "劳务分包-结算",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_lwfb_js",
	"order": 10.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Material.Purchase.CLCGHT",
	"moduleCode": "GEPS.Material.Purchase.CLCGHTModule",
	"entityCode": "GEPS.Material.Purchase.CLCGHT",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Material.Purchase.CLCGHT",
		"moduleCode": "GEPS.Material.Purchase.CLCGHTModule",
		"entityCode": "GEPS.Material.Purchase.CLCGHTCLMX",
		"children": [],
		"name": "材料-采购合同-材料明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_cl_cght_clmx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}],
	"name": "材料-采购合同",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_cl_cght",
	"order": 11.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Material.Purchase.CLCGJSD",
	"moduleCode": "GEPS.Material.Purchase.CLCGJSDModule",
	"entityCode": "GEPS.Material.Purchase.CLCGJSD",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Material.Purchase.CLCGJSD",
		"moduleCode": "GEPS.Material.Purchase.CLCGJSDModule",
		"entityCode": "GEPS.Material.Purchase.JSDCLMX",
		"children": [],
		"name": "材料-采购结算单-材料明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_cl_cgjsd_clmx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}],
	"name": "材料-采购结算单",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_cl_cgjsd",
	"order": 12.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Material.Concrete.SPHNTGYHT",
	"moduleCode": "GEPS.Material.Concrete.SPHNTGYHTModule",
	"entityCode": "GEPS.Material.Concrete.SPHNTGYHT",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Material.Concrete.SPHNTGYHT",
		"moduleCode": "GEPS.Material.Concrete.SPHNTGYHTModule",
		"entityCode": "GEPS.Material.Concrete.SPHNTGYHTSTMX",
		"children": [],
		"name": "材料-商品混凝土供应合同-商砼明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_cl_sphntgyht_stmx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}],
	"name": "材料-商品混凝土供应合同",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_cl_sphntgyht",
	"order": 13.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Material.Concrete.SPHNTJSD",
	"moduleCode": "GEPS.Material.Concrete.SPHNTJSDModule",
	"entityCode": "GEPS.Material.Concrete.SPHNTJSD",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Material.Concrete.SPHNTJSD",
		"moduleCode": "GEPS.Material.Concrete.SPHNTJSDModule",
		"entityCode": "GEPS.Material.Concrete.SPHNTJSDSTMX",
		"children": [],
		"name": "材料-商品混凝土结算单-商砼明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_cl_sphntjsd_stmx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}],
	"name": "材料-商品混凝土结算单",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_cl_sphntjsd",
	"order": 14.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Material.Leasing.ZZCLZLHT",
	"moduleCode": "GEPS.Material.Leasing.ZZCLZLHTModule",
	"entityCode": "GEPS.Material.Leasing.ZZCLZLHT",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Material.Leasing.ZZCLZLHT",
		"moduleCode": "GEPS.Material.Leasing.ZZCLZLHTModule",
		"entityCode": "GEPS.Material.Leasing.ZZCLZLHT_RZMX",
		"children": [],
		"name": "材料-周转材料租赁合同-日租明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_cl_zzclzlht_rzmx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Material.Leasing.ZZCLZLHT",
		"moduleCode": "GEPS.Material.Leasing.ZZCLZLHTModule",
		"entityCode": "GEPS.Material.Leasing.ZZCLZLHT_YZMX",
		"children": [],
		"name": "材料-周转材料租赁合同-月租明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_cl_zzclzlht_yzmx",
		"order": 2.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Material.Leasing.ZZCLZLHT",
		"moduleCode": "GEPS.Material.Leasing.ZZCLZLHTModule",
		"entityCode": "GEPS.Material.Leasing.ZZCLZLHT_GCLMX",
		"children": [],
		"name": "材料-周转材料租赁合同-工程量明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_cl_zzclzlht_gclmx",
		"order": 3.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}],
	"name": "材料-周转材料租赁合同",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_cl_zzclzlht",
	"order": 15.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Material.Leasing.ZLZZCLJSD",
	"moduleCode": "GEPS.Material.Leasing.ZLZZCLJSDModule",
	"entityCode": "GEPS.Material.Leasing.ZLZZCLJSD",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Material.Leasing.ZLZZCLJSD",
		"moduleCode": "GEPS.Material.Leasing.ZLZZCLJSDModule",
		"entityCode": "GEPS.Material.Leasing.ZLZZCLJSD_ZLMX",
		"children": [],
		"name": "材料-周转材料租赁结算单-租赁明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_cl_zzclzljsd_zlmx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Material.Leasing.ZLZZCLJSD",
		"moduleCode": "GEPS.Material.Leasing.ZLZZCLJSDModule",
		"entityCode": "GEPS.Material.Leasing.ZLZZCLJSD_GCLMX",
		"children": [],
		"name": "材料-周转材料租赁结算单-工程量明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_cl_zzclzljsd_gclmx",
		"order": 2.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}],
	"name": "材料-周转材料租赁结算单",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_cl_zzclzljsd",
	"order": 16.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Equipment.Purchase.JXSBGZHT",
	"moduleCode": "GEPS.Equipment.Purchase.JXSBGZHTModule",
	"entityCode": "GEPS.Equipment.Purchase.JXSBGZHT",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Equipment.Purchase.JXSBGZHT",
		"moduleCode": "GEPS.Equipment.Purchase.JXSBGZHTModule",
		"entityCode": "GEPS.Equipment.Purchase.JXSBGZHTJXSBMX",
		"children": [],
		"name": "机械-机械设备购置合同-机械设备明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_jx_gzht_jxmx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}],
	"name": "机械-机械设备购置合同",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_jx_gzht",
	"order": 17.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Equipment.Purchase.JXSBGZJSD",
	"moduleCode": "GEPS.Equipment.Purchase.JXSBGZJSDModule",
	"entityCode": "GEPS.Equipment.Purchase.JXSBGZJSD",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Equipment.Purchase.JXSBGZJSD",
		"moduleCode": "GEPS.Equipment.Purchase.JXSBGZJSDModule",
		"entityCode": "GEPS.Equipment.Purchase.JXSBGZJSDJXMX",
		"children": [],
		"name": "机械-机械设备购置结算单-机械设备明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_jx_gzjsd_jxmx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}],
	"name": "机械-机械设备购置结算单",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_jx_gzjsd",
	"order": 18.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Equipment.Leasing.JXSBZLHT",
	"moduleCode": "GEPS.Equipment.Leasing.JXSBZLHTModule",
	"entityCode": "GEPS.Equipment.Leasing.JXSBZLHT",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Equipment.Leasing.JXSBZLHT",
		"moduleCode": "GEPS.Equipment.Leasing.JXSBZLHTModule",
		"entityCode": "GEPS.Equipment.Leasing.JXSBZLHT_RZMX",
		"children": [],
		"name": "机械-机械设备租赁合同-日租明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_jx_zlht_rzmx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Equipment.Leasing.JXSBZLHT",
		"moduleCode": "GEPS.Equipment.Leasing.JXSBZLHTModule",
		"entityCode": "GEPS.Equipment.Leasing.JXSBZLHT_YZMX",
		"children": [],
		"name": "机械-机械设备租赁合同-月租明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_jx_zlht_yzmx",
		"order": 2.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}, {
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Equipment.Leasing.JXSBZLHT",
		"moduleCode": "GEPS.Equipment.Leasing.JXSBZLHTModule",
		"entityCode": "GEPS.Equipment.Leasing.JXSBZLHT_GCLMX",
		"children": [],
		"name": "机械-机械设备租赁合同-工程量明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_jx_zlht_gclmx",
		"order": 3.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}],
	"name": "机械-机械设备租赁合同",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_jx_zlht",
	"order": 19.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Equipment.Leasing.JXSBZLJSD",
	"moduleCode": "GEPS.Equipment.Leasing.JXSBZLJSDModule",
	"entityCode": "GEPS.Equipment.Leasing.JXSBZLJSD",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Equipment.Leasing.JXSBZLJSD",
		"moduleCode": "GEPS.Equipment.Leasing.JXSBZLJSDModule",
		"entityCode": "GEPS.Equipment.Leasing.JXSBZLJSDZLMX",
		"children": [],
		"name": "机械-机械设备租赁结算单-租赁明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_jx_zljsd_zlmx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}],
	"name": "机械-机械设备租赁结算单",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_jx_zljsd",
	"order": 20.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Capital.QTCBGL.FYJZD",
	"moduleCode": "GEPS.Capital.QTCBGL.FYJZDModule",
	"entityCode": "GEPS.Capital.QTCBGL.FYJZD",
	"children": [{
		"mainEntityKey": "master_bill_id",
		"mainEntityCode": "GEPS.Capital.QTCBGL.FYJZD",
		"moduleCode": "GEPS.Capital.QTCBGL.FYJZDModule",
		"entityCode": "GEPS.Capital.QTCBGL.FYMX",
		"children": [],
		"name": "费用记账单-费用明细",
		"priKey": "id",
		"isMainEntity": false,
		"collectionName": "gcdp_ods_fyjzd_fymx",
		"order": 1.0,
		"isDataGet": true,
		"isDeletedIdGet": true
	}],
	"name": "费用记账单",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_fyjzd",
	"order": 21.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Labor.Partner.LWFBSML",
	"moduleCode": "GEPS.Labor.Partner.LWFBSMLModule",
	"entityCode": "GEPS.Labor.Partner.LWFBSML",
	"children": [],
	"name": "劳务分包商名录",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_hzhb",
	"order": -4.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Professional.Partner.ZYFBSML",
	"moduleCode": "GEPS.Professional.Partner.ZYFBSMLModule",
	"entityCode": "GEPS.Professional.Partner.ZYFBSML",
	"children": [],
	"name": "专业分包商名录",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_hzhb",
	"order": -3.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Material.Partner.WZGYSML",
	"moduleCode": "GEPS.Material.Partner.WZGYSMLModule",
	"entityCode": "GEPS.Material.Partner.WZGYSML",
	"children": [],
	"name": "物资供应商名录",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_hzhb",
	"order": -2.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.Equipment.Partner.JXSBGYSML",
	"moduleCode": "GEPS.Equipment.Partner.JXSBGYSMLModule",
	"entityCode": "GEPS.Equipment.Partner.JXSBGYSML",
	"children": [],
	"name": "机械设备供应商名录",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_hzhb",
	"order": -1.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}, {
	"mainEntityKey": "id",
	"mainEntityCode": "GEPS.BasicData.DYZD",
	"moduleCode": "GEPS.BasicData.DYZDModule",
	"entityCode": "GEPS.BasicData.DYZD",
	"children": [],
	"name": "地域字典",
	"priKey": "id",
	"isMainEntity": true,
	"collectionName": "gcdp_ods_dic_dy",
	"order": -9.0,
	"isDataGet": true,
	"isDeletedIdGet": true
}]);


db.getCollection("gcdp_func_module_config").insert([
{
    "mainEntityKey" : "id",
    "mainEntityCode" : "GEPS.Capital.QTCBGL.FYXMZDCBKMGJ",
    "moduleCode" : "GEPS.Capital.QTCBGL.FYXMZDCBKMGJModule",
    "entityCode" : "GEPS.Capital.QTCBGL.FYXMZDCBKMGJ",
    "children" : [

    ],
    "name" : "费用项目-成本科目挂接",
    "priKey" : "id",
    "isMainEntity" : true,
    "collectionName" : "gcdp_ods_fyxm_cbkmgj",
    "order" : -14.0,
    "isDataGet" : true,
    "isDeletedIdGet" : true
},
{
    "mainEntityKey" : "id",
    "mainEntityCode" : "GEPS.Professional.Cost.ZYFBXMZDCBKMGJ",
    "moduleCode" : "GEPS.Professional.Cost.ZYFBXMZDCBKMGJModule",
    "entityCode" : "GEPS.Professional.Cost.ZYFBXMZDCBKMGJ",
    "children" : [

    ],
    "name" : "专业分包成本科目挂接",
    "priKey" : "id",
    "isMainEntity" : true,
    "collectionName" : "gcdp_ods_zyfb_cbkmgj",
    "order" : -11.0,
    "isDataGet" : true,
    "isDeletedIdGet" : true
},
{
    "mainEntityKey" : "id",
    "mainEntityCode" : "GEPS.Material.Cost.CLZDCBKMGJ",
    "moduleCode" : "GEPS.Material.Cost.CLZDCBKMGJModule",
    "entityCode" : "GEPS.Material.Cost.CLZDCBKMGJ",
    "children" : [

    ],
    "name" : "材料成本科目挂接",
    "priKey" : "id",
    "isMainEntity" : true,
    "collectionName" : "gcdp_ods_cl_cbkmgj",
    "order" : -12.0,
    "isDataGet" : true,
    "isDeletedIdGet" : true
},
{
    "mainEntityKey" : "id",
    "mainEntityCode" : "GEPS.Equipment.Cost.JXSBZDCBKMGJ",
    "moduleCode" : "GEPS.Equipment.Cost.JXSBZDCBKMGJModule",
    "entityCode" : "GEPS.Equipment.Cost.JXSBZDCBKMGJ",
    "children" : [

    ],
    "name" : "机械设备成本科目挂接",
    "priKey" : "id",
    "isMainEntity" : true,
    "collectionName" : "gcdp_ods_jx_cbkmgj",
    "order" : -13.0,
    "isDataGet" : true,
    "isDeletedIdGet" : true
},
{
    "mainEntityKey" : "id",
    "mainEntityCode" : "GEPS.Labor.Cost.LWFBXMZDCBKMGJ",
    "moduleCode" : "GEPS.Labor.Cost.LWFBXMZDCBKMGJModule",
    "entityCode" : "GEPS.Labor.Cost.LWFBXMZDCBKMGJ",
    "children" : [

    ],
    "name" : "劳务分包成本科目挂接",
    "priKey" : "id",
    "isMainEntity" : true,
    "collectionName" : "gcdp_ods_lwfb_cbkmgj",
    "order" : -10.0,
    "isDataGet" : true,
    "isDeletedIdGet" : true
},
{
    "mainEntityKey" : "id",
    "mainEntityCode" : "GEPS.BasicData.ContractCategory",
    "moduleCode" : "GEPS.BasicData.ContractCategory",
    "entityCode" : "GEPS.BasicData.ContractCategory",
    "children" : [

    ],
    "name" : "合同分类字典",
    "priKey" : "id",
    "isMainEntity" : true,
    "collectionName" : "gcdp_ods_dic_htfl",
    "order" : -15.0,
    "isDataGet" : true,
    "isDeletedIdGet" : false
}
]);




db.getCollection("gcdp_ods_cl_cght_clmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_cght_clmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_cgjsd_clmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_cgjsd_clmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_sphntgyht_stmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_sphntgyht_stmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_sphntjsd_stmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_sphntjsd_stmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_gclmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_gclmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_rzmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_rzmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_yzmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzlht_yzmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzljsd_zlmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzljsd_zlmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_ysgl").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_zyfb_ysgl").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_yjs_mx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_zyfb_yjs_mx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_js_mx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_zyfb_js_mx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_rcjpb").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_ys_rcjpb").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_rcj").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_ys_rcj").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_ratedetail").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_ys_ratedetail").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});


db.getCollection("gcdp_ods_ys_qtxm").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_ys_qtxm").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_jjcx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_ys_jjcx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_fyx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_ys_fyx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_fbfx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_ys_fbfx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys_csxm").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_ys_csxm").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_ys").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_ys").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_lwfb_ysgl").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_lwfb_ysgl").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_lwfb_yjs_mx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_lwfb_yjs_mx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_lwfb_js_mx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_lwfb_js_mx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zljsd_zlmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_jx_zljsd_zlmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zljsd").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_jx_zljsd").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zlht_yzmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_jx_zlht_yzmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zlht_rzmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_jx_zlht_rzmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zlht_gclmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});

db.getCollection("gcdp_ods_jx_zlht_gclmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_zlht").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_jx_zlht").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_gzjsd_jxmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_jx_gzjsd_jxmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_gzht_jxmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_jx_gzht_jxmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_hzhb").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_hzhb").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_rcj").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_gcys_rcj").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_qtxm").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_gcys_qtxm").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_jjcx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_gcys_jjcx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_fyx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_gcys_fyx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_fbfx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_gcys_fbfx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys_csxm").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_gcys_csxm").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_gcys").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_gcys").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});



db.getCollection("gcdp_ods_fyjzd_fymx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_fyjzd_fymx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_fyjzd").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_fyjzd").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_dic_dy").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_dic_dy").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzljsd_gclmx").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzljsd_gclmx").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});

db.getCollection("gcdp_ods_cl_zzclzljsd").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_zzclzljsd").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_project").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_cght").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_cght").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_cgjsd").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_cgjsd").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_sphntgyht").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_sphntgyht").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_cl_sphntjsd").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_sphntjsd").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_gzjsd").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_jx_gzjsd").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_htdj").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_zyfb_htdj").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_yjs").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_zyfb_yjs").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});



db.getCollection("gcdp_ods_lwfb_yjs").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_lwfb_yjs").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_lwfb_js").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_lwfb_js").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_zyfb_js").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_zyfb_js").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_lwfb_htdj").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_lwfb_htdj").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_jx_gzht").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_jx_gzht").createIndex({"tenantId":1,"master_bill_id":1},{"name":'tenantId_master_bill_id',background:true});
db.getCollection("gcdp_ods_dic_clfl").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_dic_jxsbfl").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_dic_cbkm").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_dic_cl").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_dic_jxsb").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_dic_lwfbxm").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_dic_zyfbfl").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_dic_zyfbxm").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});

db.getCollection("gcdp_ods_fyxm_cbkmgj").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_jx_cbkmgj").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_cl_cbkmgj").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_zyfb_cbkmgj").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});
db.getCollection("gcdp_ods_lwfb_cbkmgj").createIndex({"tenantId":1,"id":1},{"name":'tenantId_id',background:true});






