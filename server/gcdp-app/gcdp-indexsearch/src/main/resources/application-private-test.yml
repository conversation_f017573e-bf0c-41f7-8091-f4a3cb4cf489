ECS_REDIS_DB: 4
ECS_REDIS_HOST: r-2zeuj2nv9snsm3wgzy.redis.rds.aliyuncs.com
ECS_REDIS_PASSWORD: ZeUdcqK3jijiacost
ECS_REDIS_PORT: 6379
MYSQL_HOST: jdbc:mysql://${mysql.url}/db_cost_data_platform_pro?serverTimezone=Asia/Shanghai&characterEncoding=utf-8&useSSL=false&zeroDateTimeBehavior=CONVERT_TO_NULL
MYSQL_PASSWORD: gcdp0Dk7wmv4Xx
MYSQL_USERNAME: gcdp_admin
apiAuth:
  appKey: kqIUvqfcH7obwsZRx4U3L3yG5M3sLuRL
  g-signature: F864378123E9BA92E14E6C7862257FCC
  url: https://api-auth.glodon.com
config:
  depend:
    accountGlodonServiceKey: MiOWRrW6hYBmW6xQE8OGRH2f9I5wfxUG
    accountGlodonserverSecret: vG23xiwByuRxKdhAehgKaDEK89jyKzsC
dcost:
  sub:
    url: ${dcost.sub.domain}
logging:
  level:
    com:
      glodon: debug
    org:
      mybatis: info
      spring: info
    root: info
mybatis:
  configuration:
    map-underscore-to-camel-case: true
  log-timeout-sql: true
  mapper-locations: classpath:mapper/**/*Mapper.xml
  timeout: 0
object-storage:
  accessKeyId: ${oss.accessKeyId}
  accessKeySecret: ${oss.accessKey}
  bucket-name: ${oss.pubBucketName}
  bucketName: ${oss.pubBucketName}
  connectionTimeout: 3000
  enable: true
  end-point: ${oss.endPoint}
  endPoint: https://oss-cn-beijing-internal.aliyuncs.com
  expiredTime: 60000
  external-endpoint: ${oss.externalEndpoint}
  externalEndpoint: https://oss-cn-beijing.aliyuncs.com
  maxConnections: 5000
  path-style-access: ${oss.pathStyleAccess}
  pathStyleAccess: ${oss.pathStyleAccess}
  protocol: ${oss.protocol}
  region: beijing
  socketTimeout: 3000
  timeout: 60000
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
privateDeployment: true
sendIoLog: false
sequence:
  defaultCacheSize: 10000
  defaultLongIdStartValue: 1
server:
  max-http-header-size: 104857600
  port: 5566
  servlet:
    context-path: /gcdp-index-search
spring:
  datasource:
    dynamic:
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          password: ${mysql.password}
          type: com.alibaba.druid.pool.DruidDataSource
          url: ${MYSQL_HOST}
          username: ${mysql.username}
      primary: master
      strict: false
  redis:
    database: ${ECS_REDIS_DB}
    enable: true
    host: redis-0.redis.public-service
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
    password: c&HR@R*vZXAx63n*
    port: 6379
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
swagger:
  enable: false
