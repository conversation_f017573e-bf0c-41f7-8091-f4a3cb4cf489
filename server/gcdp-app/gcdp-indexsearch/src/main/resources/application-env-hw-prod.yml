MYSQL_HOST: jdbc:mysql://${mysql.url}/${mysql.database}?serverTimezone=Asia/Shanghai&characterEncoding=utf-8&useSSL=false&zeroDateTimeBehavior=CONVERT_TO_NULL
MYSQL_USERNAME: ${mysql.username}
MYSQL_PASSWORD: ${mysql.password}

ECS_REDIS_HOST: ${redis.host}
ECS_REDIS_PORT: ${redis.port}
ECS_REDIS_PASSWORD: ${redis.password}
ECS_REDIS_DB: 4

spring:
 config:
  import:
   - classpath:application-gcdp-common.yml
   - classpath:application-gcdp-common-hw-prod.yml
 mvc:
  pathmatch:
   matching-strategy: ant_path_matcher

swagger:
 enable: false

server:
 servlet:
  context-path: /gcdp-index-search
 # byte
 max-http-header-size: 307200
#日志配置
logging:
 level:
  root: info
  com.glodon: debug
  org.spring: info
  org.mybatis: info

mybatis:
 log-timeout-sql: true
 timeout: 0

sequence:
 defaultCacheSize: 10000
 defaultLongIdStartValue: 1

# 授权中心服务
apiAuth:
 url: ${auth.domain}
 appKey: ${auth.appKey}
 g-signature: ${auth.g-signature}

config:
 depend:
  accountGlodonServiceKey: MiOWRrW6hYBmW6xQE8OGRH2f9I5wfxUG
  accountGlodonserverSecret: vG23xiwByuRxKdhAehgKaDEK89jyKzsC
object-storage:
 enable: true
 protocol: ${oss.protocol}
 endPoint: ${oss.endPoint}
 #endPoint: https://oss-cn-beijing-internal.aliyuncs.com #内网地址
 externalEndpoint: ${oss.externalEndpoint}
 accessKeyId: ${oss.accessKeyId}
 accessKeySecret: ${oss.accessKeySecret}
 bucketName: gldzb-data-online
 region: cn-north-4
 pathStyleAccess: false # 是否强制开启路径形式的资源访问
 maxConnections: 5000
 connectionTimeout: 3000
 socketTimeout: 3000
env: hw-prod

# 部件服务
dcost-sub:
 url: ${dcost.sub.domain}

# es配置
depend:
 elasticsearch:
  hosts: ${elasticsearch.url}
  username: ${elasticsearch.username}
  password: ${elasticsearch.password}
  connectTimeOut: 200000 #连接超时时间
  socketTimeOut: 300000 #连接超时时间
  connectionRequestTimeOut: 60000 #获取连接的超时时间
  maxConnectNum: 500 #最大连接数
  maxConnectPerRoute: 200 #最大路由连接数
  maxKeepAliveTime: 600000  #
