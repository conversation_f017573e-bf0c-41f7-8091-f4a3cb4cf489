package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository;

import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject;
import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdItemIndexTemplate;
import com.glodon.gcdp.dwdservice.domain.model.OriginalContractProject;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwdProjectInfo;

import java.util.List;

public interface IDwdDataRepository {

    /**
     * 获取全部项目列表，包含项目信息
     */
    List<DwdProjectInfo> listDetailProjectInfo(String enterpriseId,
                                               List<String> orgIds,
                                               List<String> projectCodes,
                                               String projectName,
                                               List<String> areaId,
                                               String isVirtualOrg,
                                               List<String> sharedEnterpriseId,
                                               String includeSelfFlg,
                                               List<String> authControlProjectCodeList);

    DwdProjectInfo selectByProjectCode(String enterpriseId, String projectCode);

    List<DwdProjectInfo> selectByProjectCodes(String enterpriseId, List<String> projectCodes);

    List<OriginalContractProject> selectOriginalProjectIdByContractProjectIds(List<Long> contractProjectIds);

    List<DwdItemIndexTemplate> selectItemIndexTemplateByBidNodeIds(List<Long> bidNodeIds);

    List<DwdContractProject> selectProjectListByOriginalIds(List<String> originalIds);

}
