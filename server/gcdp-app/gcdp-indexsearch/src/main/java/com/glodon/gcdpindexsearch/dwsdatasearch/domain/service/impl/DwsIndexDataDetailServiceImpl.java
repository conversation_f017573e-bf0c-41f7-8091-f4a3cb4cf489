package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdp.common.domain.consts.NumericConst;
import com.glodon.gcdp.common.domain.model.IndexParent;
import com.glodon.gcdp.common.utils.CopyUtils;
import com.glodon.gcdp.common.utils.HashUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdItemIndexTemplate;
import com.glodon.gcdp.dwdservice.domain.dao.enums.IndexType;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.IndexInfoDto;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.SubjectIndexDetailDto;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.vo.IndexQueryVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.SubjectIndexDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwdDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.IndexDataDetailService;
import com.glodon.gcdpindexsearch.essearch.domain.util.IndexUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DwsIndexDataDetailServiceImpl extends IndexDataDetailService {

    @Autowired
    private IDwsIndexDataRepository indexDataRepository;
    @Autowired
    private IDwdDataRepository dwdDataRepository;
    @Autowired
    private IDwsDataRepository dwsDataRepository;

    public List<SubjectIndexDetailDto> subjectIndexDetail(List<IndexQueryVO> indexQueryVO, Integer itemCostType) {

        List<DwsIndexProjectNote> projectNotes = getProjectNotes(indexQueryVO);
        if (CollUtil.isEmpty(projectNotes)) {
            return Lists.newArrayList();
        }
        Map<Long, List<SubjectIndexDto>> indexInfoDtoMap = getAllDtSubjectIndexMap(projectNotes, itemCostType);

        List<SubjectIndexDetailDto> indexDetailDtoList = Lists.newArrayList();
        for (IndexQueryVO queryVO : indexQueryVO) {
            SubjectIndexDetailDto indexDetailDto = new SubjectIndexDetailDto();
            indexDetailDto.setDtIdentity(queryVO.getDtIdentity());

            // 组织单体科目
            List<Long> dtIds = queryVO.getDtIds();
            List<SubjectIndexDto> subjectIndexListDto = Lists.newArrayList();
            indexInfoDtoMap.forEach((a, b) -> {
                if (dtIds.contains(a)) {
                    subjectIndexListDto.addAll(b);
                }
            });

            if (CollUtil.isEmpty(subjectIndexListDto)) {
                continue;
            }

            // 构建合并hash
            List<SubjectIndexDto> subjectIndexList = buildHash(subjectIndexListDto);
            // 合并
            Set<String> allTemplateUuids = Sets.newHashSet();
            mergeSubjectIndex(subjectIndexList, allTemplateUuids);

            setIndexDetailDto(indexDetailDto, subjectIndexList, allTemplateUuids);
            indexDetailDtoList.add(indexDetailDto);
        }
        return indexDetailDtoList;
    }

    private void setIndexDetailDto(SubjectIndexDetailDto indexDetailDto, List<SubjectIndexDto> subjectIndexList, Set<String> allTemplateUuids) {
        Map<String, Integer> countByMergeHashMap = subjectIndexList
                .stream()
                .collect(Collectors.groupingBy(SubjectIndexDto::getSubjectMergeHash, Collectors.summingInt(e -> 1)));

        for (SubjectIndexDto subjectIndexDto : subjectIndexList) {
            Integer childCount = countByMergeHashMap.get(subjectIndexDto.getSubjectMergeHash());
            subjectIndexDto.setLeaf(Objects.equals(childCount, 0));
            subjectIndexDto.setChildCount(childCount == null ? 0 : childCount);
            int levelSize = subjectIndexDto.getCode().split("\\.").length;
            subjectIndexDto.setLevel(levelSize + 1);
        }
        List<IndexInfoDto> indexInfoDtos = CopyUtils.copyList(subjectIndexList, IndexInfoDto::new);
        // 重新生成code pid
        makePid(indexInfoDtos);
        makeCode(indexInfoDtos);
        indexDetailDto.setTemplateUuids(Lists.newArrayList(allTemplateUuids));
        indexDetailDto.setIndexInfoDtos(indexInfoDtos);
    }



    private List<SubjectIndexDto> buildHash(List<SubjectIndexDto> subjectIndexListDto) {
        List<SubjectIndexDto> indexTree = IndexUtils.buildIndexTree(subjectIndexListDto);
        List<SubjectIndexDto> subjectIndexList = Lists.newArrayList();
        IndexUtils.foreachTree(indexTree, indexDto -> {
            buildMergeHashValue(indexDto, subjectIndexListDto);
            subjectIndexList.add(indexDto);
        });
        return subjectIndexList;
    }

    private List<DwsIndexProjectNote> getProjectNotes(List<IndexQueryVO> indexQueryVO) {
        List<Long> noteIds = indexQueryVO
                .stream()
                .map(IndexQueryVO::getDtIds)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        return dwsDataRepository.selectNotesByIds(noteIds);
    }


    private Map<Long, String> getTemplateUuidMap(List<DwsIndexProjectNote> projectNotes) {
        Map<Long, String> templateUuidMap = Maps.newHashMap();
        // 查询科目模板uuid
        List<Long> bidnodeIds = projectNotes
                .stream()
                .map(DwsIndexProjectNote::getBidnodeId)
                .collect(Collectors.toList());


        List<DwdItemIndexTemplate> dwdItemIndexTemplates = dwdDataRepository.selectItemIndexTemplateByBidNodeIds(bidnodeIds);
        if (CollUtil.isNotEmpty(dwdItemIndexTemplates)) {
            templateUuidMap = dwdItemIndexTemplates
                    .stream()
                    .filter(c -> c.getIndexType() != null &&
                            IndexType.CBZB.getIndex() == c.getIndexType())
                    .collect(Collectors.toMap(DwdItemIndexTemplate::getBidnodeId, DwdItemIndexTemplate::getTemplateUuid, (a, b) -> a));
        }
        return templateUuidMap;
    }


    private Map<Long, List<SubjectIndexDto>> getAllDtSubjectIndexMap(List<DwsIndexProjectNote> projectNotes, Integer itemCostType) {

        List<Long> noteIds = Lists.newArrayList();
        Map<Long, DwsIndexProjectNote> projectNoteMap = projectNotes
                .stream()
                .peek(c -> noteIds.add(c.getId()))
                .collect(Collectors.toMap(DwsIndexProjectNote::getId, a -> a, (a, b) -> a));

        // 查询科目
        List<SubjectIndexDto> subjectIndexDtos = dwsDataRepository.selectCostAndUsageIndexByNoteId(noteIds, itemCostType);
        return subjectIndexDtos
                .stream()
                .peek(c -> {
                    // 设置归档时间
                    DwsIndexProjectNote projectNote = projectNoteMap.get(c.getIndexProjectNoteId());
                    c.setArchiveDate(projectNote.getArchiveDate());
                    c.setBidnodeId(projectNote.getBidnodeId());
                    c.setOriginalTemplateUuid(projectNote.getOriginalTemplateUuid());
                })
                .collect(Collectors.groupingBy(SubjectIndexDto::getIndexProjectNoteId));
    }


    private void mergeSubjectIndex(List<SubjectIndexDto> subjectIndexList, Set<String> allTemplateUuids) {
        Map<String, List<SubjectIndexDto>> indexMergeMap = subjectIndexList.stream().collect(Collectors.groupingBy(IndexParent::getSubjectMergeHash));
        subjectIndexList.clear();
        for (Map.Entry<String, List<SubjectIndexDto>> entry : indexMergeMap.entrySet()) {
            List<SubjectIndexDto> indexInfoList = entry.getValue();
            if (CollUtil.isEmpty(indexInfoList)) {
                continue;
            }
            SubjectIndexDto finalSubjectIndex = indexInfoList.get(0);
            BigDecimal zhdjAmount = BigDecimal.ZERO;
            BigDecimal zhdjAmountIncludeTax = BigDecimal.ZERO;
            BigDecimal dfzjAmount = BigDecimal.ZERO;
            BigDecimal dfzjAmountIncludeTax = BigDecimal.ZERO;
            BigDecimal quantities = BigDecimal.ZERO;

            BigDecimal originalAmount = BigDecimal.ZERO;
            BigDecimal originalAmountIncludeTax = BigDecimal.ZERO;
            BigDecimal originalQuantities = BigDecimal.ZERO;

            Date latestArchiveDate = finalSubjectIndex.getArchiveDate();

            List<Long> originalIdList = Lists.newArrayList();
            List<String> originalCodeList = Lists.newArrayList();
            Set<String> templateUuids = Sets.newHashSet();
            for (SubjectIndexDto index : indexInfoList) {
                originalAmount = MathUtil.add(originalAmount, index.getAmount());
                originalAmountIncludeTax = MathUtil.add(originalAmountIncludeTax, index.getAmountIncludeTax());
                BigDecimal quantity = MathUtil.mul(index.getZylIndexValue(), index.getZylCalculateValue());
                if (BigDecimal.ZERO.compareTo(quantity) == 0) {
                    quantity = index.getSwlCalculateValue();
                }
                originalQuantities = MathUtil.add(originalQuantities, quantity);

                zhdjAmount = MathUtil.add(zhdjAmount, MathUtil.mul(index.getSwlIndexValue(), index.getSwlCalculateValue()));
                zhdjAmountIncludeTax = MathUtil.add(zhdjAmountIncludeTax, MathUtil.mul(index.getSwlIndexValueIncludeTax(), index.getSwlCalculateValue()));
                dfzjAmount = MathUtil.add(dfzjAmount, MathUtil.mul(index.getDfIndexValue(), index.getDfCalculateValue()));
                dfzjAmountIncludeTax = MathUtil.add(dfzjAmountIncludeTax, MathUtil.mul(index.getDfIndexValueIncludeTax(), index.getDfCalculateValue()));
                quantities = MathUtil.add(quantities, quantity);

                Date archiveDate = index.getArchiveDate();
                if (StringUtils.isNotBlank(index.getOriginalTemplateUuid())) {
                    templateUuids.add(index.getOriginalTemplateUuid());
                    allTemplateUuids.add(index.getOriginalTemplateUuid());
                }
                if (latestArchiveDate != null && archiveDate.compareTo(latestArchiveDate) > 0) {
                    latestArchiveDate = archiveDate;
                    finalSubjectIndex = index;
                }
                originalIdList.add(index.getId());
                originalCodeList.add(index.getCode());
            }
            BigDecimal calculateValue = finalSubjectIndex.getCalculateValue();
            finalSubjectIndex.setOriginalIdList(originalIdList);
            finalSubjectIndex.setIds(StringUtils.join(originalIdList, ","));
            finalSubjectIndex.setOriginalCodeList(originalCodeList);
            // 重新生成id
            finalSubjectIndex.setTotalCost(originalAmount);
            finalSubjectIndex.setTotalCostIncludeTax(originalAmountIncludeTax);
            finalSubjectIndex.setQuantities(originalQuantities);
            finalSubjectIndex.setCaliberName(finalSubjectIndex.getCalculateName());
            finalSubjectIndex.setCaliberValue(calculateValue);
            finalSubjectIndex.setUnitContent(MathUtil.div(quantities, calculateValue));
            finalSubjectIndex.setCompUnitPriceIncludeTax(MathUtil.div(zhdjAmountIncludeTax, quantities));
            finalSubjectIndex.setCompUnitPrice(MathUtil.div(zhdjAmount, quantities));
            finalSubjectIndex.setUnitCostIncludeTax(MathUtil.div(dfzjAmountIncludeTax, calculateValue));
            finalSubjectIndex.setUnitCost(MathUtil.div(dfzjAmount, calculateValue));
            finalSubjectIndex.setTemplateUuids(Lists.newArrayList(templateUuids));
            subjectIndexList.add(finalSubjectIndex);
        }
    }


    private void buildMergeHashValue(SubjectIndexDto subjectIndexDto, List<SubjectIndexDto> subjectIndexListDto) {
        setCalculateInfo(subjectIndexDto);
        String hashKey = getHashKey(subjectIndexDto);
        if (Objects.equals(subjectIndexDto.getPid(), NumericConst.PID)) {
            String subjectMergeHash = HashUtil.hash64WithFnv1a(hashKey);
            subjectIndexDto.setSubjectMergeHash(subjectMergeHash);
            return;
        }
        Map<Long, SubjectIndexDto> indexMap = subjectIndexListDto.stream().collect(Collectors.toMap(SubjectIndexDto::getId, a -> a));
        SubjectIndexDto pSubjectIndex = indexMap.get(subjectIndexDto.getPid());
        //hash(hash(子节点)+hash(父节点))
        String pSubjectMergeHash = pSubjectIndex.getSubjectMergeHash();
        String subjectMergeHash = HashUtil.hash64WithFnv1a(HashUtil.hash64WithFnv1a(hashKey) + pSubjectMergeHash);
        subjectIndexDto.setSubjectMergeHash(subjectMergeHash);
    }

    private void setCalculateInfo(SubjectIndexDto subjectIndexDto) {
        String calculateName = StringUtils.isNotBlank(subjectIndexDto.getZylCalculateName()) ?
                subjectIndexDto.getZylCalculateName() : subjectIndexDto.getDfCalculateName();
        BigDecimal calculateValue = subjectIndexDto.getZylCalculateValue() != null ?
                subjectIndexDto.getZylCalculateValue() : subjectIndexDto.getDfCalculateValue();
        subjectIndexDto.setCalculateName(calculateName);
        subjectIndexDto.setCalculateValue(calculateValue);
        BigDecimal quantities = subjectIndexDto.getSwlCalculateValue() != null ?
                subjectIndexDto.getSwlCalculateValue() : MathUtil.mul(subjectIndexDto.getZylIndexValue(), subjectIndexDto.getZylCalculateValue());
        subjectIndexDto.setQuantities(quantities);
    }

    private String getHashKey(SubjectIndexDto subjectIndexDto) {
        List<String> fields = Lists.newArrayList();
        fields.add(CharSequenceUtil.emptyToDefault(subjectIndexDto.getName(), CharConst.LINE));
        fields.add(CharSequenceUtil.emptyToDefault(subjectIndexDto.getNamePath(), CharConst.LINE));
        fields.add(CharSequenceUtil.emptyToDefault(subjectIndexDto.getUnit(), CharConst.LINE));
        fields.add(CharSequenceUtil.emptyToDefault(subjectIndexDto.getCalculateName(), CharConst.LINE));
        fields.add(subjectIndexDto.getItemCostType() != null ? subjectIndexDto.getItemCostType().toString() : CharConst.LINE);
        return CharSequenceUtil.join(CharConst.DOUBLE_AT, fields);
    }
}

