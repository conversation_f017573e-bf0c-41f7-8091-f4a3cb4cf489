package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 全成本指标库-单方含量-指标区间组成-Dto
 * @date 2023-05-16 15:01
 */
@Data
public class CostIndexMakeupQCBZBLibDFHLDto extends CostIndexMakeupBaseDto {
    /**
     * 科目单位
     */
    private String unit;
    /**
     * 单方含量-对应中台的主要量指标
     */
    private BigDecimal zylIndexValue;
    /**
     * 工程量
     */
    private BigDecimal quantity;
    /**
     * 计算口径
     */
    private String calculateName;
    /**
     * 建筑面积
     */
    private BigDecimal calculateValue;
}
