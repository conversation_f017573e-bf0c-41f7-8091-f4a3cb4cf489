package com.glodon.gcdpindexsearch.infrastructure.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @packageName: com.glodon.gcdpindexsearch.infrastructure.config
 * @className: CommonConfig
 * @author: yany<PERSON><PERSON> <EMAIL>
 * @date: 2023/1/4 10:22
 * @description:
 */
@Configuration
@Data
public class CommonConfig {

    /**
     * 广联云接口调用秘钥
     */
    @Value("${config.depend.accountGlodonServiceKey}")
    private String serviceKey;
    @Value("${config.depend.accountGlodonserverSecret}")
    private String serverSecret;

}
