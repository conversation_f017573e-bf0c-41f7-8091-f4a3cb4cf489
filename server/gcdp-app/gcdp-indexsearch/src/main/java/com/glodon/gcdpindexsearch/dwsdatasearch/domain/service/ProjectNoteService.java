package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import com.glodon.gcdp.common.domain.Page;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwdProjectInfo;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterConditionVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.NoteExistVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.SampleNoteIndexDataVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.SingleProjectReqVO;

import java.util.List;
import java.util.Map;

/**
 * @description: 工程数据接口Service
 * <AUTHOR>
 * @date 2022/10/24 10:58
 */
public interface ProjectNoteService {

    /**
     * @description: 反推筛选条件-工程分类、造价类型
     * @param enterpriseId
     * @param sumCondition
     * @param orgIds
     * @return com.glodon.gcdpindexsearch.dwsdatasearch.application.dto.ConditionDto
     * <AUTHOR>
     * @date 2022/10/25 18:35
     */
    ConditionDto getCondition(String enterpriseId, Integer sumCondition, List<String> orgIds,List<String> sharedEnterpriseIds, List<String> productSource, List<String> authControlProjectCodes);

    /**
     * @description: 工程维度列表
     * @param enterpriseId
     * @param filterConditionVO
     * @param orgIds
     * @return com.glodon.gcdp.common.domain.Page<com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ProjectSearchDto>
     * <AUTHOR>
     * @date 2022/11/2 14:21
     */
    Page<ProjectSearchDto> projectAnalyzeList(String enterpriseId, FilterConditionVO filterConditionVO, List<String> orgIds) throws Exception;

    ContractProjectDto contractProjectList(String enterpriseId, FilterConditionVO filterCondition, List<String> orgIds) throws Exception;

    /**
     * 查询工程特征信息
     * @param enterpriseId
     * @param originalIds
     * @return
     */
    List<ProjectAttrDto> getProjectAttr(String enterpriseId, List<Long> noteIds, List<String> originalIds);

    List<SampleNoteDto> ldNoteList(String enterpriseId, SampleNoteIndexDataVO sampleNoteIndexDataVO);

    /**
     * 根据过滤条件获取有效的项目ID集合（项目信息）
     * @param filterConditionVO
     * @param enterpriseId
     * @param orgIds
     * @return
     * @throws Exception
     */
    List<DwdProjectInfo> getProjectsByDetailCondition(FilterConditionVO filterConditionVO, String enterpriseId, List<String> orgIds)throws Exception;

    List<DwsIndexProjectNote> getValidNotes(String enterpriseId,
                                            FilterConditionVO filterConditionVO,
                                            List<String> orgIds,
                                            Map<String, DwdProjectInfo> projectInfoMap) throws Exception;

    /**
     * 根据合同信息，工程特征，指标类型，项目户口簿ID，业态，企业ID 获取原始指标数据
     * @param filterConditionVO
     * @param noteList
     * @return
     * @throws Exception
     */
    List<DwsIndexProjectNote> getNoteByCondition(FilterConditionVO filterConditionVO, List<DwsIndexProjectNote> noteList)throws Exception;

    List<MakeUpIndexData> getMakeUpIndexDataList(String enterpriseId, List<Long> sampleNoteIds);

    /**
     * 根据传入的参数判断note是否存在
     * <AUTHOR>
     * @date 2023-03-08 13:56:02
     * @param enterpriseId
     * @param noteExistVOS
     * @return java.util.List<com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.NoteExistVO>
     */
    List<NoteExistVO> noteExist(String enterpriseId, List<NoteExistVO> noteExistVOS);

    /**
     * 查询详情页面页签范围
     * @param reqVO
     * @return
     */
    ProjectDetailRangeDto detailRange(SingleProjectReqVO reqVO);
}
