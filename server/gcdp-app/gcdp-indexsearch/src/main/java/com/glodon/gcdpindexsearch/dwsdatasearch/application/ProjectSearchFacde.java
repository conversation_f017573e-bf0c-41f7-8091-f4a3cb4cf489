package com.glodon.gcdpindexsearch.dwsdatasearch.application;

import com.glodon.gcdp.common.domain.Page;
import com.glodon.gcdp.common.domain.model.StandardBuilderDto;
import com.glodon.gcdp.common.domain.model.StandardBuilderNewDto;
import com.glodon.gcdp.common.utils.SpringUtil;
import com.glodon.gcdpindexsearch.common.entity.ContractInfoVO;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.SubjectIndexDetailDto;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.vo.IndexQueryVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.IndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.SingleProjectIndexDataDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.DwsIndexDataDetailServiceImpl;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.StructProjectNoteServiceImpl;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ProjectSearchFacde {

    @Autowired
    private ProjectNoteService projectNoteServiceImpl;
    @Autowired
    private NewArchiveDataService archiveDataService;
    @Autowired
    private DwsIndexDataDetailServiceImpl dwsIndexDataDetailService;
    @Autowired
    private SingleProjectIndexDataService singleProjectIndexDataService;
    @Autowired
    private StructProjectNoteServiceImpl structProjectNoteServiceImpl;
    @Autowired
    private BuildStandardService buildStandardService;


    /**
     * 服务前缀
     */
    private String service = "projectIndexDataService-";

    /**
     * 根据传入的参数判断note是否存在
     * <AUTHOR>
     * @date 2023-03-08 13:51:07
     * @param enterpriseId
     * @param noteExistVOS
     * @return java.util.List<com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.NoteExistVO>
     */
    public List<NoteExistVO> noteExist(String enterpriseId, List<NoteExistVO> noteExistVOS) {
        return projectNoteServiceImpl.noteExist(enterpriseId, noteExistVOS);
    }

    /**
     * @param enterpriseId
     * @param sumCondition
     * @param orgIds
     * @return com.glodon.gcdpindexsearch.dwsdatasearch.application.dto.ConditionDto
     * @description: 反推筛选条件-工程分类、造价类型
     * <AUTHOR>
     * @date 2022/10/25 18:34
     */
    public ConditionDto getCondition(String enterpriseId, Integer sumCondition, List<String> orgIds, List<String> sharedEnterpriseIds, List<String> productSource, List<String> authControlProjectCodeList) {
        return projectNoteServiceImpl.getCondition(enterpriseId, sumCondition, orgIds, sharedEnterpriseIds, productSource, authControlProjectCodeList);
    }

    /**
     * @description: 工程维度列表
     * @param enterpriseId
     * @param filterConditionVO
     * @param orgIds
     * @return com.glodon.gcdp.common.domain.Page<com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ProjectSearchDto>
     * <AUTHOR>
     * @date 2022/11/2 14:21
     */
    public Page<ProjectSearchDto> projectAnalyzeList(String enterpriseId, FilterConditionVO filterConditionVO, List<String> orgIds) throws Exception {
        return projectNoteServiceImpl.projectAnalyzeList(enterpriseId, filterConditionVO, orgIds);
    }

    public ContractProjectDto contractProjectList(String enterpriseId, FilterConditionVO filterCondition, List<String> orgIds) throws Exception {
        return projectNoteServiceImpl.contractProjectList(enterpriseId, filterCondition, orgIds);
    }


    /**
     * 返回结构化的工程列表
     * @param enterpriseId
     * @param filterIndexDataVO
     * @param orgIds
     * @return
     * @throws Exception
     */
    public StructContractProjectDto structProjectList(
            String enterpriseId, FilterConditionVO filterIndexDataVO, ContractInfoVO standardCondition, List<String> orgIds) throws Exception{
        return structProjectNoteServiceImpl.structProjectList(enterpriseId, filterIndexDataVO, standardCondition, orgIds);
    }

    /**
     * 查询详情页面页签范围
     * @param reqVO
     * @return
     */
    public ProjectDetailRangeDto detailRange(SingleProjectReqVO reqVO) {
        return projectNoteServiceImpl.detailRange(reqVO);
    }
    /**
     * @description: 查询4类指标
     * @param filterIndexDataVO
     * @return com.glodon.gcdpindexsearch.dwsdatasearch.application.dto.ConditionDto
     * <AUTHOR>
     * @date 2022/10/25 18:35
     */
    public IndexData getIndexData(FilterIndexDataVO filterIndexDataVO) throws Exception{
        ProjectIndexDataService projectIndexDataService = SpringUtil.getBean(service + filterIndexDataVO.getType());
        return projectIndexDataService.getIndexData(filterIndexDataVO);
    }

    public SingleProjectIndexDataDto getSingleProjectIndex(SingleProjectIndexReqVO reqVO){
        return singleProjectIndexDataService.getSingleProjectIndexData(reqVO);
    }

    /**
     * 查询设计指标
     *
     * @param enterpriseId
     * @param projectCode
     * @return
     */
    public List<ProjectPlanDto> getProjectPlan(String enterpriseId, String projectCode, String phase) {
        return archiveDataService.getProjectPlan(enterpriseId, projectCode, phase);
    }

    public StandardBuilderDto getBuildStandard(BuildStandardVO buildStandardVO) {
        return archiveDataService.getBuildStandard(buildStandardVO);
    }


    public StandardBuilderNewDto getBuildStandardV2(BuildStandardVO buildStandardVO) {
        return buildStandardService.getBuildStandard(buildStandardVO);
    }


    /**
     * 查询工程特征信息
     * @param enterpriseId 企业id
     * @param noteIds 单体ids
     * @param originalIds 源工程Ids（OPEN接口使用）
     * @return 工程特征List
     */
    public List<ProjectAttrDto> getProjectAttr(String enterpriseId, List<Long> noteIds, List<String> originalIds) {
        return projectNoteServiceImpl.getProjectAttr(enterpriseId, noteIds, originalIds);
    }

    public List<SubjectIndexDetailDto> subjectIndexDetail(List<IndexQueryVO> indexQueryVO, Integer itemCostType) {
        return dwsIndexDataDetailService.subjectIndexDetail(indexQueryVO, itemCostType);
    }
}
