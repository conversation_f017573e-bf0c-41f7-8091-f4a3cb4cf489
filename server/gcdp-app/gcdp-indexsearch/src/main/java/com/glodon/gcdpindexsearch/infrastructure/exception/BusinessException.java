package com.glodon.gcdpindexsearch.infrastructure.exception;

import cn.hutool.core.util.StrUtil;
import com.glodon.gcdpindexsearch.infrastructure.util.api.ResultCode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @packageName: com.glodon.gcdpindexsearch.infrastructure.exception
 * @className: BusinessException
 * @author: yany<PERSON><PERSON> <EMAIL>
 * @date: 2023/1/4 11:06
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessException extends RuntimeException {

    private ResultCode resultCode;

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.resultCode = resultCode;
    }

    protected BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessException(String message, Object... args) {
        super(StrUtil.format(message, args));
    }
}
