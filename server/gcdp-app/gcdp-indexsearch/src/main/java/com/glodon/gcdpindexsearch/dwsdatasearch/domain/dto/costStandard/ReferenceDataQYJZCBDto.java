package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 企业基准成本-参考历史数据 Dto
 * @date 2023-05-15 15:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReferenceDataQYJZCBDto extends ReferenceDataBaseDto {
    @JsonIgnore
    @ApiModelProperty(value = "建造标准名称， 只有目标成本来源才有此字段")
    private String standardName;

    @ApiModelProperty(value = "单方造价（不含税）")
    private BigDecimal dfIndexValue;

    @ApiModelProperty(value = "单方造价（含税）")
    private BigDecimal dfIndexValueIncludeTax;

    @ApiModelProperty(value = "单方含量")
    private BigDecimal zylIndexValue;

    @ApiModelProperty(value = "综合单价（不含税）")
    private BigDecimal swlIndexValue;

    @ApiModelProperty(value = "综合单价（含税）")
    private BigDecimal swlIndexValueIncludeTax;

    @JsonIgnore
    @ApiModelProperty(value = "indexCostId")
    private String indexCostId;

    @ApiModelProperty(value = "科目费用类型，1全费 2非全费", required = true, example = "1")
    private Integer itemCostType;
}
