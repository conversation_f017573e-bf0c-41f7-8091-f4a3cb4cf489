package com.glodon.gcdpindexsearch.infrastructure.advice;

import com.alibaba.fastjson.JSON;
import com.glodon.gcdpindexsearch.infrastructure.util.api.ApiResult;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 统一返回值处理
 */
@ControllerAdvice(basePackages = {"com.glodon.gcdpindexsearch.dynamic.controller",
        "com.glodon.gcdpindexsearch.dwsdatasearch.controller",
        "com.glodon.gcdpindexsearch.dwddatasearch.controller",
        "com.glodon.gcdpindexsearch.essearch.controller"
})
public class GResponseBodyAdvice implements ResponseBodyAdvice {
    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        if (body instanceof ApiResult || body instanceof ResponseEntity) {
            return body;
        }
        if (body instanceof String) {
            return JSON.toJSONString(ApiResult.success(body));
        }
        return ApiResult.success(body);
    }
}
