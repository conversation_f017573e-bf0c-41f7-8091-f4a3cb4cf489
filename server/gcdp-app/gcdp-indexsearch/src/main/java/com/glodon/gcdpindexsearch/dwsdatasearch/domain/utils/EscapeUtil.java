package com.glodon.gcdpindexsearch.dwsdatasearch.domain.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: yhl
 * @DateTime: 2023/8/21 18:11
 * @Description:
 */
public class EscapeUtil {
    private EscapeUtil() {}
    //mysql的模糊查詢時特殊字符轉義
    public static String escapeChar(String before){
        if(StringUtils.isNotBlank(before)){
            before = before.replace("\\", "\\\\");
            before = before.replace("_", "\\_");
            before = before.replace("%", "\\%");
        }
        return before ;
    }
}
