package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 企业建造标准-参考历史数据 Dto
 * @date 2023-05-15 15:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReferenceDataQYJZBZDto extends ReferenceDataBaseDto {
    @ApiModelProperty(value = "单价（不含税）", required = true)
    private BigDecimal rate;
    @ApiModelProperty(value = "单价（含税）", required = true)
    private BigDecimal rateIncludeTax;
}
