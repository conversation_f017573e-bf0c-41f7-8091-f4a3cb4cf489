package com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor;

import cn.hutool.core.util.StrUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ProjectIndexData;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @packageName: com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor
 * @className: AbstractIndexCostProcessor
 * @author: yany<PERSON><PERSON> <EMAIL>
 * @date: 2023/4/18 13:46
 * @description: 成本指标数据处理类
 */
@Data
public abstract class AbstractProcessor<T extends ProjectIndexData> {

    protected AbstractProcessor<T> nextProcessor;

    /**
     * 数据层面处理dws侧的指标数据
     *
     * @param dataList 数据list
     * @return 处理之后的数据list
     */
    public abstract List<T> process(List<T> dataList);

    protected List<T> processNext(List<T> dataList) {
        AbstractProcessor<T> nextProcessor = getNextProcessor();
        if (nextProcessor != null) {
            return nextProcessor.process(dataList);
        }
        return dataList;
    }

    public BigDecimal zeroNull(BigDecimal value) {
        return MathUtil.isEqualFive(BigDecimal.ZERO, value) ? null : value;
    }

    public String orderedIds(String ids) {
        if (StrUtil.isNotEmpty(ids)) {
            List<String> resultIds = StrUtil.split(ids, StrUtil.COMMA, true, true);
            List<String> list = resultIds.stream().sorted(Comparator.comparingLong(Long::valueOf)).collect(Collectors.toList());
            return StrUtil.join(StrUtil.COMMA, list);
        }
        return ids;
    }
}
