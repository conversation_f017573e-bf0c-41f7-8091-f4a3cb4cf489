package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.domain.enums.EconomicCalculateType;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsIndex;
import com.glodon.gcdpindexsearch.common.enums.FilterTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexEconomics;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.EconomicMakeupIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexEconomicsMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.MakeupIndexDetailService;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service("makeupIndexDetailServiceBZZB")
public class EconomicMakeupIndexDetailServiceImpl implements MakeupIndexDetailService {
    @Autowired
    private GcdpDwsIndexEconomicsMapper economicsMapper;

    @Override
    public List<MakeUpIndexData> listMakeupIndexDetail(List<MakeUpIndexData> indexDataList, String itemIds, FilterTypeEnums filterType) {
        if (CollUtil.isEmpty(indexDataList)) {
            return Lists.newArrayList();
        }
        //查询科目信息
        List<DwsIndexEconomics> economics = assembleIndexes(itemIds);
        if (CollUtil.isEmpty(economics)) {
            return Lists.newArrayList();
        }
        return getIndexData(indexDataList, economics);
    }

    /**
     * 查询经济指标科目数据
     * @param itemIds
     * @return
     */
    List<DwsIndexEconomics> assembleIndexes(String itemIds){
        List<Long> itemIdList = Arrays.stream(itemIds.split(Constant.COMMA)).map(Long::new).collect(Collectors.toList());
        List<DwsIndexEconomics> economics = economicsMapper.selectByItemIds(itemIdList);
        if (CollUtil.isEmpty(economics)){
            return Lists.newArrayList();
        }

        return economics.stream()
                .filter(x -> x.getIndexValueIncludeTax() != null).collect(Collectors.toList());
    }

    private List<MakeUpIndexData> getIndexData(List<MakeUpIndexData> indexDataList, List<DwsIndexEconomics> economics) {
        Map<Long, List<DwsIndexEconomics>> indexEconomicMap = economics.stream().filter(x -> x.getIndexValueIncludeTax() != null)
                .collect(Collectors.groupingBy(DwsIndexEconomics::getIndexProjectNoteId));
        List<MakeUpIndexData> data = new ArrayList<>();
        for (MakeUpIndexData makeUpIndexData : indexDataList) {
            String idStr = makeUpIndexData.getIds();
            String[] idStrs = idStr.split(Constant.COMMA);
            List<DwsIndexEconomics> economicList = getEconomicList(idStrs, indexEconomicMap);
            if (CollUtil.isEmpty(economicList)) {
                continue;
            }

            Map<String ,List<DwsIndexEconomics>> calculateValueMap = economicList.stream()
                    .collect(Collectors.groupingBy(DwsIndexEconomics::getSubjectLdMergeHash));
            for (Map.Entry<String,List<DwsIndexEconomics>> entry : calculateValueMap.entrySet()){
                List<DwsIndexEconomics> economicIndexList = entry.getValue();
                EconomicMakeupIndexData economicMakeupIndexData = new EconomicMakeupIndexData();
                BeanUtils.copyProperties(makeUpIndexData, economicMakeupIndexData);
                setIndexDataField(economicMakeupIndexData,economicIndexList);
                data.add(economicMakeupIndexData);
            }
        }
        return data;
    }

    private void setIndexDataField(EconomicMakeupIndexData economicMakeupIndexData, List<DwsIndexEconomics> dwsEconomicList) {
        Optional<DwsIndexEconomics> latestEconomics = dwsEconomicList.stream().
                max(Comparator.comparing(DwsIndexEconomics::getArchiveDate, Comparator.nullsFirst(Comparator.naturalOrder())));
        if (!latestEconomics.isPresent()) {
            return;
        }
        DwsIndexEconomics dwsIndexEconomics = latestEconomics.get();
        BigDecimal fmValue;
        BigDecimal fzValue;
        if (Objects.nonNull(dwsIndexEconomics.getFmType()) && (EconomicCalculateType.CALCULATION.getType() == dwsIndexEconomics.getFmType() || EconomicCalculateType.PURE_NUMBER.getType() == dwsIndexEconomics.getFmType())) {
            fmValue = dwsIndexEconomics.getFmValueIncludeTax();
        } else  {
            fmValue = dwsEconomicList.stream().map(DwsIndexEconomics::getFmValueIncludeTax)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(dwsEconomicList.size()), 6, RoundingMode.HALF_DOWN);
        }
        if (Objects.nonNull(dwsIndexEconomics.getFzType()) && (EconomicCalculateType.CALCULATION.getType() == dwsIndexEconomics.getFzType() || EconomicCalculateType.PURE_NUMBER.getType() == dwsIndexEconomics.getFzType())) {
            fzValue = dwsIndexEconomics.getFzValueIncludeTax();
        } else {
            fzValue = dwsEconomicList.stream().map(DwsIndexEconomics::getFzValueIncludeTax)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(dwsEconomicList.size()), 6, RoundingMode.HALF_DOWN);
        }
        if (fzValue != null && fmValue != null) {
            economicMakeupIndexData.setIndexValue(MathUtil.divStr(fzValue, fmValue));
        } else {
            economicMakeupIndexData.setIndexValue(Constant.LINE);
        }
        DwsIndexEconomics indexEconomic = dwsEconomicList.get(0);
        economicMakeupIndexData.setName(indexEconomic.getName());
        economicMakeupIndexData.setFormula(indexEconomic.getFormula());
        economicMakeupIndexData.setUnit(indexEconomic.getUnit());
        economicMakeupIndexData.setFzValue(fzValue == null ? Constant.LINE : fzValue.toString());
        economicMakeupIndexData.setFmValue(fmValue == null ? Constant.LINE : fmValue.toString());
    }

    private List<DwsIndexEconomics> getEconomicList(String[] idStrs, Map<Long, List<DwsIndexEconomics>> indexCostMap) {
        List<DwsIndexEconomics> economicList = new ArrayList<>();
        for (String id : idStrs) {
            List<DwsIndexEconomics> indexCosts = indexCostMap.get(Long.valueOf(id));
            if (CollUtil.isNotEmpty(indexCosts)) {
                economicList.addAll(indexCosts);
            }
        }
        return economicList;
    }
}
