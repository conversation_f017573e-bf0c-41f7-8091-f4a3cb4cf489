package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl;

import com.glodon.gcdp.dimservice.domain.dao.entity.DimZbStandardsMainQuantity;
import com.glodon.gcdp.dimservice.domain.dao.mapper.DimZbStandardsMainQuantityMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDimZbStandardsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: luoml-b
 * @date: 2023/9/25 17:30
 * @description:
 */
@Repository
public class DimZbStandardsRepositoryImpl implements IDimZbStandardsRepository {

    @Autowired
    private DimZbStandardsMainQuantityMapper dimZbStandardsMainQuantityMapper;


    @Override
    public List<DimZbStandardsMainQuantity> selectByCustomerCode(String customerCode) {
        return dimZbStandardsMainQuantityMapper.selectByCustomerCode(customerCode);
    }
}
