package com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.usage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexUsage;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.AbstractProcessor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @packageName: com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.usage
 * @className: IndexUsageLdProcessor
 * @author: yanyuhui <EMAIL>
 * @date: 2023/4/18 15:41
 * @description:
 */
@SuppressWarnings("all")
@Slf4j
public class IndexUsageLdProcessor extends AbstractProcessor<GcdpDwsItemIndexUsage> {
    @Override
    public List<GcdpDwsItemIndexUsage> process(List<GcdpDwsItemIndexUsage> dataList) {
        Map<String, List<GcdpDwsItemIndexUsage>> hashIndexMap = dataList.parallelStream().collect(Collectors.groupingBy(GcdpDwsItemIndexUsage::getSubjectLdMergeHash));
        log.info("含量楼栋维度的数据长度为:[{}] 含量楼栋维度的map的长度为:[{}]", dataList.size(), hashIndexMap.size());
        if (dataList.size() == hashIndexMap.size()) {
            return processNext(dataList);
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<GcdpDwsItemIndexUsage> indexUsages = Collections.synchronizedList(new LinkedList<>());
        hashIndexMap.entrySet().parallelStream().forEach(node -> {
            List<GcdpDwsItemIndexUsage> usage = node.getValue();
            GcdpDwsItemIndexUsage source = CollUtil.getFirst(usage);
            if (usage.size() == 1) {
                indexUsages.add(source);
                return;
            }
            GcdpDwsItemIndexUsage dest = new GcdpDwsItemIndexUsage();
            BeanUtil.copyProperties(source, dest);
            indexUsages.add(dest);
            combineReturnField(usage, dest);
        });
        stopWatch.stop();
        log.info("含量楼栋维度数据组装耗时为:[{}]秒", stopWatch.getLastTaskInfo().getTimeSeconds());
        return processNext(indexUsages);
    }

    /**
     * 将数据库的处理逻辑改成多线程处理了  有可能会存在同一栋楼被划分在多个组里, 所以需要在在数据汇总的时候 进行一次手动计算
     *
     * @param usages 含量数据
     * @param dest   组装的数据
     */
    private void combineReturnField(List<GcdpDwsItemIndexUsage> usages, GcdpDwsItemIndexUsage dest) {
        List<String> zylSampleIds = new ArrayList<>(usages.size());
        List<String> jmhlSampleIds = new ArrayList<>(usages.size());
        List<String> ids = new ArrayList<>(usages.size());

        BigDecimal zylQuantity = BigDecimal.ZERO;
        BigDecimal jmhlQuantity = BigDecimal.ZERO;

        for (GcdpDwsItemIndexUsage cost : usages) {
            ids.add(cost.getIds());
            jmhlSampleIds.add(cost.getJmhlSampleIds());
            zylSampleIds.add(cost.getZylSampleIds());

            if (cost.getZylQuantity() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, cost.getZylQuantity())) {
                zylQuantity = NumberUtil.add(zylQuantity, cost.getZylQuantity());
            }

            if (cost.getJmhlQuantity() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, cost.getJmhlQuantity())) {
                jmhlQuantity = NumberUtil.add(jmhlQuantity, cost.getJmhlQuantity());
            }

        }

        dest.setZylIndexValue(zeroNull(MathUtil.div(zylQuantity, dest.getZylCalculateValue())));
        dest.setJmhlIndexValue(zeroNull(MathUtil.div(jmhlQuantity, dest.getJmhlCalculateValue())));

        dest.setZylQuantity(zeroNull(zylQuantity));
        dest.setJmhlQuantity(zeroNull(jmhlQuantity));

        dest.setIds(StrUtil.join(StrUtil.COMMA, ids));
        dest.setJmhlSampleIds(StrUtil.join(StrUtil.COMMA, jmhlSampleIds));
        dest.setZylSampleIds(StrUtil.join(StrUtil.COMMA, zylSampleIds));
    }
}
