package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.glodon.gcdp.common.domain.PageData;
import com.glodon.gcdp.common.utils.RegularUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/17 11:21
 */
@Data
@ApiModel(value = "工程维度列表", description = "工程维度列表")
public class ContractProjectDto {

    @ApiModelProperty(value = "分页信息")
    private PageData page;

    @ApiModelProperty(value = "列表数据")
    private List<NoteMergeDto> contractProjectList;

    @ApiModelProperty(value = "单体累加标记")
    private Integer simpleNoteSumFlag;

    @Data
    public static class NoteMergeDto {
        // 项目信息
        @ApiModelProperty(value = "项目编码", example = "XMHA2210240002")
        private String projectCode;
        @ApiModelProperty(value = "项目名称", example = "保利")
        private String projectName;
        @ApiModelProperty(value = "项目id", example = "123456")
        private String projectId;
        @ApiModelProperty(value = "省编码", example = "11")
        private String provinceId;
        @ApiModelProperty(value = "省名称", example = "河南")
        private String provinceName;
        @ApiModelProperty(value = "市编码", example = "149")
        private String cityId;
        @ApiModelProperty(value = "市名称", example = "郑州")
        private String cityName;
        @ApiModelProperty(value = "区编码", example = "1251")
        private String districtId;
        @ApiModelProperty(value = "区名称", example = "金水区")
        private String districtName;
        @ApiModelProperty(value = "项目创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date projectCreateDate;
        @ApiModelProperty(value = "项目开工时间")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date projectStartTime;
        @ApiModelProperty(value = "项目竣工时间")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date projectEndTime;
        @ApiModelProperty(value = "项目规模", example = "1000")
        private BigDecimal projectScale;
        @ApiModelProperty(value = "项目规模单位", example = "m2")
        private String projectScaleUnit;
        @ApiModelProperty(value = "项目产品定位", example = "中档")
        private String projectProductPositioning;
        @ApiModelProperty(value = "项目建设性质")
        private String projectJianshexingzhi;
        @ApiModelProperty(value = "项目建设单位")
        private String projectConstructionUnit;
        @ApiModelProperty(value = "项目信息json，剔除空值并压缩")
        private List<JSONObject> projectInfoDetail;

        // 合并信息
        @ApiModelProperty(value = "中台节点ID集合，外部不可持久化", example = "[10446,10447]")
        private List<String> tempNoteIds;
        @ApiModelProperty(value = "中台节点ID集合，外部不可持久化", example = "[10446,10447]")
        private List<String> allTempNoteIds;
        @ApiModelProperty(value = "建面单方计算使用的阶段", example = "结算价")
        private String phase;
        @ApiModelProperty(value = "包含的造价阶段", example = "['结算价']")
        private List<String> phases;
        @ApiModelProperty(value = "业态名称", example = "[超高层办公楼-1]")
        private List<String> categoryName;
        @ApiModelProperty(value = "业态名称", example = "[超高层办公楼-1]")
        private List<String> categoryPath;
        @ApiModelProperty(value = "楼栋标识", example = "[1#]")
        private List<String> ldNameIdentify;
        @ApiModelProperty(value = "楼栋原始名称", example = "[1号楼、1#楼]")
        private List<String> ldNameOriginal;
        @ApiModelProperty(value = "合同名称", example = "[文件1、文件2]")
        private List<String> fileName;
        @ApiModelProperty(value = "工程归档时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date archiveDate;
        @ApiModelProperty(value = "总造价（不含税）", example = "0")
        private BigDecimal total;
        @ApiModelProperty(value = "总造价（含税）", example = "0")
        private BigDecimal totalIncludeTax;
        @ApiModelProperty(value = "建筑面积", example = "0")
        private BigDecimal buildArea;
        @ApiModelProperty(value = "工程规模", example = "0")
        private BigDecimal scale;
        @ApiModelProperty(value = "单方造价（不含税）", example = "0")
        private BigDecimal dfIndexValue;
        @ApiModelProperty(value = "单方造价（含税）", example = "0")
        private BigDecimal dfIndexValueIncludeTax;
        @ApiModelProperty(value = "编制时间")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private List<Date> compileDate;

        @ApiModelProperty(value = "全费总造价（不含税）", required = false, example = "0")
        private BigDecimal fullCostTotal;
        @ApiModelProperty(value = "全费总造价（含税）", required = false, example = "0")
        private BigDecimal fullCostTotalIncludeTax;
        @ApiModelProperty(value = "全费单方造价（不含税）", required = false, example = "0")
        private BigDecimal fullCostDfIndexValue;
        @ApiModelProperty(value = "全费单方造价（含税）", required = false, example = "0")
        private BigDecimal fullCostDfIndexValueIncludeTax;

        @ApiModelProperty(value = "非全费总造价（不含税）", required = false, example = "0")
        private BigDecimal nonFullCostTotal;
        @ApiModelProperty(value = "非全费总造价（含税）", required = false, example = "0")
        private BigDecimal nonFullCostTotalIncludeTax;
        @ApiModelProperty(value = "非全费单方造价（不含税）", required = false, example = "0")
        private BigDecimal nonFullCostDfIndexValue;
        @ApiModelProperty(value = "非全费单方造价（含税）", required = false, example = "0")
        private BigDecimal nonFullCostDfIndexValueIncludeTax;

        @ApiModelProperty(value = "总造价（不含税）", example = "0")
        private BigDecimal totalAmount;
        @ApiModelProperty(value = "总造价（含税）", example = "0")
        private BigDecimal totalAmountIncludeTax;
        @ApiModelProperty(value = "单方造价（不含税）", example = "0")
        private BigDecimal totalDfIndexValue;
        @ApiModelProperty(value = "单方造价（含税）", example = "0")
        private BigDecimal totalDfIndexValueIncludeTax;

        @ApiModelProperty(value = "综合单价取费", example = "[1,2]")
        private List<Integer> itemCostType;
        @ApiModelProperty(value = "专业名称", example = "['专业1','专业2']")
        private List<String> tradeName;

        // 合并项明细信息
        @ApiModelProperty(value = "合并项明细信息")
        private List<NoteInfoDto> mergeInfos;

        // 用于指标库的共享企业业务
        @ApiModelProperty(value = "企业编码")
        private String enterpriseId;
    }

    @Data
    public static class NoteInfoDto{
        @ApiModelProperty(value = "中台节点ID", example = "10446")
        private String tempNoteId;
        @ApiModelProperty(value = "数据来源", example = "zbsq")
        private String productSource;
        @ApiModelProperty(value = "工程id", example = "10446")
        private String contractProjectId;
        @ApiModelProperty(value = "类型", example = "ZY/YT/FL/LD/XNJD/FJA")
        private String type;
        @ApiModelProperty(value = "名称", example = "1#")
        private String name;
        @ApiModelProperty(value = "展示名称", example = "1号楼")
        private String displayName;
        @ApiModelProperty(value = "科目费用类型，1全费 2非全费", example = "1")
        private Integer itemCostType;
        @ApiModelProperty(value = "合同信息json，剔除空值并压缩", example = "")
        private List<JSONObject> contractInfoDetail;
        @ApiModelProperty(value = "工程特征json，剔除空值并压缩", example = "")
        private List<ProjectAttrDto> featureDetail;
        @ApiModelProperty(value = "归档时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date archiveDate;
        @ApiModelProperty(value = "专业名称", example = "土建工程")
        private String tradeName;
        @ApiModelProperty(value = "造价阶段", example = "结算价")
        private String phase;
    }

}
