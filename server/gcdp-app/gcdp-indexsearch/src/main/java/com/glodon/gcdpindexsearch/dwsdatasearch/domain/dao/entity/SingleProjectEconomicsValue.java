package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "科目返回值", description = "科目返回值")
public class SingleProjectEconomicsValue extends ItemDataResp {
    @ApiModelProperty(value = "具体指标数据")
    private Map<String, SingleEconomicsValueDto> indexes;
}
