package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 企业限额指标-参考历史数据 Dto
 * @date 2023-05-15 15:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReferenceDataQYXEZBDto extends ReferenceDataBaseDto {
    @ApiModelProperty(value = "限额指标 即：指标结果(含税)")
    private BigDecimal indexValueIncludeTax;

    @ApiModelProperty(value = "计算规则说明")
    private String formula;
}
