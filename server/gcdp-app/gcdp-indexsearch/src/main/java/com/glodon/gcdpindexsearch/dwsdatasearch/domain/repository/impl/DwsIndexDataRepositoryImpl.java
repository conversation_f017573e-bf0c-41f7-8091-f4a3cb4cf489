package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterIndexDataVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/*
 * @Class com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl DwsIndexUsageDataRepositoryImpl
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 含量指标查询
 * @Date 10:56 2022/11/3
 **/
@Log4j2
@Repository
public class DwsIndexDataRepositoryImpl implements IDwsIndexDataRepository {

    @Autowired
    GcdpDwsIndexUsageMapper gcdpDwsIndexUsageMapper;
    @Autowired
    GcdpDwsIndexCostMapper gcdpDwsIndexCostMapper;
    @Autowired
    GcdpDwsIndexEconomicsMapper gcdpDwsIndexEconomicsMapper;
    @Autowired
    GcdpDwsIndexMainMapper gcdpDwsIndexMainMapper;
    @Autowired
    DwsIndexProjectNoteMapper indexSearchDwsIndexProjectNoteMapper;
    @Autowired
    GcdpDwsBuildStandardIndexMapper gcdpDwsBuildStandardIndexMapper;

    @Autowired
    private GcdpDwsIndexMapper gcdpDwsIndexMapper;

    @Override
    public List<ProjectIndexData> selectCostIndexList(FilterIndexDataVO filterIndexDataVO) {
        return gcdpDwsIndexCostMapper.getIndex(filterIndexDataVO.getIds());
    }

    @Override
    public List<ProjectIndexData> selectEconomicsIndexList(FilterIndexDataVO filterIndexDataVO) {
        return gcdpDwsIndexEconomicsMapper.getIndex(filterIndexDataVO.getIds());
    }


    @Override
    public List<ProjectIndexData> selectMainIndexList(FilterIndexDataVO filterIndexDataVO) {
        return gcdpDwsIndexMainMapper.getIndex(filterIndexDataVO.getIds());
    }

    @Override
    public List<ProjectIndexData> selectUsageIndexList(FilterIndexDataVO filterIndexDataVO) {
        return gcdpDwsIndexUsageMapper.getIndex(filterIndexDataVO.getIds());
    }


    @Override
    public List<DwsIndexProjectNote> getTotal(List<String> ids) {
        return indexSearchDwsIndexProjectNoteMapper.getTotal(ids);
    }

    @Override
    public List<GcdpDwsItemIndexCost> selectCostItemIndexList(List<String> ids, Integer showAll) {
        return gcdpDwsIndexCostMapper.getItemIndex(ids, showAll);
    }

    @Override
    public List<GcdpDwsItemIndexUsage> selectUsageItemIndexList(List<String> ids, Integer showAll) {
        return gcdpDwsIndexUsageMapper.getItemIndex(ids, showAll);
    }

    @Override
    public List<ProjectIndexData> selectEconomicItemIndexList(List<String> ids, Integer showAll) {
        return gcdpDwsIndexEconomicsMapper.getItemIndex(ids, showAll);
    }

    @Override
    public List<ProjectIndexData> selectMainResItemIndexList(List<String> ids, Integer showAll) {
        return gcdpDwsIndexMainMapper.getItemIndex(ids, showAll);
    }

    @Override
    public List<CostIndexBaseDto> selectCostIndexMergeByItemHash(List<Long> ids) {
        return gcdpDwsIndexCostMapper.selectCostIndexMergeByItemHash(ids);
    }

    @Override
    public List<CostIndexBaseDto> selectUsageIndexMergeByItemHash(List<Long> ids) {
        return gcdpDwsIndexUsageMapper.selectUsageIndexMergeByItemHash(ids);
    }

    @Override
    public List<CostIndexBaseDto> selectBuildStandardIndex(List<Long> ids) {
        return gcdpDwsBuildStandardIndexMapper.selectBuildStandardIndex(ids);
    }


    @Override
    public List<GcdpDwsIndex> selectItemIndexByNoteId(String indexTypePrefix, String calcType, List<Long> ids, Integer itemCostType) {
        return gcdpDwsIndexMapper.selectItemIndexByNoteId(indexTypePrefix, calcType, ids, itemCostType);
    }

    @Override
    public List<SingleProjectDbIndex> selectDwsIndexByNoteId(String indexTypePrefix,
                                                             String calcType,
                                                             List<Long> ids,
                                                             Integer itemCostType,
                                                             Boolean onlyXNLDFlag,
                                                             String baseTradeName) {
        return gcdpDwsIndexMapper.selectDwsIndexByNoteId(indexTypePrefix, calcType, ids, itemCostType, onlyXNLDFlag, baseTradeName);
    }
}

