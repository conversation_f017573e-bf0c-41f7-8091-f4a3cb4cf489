package com.glodon.gcdpindexsearch.dwsdatasearch.controller;

import com.glodon.gcdpindexsearch.common.BaseController;
import com.glodon.gcdpindexsearch.common.enums.CostStandardTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.application.CostStandardSearchFacade;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.ReferenceDataVO;
import com.glodon.gcdpindexsearch.infrastructure.annotation.TakeTime;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: 企业成本标准相关库查询数据集合接口层
 * <AUTHOR>
 * @date 2023/5/15 14:15
 */
@Api(tags = "企业成本标准相关库查询接口层")
@Slf4j
@RestController
@RequestMapping("/cost-standard-data")
public class CostStandardSearchController extends BaseController {
    @Autowired
    CostStandardSearchFacade costStandardSearchFacade;

    @ApiOperation(value = "查询参考的历史数据")
    @TakeTime
    @PostMapping("/reference-data/{enterpriseId}")
    public List<ReferenceDataBaseDto> referenceData(@PathVariable String enterpriseId, @RequestBody ReferenceDataVO referenceDataVO){
        referenceDataVO.setOrgIds(getOrgIds());
        referenceDataVO.setEnterpriseId(enterpriseId);
        referenceDataVO.setDataSourceList(CostStandardTypeEnums.getDataSourceList(referenceDataVO.getType()));
        return costStandardSearchFacade.referenceData(referenceDataVO);
    }
}
