package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.ReferenceDataVO;

import java.util.List;


/**
 * @description: 企业成本标准相关库查询数据
 * <AUTHOR>
 * @date 2023/5/15 14:15
 */
public interface CostStandardService {
    /**
     * @description: 参考历史数据查询（三库）
     * @param referenceDataVO:
     * @return com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto
     * @author: lif-k
     * @date: 2023/5/17 13:59
     */
    <T extends ReferenceDataBaseDto> List<T> referenceData(ReferenceDataVO referenceDataVO);
}
