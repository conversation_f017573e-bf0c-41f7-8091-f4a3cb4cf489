package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.domain.enums.ItemCostTypeEnums;
import com.glodon.gcdp.dimservice.domain.dao.service.CustomCodeService;
import com.glodon.gcdpindexsearch.common.util.FilterUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ItemData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.SumTypeEnum;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDimZbStandardsRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterConditionVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.ItemIndexQueryReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.ProjectNoteServiceImpl.*;

/**
 * <AUTHOR>
 * @description: 科目service
 * @date 2022/10/24 10:58
 */
@Slf4j
public abstract class ItemDetailService {

    protected static final int DEFAULT_NOTE_BATCH_SIZE = 100;

    @Autowired
    private ProjectNoteService projectNoteService;
    @Autowired
    private IDwsDataRepository dwsDataRepository;

    @Autowired
    private CustomCodeService customCodeService;

    @Autowired
    private IDimZbStandardsRepository zbStandardsRepository;
    @Autowired
    @Qualifier("selectDataServiceExecutor")
    protected ThreadPoolTaskExecutor selectDataServiceExecutor;

    private static final String poin = ".";

    /**
     * @param result
     * @description: 设置pid
     * <AUTHOR>
     * @date 2022/10/25 18:35
     */
    protected <T extends ItemData> void makePid(List<T> result) {
        long start = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        /*
        父子关系会在groupby之后丢失，因此做法如下：
        1.sql中group_contract用逗号拼接id
        2.如果发生合并科目合并了几条上述结果id就会拼接几个
        3.用拼接后的id，拆开做key生成Map<id, id>
        4.查找pid，此处算法参考力扣第一题两数之和
        */
        Map<Long, Long> idMapping = new HashMap<>();
        result.forEach(item -> {
            for (String id : item.getItemIds().split(",")) {
                if (StringUtils.isEmpty(id)) {
                    continue;
                }
                idMapping.put(Long.parseLong(id), item.getId());
            }
        });
        result.forEach(item -> {
            if (idMapping.get(item.getPid()) == null) {
                item.setPid(-1l);
            } else {
                item.setPid(idMapping.get(item.getPid()));
            }
        });
        long end = System.currentTimeMillis();
        log.info("makePid:{}", end - start);
    }

    protected <T extends ItemData> void makeCode(List<T> result) {
        long start = System.currentTimeMillis();
        if (CollUtil.isEmpty(result)) {
            return;
        }
        // 重新编制code
        Map<Long, List<ItemData>> resultMap = result.stream().collect(Collectors.groupingBy(ItemData::getPid));
        for (int i = 1; i < resultMap.get(-1l).size() + 1; i++) {
            String code = "" + i;
            resultMap.get(-1l).get(i - 1).setCode(code);
            this.setCode(resultMap, resultMap.get(-1l).get(i - 1).getId(), code);
        }
        long end = System.currentTimeMillis();
        log.info("makeCode:{}", end - start);
    }

    /**
     * @param resultMap 1
     * @param pid       2
     * @param pCode     3
     * @return void
     * @throws
     * @description: 递归生成code，从跟节点循环开始生成，如果有子节点一直递归
     * <AUTHOR>
     * @date 2022/11/10 15:48
     */
    private void setCode(Map<Long, List<ItemData>> resultMap, Long pid, String pCode) {
        if (!CollectionUtils.isEmpty(resultMap.get(pid))) {
            for (int i = 1; i < resultMap.get(pid).size() + 1; i++) {
                String code = pCode + poin + i;
                resultMap.get(pid).get(i - 1).setCode(code);
                this.setCode(resultMap, resultMap.get(pid).get(i - 1).getId(), code);
            }
        }
    }

    protected List<DwsIndexProjectNote> getIdsByFilter(ItemIndexQueryReqVO itemIndexQueryReqVO) throws Exception {
        List<String> ids = itemIndexQueryReqVO.getTempNodeIds();
        String enterpriseId = itemIndexQueryReqVO.getEnterpriseId();
        if (CollUtil.isNotEmpty(ids)) {
            return dwsDataRepository.selectNoteByIds(enterpriseId, ids.stream().map(Long::new).collect(Collectors.toList()));
        }
        FilterConditionVO filterConditionVO = itemIndexQueryReqVO.getFilterConditionVO();
        List<String> orgIds = itemIndexQueryReqVO.getOrgIds();
        List<DwsIndexProjectNote> validNotes = projectNoteService.getValidNotes(enterpriseId, filterConditionVO, orgIds, null);
        // 综合单价取费的筛选
        validNotes = costTypeFilter(filterConditionVO.getItemCostType(), validNotes);
        // 汇总并筛选
        mergeAfterFilter(itemIndexQueryReqVO, filterConditionVO, validNotes);
        return validNotes;
    }

    private List<DwsIndexProjectNote> costTypeFilter(Integer itemCostType, List<DwsIndexProjectNote> validNotes) {
        if (itemCostType == null) {
            return validNotes;
        }

        return validNotes.stream().filter(projectNote -> Objects.equals(projectNote.getItemCostType(), itemCostType) ||
                                Objects.equals(projectNote.getItemCostType(), ItemCostTypeEnums.FULL_AND_NOT_FULL_COST.getItemCostType()))
                .collect(Collectors.toList());
    }

    private void mergeAfterFilter(ItemIndexQueryReqVO itemIndexQueryReqVO, FilterConditionVO filterConditionVO, List<DwsIndexProjectNote> validNotes) {
        if (SumTypeEnum.DT.getCode().equals(itemIndexQueryReqVO.getSumCondition())
                && (StringUtils.isNotBlank(filterConditionVO.getScale()) || StringUtils.isNotBlank(filterConditionVO.getTotal()))) {
            // 聚合分组
            Map<String, List<DwsIndexProjectNote>> groupMap = CommonHandler.noteGroupBySumCondition(itemIndexQueryReqVO.getSumCondition(), validNotes);
            // 筛选
            groupMap.entrySet().removeIf(entry -> !passesFilters(filterConditionVO, entry.getValue()));
            // 收集所有有效的ID
            Set<Long> validIds = groupMap.values().stream()
                    .flatMap(List::stream)
                    .map(DwsIndexProjectNote::getId)
                    .collect(Collectors.toSet());

            // 移除无效的notes
            validNotes.removeIf(note -> !validIds.contains(note.getId()));
        }
    }

    private boolean passesFilters(FilterConditionVO filterVO, List<DwsIndexProjectNote> noteList) {
        boolean passesScaleFilter = filterScale(filterVO.getScale(), noteList);
        boolean passesTotalFilter = filterTotal(filterVO.getTotal(), noteList, filterVO.getSimpleNoteSumFlag(), filterVO.getItemCostType());
        return passesScaleFilter && passesTotalFilter;
    }

    /**
     * 【工程规模】区间筛选
     */
    private boolean filterScale(String filterScale, List<DwsIndexProjectNote> noteList) {
        if (StringUtils.isBlank(filterScale)) {
            return true;
        }
        BigDecimal buildArea = CommonHandler.calcBuildArea(noteList);
        return FilterUtil.matchValueByNumber(filterScale, buildArea);
    }

    /**
     * 【工程造价】区间筛选
     */
    private boolean filterTotal(String filterTotal, List<DwsIndexProjectNote> noteList, Integer simpleNoteSumFlag, Integer itemCostType) {
        if (StringUtils.isBlank(filterTotal)) {
            return true;
        }
        BigDecimal[] totalArr = CommonHandler.calculateTotal(simpleNoteSumFlag, noteList);
        return evaluateTotalCost(filterTotal, itemCostType, totalArr);
    }

    private boolean evaluateTotalCost(String filterTotal, Integer itemCostType, BigDecimal[] totalArr) {
        ItemCostTypeEnums itemCostTypeEnums = ItemCostTypeEnums.fromItemCostType(itemCostType);
        // 根据 cost 类型判断对应的总和条件
        switch (itemCostTypeEnums) {
            case FULL_COST:
                return FilterUtil.matchValueByNumber(filterTotal, totalArr[2]) || FilterUtil.matchValueByNumber(filterTotal, totalArr[3]);
            case NON_FULL_COST:
                return FilterUtil.matchValueByNumber(filterTotal, totalArr[4]) || FilterUtil.matchValueByNumber(filterTotal, totalArr[5]);
            default:
                return true;
        }
    }

    public <T extends ItemData> List<T> filterInvalidData(List<T> result) {
        long start = System.currentTimeMillis();
        if (CollUtil.isEmpty(result)) {
            return result;
        }

        Set<Long> showSet = new HashSet<>();

        Map<Long, Long> idAndPidMap = result.parallelStream().collect(Collectors.toMap(ItemData::getId, ItemData::getPid, (v1, v2) -> v2));

        result.forEach(item -> {
            if (isShow(item)) {
                Long id = item.getId();
                findParent(id, idAndPidMap, showSet);
            }
        });
        long end = System.currentTimeMillis();
        log.info("filterInvalidData:{}", end - start);

        return result.stream().filter(x -> showSet.contains(x.getId())).collect(Collectors.toList());
    }

    private void findParent(Long id, Map<Long, Long> idAndPidMap, Set<Long> showSet) {
        showSet.add(id);
        Long pid = idAndPidMap.get(id);

        if (pid != -1L && !showSet.contains(pid)) {
            findParent(pid, idAndPidMap, showSet);
        }
    }

    protected abstract boolean isShow(ItemData itemData);
}
