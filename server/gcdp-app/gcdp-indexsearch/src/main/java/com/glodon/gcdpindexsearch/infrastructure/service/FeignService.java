package com.glodon.gcdpindexsearch.infrastructure.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdpindexsearch.common.constant.CommonConstants;
import com.glodon.gcdpindexsearch.infrastructure.cache.MemoryCache;
import com.glodon.gcdpindexsearch.infrastructure.config.CommonConfig;
import com.glodon.gcdpindexsearch.infrastructure.exception.BusinessException;
import com.glodon.gcdpindexsearch.infrastructure.feign.CloudAccountFeignService;
import com.glodon.gcdpindexsearch.infrastructure.feign.EntFeignService;
import jodd.util.Base64;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @packageName: com.glodon.gcdpindexsearch.infrastructure.service
 * @className: FeignService
 * @author: yanyuhui <EMAIL>
 * @date: 2023/1/4 11:00
 * @description:
 */
@Service
@Slf4j
public class FeignService {

    // feign service
    @Resource
    private CommonConfig config;
    @Resource
    private CloudAccountFeignService cloudAccountFeignService;
    @Resource
    private EntFeignService entFeignService;

    public Map<String, String> getEnterpriseEmployee(String enterpriseId) {
        JSONArray array = getAllEmployee(enterpriseId);
        Map<String, String> allEmployConvert = new HashMap<>();
        for (int i = 0; i < array.size(); i++) {
            JSONObject jsonObject = array.getJSONObject(i);
            String globalId = jsonObject.getString("globalId");
            String fullName = jsonObject.getString("name");
            allEmployConvert.put(globalId, fullName);
        }
        return allEmployConvert;
    }



    /**
     * 获取应用级的accessToken
     *
     * @return
     * <AUTHOR>
     */
    public String getClientCredentialsAccessToken() {
        String accessToken = MemoryCache.getAccessToken();
        if (StringUtils.isNotEmpty(accessToken)) {
            return accessToken;
        }
        JSONObject tokenObj = getAccessToken(null, "client_credentials");
        if (tokenObj != null && tokenObj.getJSONObject("data") != null) {
            accessToken = tokenObj.getJSONObject("data").getString("access_token");
            MemoryCache.setAccessToken(accessToken);
            return accessToken;
        }
        log.error("调用广联云获取应用级的accessToken接口异常,返回值为:[{}]", tokenObj);
        throw new BusinessException("调用广联云获取应用级的accessToken接口异常");
    }


    private JSONArray getAllEmployee(String enterpriseId) {
        String accessToken = getClientCredentialsAccessToken();
        JSONArray array = new JSONArray();
        boolean isLastPage = true;
        int pageNum = 1;
        do {
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("identity", null);
            paramsMap.put("entId", enterpriseId);
            paramsMap.put("pageNum", String.valueOf(pageNum));
            paramsMap.put("pageSize", "150");
            paramsMap.put("deleted", "0");
            long start = System.currentTimeMillis();
            JSONObject data = entFeignService.queryNumberList(paramsMap, "Bearer " + accessToken);
            log.info("调用广联云后台系统直接查询成员列表，耗时：[{}]毫秒", System.currentTimeMillis() - start);

            int code = data.getIntValue(CommonConstants.CODE);
            if (CommonConstants.CODE_0 == code) {
                JSONObject dataObj = data.getJSONObject("data");
                isLastPage = dataObj.getBoolean("isLastPage");
                JSONArray list = dataObj.getJSONArray("list");
                if (CollUtil.isNotEmpty(list)) {
                    array.addAll(list);
                } else {
                    break;
                }
            }
            pageNum++;
        } while (!isLastPage);
        return array;
    }

    /**
     * 通过userId免密获取用户级accessToken
     * 参考文档：http://pm.glodon.com/wiki/pages/viewpage.action?pageId=********
     *
     * @return access token
     */
    private JSONObject getAccessToken(String globalId, String grantType) {
        String url = "/v3/oauth2/token";
        try {
            long start = System.currentTimeMillis();

            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("grant_type", grantType);
            if (globalId != null) {
                paramsMap.put("uid", globalId);
            }
            JSONObject resultBody = cloudAccountFeignService.searchToken(paramsMap, headerValue());
            log.info("调用广联云用户中心查询accessToken，耗时：[{}] 毫秒", System.currentTimeMillis() - start);
            return resultBody;
        } catch (Exception e) {
            log.error("访问广联云接口 getAccessToken error,url={},globalId={},grantType={},e={}", url, globalId, grantType, e.getMessage());
            throw new BusinessException("调用广联云查询授权token接口异常,异常信息为:[{}]", e.getMessage());
        }
    }

    public String headerValue() {
        String credential = config.getServiceKey() + StrUtil.COLON + config.getServerSecret();
        String encoder = Base64.encodeToString(credential);
        return "Basic " + encoder;
    }

}
