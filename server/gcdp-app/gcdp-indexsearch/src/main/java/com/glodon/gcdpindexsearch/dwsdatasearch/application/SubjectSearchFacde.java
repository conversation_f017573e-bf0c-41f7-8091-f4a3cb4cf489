package com.glodon.gcdpindexsearch.dwsdatasearch.application;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdp.common.domain.enums.ItemCostTypeEnums;
import com.glodon.gcdp.common.utils.SpringUtil;
import com.glodon.gcdp.dwdservice.domain.dao.enums.IndexMakeupType;
import com.glodon.gcdpindexsearch.common.constant.ProjectConst;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.BqItemDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.*;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.glodon.gcdpindexsearch.infrastructure.exception.BusinessException;
import com.glodon.gcdpindexsearch.infrastructure.util.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SubjectSearchFacde {

    @Autowired
    private ProjectNoteService projectNoteServiceImpl;
    @Autowired
    private MakeupDetailService makeupDetailServiceImpl;
    @Autowired
    private MakeupIndexService makeupIndexService;

    @Autowired
    private MaterialCostService materialCostService;

    @Autowired
    private ItemIndexDataService indexDataService;

    @Autowired
    @Qualifier("selectDataServiceExecutor")
    private ThreadPoolTaskExecutor selectDataServiceExecutor;

    public List<SampleNoteDto> ldNoteList(String enterpriseId, SampleNoteIndexDataVO sampleNoteIds) {
        return projectNoteServiceImpl.ldNoteList(enterpriseId, sampleNoteIds);
    }

    public List<MakeupDetailBaseVO> makeupDetail(MakeupQueryVO makeupQueryVO) {
        return makeupDetailServiceImpl.makeupDetail(makeupQueryVO);
    }

    public List<BqItemDto> ldMakeupDetail(MakeupQueryVO makeupQueryVO) {
        return makeupDetailServiceImpl.ldMakeupDetail(makeupQueryVO);
    }

    public MakeupIndexDto makeupIndexList(MakeUpIndexVo makeUpIndexVo) {
        return makeupIndexService.selectMakeupIndexList(makeUpIndexVo);
    }

    public List<ProjectIndexData> getIndexDetailList(FilterIndexDataVO filterIndexDataVO) throws Exception {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String indexType = filterIndexDataVO.getIndexType();
        filterIndexDataVO.setIndexType(indexType);
        ProjectIndexDataDetailService projectIndexDataDetailService = SpringUtil.getBean(indexType);
        IndexData costIndex = projectIndexDataDetailService.getItemIndexDataDetail(filterIndexDataVO);
        List<ProjectIndexData> costIndexList = costIndex.getList();
        if(CollUtil.isEmpty(costIndexList)) {
            return costIndexList;
        }

        // 查询所有样本量实体
        List<Long> ids = costIndex.getSampleNoteId().stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<MakeUpIndexData> makeUpIndexDataList = projectNoteServiceImpl.getMakeUpIndexDataList(filterIndexDataVO.getEnterpriseId(), ids);
        log.info("数据库中查询到的单体size={}", makeUpIndexDataList.size());
        CountDownLatch countDownLatch = new CountDownLatch(1);
        selectDataServiceExecutor.execute(()->{
            try{
                for (ProjectIndexData projectIndexData : costIndexList) {
                   long startTimeInFor =  System.currentTimeMillis();
                    projectIndexData.setIndexType(indexType);
                    switch (indexType) {
                        case ProjectConst.TemplateType.INDEX_COST_TYPE:
                            setCostMakeUpIndexData(projectIndexData, makeUpIndexDataList);
                            break;
                        case ProjectConst.TemplateType.INDEX_USAGE_TYPE:
                            setUsageMakeUpIndexData(projectIndexData, makeUpIndexDataList);
                            break;
                        case ProjectConst.TemplateType.INDEX_MAIN_RESOURCE_TYPE:
                            setMainResMakeUpIndexData(projectIndexData, makeUpIndexDataList);
                            break;
                        case ProjectConst.TemplateType.INDEX_ECONOMIC_TYPE:
                            setEconomicMakeUpIndexData(projectIndexData, makeUpIndexDataList);
                            break;
                        default:
                    }
                    log.info("科目id={},耗时{}", projectIndexData.getId(), System.currentTimeMillis() - startTimeInFor);
                }
            }catch (Exception e) {
                log.error("填充科目详情出错 科目数量为={}",costIndexList.size(),e);
            }finally {
                countDownLatch.countDown();
            }
        });
        countDownLatch.await();
        stopWatch.stop();
        log.info("{}指标总耗时{}", indexType, stopWatch.getTotalTimeSeconds());
        return costIndexList;
    }

    private void setCostMakeUpIndexData(ProjectIndexData projectIndexData, List<MakeUpIndexData> makeUpIndexDataList) {
        GcdpDwsItemIndexCost costJm = (GcdpDwsItemIndexCost)projectIndexData;
        List<MakeUpIndexData> costMakeUpIndexDataList = getMackUpIndexList(projectIndexData,costJm.getJmSampleIds(), costJm.getItemIds(), costJm.getJmDictCode(), makeUpIndexDataList);
        costJm.setJmResInfoList(costMakeUpIndexDataList);
        GcdpDwsItemIndexCost costSwl = (GcdpDwsItemIndexCost)projectIndexData;
        List<MakeUpIndexData> costSwlMakeUpIndexDataList = getMackUpIndexList(projectIndexData,costSwl.getSwlSampleIds(), costSwl.getItemIds(), costSwl.getSwlDictCode(), makeUpIndexDataList);
        costSwl.setSwlResInfoList(costSwlMakeUpIndexDataList);

        GcdpDwsItemIndexCost costDf = (GcdpDwsItemIndexCost)projectIndexData;
        List<MakeUpIndexData> costDfMakeUpIndexDataList = getMackUpIndexList(projectIndexData,costDf.getDfSampleIds(), costDf.getItemIds(), costDf.getDfDictCode(), makeUpIndexDataList);
        costDf.setDfResInfoList(costDfMakeUpIndexDataList);
    }

    private void setUsageMakeUpIndexData(ProjectIndexData projectIndexData, List<MakeUpIndexData> makeUpIndexDataList) {
        GcdpDwsItemIndexUsage usageZyl = (GcdpDwsItemIndexUsage)projectIndexData;
        List<MakeUpIndexData> usageZylMakeUpIndexDataList = getMackUpIndexList(projectIndexData,usageZyl.getZylSampleIds(), usageZyl.getItemIds(), usageZyl.getZylDictCode(), makeUpIndexDataList);
        usageZyl.setZylResInfoList(usageZylMakeUpIndexDataList);
        GcdpDwsItemIndexUsage usageJmhl = (GcdpDwsItemIndexUsage)projectIndexData;
        List<MakeUpIndexData> usageJmhlMakeUpIndexDataList = getMackUpIndexList(projectIndexData,usageJmhl.getJmhlSampleIds(), usageJmhl.getItemIds(), usageJmhl.getJmhlDictCode(), makeUpIndexDataList);
        usageJmhl.setJmhlResInfoList(usageJmhlMakeUpIndexDataList);
    }

    private void setMainResMakeUpIndexData(ProjectIndexData projectIndexData, List<MakeUpIndexData> makeUpIndexDataList) {
        GcdpDwsItemIndexMainRes djTaxMainResource = (GcdpDwsItemIndexMainRes)projectIndexData;
        List<MakeUpIndexData> djTaxMainResList = getMackUpIndexList(projectIndexData,djTaxMainResource.getDjTaxSampleIds(), djTaxMainResource.getItemIds(), djTaxMainResource.getDjTaxDictCode(), makeUpIndexDataList);
        djTaxMainResource.setDjTaxResInfoList(djTaxMainResList);
        GcdpDwsItemIndexMainRes djMainResource = (GcdpDwsItemIndexMainRes)projectIndexData;
        List<MakeUpIndexData> djMainResList = getMackUpIndexList(projectIndexData,djMainResource.getDjSampleIds(), djMainResource.getItemIds(), djMainResource.getDjDictCode(), makeUpIndexDataList);
        djMainResource.setDjResInfoList(djMainResList);
        GcdpDwsItemIndexMainRes glhlMainResource = (GcdpDwsItemIndexMainRes)projectIndexData;
        List<MakeUpIndexData> mainResList = getMackUpIndexList(projectIndexData,glhlMainResource.getGlhlSampleIds(), glhlMainResource.getItemIds(), glhlMainResource.getGlhlDictCode(), makeUpIndexDataList);
        glhlMainResource.setGlhlResInfoList(mainResList);
    }

    private void setEconomicMakeUpIndexData(ProjectIndexData projectIndexData, List<MakeUpIndexData> makeUpIndexDataList) {
        GcdpDwsItemIndexEconomic economic = (GcdpDwsItemIndexEconomic)projectIndexData;
        List<MakeUpIndexData> economicList = getMackUpIndexList(projectIndexData,economic.getSampleIds(), economic.getItemIds(), economic.getDictCode(), makeUpIndexDataList);
        economic.setResInfoList(economicList);
    }

    private List<MakeUpIndexData> getMackUpIndexList(ProjectIndexData projectIndexData,String ids, String itemIds, String dictCode,  List<MakeUpIndexData> makeUpIndexDataList) {
        MakeUpIndexVo makeUpIndexVo = new MakeUpIndexVo();
        makeUpIndexVo.setIds(ids);
        makeUpIndexVo.setItemIds(itemIds);
        makeUpIndexVo.setDictCode(dictCode);
        try{
            List<MakeUpIndexData> dictCodeMakeUpIndexDataList = getListByIds(ids, makeUpIndexDataList);
            MakeupIndexDto makeupIndexDto = makeupIndexService.selectAllMakeupIndexList(makeUpIndexVo, dictCodeMakeUpIndexDataList);
            return makeupIndexDto.getData();
        }catch (Exception e) {
            log.error("处理科目组成数据出错，科目信息为：{}", projectIndexData);
        }
       return null;
    }

    private List<MakeUpIndexData> getListByIds(String ids, List<MakeUpIndexData> allMakeUpIndexDataList) throws IOException, ClassNotFoundException {
        List<String> idList = Arrays.stream(ids.split(Constant.COMMA)).collect(Collectors.toList());
        if (CollUtil.isEmpty(idList)) {
            return null;
        }
        List<MakeUpIndexData> returnList = new ArrayList<>();
        List<MakeUpIndexData> newMakeUpIndexDataList = allMakeUpIndexDataList.stream().filter(makeUpIndexData -> idList.contains(makeUpIndexData.getIds())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(newMakeUpIndexDataList)) {
            List<MakeUpIndexData> makeUpIndexDataList = ListUtils.deepCopy(newMakeUpIndexDataList);
            // 按照note.phase,note.ld_name_identify,note.project_code,note.build_area做合并
            Map<String, MakeUpIndexData> map = new HashMap<>();
            for (MakeUpIndexData makeUpIndexData : makeUpIndexDataList) {
                String[] keyArr = {makeUpIndexData.getPhase(), makeUpIndexData.getNoteName(), makeUpIndexData.getProjectCode(), makeUpIndexData.getBuildArea()};
                String keyStr = StrUtil.join(StrUtil.COMMA, (Object) keyArr);
                if (!map.containsKey(keyStr)) {
                    map.put(keyStr, makeUpIndexData);
                    returnList.add(makeUpIndexData);
                } else {
                    MakeUpIndexData innerMakeUpIndexData = map.get(keyStr);
                    String itemIds = innerMakeUpIndexData.getIds();
                    itemIds = itemIds + StrUtil.COMMA + makeUpIndexData.getIds();
                    innerMakeUpIndexData.setIds(itemIds);
                }
            }
            return returnList;
        }
        return null;
    }

    public JSONObject makeupMaterialList(String id, Integer type) {
        JSONObject jsonRes;
        switch (IndexMakeupType.getTypeName(type)) {
            case FBFX:
            case CSXM:
                // 清单、措施 -> gcdp_dwd_bqitem_lmm
                jsonRes = materialCostService.bqItemMaterialList(id);
                break;
            case NORM:
                // 定额 -> gcdp_dwd_norm_item_lmm
                jsonRes = materialCostService.normItemMaterialList(id);
                break;
            default:
                jsonRes = new JSONObject();
                break;
        }
        return jsonRes;
    }

    public ItemIndexData getItemIndexData(String enterpriseId, List<String> orgIds, ItemIndexQueryReqVO itemIndexQueryReqVO) {
        try {
            assembleItemIndexReq(enterpriseId, orgIds, itemIndexQueryReqVO);
            return indexDataService.getItemIndexData(itemIndexQueryReqVO);
        } catch (Exception e) {
            log.error("科目维度查询指标详情异常", e);
            throw new BusinessException("科目维度查询指标详情异常");
        }
    }


    public ItemIndexData getItemIndexDataDetails(String enterpriseId, List<String> orgIds, ItemIndexQueryReqVO itemIndexQueryReqVO) {
        try {
            assembleItemIndexReq(enterpriseId, orgIds, itemIndexQueryReqVO);
            return indexDataService.getItemIndexDataDetails(itemIndexQueryReqVO);
        } catch (Exception e) {
            log.error("查询所有指标类型的科目列表与科目组成单体详情-单指标异常", e);
            throw new BusinessException("查询所有指标类型的科目列表与科目组成单体详情-单指标异常");
        }
    }

    private void assembleItemIndexReq(String enterpriseId, List<String> orgIds, ItemIndexQueryReqVO itemIndexQueryReqVO) {
        itemIndexQueryReqVO.setEnterpriseId(enterpriseId);
        itemIndexQueryReqVO.setOrgIds(orgIds);
        FilterConditionVO filterConditionVO = itemIndexQueryReqVO.getFilterConditionVO() == null ? new FilterConditionVO() : itemIndexQueryReqVO.getFilterConditionVO();
        filterConditionVO.setProductSource(itemIndexQueryReqVO.getProductSource());
        filterConditionVO.setSumCondition(itemIndexQueryReqVO.getSumCondition());
        filterConditionVO.setAuthControlProjectCodeList(itemIndexQueryReqVO.getAuthControlProjectCodeList());
        itemIndexQueryReqVO.setFilterConditionVO(filterConditionVO);
        if (itemIndexQueryReqVO.getItemCostType() == null) {
            itemIndexQueryReqVO.setItemCostType(ItemCostTypeEnums.FULL_COST.getItemCostType());
        }
    }

    public List<CBZBKMakeupDetailDto> costIndexDetail(MakeupQueryVO makeupQueryVO) {
        return makeupDetailServiceImpl.costIndexDetail(makeupQueryVO);
    }
}
