package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexConditionDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexListVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexMakeupVO;

import java.util.List;

/**
 * @description: 建造做法指标库、全成本指标库查询数据
 * @date 2023/5/16 14:27
 */
public interface CostDataService {
    /**
     * @description:  查询指标区间组成
     * @param costIndexMakeupVO:
     * @return CostIndexMakeupBaseDto
     * @date: 2023/5/16 15:27
     */
    List<CostIndexMakeupBaseDto> costIndexMakeup(CostIndexMakeupVO costIndexMakeupVO);

    /**
     * 查询反推条件
     *
     * @param enterpriseId
     * @param orgIds
     * @param productSource
     * @param authControlProjectCodeList
     * @return
     */
    CostIndexConditionDto getCondition(String enterpriseId, List<String> orgIds, List<String> productSource, List<String> authControlProjectCodeList);

    /**
     *  查询全成本指标库和建造做法库列表
     * @param costIndexListVO
     * @return
     */
    List<CostIndexBaseDto> costIndexList(CostIndexListVO costIndexListVO)throws Exception;

}
