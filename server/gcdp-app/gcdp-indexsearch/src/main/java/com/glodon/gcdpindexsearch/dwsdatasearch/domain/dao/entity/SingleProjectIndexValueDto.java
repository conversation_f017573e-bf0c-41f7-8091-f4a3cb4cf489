package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/24 18:29
 */
@Data
@ApiModel(value = "指标值详情", description = "指标值详情")
public class SingleProjectIndexValueDto {
    @ApiModelProperty(value = "计算口径名称", example = "建筑面积")
    private String calcName;
    @ApiModelProperty(value = "计算值", example = "xx")
    private BigDecimal calcValue;
    @ApiModelProperty(value = "计算单位", example = "xx")
    private String calcUnit;
    @ApiModelProperty(value = "指标单位", example = "xx")
    private String indexUnit;
    @ApiModelProperty(value = "工程量", example = "1000")
    private BigDecimal quantity;
    @ApiModelProperty(value = "合价", example = "1000.25")
    private BigDecimal amount;
    @ApiModelProperty(value = "含税合价", example = "1200.75")
    private BigDecimal amountIncludeTax;
    @ApiModelProperty(value = "父级合价", example = "5000.75")
    private BigDecimal parentAmount;
    @ApiModelProperty(value = "父级含税合价", example = "5000.75")
    private BigDecimal parentAmountIncludeTax;
    @ApiModelProperty(value = "指标值", example = "22")
    private BigDecimal indexValue;
    @ApiModelProperty(value = "指标值含税", example = "22")
    private BigDecimal indexValueIncludeTax;
    @ApiModelProperty(value = "父级百分比", example = "0.5")
    private String parentPercent;
    @ApiModelProperty(value = "父级百分比含税", example = "0.5")
    private String parentPercentIncludeTax;
    @ApiModelProperty(value = "总百分比", example = "0.8")
    private String totalPercent;
    @ApiModelProperty(value = "总百分比含税", example = "0.8")
    private String totalPercentIncludeTax;
    @ApiModelProperty(value = "科目ID列表", example = "xx,xx")
    private String itemIds;
    @ApiModelProperty(value = "样本量noteId", example = "17020444,17020442")
    private String tempNoteIds;

    @ApiModelProperty(value = "科目费用类型，1全费 2非全费", example = "1")
    private Integer itemCostType;
    @ApiModelProperty(value = "人工费", example = "1000.25")
    private BigDecimal laborAmount;
    @ApiModelProperty(value = "人工费占比", example = "89%")
    private String percentLaborAmount;
    @ApiModelProperty(value = "材料费", example = "1000.25")
    private BigDecimal materialAmount;
    @ApiModelProperty(value = "材料费占比", example = "89%")
    private String percentMaterialAmount;
    @ApiModelProperty(value = "机械费", example = "1000.25")
    private BigDecimal machineAmount;
    @ApiModelProperty(value = "机械费占比", example = "89%")
    private String percentMachineAmount;
    @ApiModelProperty(value = "综合费", example = "1000.25")
    private BigDecimal otherAmount;
    @ApiModelProperty(value = "综合费占比", example = "89%")
    private String percentOtherAmount;

    @ApiModelProperty(value = "全费总百分比", example = "0.8")
    private String fullCostTotalPercent;
    @ApiModelProperty(value = "全费总百分比含税", example = "0.8")
    private String fullCostTotalPercentIncludeTax;

    @ApiModelProperty(value = "非全费总百分比", example = "0.8")
    private String nonFullCostTotalPercent;
    @ApiModelProperty(value = "非全费总百分比含税", example = "0.8")
    private String nonFullCostTotalPercentIncludeTax;
    @ApiModelProperty(value = "是否分摊科目", example = "0 未分摊 1 分摊")
    private Integer isShareCost;
}
