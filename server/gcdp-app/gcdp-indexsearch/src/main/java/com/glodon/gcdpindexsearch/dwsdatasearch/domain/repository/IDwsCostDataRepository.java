package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.*;

import java.util.List;
import java.util.Set;

public interface IDwsCostDataRepository {
    List<CostIndexMakeupQCBZBLibJMDFDto> selectQCBZBLibJmdfCostIndexByIds(List<Long> itemIds);

    List<CostIndexMakeupQCBZBLibDFHLDto> selectQCBZBLibDfhlCostIndexByIds(List<Long> itemIds);

    List<CostIndexMakeupQCBZBLibDFZJDto> selectQCBZBlibDfzjCostIndexByIds(List<Long> itemIds);

    List<CostIndexMakeupQCBZBLibZHDJDto> selectQCBZBlibZhdjCostIndexByIds(List<Long> itemIds);

    List<CostIndexMakeupJZZFZBLibDFZJDto> selectJZZFZBlibDfzjCostIndexByIds(List<Long> itemIds);

    List<CostIndexMakeupJZZFZBLibZHDJDto> selectJZZFZZBlibtZhdjCostIndexByIds(List<Long> itemIds);

    List<CostIndexMakeupBaseDto> selectProjectInfoByNoteIds(Set<Long> indexProjectNoteIds);
}
