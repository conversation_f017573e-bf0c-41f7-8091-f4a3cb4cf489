package com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dimservice.domain.dao.enums.IndexTaxtRelDictEnum;
import com.glodon.gcdpindexsearch.common.util.EmptyUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndex;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ItemDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.lang.Math.abs;

/**
 * @className: IndexMergeProcessor
 * @author: lml
 * @description: 科目维度维度处理器
 */
@SuppressWarnings("all")
@Slf4j
public class IndexMergeProcessor extends AbstractIndexProcessor<GcdpDwsIndex> {
    // 最小样本量数量控制
    private static final int minSampleItemCount = 5;
    private final ItemDetailService detailService;
    public IndexMergeProcessor(ItemDetailService detailService){
        this.detailService = detailService;
    }

    /**
     * 指标样本范围
     */
    class SampleCalcContext {
        /**
         * 不含税指标正态分布样本id
         */
        private Set<Long> sampleIndexIds = new HashSet<>();

        /**
         * 含税指标正态分布样本id
         */
        private Set<Long> sampleIndexIncludeIds = new HashSet<>();

        /**
         * 判断是否需要统计样本
         * @param dwsIndex
         * @param includeTax
         * @return
         */
        private boolean isNeedCalcIndex(GcdpDwsIndex dwsIndex, boolean includeTax){
            Set<Long> indexIds = includeTax ? sampleIndexIncludeIds : sampleIndexIds;

            // 如果没使用正态分布或样本集包含了当前指标数据
            if (CollUtil.isEmpty(indexIds) || indexIds.contains(dwsIndex.getId())) {
                return true;
            }

            return false;
        }
    }

    @Override
    public List<GcdpDwsIndex> process(List<GcdpDwsIndex> indexes, String indexType) {
        StopWatch stopwatch = new StopWatch();
        stopwatch.start();
        // 过滤掉指标值含税、不含税为0的科目
        if (detailService != null){
            indexes = detailService.filterInvalidData(indexes);
        }

        // 计算合并后科目后的指标
        List<GcdpDwsIndex> returnResult = Collections.synchronizedList(new LinkedList<>());
        Map<String, List<GcdpDwsIndex>> hashCostMap = indexes.parallelStream().collect(Collectors.groupingBy(GcdpDwsIndex::getIndexMergeHash));
        log.info("MergerHash的hashCostMap数量:[{}]", hashCostMap.size());
        // 如果科目样本量大于5,用正态分布去计算
        hashCostMap.entrySet().parallelStream().forEach(node -> {
            List<GcdpDwsIndex> costs = node.getValue();
            GcdpDwsIndex source = costs.stream()
                    .filter(index -> EmptyUtil.isNotEmpty(index.getIndexValue()) || EmptyUtil.isNotEmpty(index.getIndexValueIncludeTax()))
                    .findFirst()
                    .orElse(CollUtil.getFirst(costs));
            GcdpDwsIndex dest = new GcdpDwsIndex();
            BeanUtil.copyProperties(source, dest);
            // 计算合并后单科目的指标出值
            SampleCalcContext sampleRangeContext = calcRationalNoteIds(costs);
            combineReturnField(costs, dest, indexType, sampleRangeContext);
            returnResult.add(dest);
        });
        stopwatch.stop();
        log.info("IndexMergerProcessor合并处理耗时:[{}]秒", stopwatch.getLastTaskInfo().getTimeSeconds());
        return processNext(returnResult, indexType);
    }

    private Set<Long> calcValidSampleIndexList(List<GcdpDwsIndex> sampleIndexes, boolean includeTax){
        int sampleCount = 0;
        int sampleCountIncludeTax = 0;
        // 1.统计所有科目的不含税指标的合计
        BigDecimal sumIndexValue = BigDecimal.ZERO;
        for (GcdpDwsIndex dwsIndex : sampleIndexes){
            if (includeTax) {
                sumIndexValue = sumIndexValue.add(dwsIndex.getIndexValueIncludeTax());
            } else {
                sumIndexValue = sumIndexValue.add(dwsIndex.getIndexValue());
            }
            sampleCount++;
        }

        // 根据不含税没有统计出样本
        if (sampleCount == 0){
            return null;
        }

        // 2.计算均值
        BigDecimal sampleMeanValue = MathUtil.div(sumIndexValue, BigDecimal.valueOf(sampleCount));

        // 3.根据均值计算标准差,取值公式∑(科目的不含税指标-均值)的平方/样本数量
        BigDecimal accum = BigDecimal.ZERO;
        for (GcdpDwsIndex dwsIndex : sampleIndexes){
            BigDecimal indexValue = includeTax? dwsIndex.getIndexValueIncludeTax() : dwsIndex.getIndexValue();
            BigDecimal dev = indexValue.subtract(sampleMeanValue);
            accum = accum.add(dev.multiply(dev));
        }
        BigDecimal stdDev = new BigDecimal(Math.sqrt(MathUtil.div(accum, BigDecimal.valueOf(sampleCount)).doubleValue()));

        // 4.根据样本标准差,筛选样本,取一个标准差,将样本范围限制在0.68的范围分布
        BigDecimal lowerBound = MathUtil.sub(sampleMeanValue, stdDev);
        BigDecimal upper_bound = MathUtil.add(sampleMeanValue, stdDev);

        Set<Long> validIndexeIds = sampleIndexes.parallelStream().filter(item->{
            BigDecimal indexValue = includeTax? item.getIndexValueIncludeTax() : item.getIndexValue();
            if ((indexValue.compareTo(lowerBound) >= 0) && (upper_bound.compareTo(indexValue) >= 0)){
                return true;
            }

            return false;
        }).map(GcdpDwsIndex::getId).collect(Collectors.toSet());

        return validIndexeIds;
    }
    /**
     * 计算均值和标准差,这里需要考虑含税、不含税的影响
     * 按每个科目计算均值和方差
     * @param indexes
     * @param dest
     * @param type
     */
    private SampleCalcContext calcRationalNoteIds(List<GcdpDwsIndex> indexes) {
        List<GcdpDwsIndex> indexList = new ArrayList<>();
        List<GcdpDwsIndex> indexIncludeList = new ArrayList<>();
        // 筛选不含税、含税有效样本
        indexes.forEach(item->{
            if (MathUtil.notNullAndNotZero(item.getIndexValue())){
                indexList.add(item);
            }

            if (MathUtil.notNullAndNotZero(item.getIndexValueIncludeTax())){
                indexIncludeList.add(item);
            }
        });

        // 计算不含税指标
        SampleCalcContext sampleRangeContext = new SampleCalcContext();
        if (indexList.size() > minSampleItemCount){
            sampleRangeContext.sampleIndexIds = calcValidSampleIndexList(indexList, false);
        }

        // 统计含税样本
        if (indexIncludeList.size() > minSampleItemCount){
            sampleRangeContext.sampleIndexIncludeIds = calcValidSampleIndexList(indexIncludeList, true);;
        }

        return sampleRangeContext;
    }

    /**
     * 计算按科目hash合并的科目的指标出值
     * @param indexes
     * @param dest
     * @param indexType
     */

    private void combineReturnField(List<GcdpDwsIndex> indexes, GcdpDwsIndex dest, String indexType, SampleCalcContext sampleRange) {
        List<String> tempNoteIds = new ArrayList<>(indexes.size());
        List<GcdpDwsIndex> sampleRangeIndexes = new ArrayList<>();
        List<GcdpDwsIndex> sampleRangeIncludeTaxIndexes = new ArrayList<>();
        List<String> ids = new ArrayList<>(indexes.size());
        List<String> idsIncludeTax = new ArrayList<>(indexes.size());
        List<String> rationalIds = new ArrayList<>();
        List<String> rationalIncludeTaxIds = new ArrayList<>();
        int sampleCount = 0;
        int sampleIncludeTaxCount = 0;
        String unit = StrUtil.EMPTY;
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal amountIncludeTax = BigDecimal.ZERO;
        BigDecimal indexSum = BigDecimal.ZERO;
        BigDecimal indexSumIncludeTax = BigDecimal.ZERO;
        BigDecimal calculateValue = BigDecimal.ZERO;
        BigDecimal calculateValueIncludeTax = BigDecimal.ZERO;

        for (GcdpDwsIndex index : indexes) {
            // 现在制作hash有单位，可以考虑合并时候不判断单位
            if (StrUtil.isEmpty(unit) && StrUtil.isNotEmpty(index.getUnit())) {
                unit = index.getUnit();
            }

            // 样本的单体列表
            if (StringUtils.isNotBlank(index.getTempNoteIds())) {
                tempNoteIds.add(index.getTempNoteIds());
            }

            // 样本的科目id
            if (StringUtils.isNotBlank(index.getItemIds())) {
                ids.add(index.getItemIds());
            }

            // 统计不含税样本出值
            if (MathUtil.notNullAndNotZero(index.getIndexValue())) {
                // 不含税使用正态分布或不使用正态分布
                if (sampleRange.isNeedCalcIndex(index, false)) {
                    rationalIds.add(index.getTempNoteIds());
                    sampleCount++;
                    sampleRangeIndexes.add(index);
                    if (MathUtil.notNullAndNotZero(index.getCalcValue())) {
                        calculateValue = NumberUtil.add(calculateValue, index.getCalcValue());
                    }

                    if (MathUtil.notNullAndNotZero(index.getAmount())) {
                        amount = NumberUtil.add(amount, index.getAmount());
                    }

                    if (MathUtil.notNullAndNotZero(index.getIndexValue())) {
                        indexSum = NumberUtil.add(indexSum, index.getIndexValue());
                    }
                }
            }

            // 统计含税样本出值
            if (MathUtil.notNullAndNotZero(index.getIndexValueIncludeTax())) {
                // 不含税使用正态分布或不使用正态分布
                if (sampleRange.isNeedCalcIndex(index, true)) {
                    rationalIncludeTaxIds.add(index.getTempNoteIds());
                    sampleIncludeTaxCount++;
                    sampleRangeIncludeTaxIndexes.add(index);
                    if (MathUtil.notNullAndNotZero(index.getCalcValue())) {
                        calculateValueIncludeTax = NumberUtil.add(calculateValueIncludeTax, index.getCalcValue());
                    }

                    if (MathUtil.notNullAndNotZero(index.getAmountIncludeTax())) {
                        amountIncludeTax = NumberUtil.add(amountIncludeTax, index.getAmountIncludeTax());
                    }

                    if (MathUtil.notNullAndNotZero(index.getIndexValueIncludeTax())) {
                        indexSumIncludeTax = NumberUtil.add(indexSumIncludeTax, index.getIndexValueIncludeTax());
                    }
                }
            }
        }

        dest.setUnit(unit);

        // 设置样本的范围
        setItemMaxAndMin(sampleRangeIndexes, sampleRangeIncludeTaxIndexes, dest);
        dest.setSampleCount(sampleRangeIndexes.size());
        dest.setSampleCountIncludeTax(sampleRangeIncludeTaxIndexes.size());

        dest.setRationalNoteIds(StrUtil.join(StrUtil.COMMA, rationalIds));
        dest.setRationalNoteIdsIncludeTax(StrUtil.join(StrUtil.COMMA, rationalIncludeTaxIds));
        dest.setTempNoteIds(StrUtil.join(StrUtil.COMMA, tempNoteIds));

        // 计算均值不含税
        dest.setAvg(MathUtil.divStrZeroDashed(indexSum, new BigDecimal(sampleCount)));
        dest.setAvgIncludeTax(MathUtil.divStrZeroDashed(indexSumIncludeTax, new BigDecimal(sampleIncludeTaxCount)));

        // 计算加权均值
        dest.setWeightedAvg(MathUtil.divStrZeroDashed(amount, calculateValue));
        dest.setWeightedAvgIncludeTax(MathUtil.divStrZeroDashed(amountIncludeTax, calculateValueIncludeTax));

        // code根据type取
        dest.setDictCode(IndexTaxtRelDictEnum.getEnumByType(indexType).getCode());
        dest.setDictCodeIncludeTax(IndexTaxtRelDictEnum.getEnumByType(indexType).getCodeIncludeTax());

        dest.setItemIds(StrUtil.join(StrUtil.COMMA, ids));
        dest.setItemIds(orderedIds(dest.getItemIds()));

        //设置合并指标值为了判断是否需要过滤调该科目
        dest.setIndexValue(indexSum);
        dest.setIndexValueIncludeTax(indexSumIncludeTax);
    }

    private void setItemMaxAndMin(List<GcdpDwsIndex> indexes, List<GcdpDwsIndex> includeTaxIndexes, GcdpDwsIndex dest) {
        List<BigDecimal> indexs = indexes.stream().map(GcdpDwsIndex::getIndexValue).filter(MathUtil::notNullAndNotZero).collect(Collectors.toList());
        String minAndMax = MathUtil.combineMinAndMax(MathUtil.min(indexs), MathUtil.max(indexs));
        dest.setMinAndMax(minAndMax);

        // 由于正态分布按不含税统计的,所以含税的可能是0但样本也被选中了,这里区间还是统计0
        List<BigDecimal> includeTaxs = includeTaxIndexes.stream().map(GcdpDwsIndex::getIndexValueIncludeTax).filter(MathUtil::notNullAndNotZero).collect(Collectors.toList());
        String minAndMaxIncludeTaxs = MathUtil.combineMinAndMax(MathUtil.min(includeTaxs), MathUtil.max(includeTaxs));
        dest.setMinAndMaxIncludeTax(minAndMaxIncludeTaxs);
    }
}
