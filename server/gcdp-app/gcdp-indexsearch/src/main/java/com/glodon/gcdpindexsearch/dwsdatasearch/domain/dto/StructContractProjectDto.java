package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.glodon.gcdp.common.domain.PageData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "结构化的工程列表", description = "项目、业态、楼、专业")
public class StructContractProjectDto {

    @ApiModelProperty(value = "分页信息")
    private PageData page;

    @ApiModelProperty(value = "列表数据")
    private List<NoteMergeItem> projectList;

    @ApiModelProperty(value = "单体累加标记")
    private Integer simpleNoteSumFlag;

    @Data
    public static class NoteMergeItem implements Comparator<NoteMergeItem> {
        @ApiModelProperty(value = "显示名称", example = "项目、1#、土建工程")
        private String displayName;
        @ApiModelProperty(value = "项目编码", example = "XMHA2210240002")
        private String projectCode;
        @ApiModelProperty(value = "项目名称", example = "保利")
        private String projectName;
        @ApiModelProperty(value = "项目规模", example = "1000")
        private BigDecimal projectScale;
        @ApiModelProperty(value = "项目规模单位", example = "m2")
        private String projectScaleUnit;
        @ApiModelProperty(value = "项目id", example = "123456")
        private String projectId;
        @ApiModelProperty(value = "省编码", example = "11")
        private String provinceId;
        @ApiModelProperty(value = "省名称", example = "河南")
        private String provinceName;
        @ApiModelProperty(value = "市编码", example = "149")
        private String cityId;
        @ApiModelProperty(value = "市名称", example = "郑州")
        private String cityName;
        @ApiModelProperty(value = "区编码", example = "1251")
        private String districtId;
        @ApiModelProperty(value = "区名称", example = "金水区")
        private String districtName;
        @ApiModelProperty(value = "工程分类/业态编码", example = "001")
        private String projectCategoryCode;
        @ApiModelProperty(value = "工程分类/业态名称", example = "多层住宅")
        private String projectCategoryName;
        @ApiModelProperty(name = "beginDate", value = "开工时间")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date beginDate;
        @ApiModelProperty(name = "completionDate", value = "竣工时间")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date completionDate;

        // 合并信息
        @ApiModelProperty(value = "中台节点ID集合，外部不可持久化", example = "[10446,10447]")
        private List<String> tempNoteIds;
        @ApiModelProperty(value = "建面单方计算使用的阶段", example = "结算价")
        private String phase;
        @ApiModelProperty(value = "业态名称", example = "[超高层办公楼-1]")
        private List<String> categoryName;
        @ApiModelProperty(value = "业态名称", example = "[超高层办公楼-1]")
        private List<String> categoryPath;
        @ApiModelProperty(value = "楼栋标识", example = "[1#]")
        private List<String> ldNameIdentify;
        @ApiModelProperty(value = "楼栋原始名称", example = "[1号楼、1#楼]")
        private List<String> ldNameOriginal;
        @ApiModelProperty(value = "工程归档时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date archiveDate;
        @ApiModelProperty(value = "建筑面积", example = "0")
        private BigDecimal buildArea;

        @ApiModelProperty(value = "全费总造价（不含税）", required = false, example = "0")
        private BigDecimal fullCostTotal;
        @ApiModelProperty(value = "全费总造价（含税）", required = false, example = "0")
        private BigDecimal fullCostTotalIncludeTax;
        @ApiModelProperty(value = "全费单方造价（不含税）", required = false, example = "0")
        private BigDecimal fullCostDfIndexValue;
        @ApiModelProperty(value = "全费单方造价（含税）", required = false, example = "0")
        private BigDecimal fullCostDfIndexValueIncludeTax;

        @ApiModelProperty(value = "非全费总造价（不含税）", required = false, example = "0")
        private BigDecimal nonFullCostTotal;
        @ApiModelProperty(value = "非全费总造价（含税）", required = false, example = "0")
        private BigDecimal nonFullCostTotalIncludeTax;
        @ApiModelProperty(value = "非全费单方造价（不含税）", required = false, example = "0")
        private BigDecimal nonFullCostDfIndexValue;
        @ApiModelProperty(value = "非全费单方造价（含税）", required = false, example = "0")
        private BigDecimal nonFullCostDfIndexValueIncludeTax;

        @ApiModelProperty(value = "总造价（不含税）", example = "0")
        private BigDecimal totalAmount;
        @ApiModelProperty(value = "总造价（含税）", example = "0")
        private BigDecimal totalAmountIncludeTax;
        @ApiModelProperty(value = "单方造价（不含税）", example = "0")
        private BigDecimal totalDfIndexValue;
        @ApiModelProperty(value = "单方造价（含税）", example = "0")
        private BigDecimal totalDfIndexValueIncludeTax;

        @ApiModelProperty(value = "综合单价取费", example = "[1,2]")
        private List<Integer> itemCostType;
        @ApiModelProperty(value = "专业名称", example = "['专业1','专业2']")
        private List<String> tradeName;

        @ApiModelProperty(value = "专业维度工程特征,相同项取非空最新", example = "['专业1','专业2']")
        List<Feature> features;

        @ApiModelProperty(value = "项目信息json，剔除空值并压缩")
        private List<JSONObject> projectInfoDetail;

        // 合并项明细信息
        @ApiModelProperty(value = "合并项明细信息")
        private List<ContractProjectDto.NoteInfoDto> mergeInfos;

        // 用于指标库的共享企业业务
        @ApiModelProperty(value = "企业编码")
        private String enterpriseId;

        @ApiModelProperty(value = "产品来源标识[qyqd, ystz]")
        private List<String> productSource;

        // 项目、业态、楼栋、专业
        private String type;

        private Integer recommendScore = -1;
        private boolean recommend = false;

		// 当前节点的合并条件
        private String searchIds;

        // 项目下业态节点
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private List<NoteMergeItem> categoryList;
        // 楼栋列表
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private List<NoteMergeItem> ldList;
        // 虚拟楼栋
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private List<NoteMergeItem> zyList;
        // 虚拟节点
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private List<NoteMergeItem> xnjdList;

        @Override
        public int compare(NoteMergeItem o1, NoteMergeItem o2) {
            int result = o2.getRecommendScore().compareTo(o1.getRecommendScore());
            if (result == 0) {
                return DateUtil.compare(o2.getArchiveDate(), o1.getArchiveDate());
            }
            return result;
        }

        public void addFeature(Feature feature){
            if (CollUtil.isEmpty(features)){
                features = new ArrayList<>();
            }

            features.add(feature);
        }

        public void addLdNote(NoteMergeItem ldNote){
            if (CollUtil.isEmpty(ldList)){
                ldList = new ArrayList<>();
            }

            ldList.add(ldNote);
        }

        public void addCategoryNote(NoteMergeItem note){
            if (CollUtil.isEmpty(categoryList)){
                categoryList = new ArrayList<>();
            }

            categoryList.add(note);
        }

        public void addZyNote(NoteMergeItem zyNote){
            if (CollUtil.isEmpty(zyList)){
                zyList = new ArrayList<>();
            }

            zyList.add(zyNote);
        }
    }

    @Data
    public static class Feature{
        @ApiModelProperty(name = "name", value = "专业名称")
        private String tradeName;
        @ApiModelProperty(name = "values", value = "专业下的特征值")
        private List<FeatureValueOfTrade> values;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class FeatureValueOfTrade{
            @ApiModelProperty(name = "featureName", value = "特征名称")
            private String featureName;
            @ApiModelProperty(name = "value", value = "特征值")
            private String value;
            @ApiModelProperty(name = "unit", value = "特征单位")
            private String unit;
        }
    }

}
