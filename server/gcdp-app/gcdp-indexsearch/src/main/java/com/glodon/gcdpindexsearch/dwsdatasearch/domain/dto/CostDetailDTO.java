package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program ebq
 * @description TODO
 * @create 2023/8/17 10:24
 */

@Data
public class CostDetailDTO {
    private Long id;
    /** 清单编码 */
    private String itemCode;
    /** 清单名称 */
    private String itemName;
    /** 清单备注 */
    private String remark;
    /** 组价名称 */
    private String costName;
    /** 单价 */
    private BigDecimal rate;
    /** 合价 */
    private BigDecimal amount;
    /** 组价编码 */
    private String costIdentity;
    /** 计算公式 */
    private String baseAmount;
    /** 计算公式备注 */
    private String baseAmountRemark;
}
