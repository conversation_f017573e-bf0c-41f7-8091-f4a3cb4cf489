package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class TbAreaDto {
    private String areaid;
    private String pid;
    private String shortName;
    private String name;
    private Integer level;
    private Integer sort;
    private String areaCode;
    private List<TbAreaDto> children = Lists.newArrayList();
}
