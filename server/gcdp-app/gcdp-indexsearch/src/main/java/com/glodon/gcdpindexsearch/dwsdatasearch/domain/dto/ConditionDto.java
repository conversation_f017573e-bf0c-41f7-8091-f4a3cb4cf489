package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 反推筛选项的dto
 * @date 2022/10/25 18:15
 */
@Data
public class ConditionDto {

    @ApiModelProperty(value = "工程分类", required = true)
    public List<CategoryDto> category;

    @ApiModelProperty(value = "造价类型/测算阶段", required = true)
    public List<String> phase;

    @ApiModelProperty(value = "工程分类", required = true)
    public List<TbAreaDto> tbArea;

    @ApiModelProperty(value = "数据来源", required = true)
    public List<String> productSource;

    @ApiModelProperty(value = "数据来源", required = true)
    public List<String> tradeName;
}
