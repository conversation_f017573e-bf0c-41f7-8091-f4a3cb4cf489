package com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.cost;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexCost;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.AbstractProcessor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @packageName: com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.indexcost
 * @className: IndexCostLdProcessor
 * @author: yanyuhui <EMAIL>
 * @date: 2023/4/18 14:03
 * @description:
 */
@SuppressWarnings("all")
@Slf4j
public class IndexCostLdProcessor extends AbstractProcessor<GcdpDwsItemIndexCost> {

    @Override
    public List<GcdpDwsItemIndexCost> process(List<GcdpDwsItemIndexCost> indexCosts) {
        Map<String, List<GcdpDwsItemIndexCost>> hashCostMap = indexCosts.parallelStream().collect(Collectors.groupingBy(GcdpDwsItemIndexCost::getSubjectLdMergeHash));
        log.info("indexCosts数组的数量为:[{}] 楼栋hashCostMap数量:[{}]", indexCosts.size(), hashCostMap.size());
        if (indexCosts.size() == hashCostMap.size()) {
            return processNext(indexCosts);
        }
        StopWatch stopwatch = new StopWatch();
        stopwatch.start();
        List<GcdpDwsItemIndexCost> dataList = Collections.synchronizedList(new LinkedList<>());
        hashCostMap.entrySet().parallelStream().forEach(node -> {
            List<GcdpDwsItemIndexCost> costs = node.getValue();
            GcdpDwsItemIndexCost source = CollUtil.getFirst(costs);
            if (costs.size() == 1) {
                dataList.add(source);
                return;
            }
            GcdpDwsItemIndexCost dest = new GcdpDwsItemIndexCost();
            BeanUtil.copyProperties(source, dest);
            dataList.add(dest);
            combineReturnField(costs, dest);
        });
        stopwatch.stop();
        log.info("IndexCostLdProcessor合并处理耗时:[{}]秒", stopwatch.getLastTaskInfo().getTimeSeconds());
        return processNext(dataList);
    }

    /**
     * 将数据库的处理逻辑改成多线程处理了  有可能会存在同一栋楼被划分在多个组里, 所以需要在在数据汇总的时候 进行一次手动计算
     *
     * @param costs 成本数据
     * @param dest  组装的数据
     */
    private void combineReturnField(List<GcdpDwsItemIndexCost> costs, GcdpDwsItemIndexCost dest) {
        List<String> dfSampleIds = new ArrayList<>(costs.size());
        List<String> swlSampleIds = new ArrayList<>(costs.size());
        List<String> jmSampleIds = new ArrayList<>(costs.size());
        List<String> ids = new ArrayList<>(costs.size());

        BigDecimal swlAmount = BigDecimal.ZERO;
        BigDecimal swlAmountIncludeTax = BigDecimal.ZERO;
        BigDecimal jmAmount = BigDecimal.ZERO;
        BigDecimal jmAmountIncludeTax = BigDecimal.ZERO;
        BigDecimal dfAmount = BigDecimal.ZERO;
        BigDecimal dfAmountIncludeTax = BigDecimal.ZERO;

        for (GcdpDwsItemIndexCost cost : costs) {
            ids.add(cost.getIds());
            swlSampleIds.add(cost.getSwlSampleIds());
            jmSampleIds.add(cost.getJmSampleIds());
            dfSampleIds.add(cost.getDfSampleIds());

            if (cost.getJmAmount() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, cost.getJmAmount())) {
                jmAmount = NumberUtil.add(jmAmount, cost.getJmAmount());
            }
            if (cost.getJmAmountIncludeTax() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, cost.getJmAmountIncludeTax())) {
                jmAmountIncludeTax = NumberUtil.add(jmAmountIncludeTax, cost.getJmAmountIncludeTax());
            }

            if (cost.getSwlAmount() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, cost.getSwlAmount())) {
                swlAmount = NumberUtil.add(swlAmount, cost.getSwlAmount());
            }
            if (cost.getSwlAmountIncludeTax() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, cost.getSwlAmountIncludeTax())) {
                swlAmountIncludeTax = NumberUtil.add(swlAmountIncludeTax, cost.getSwlAmountIncludeTax());
            }

            if (cost.getDfAmount() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, cost.getDfAmount())) {
                dfAmount = NumberUtil.add(dfAmount, cost.getDfAmount());
            }
            if (cost.getDfAmountIncludeTax() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, cost.getDfAmountIncludeTax())) {
                dfAmountIncludeTax = NumberUtil.add(dfAmountIncludeTax, cost.getDfAmountIncludeTax());
            }
        }


        dest.setJmIndexValue(zeroNull(MathUtil.div(jmAmount, dest.getJmCalculateValue())));
        dest.setJmIndexValueIncludeTax(zeroNull(MathUtil.div(jmAmountIncludeTax, dest.getJmCalculateValue())));
        dest.setSwlIndexValue(zeroNull(MathUtil.div(swlAmount, dest.getSwlCalculateValue())));
        dest.setSwlIndexValueIncludeTax(zeroNull(MathUtil.div(swlAmountIncludeTax, dest.getSwlCalculateValue())));
        dest.setDfIndexValue(zeroNull(MathUtil.div(dfAmount, dest.getDfCalculateValue())));
        dest.setDfIndexValueIncludeTax(zeroNull(MathUtil.div(dfAmountIncludeTax, dest.getDfCalculateValue())));

        dest.setSwlAmount(zeroNull(swlAmount));
        dest.setSwlAmountIncludeTax(zeroNull(swlAmountIncludeTax));
        dest.setJmAmount(zeroNull(jmAmount));
        dest.setJmAmountIncludeTax(zeroNull(jmAmountIncludeTax));
        dest.setDfAmount(zeroNull(dfAmount));
        dest.setDfAmountIncludeTax(zeroNull(dfAmountIncludeTax));

        dest.setIds(StrUtil.join(StrUtil.COMMA, ids));
        dest.setSwlSampleIds(StrUtil.join(StrUtil.COMMA, swlSampleIds));
        dest.setJmSampleIds(StrUtil.join(StrUtil.COMMA, jmSampleIds));
        dest.setDfSampleIds(StrUtil.join(StrUtil.COMMA, dfSampleIds));
    }


}
