package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexBaseDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 建造标准对应的mapper类
 * <AUTHOR>
 * @date 2023-5-19
 */
@Mapper
public interface GcdpDwsBuildStandardIndexMapper {
    /**
     * 查询建造标准指标数据
     * @param ids
     * @return
     */
    List<CostIndexBaseDto> selectBuildStandardIndex(@Param(value = "ids")List<Long> ids);
}