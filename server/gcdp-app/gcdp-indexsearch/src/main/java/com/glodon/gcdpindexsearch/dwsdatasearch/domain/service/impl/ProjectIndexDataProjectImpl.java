package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.utils.SpringUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.IndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ProjectIndexDataDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ProjectIndexDataService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterIndexDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description: 查询工程维度指标Service
 * <AUTHOR>
 * @date 2022/10/24 11:01
 */
@Service("projectIndexDataService-project")
@Slf4j
public class ProjectIndexDataProjectImpl implements ProjectIndexDataService {
    @Override
    public IndexData getIndexData(FilterIndexDataVO filterIndexDataVO) {
        if (CollUtil.isEmpty(filterIndexDataVO.getIds())){
            return null;
        }
        ProjectIndexDataDetailService projectIndexDataDetailService = SpringUtil.getBean(filterIndexDataVO.getIndexType());
        return projectIndexDataDetailService.getIndexDataDetail(filterIndexDataVO);
    }

}
