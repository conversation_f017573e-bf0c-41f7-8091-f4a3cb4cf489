package com.glodon.gcdpindexsearch.infrastructure.business.itemapportion;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.domain.enums.ProductSource;
import com.glodon.gcdp.common.utils.MathUtil;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @packageName: com.glodon.gcdpindexsearch.infrastructure.business.itemapportion
 * @className: ApportionCalculate
 * @author: yanyh <EMAIL>
 * @date: 2024-05-15 15:52
 * @description: 费用分摊计算逻辑  仅仅包含某种数据源的时候进行分摊占比的计算
 */
public class ApportionCalculate {

    private static final Set<String> CAN_CALCULATE_PRODUCT_SOURCES = CollUtil.newHashSet(
            ProductSource.GCDP_ZBSQ_WEB.getIndex(),
            ProductSource.GCDP_ZBSQ_WEB_GBQD.getIndex(),
            ProductSource.HG_PLATFORM.getIndex(),
            ProductSource.GCDP_ZBW_ZBSQ_WEB.getIndex(),
            ProductSource.ZBSQ.getIndex(),
            ProductSource.GBQD.getIndex(),
            ProductSource.ZBGX.getIndex());

    private Set<String> currentDataSource = new HashSet<>();

    public ApportionCalculate(List<String> dataSource) {
        if (CollUtil.isNotEmpty(dataSource)) {
            this.currentDataSource = new HashSet<>(dataSource);
        }
    }

    public static ApportionCalculate apply(List<String> dataSources) {
        return new ApportionCalculate(dataSources);
    }


    public String calculateApportion(BigDecimal fz, BigDecimal fm) {
        if (isApportion()) {
            return MathUtil.divStr(fz, fm);
        }
        return "-";
    }


    public boolean isApportion() {
        for (String ds : currentDataSource) {
            if (CAN_CALCULATE_PRODUCT_SOURCES.contains(ds)) {
                continue;
            }
            return false;
        }
        return true;

    }

}
