package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "列头信息", description = "列头信息")
@Data
public class ColsDto {
    @ApiModelProperty(value = "列头编码", required = false, example = "xxx")
    private String code;
    @ApiModelProperty(value = "列头编码合并集合", required = false, example = "xxx")
    private List<String> codeList;
    @ApiModelProperty(value = "列头名称", required = false, example = "xxx")
    private String caption;
    @ApiModelProperty(value = "原始名称", required = false, example = "xxx")
    private String originalCaption;
    @JsonIgnore
    private BigDecimal buildArea;
    /**
     * 当code包含多个codeList时，统一最终的code
     */
    @JsonIgnore
    private String attrColCode;
}
