package com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndex;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexCost;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexUsage;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.cost.IndexCostLdProcessor;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.cost.IndexCostUnLdProcessor;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.usage.IndexUsageLdProcessor;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.usage.IndexUsageUnLdProcessor;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ItemDetailService;

import java.util.List;

/**
 * @packageName: com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.indexcost
 * @className: ProcessFactory
 * @author: yanyuhui <EMAIL>
 * @date: 2023/4/18 13:47
 * @description: 数据处理工厂
 */
public class ProcessFactory {
    private ProcessFactory() {}
    /**
     * 成本指标处理器
     *
     * @param dataList 数据集合
     * @return 处理后的dataList
     */
    public static List<GcdpDwsItemIndexCost> indexCostProcessor(List<GcdpDwsItemIndexCost> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return dataList;
        }
        AbstractProcessor<GcdpDwsItemIndexCost> ldProcessor = new IndexCostLdProcessor();
        AbstractProcessor<GcdpDwsItemIndexCost> unLdProcessor = new IndexCostUnLdProcessor();
        ldProcessor.setNextProcessor(unLdProcessor);
        return ldProcessor.process(dataList);
    }

    public static List<GcdpDwsItemIndexUsage> indexUsageProcessor(List<GcdpDwsItemIndexUsage> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return dataList;
        }
        AbstractProcessor<GcdpDwsItemIndexUsage> ldProcessor = new IndexUsageLdProcessor();
        AbstractProcessor<GcdpDwsItemIndexUsage> unLdProcessor = new IndexUsageUnLdProcessor();
        ldProcessor.setNextProcessor(unLdProcessor);
        return ldProcessor.process(dataList);
    }

    public static List<GcdpDwsIndex> indexProcessor(List<GcdpDwsIndex> dataList, String indexType, ItemDetailService detailService) {
        if (CollUtil.isEmpty(dataList)) {
            return dataList;
        }
        AbstractIndexProcessor<GcdpDwsIndex> indexCalcMergeProcessor = new IndexCalcMergeProcessor();
        AbstractIndexProcessor<GcdpDwsIndex> indexMergeProcessor = new IndexMergeProcessor(detailService);
        AbstractIndexProcessor<GcdpDwsIndex> indexPostProcessor = new IndexPostProcessor();
        indexCalcMergeProcessor.setNextProcessor(indexPostProcessor);
        indexPostProcessor.setNextProcessor(indexMergeProcessor);
        return indexCalcMergeProcessor.process(dataList, indexType);
    }


}
