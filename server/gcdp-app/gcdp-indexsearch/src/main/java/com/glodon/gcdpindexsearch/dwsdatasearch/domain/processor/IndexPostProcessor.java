package com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.common.utils.SpringUtil;
import com.glodon.gcdpindexsearch.common.util.TreeUtils;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndex;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.CommonIndexPostProcessService;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 指标后置处理
 * @date 2024-02-23 10:39
 * @email <EMAIL>
 */
@Slf4j
@SuppressWarnings("all")
public class IndexPostProcessor extends AbstractIndexProcessor<GcdpDwsIndex> {

    private CommonIndexPostProcessService<GcdpDwsIndex> dwsIndexPostProcessService;

    IndexPostProcessor() {
        dwsIndexPostProcessService = SpringUtil.getBean("CommonPostProcess");
    }

    @Override
    public List<GcdpDwsIndex> process(List<GcdpDwsIndex> dataList, String indexType) {
        TreeUtils.makePid(dataList);
        dwsIndexPostProcessService.processParentIndex(dataList, indexType);
        dataList.forEach(this::rebuildIndexValue);
        return processNext(dataList, indexType);
    }

    private void rebuildIndexValue(GcdpDwsIndex dwsIndex) {
        if (CollUtil.isEmpty(dwsIndex.getChildren())) {
            return;
        }
        // 父级指标重新构建  amount重新赋值 指标值重新赋值
        dwsIndex.setAmount(dwsIndex.getTotal());
        dwsIndex.setAmountIncludeTax(dwsIndex.getTotalIncludeTax());
        BigDecimal calcValue = dwsIndex.getCalcValue();
        //计算指标值
        if (MathUtil.notNullAndNotZero(dwsIndex.getTotal()) && MathUtil.notNullAndNotZero(calcValue)) {
            dwsIndex.setIndexValue(MathUtil.div(dwsIndex.getTotal(), calcValue));
        }
        if (MathUtil.notNullAndNotZero(dwsIndex.getTotalIncludeTax()) && MathUtil.notNullAndNotZero(calcValue)) {
            dwsIndex.setIndexValueIncludeTax(MathUtil.div(dwsIndex.getTotalIncludeTax(), calcValue));

        }

    }
}
