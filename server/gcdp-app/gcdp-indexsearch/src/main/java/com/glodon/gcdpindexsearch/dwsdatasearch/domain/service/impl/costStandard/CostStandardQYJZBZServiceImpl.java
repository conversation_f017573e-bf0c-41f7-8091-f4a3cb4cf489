package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.costStandard;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.StandardDescription;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYJZBZDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.ReferenceDataVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 企业建造标准-查询数据
 * <AUTHOR>
 * @date 2023/5/16 9:15
 */
@Service("CostStandardService-qyjzbz")
@Slf4j
public class CostStandardQYJZBZServiceImpl extends CostStandardAbsService {

    @Override
    public List<ReferenceDataQYJZBZDto> referenceData(ReferenceDataVO referenceDataVO) {
        List<ReferenceDataQYJZBZDto> jzbzDtoList = super.referenceData(referenceDataVO);
        return filterEmptyStandardValue(jzbzDtoList);
    }


    /**
     * @description:  过滤掉 配置说明 字段为空的 standardDescription 对象
     * @param jzbzDtoList:
     * @return void
     * @date: 2023/5/25 8:56
     */
    private  List<ReferenceDataQYJZBZDto> filterEmptyStandardValue(List<ReferenceDataQYJZBZDto> jzbzDtoList) {
        return jzbzDtoList.stream().filter(item -> {
            List<StandardDescription> standardDescriptionList = item.getStandardDescription();
            if (CollectionUtils.isEmpty(standardDescriptionList)) {
                return false;
            } else {
                item.setStandardDescription(standardDescriptionList.stream().filter(
                        des -> !StringUtils.isEmpty(des.getStandardValue())).collect(Collectors.toList()));
                return CollectionUtils.isNotEmpty(item.getStandardDescription());
            }
        }).collect(Collectors.toList());
    }

    @Override
    public <T extends ReferenceDataBaseDto> List<T> doSelectReferenceDataByNoteIdsAndName(Map<String, ReferenceDataBaseDto> projectNoteIdsMap, String name, Integer itemCostType) {
        return  (List<T>) referenceDataRepository.selectReferenceDataToQYJZBZLib(projectNoteIdsMap, name);
    }
}
