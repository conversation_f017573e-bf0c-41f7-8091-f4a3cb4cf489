package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.gcdp.common.domain.responsitory.dao.CostBaseMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsNewArchiveData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DwsNewArchiveDataMapper extends BaseMapper<DwsNewArchiveData>, CostBaseMapper<DwsNewArchiveData> {
    List<DwsNewArchiveData> getDataJsonByProjectCode(String enterpriseId, String projectCode, String phase);
    List<DwsNewArchiveData> getIncludeByProjectCode(String enterpriseId, String projectCode, String phase);
}
