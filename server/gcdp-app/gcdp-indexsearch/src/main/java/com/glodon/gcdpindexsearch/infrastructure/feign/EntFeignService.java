package com.glodon.gcdpindexsearch.infrastructure.feign;

import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdpindexsearch.infrastructure.config.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @className: ApiAuthFeignService
 * @description: 广联云ent服务接口调用层
 * @author: lijj-h
 * @date: 2022/7/13
 **/
@FeignClient(name = "EntFeignService",url = "https://ent.glodon.com",configuration = FeignConfiguration.class)
public interface EntFeignService {

    /**
    　　* @description: 后台系统直接查询成员列表
    　　* @param jsonObject,authorization
    　　* @return JSONObject
    　　* <AUTHOR>
    　　* @date 2022/7/13 17:28
    　　*/
    @RequestMapping(method = RequestMethod.POST, value = "/api/member/direct/query", consumes = {MediaType.APPLICATION_FORM_URLENCODED_VALUE})
    JSONObject queryNumberList(@RequestBody Map<String, ?> entityBody, @RequestHeader("Authorization") String authorization);

    /**
     　　* @description: 后台直接查询指定成员信息接口
     　　* @param userId, authorization
     　　* @return JSONObject
     　　* <AUTHOR>
     　　* @date 2022/7/13 17:28
     　　*/
    @RequestMapping(value = "/api/member/direct/queryOne?userId={userId}",method = RequestMethod.GET,headers = "Authorization={authorization}")
    JSONObject getNumberInfo(@PathVariable("userId") String userId, @PathVariable("authorization") String authorization);

    /**
     　　* @description: 根据用户ID查询用户及企业信息
     　　* @param userId, authorization
     　　* @return JSONObject
     　　* <AUTHOR>
     　　* @date 2022/7/13 17:28
     　　*/
    @RequestMapping(value = "/api/ent/getEntInfor?userId={userId}",method = RequestMethod.GET,headers = "Authorization={authorization}")
    JSONObject getEntInfo(@PathVariable("userId") String userId, @PathVariable("authorization") String authorization);

}
