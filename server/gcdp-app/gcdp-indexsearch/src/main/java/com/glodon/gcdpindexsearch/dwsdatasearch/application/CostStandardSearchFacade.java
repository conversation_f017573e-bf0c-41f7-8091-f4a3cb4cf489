package com.glodon.gcdpindexsearch.dwsdatasearch.application;


import com.glodon.gcdp.common.utils.SpringUtil;
import com.glodon.gcdpindexsearch.common.enums.CostStandardTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.CostStandardService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.ReferenceDataVO;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * @description: 企业成本标准相关库查询数据Facade
 * <AUTHOR>
 * @date 2023/5/15 14:15
 */
@Component
public class CostStandardSearchFacade {
    private final static String COST_STANDARD_SERVICE_PREFIX = "CostStandardService-";

    public  List<ReferenceDataBaseDto> referenceData(ReferenceDataVO referenceDataVO) {
        CostStandardService costStandardService = SpringUtil.getBean(COST_STANDARD_SERVICE_PREFIX + CostStandardTypeEnums.getTypeName(referenceDataVO.getType()));
        return costStandardService.referenceData(referenceDataVO);
    }
}
