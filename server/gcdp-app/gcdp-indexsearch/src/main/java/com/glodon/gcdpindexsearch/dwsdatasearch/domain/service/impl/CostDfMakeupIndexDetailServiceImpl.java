package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsIndex;
import com.glodon.gcdpindexsearch.common.enums.FilterTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.CostDfMakeupIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.MakeupIndexDetailService;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("DuplicatedCode")
@Service("makeupIndexDetailServiceDF")
public class CostDfMakeupIndexDetailServiceImpl implements MakeupIndexDetailService {

    @Autowired
    private GcdpDwsIndexMapper gcdpDwsIndexMapper;

    @Override
    public List<MakeUpIndexData> listMakeupIndexDetail(List<MakeUpIndexData> indexDataList, String itemIds, FilterTypeEnums filterType) {
        if (CollUtil.isEmpty(indexDataList)) {
            return Lists.newArrayList();
        }
        //查询科目信息
        List<DwsIndex> dwsIndices = assembleIndexes(itemIds, filterType);
        if (CollUtil.isEmpty(dwsIndices)) {
            return Lists.newArrayList();
        }
        return getIndexData(indexDataList, dwsIndices);
    }

    /**
     * 查询科目数据
     * @param itemIds: 科目id
     * @param filterType: 过滤类别
     */
    List<DwsIndex> assembleIndexes(String itemIds, FilterTypeEnums filterType){
        List<Long> itemIdList = Arrays.stream(itemIds.split(Constant.COMMA)).map(Long::new).collect(Collectors.toList());
        List<DwsIndex> dwsIndices = gcdpDwsIndexMapper.selectByItemIds(itemIdList);
        if (CollUtil.isEmpty(dwsIndices)){
            return Lists.newArrayList();
        }

        switch (filterType){
            case NO_TAX_VALID:
                return dwsIndices.stream()
                        .filter(x -> (x.getDfIndexValue() != null && x.getDfIndexValue().compareTo(BigDecimal.ZERO) != 0)).collect(Collectors.toList());
            case INCLUDE_TAX_VALID:
                return dwsIndices.stream()
                        .filter(x -> (x.getDfIndexValueIncludeTax() != null && x.getDfIndexValueIncludeTax().compareTo(BigDecimal.ZERO) != 0)).collect(Collectors.toList());
            default:
                return dwsIndices.stream()
                        .filter(x -> (x.getDfIndexValueIncludeTax() != null && x.getDfIndexValueIncludeTax().compareTo(BigDecimal.ZERO) != 0)
                                || (x.getDfIndexValue() != null && x.getDfIndexValue().compareTo(BigDecimal.ZERO) != 0)).collect(Collectors.toList());
        }
    }

    private List<MakeUpIndexData> getIndexData(List<MakeUpIndexData> indexDataList, List<DwsIndex> dwsIndices) {
        List<MakeUpIndexData> data = new ArrayList<>();
        Map<String, List<DwsIndex>> calculateValueMap = dwsIndices.stream().collect(Collectors.groupingBy(DwsIndex::getDfIndexWithCalcMergeHash));
        for (Map.Entry<String, List<DwsIndex>> entry : calculateValueMap.entrySet()) {
            List<DwsIndex> dwsIndexList = entry.getValue();
            CostDfMakeupIndexData costDfMakeupIndexData = new CostDfMakeupIndexData();
            MakeUpIndexData makeUpIndexData = this.setSharedFields(indexDataList, entry);
            if (makeUpIndexData == null) {
                continue;
            }
            BeanUtils.copyProperties(makeUpIndexData, costDfMakeupIndexData);
            setIndexDataField(costDfMakeupIndexData, dwsIndexList);
            data.add(costDfMakeupIndexData);
        }
        return data;
    }

    private void setIndexDataField(CostDfMakeupIndexData costDfMakeupIndexData, List<DwsIndex> dwsIndices) {
        DwsIndex dwsIndex = dwsIndices.get(0);
        costDfMakeupIndexData.setName(dwsIndex.getName());
        costDfMakeupIndexData.setUnit(dwsIndex.getDfIndexUnit());
        costDfMakeupIndexData.setCalculateName(dwsIndex.getDfCalculateName() == null ? Constant.LINE : dwsIndex.getDfCalculateName());
        //计算口径取最新归档的
        BigDecimal calValue = dwsIndices.stream().filter(item -> MathUtil.notNullAndNotZero(item.getDfIndexValue())
                        || MathUtil.notNullAndNotZero(item.getDfIndexValueIncludeTax()))
                .sorted(Comparator.comparing(DwsIndex::getArchiveDate).reversed())
                .map(DwsIndex::getDfCalculateValue).filter(Objects::nonNull).findFirst().orElse(null);
        setValue(costDfMakeupIndexData, dwsIndices, calValue);
    }

    private void setValue(CostDfMakeupIndexData costDfMakeupIndexData, List<DwsIndex> dwsIndices, BigDecimal dfCalculateValue) {
        BigDecimal amount = dwsIndices.stream().filter(x -> x.getDfCalculateValue() != null && x.getDfIndexValue() != null)
                .map(item -> MathUtil.isGreaterZero(item.getAmount()) ? item.getAmount() : item.getDfCalculateValue().multiply(item.getDfIndexValue()).setScale(6, RoundingMode.HALF_DOWN))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal amountIncludeTax = dwsIndices.stream().filter(x -> x.getDfCalculateValue() != null && x.getDfIndexValueIncludeTax() != null)
                .map(item -> MathUtil.isGreaterZero(item.getAmountIncludeTax()) ? item.getAmountIncludeTax() : item.getDfCalculateValue().multiply(item.getDfIndexValueIncludeTax()).setScale(6, RoundingMode.HALF_DOWN))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        costDfMakeupIndexData.setAmount(amount.toString());
        costDfMakeupIndexData.setAmountIncludeTax(amountIncludeTax.toString());

        if (dfCalculateValue == null) {
            costDfMakeupIndexData.setCalculateValue(Constant.LINE);
            costDfMakeupIndexData.setDfIndexValue(Constant.LINE);
        } else if (BigDecimal.ZERO.compareTo(dfCalculateValue) == 0) {
            costDfMakeupIndexData.setDfIndexValue(Constant.LINE);
            costDfMakeupIndexData.setCalculateValue(dfCalculateValue.toString());
        } else {
            costDfMakeupIndexData.setDfIndexValue(amount.divide(dfCalculateValue, 6, RoundingMode.HALF_DOWN).toString());
            costDfMakeupIndexData.setDfIndexValueIncludeTax(amountIncludeTax.divide(dfCalculateValue, 6, RoundingMode.HALF_DOWN).toString());
            costDfMakeupIndexData.setCalculateValue(dfCalculateValue.toString());
        }
    }

}
