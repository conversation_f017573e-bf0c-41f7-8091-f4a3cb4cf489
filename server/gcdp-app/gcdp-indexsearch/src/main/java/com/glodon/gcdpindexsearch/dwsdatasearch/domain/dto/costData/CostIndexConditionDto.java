package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.CategoryDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 条件反推返回数据实体
 * <AUTHOR>
 * @date 2023-5-16
 */
@Data
public class CostIndexConditionDto {
    @ApiModelProperty(value = "工程分类", required = true)
    public List<CategoryDto> category;

    @ApiModelProperty(value = "造价类型/测算阶段", required = true,example = "[\"中标价\",\"可研版\"]")
    public List<String> phase;

    @ApiModelProperty(value = "产品定位", required = true,example = "[\"高档\",\"中档\"]")
    public List<String> position;
}
