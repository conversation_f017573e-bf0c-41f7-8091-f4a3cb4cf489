package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.costData;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.glodon.gcdp.dwdservice.domain.service.project.IDwdProjectAuthEntInfoService;
import com.glodon.gcdp.dwdservice.domain.vo.OrgAuthVo;
import com.glodon.gcdpindexsearch.common.util.EmptyUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.CategoryDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexConditionDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.StandardDescription;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsCostDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.CostDataService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexListVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexMakeupVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 全成本指标库和建造做法指标库抽象类
 *
 * <AUTHOR>
 * @date 2023-5-17
 */
@Log4j2
public abstract class CostDataAbsService implements CostDataService {
    @Autowired
    protected IDwsDataRepository dwsDataRepository;
    @Autowired
    protected IDwsIndexDataRepository indexDataRepository;
    @Autowired
    protected IDwsCostDataRepository dwsCostDataRepository;

    @Autowired
    protected IDwdProjectAuthEntInfoService projectAuthEntInfoService;

    /**
     * @param costIndexMakeupVO:
     * @description: 根据 CostIndexMakeupVO 中不同的参数去获取指定的组成信息，需要子类实现
     * @date: 2023/5/22 14:52
     */
    abstract List<CostIndexMakeupBaseDto> getCostIndex(CostIndexMakeupVO costIndexMakeupVO);

    @Override
    public List<CostIndexMakeupBaseDto> costIndexMakeup(CostIndexMakeupVO costIndexMakeupVO) {
        // 获取指标科目数据
        List<CostIndexMakeupBaseDto> indexDtos = getCostIndex(costIndexMakeupVO);
        // 建造做法处理
        handleBuildStandard(indexDtos);
        // 指标科目关联其项目信息
        itemAssociateProjectInfo(indexDtos, getProjectInfos(indexDtos));
        return indexDtos;
    }

    @Override
    public CostIndexConditionDto getCondition(String enterpriseId, List<String> orgIds, List<String> productSource, List<String> authControlProjectCodeList) {
        CostIndexConditionDto costIndexConditionDto = new CostIndexConditionDto();

        OrgAuthVo orgAuthVo = projectAuthEntInfoService.countByEnterpriseId(enterpriseId, orgIds);

        List<DwsIndexProjectNote> categoryCodeAndPhaseAndPositionList =
                dwsDataRepository.selectCategoryCodeAndPhaseAndPosition(enterpriseId, productSource, orgAuthVo.getOrgIds(), orgAuthVo.getIsVirtualOrg(), authControlProjectCodeList);
        if (CollUtil.isNotEmpty(categoryCodeAndPhaseAndPositionList)) {
            categoryCodeAndPhaseAndPositionList = categoryCodeAndPhaseAndPositionList.stream().filter(Objects::nonNull).collect(Collectors.toList());
            // 造价类型/测算阶段
            List<String> phaseList = new ArrayList<>();
            //产品定位
            List<String> positionList = new ArrayList<>();
            // 工程分类根据名称转成父子结构   排序规则：按code排序
            List<CategoryDto> categoryDto = CategoryDto.buildTree(categoryCodeAndPhaseAndPositionList);
            costIndexConditionDto.setCategory(categoryDto);
            categoryCodeAndPhaseAndPositionList.stream().forEach(item -> {
                if (StringUtils.isNotBlank(item.getPhase()) && !phaseList.contains(item.getPhase())) {
                    phaseList.add(item.getPhase());
                }
                if (StringUtils.isNotBlank(item.getProductPosition()) && !positionList.contains(item.getProductPosition())) {
                    positionList.add(item.getProductPosition());
                }
            });
            costIndexConditionDto.setPhase(phaseList);
            costIndexConditionDto.setPosition(positionList);
        }
        return costIndexConditionDto;
    }

    /**
     * 根据筛选条件过滤样本量
     *
     * @param costIndexListVO
     * @return
     */
    protected List<DwsIndexProjectNote> filterProjectNoteByCondition(CostIndexListVO costIndexListVO) {
        OrgAuthVo orgAuthVo = projectAuthEntInfoService.countByEnterpriseId(costIndexListVO.getEnterpriseId(), costIndexListVO.getOrgIds());
        costIndexListVO.setIsVirtualOrg(orgAuthVo.getIsVirtualOrg());
        costIndexListVO.setOrgIds(orgAuthVo.getOrgIds());
        return dwsDataRepository.selectNoteByCondition(costIndexListVO);
    }

    /**
     * @param result
     * @return com.glodon.gcdpindexsearch.dwsdatasearch.application.dto.ConditionDto
     * @description: 设置pid
     * <AUTHOR>
     * @date 2022/10/25 18:35
     */
    protected <T extends CostIndexBaseDto> void makePid(List<T> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        /*
        父子关系会在groupby之后丢失，因此做法如下：
        1.sql中group_contract用逗号拼接id
        2.如果发生合并科目合并了几条上述结果id就会拼接几个
        3.用拼接后的id，拆开做key生成Map<id, id>
        4.查找pid，此处算法参考力扣第一题两数之和
        */
        Map<Long, Long> idMapping = new HashMap<>();
        result.forEach(item -> {
            for (String id : item.getIds().split(",")) {
                if (org.springframework.util.StringUtils.isEmpty(id)) {
                    continue;
                }
                idMapping.put(Long.parseLong(id), item.getId());
            }
        });
        result.forEach(item -> {
            if (idMapping.get(item.getPid()) == null) {
                item.setPid(-1l);
            } else {
                item.setPid(idMapping.get(item.getPid()));
            }
        });
    }

    /**
     * @param dtoList
     * @return java.util.List<com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupBaseDto>
     * @Description: 获取科目的项目信息
     * @Author: zhangj-cl
     * @Date: 2023/5/18 17:22
     */
    protected List<CostIndexMakeupBaseDto> getProjectInfos(List<CostIndexMakeupBaseDto> dtoList) {
        Set<Long> indexProjectNoteIds = dtoList.stream().map(CostIndexMakeupBaseDto::getIndexProjectNoteId).collect(Collectors.toSet());
        if (EmptyUtil.isEmpty(indexProjectNoteIds)) {
            return new ArrayList<>();
        }
        return dwsCostDataRepository.selectProjectInfoByNoteIds(indexProjectNoteIds);
    }

    /**
     * @param indexDtos
     * @Description: 建造标准处理
     * @Author: zhangj-cl
     * @Date: 2023/5/19 14:54
     */
    protected void handleBuildStandard(List<CostIndexMakeupBaseDto> indexDtos) {
        indexDtos.forEach(item -> {
            String standardJson = item.getStandardValue();
            List<StandardDescription> standardDescriptions = JSON.parseArray(standardJson, StandardDescription.class);
            if (EmptyUtil.isNotEmpty(standardDescriptions)) {
                item.setStandardDescription(standardDescriptions);
            }
        });
    }

    /**
     * @return java.util.List<T>
     * @Description: 科目关联项目信息
     * @Author: zhangj-cl
     * @Date: 2023/5/18 16:38
     */
    protected void itemAssociateProjectInfo(List<CostIndexMakeupBaseDto> itemList, List<CostIndexMakeupBaseDto> projectInfos) {
        Map<Long, CostIndexMakeupBaseDto> projectInfoMap = projectInfos.stream().collect(Collectors.toMap(CostIndexMakeupBaseDto::getIndexProjectNoteId, Function.identity()));
        itemList.forEach(item -> {
            CostIndexMakeupBaseDto projectInfo = projectInfoMap.get(item.getIndexProjectNoteId());
            if (EmptyUtil.isNotEmpty(projectInfo)) {
                BeanUtils.copyProperties(projectInfo, item, "id", "indexProjectNoteId", "standardValue", "name", "standardDescription");
            }
        });
    }
}
