package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterConditionVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterIndexDataVO;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 查询指标Service父类
 * @date 2022/10/24 10:58
 */
@Slf4j
public class IndexDataDetailService {

    protected static final int DEFAULT_NOTE_BATCH_SIZE = 100;

    @Autowired
    private ProjectNoteService projectNoteService;
    @Autowired
    private IDwsDataRepository dwsDataRepository;
    @Autowired
    @Qualifier("selectDataServiceExecutor")
    protected ThreadPoolTaskExecutor selectDataServiceExecutor;

    private static final String poin = ".";

    /**
     * @param result
     * @return com.glodon.gcdpindexsearch.dwsdatasearch.application.dto.ConditionDto
     * @description: 设置pid
     * <AUTHOR>
     * @date 2022/10/25 18:35
     */
    protected <T extends ProjectIndexData> void makePid(List<T> result) {
        long start = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        /*
        父子关系会在groupby之后丢失，因此做法如下：
        1.sql中group_contract用逗号拼接id
        2.如果发生合并科目合并了几条上述结果id就会拼接几个
        3.用拼接后的id，拆开做key生成Map<id, id>
        4.查找pid，此处算法参考力扣第一题两数之和
        */
        Map<Long, Long> idMapping = new HashMap<>();
        result.forEach(item -> {
            for (String id : item.getIds().split(",")) {
                if (StringUtils.isEmpty(id)) {
                    continue;
                }
                idMapping.put(Long.parseLong(id), item.getId());
            }
        });
        result.forEach(item -> {
            if (idMapping.get(item.getPid()) == null) {
                item.setPid(-1l);
            } else {
                item.setPid(idMapping.get(item.getPid()));
            }
        });
        long end = System.currentTimeMillis();
        log.info("makePid:{}", end-start);
    }

    protected <T extends ProjectIndexData> void makeCode(List<T> result) {
        long start = System.currentTimeMillis();
        if (CollUtil.isEmpty(result)) {
            return;
        }
        // 重新编制code
        Map<Long, List<ProjectIndexData>> resultMap = result.stream().collect(Collectors.groupingBy(ProjectIndexData::getPid));
        for (int i = 1; i < resultMap.get(-1l).size() + 1; i++) {
            String code = "" + i;
            resultMap.get(-1l).get(i - 1).setCode(code);
            this.setCode(resultMap, resultMap.get(-1l).get(i - 1).getId(), code);
        }
        long end = System.currentTimeMillis();
        log.info("makeCode:{}", end-start);
    }

    /**
     * @param resultMap 1
     * @param pid       2
     * @param pCode     3
     * @return void
     * @throws
     * @description: 递归生成code，从跟节点循环开始生成，如果有子节点一直递归
     * <AUTHOR>
     * @date 2022/11/10 15:48
     */
    private void setCode(Map<Long, List<ProjectIndexData>> resultMap, Long pid, String pCode) {
        if (!CollectionUtils.isEmpty(resultMap.get(pid))) {
            for (int i = 1; i < resultMap.get(pid).size() + 1; i++) {
                String code = pCode + poin + i;
                resultMap.get(pid).get(i - 1).setCode(code);
                this.setCode(resultMap, resultMap.get(pid).get(i - 1).getId(), code);
            }
        }
    }


    /**
     * 针对组成的数据添加排序
     * @param result 结果数据
     * @return 排序后的数据
     * @param <T> 指标数据
     */
    protected <T extends ProjectIndexData> List<T> sorted(List<T> result) {
        return result.stream().peek(x -> {
            // 有些特殊情况下科目无code 排序在最前面
            if (StrUtil.isEmpty(x.getCode())) {
                x.setCode("1");
            }
        }).sorted(Comparator.comparing(T::getCode)).sorted(Comparator.comparing(T::getName)).collect(Collectors.toList());
    }


    /**
     * 获取单体列表
     *
     * @param filterIndexDataVO
     * @return
     */
    protected List<DwsIndexProjectNote> getIdsByFilter(FilterIndexDataVO filterIndexDataVO) throws Exception {
        List<String> ids = filterIndexDataVO.getIds();
        String enterpriseId = filterIndexDataVO.getEnterpriseId();
        if (CollUtil.isNotEmpty(ids)) {
            return dwsDataRepository.selectNoteByIds(enterpriseId, ids.stream().map(Long::new).collect(Collectors.toList()));
        }
        FilterConditionVO filterConditionVO = filterIndexDataVO.getFilterConditionVO() == null ? new FilterConditionVO() : filterIndexDataVO.getFilterConditionVO();
        List<String> orgIds = filterIndexDataVO.getOrgIds();
        filterConditionVO.setAuthControlProjectCodeList(filterIndexDataVO.getAuthControlProjectCodeList());
        return projectNoteService.getValidNotes(enterpriseId, filterConditionVO, orgIds, null);
    }

    /**
     * 计算单体合并后的样本量个数
     *
     * @param jmSimpleIds
     * @param projectNoteMap
     * @return
     */
    protected Map<String, List<DwsIndexProjectNote>> mergeSampleIds(List<String> jmSimpleIds, Map<String, DwsIndexProjectNote> projectNoteMap) {
        if (CollUtil.isEmpty(jmSimpleIds)) {
            return new HashMap<>();
        }
        Map<String, List<DwsIndexProjectNote>> map = new HashMap<>();
        for (int i = 0; i < jmSimpleIds.size(); i++) {
            String jmSimpleId = jmSimpleIds.get(i);
            DwsIndexProjectNote note = projectNoteMap.get(jmSimpleId);
            if (note == null) {
                continue;
            }
            String key = note.getPhase() + Constant.LINE + note.getLdNameIdentify() + Constant.LINE + note.getProjectCode() + Constant.LINE + note.getBuildArea();
            List<DwsIndexProjectNote> list = map.get(key);
            if (list == null) {
                list = new ArrayList<>();
                list.add(note);
                map.put(key, list);
            } else {
                list.add(note);
            }
        }
        return map;
    }
    protected List<ProjectIndexData> filterInvalidData(List<ProjectIndexData> result) {
        long start = System.currentTimeMillis();
        if (CollUtil.isEmpty(result)) {
            return result;
        }

        Set<Long> showSet = new HashSet<>();

        Map<Long, Long> idAndPidMap = result.parallelStream().collect(Collectors.toMap(ProjectIndexData::getId, ProjectIndexData::getPid, (v1, v2) -> v2));

        result.forEach(item -> {
            if (isShow(item)) {
                Long id = item.getId();
                findParent(id, idAndPidMap, showSet);
            }
        });
        long end = System.currentTimeMillis();
        log.info("filterInvalidData:{}", end-start);

        return result.stream().filter(x -> showSet.contains(x.getId())).collect(Collectors.toList());
    }

    private void findParent(Long id, Map<Long, Long> idAndPidMap, Set<Long> showSet) {
        showSet.add(id);
        Long pid = idAndPidMap.get(id);

        if (pid != -1l && !showSet.contains(pid)) {
            findParent(pid, idAndPidMap, showSet);
        }
    }

    private boolean isShow(ProjectIndexData projectIndexData) {
        if (projectIndexData instanceof GcdpDwsIndexCost) {
            GcdpDwsIndexCost gcdpDwsIndexCost = (GcdpDwsIndexCost) projectIndexData;

            return (!"-".equals(gcdpDwsIndexCost.getJmDfValue()) && !"0".equals(gcdpDwsIndexCost.getJmDfValue()))
                    || (!"-".equals(gcdpDwsIndexCost.getJmDfValueIncludeTax()) && !"0".equals(gcdpDwsIndexCost.getJmDfValueIncludeTax()))
                    || (!"-".equals(gcdpDwsIndexCost.getSwlDfValue()) && !"0".equals(gcdpDwsIndexCost.getSwlDfValue()))
                    || (!"-".equals(gcdpDwsIndexCost.getSwlDfValueIncludeTax()) && !"0".equals(gcdpDwsIndexCost.getSwlDfValueIncludeTax()))
                    || (!"-".equals(gcdpDwsIndexCost.getDfZbValue()) && !"0".equals(gcdpDwsIndexCost.getDfZbValue()))
                    || (!"-".equals(gcdpDwsIndexCost.getDfZbValueIncludeTax()) && !"0".equals(gcdpDwsIndexCost.getDfZbValueIncludeTax()))
                    || (!"-".equals(gcdpDwsIndexCost.getParentPercent()) && !"0%".equals(gcdpDwsIndexCost.getParentPercent()))
                    || (!"-".equals(gcdpDwsIndexCost.getParentPercentIncludeTax()) && !"0%".equals(gcdpDwsIndexCost.getParentPercentIncludeTax()))
                    || (!"-".equals(gcdpDwsIndexCost.getTotalPercent()) && !"0%".equals(gcdpDwsIndexCost.getTotalPercent()))
                    || (!"-".equals(gcdpDwsIndexCost.getTotalPercentIncludeTax()) && !"0%".equals(gcdpDwsIndexCost.getTotalPercentIncludeTax()));
        } else if (projectIndexData instanceof GcdpDwsIndexUsage) {
            GcdpDwsIndexUsage gcdpDwsIndexUsage = (GcdpDwsIndexUsage) projectIndexData;

            return (!"-".equals(gcdpDwsIndexUsage.getZylIndexValue()) && !"0".equals(gcdpDwsIndexUsage.getZylIndexValue()))
                    || (!"-".equals(gcdpDwsIndexUsage.getJmhlIndexValue()) && !"0".equals(gcdpDwsIndexUsage.getJmhlIndexValue()));
        } else if (projectIndexData instanceof GcdpDwsIndexMainRes) {
            GcdpDwsIndexMainRes gcdpDwsIndexMainRes = (GcdpDwsIndexMainRes) projectIndexData;

            return (!"-".equals(gcdpDwsIndexMainRes.getDjIndexValue()) && !"0".equals(gcdpDwsIndexMainRes.getDjIndexValue()))
                    || (!"-".equals(gcdpDwsIndexMainRes.getDjIndexValueIncludeTax()) && !"0".equals(gcdpDwsIndexMainRes.getDjIndexValueIncludeTax()))
                    || (!"-".equals(gcdpDwsIndexMainRes.getHlIndexValue()) && !"0".equals(gcdpDwsIndexMainRes.getHlIndexValue()))
                    || (!"-".equals(gcdpDwsIndexMainRes.getPercent()) && !"0%".equals(gcdpDwsIndexMainRes.getPercent()));
        } else if (projectIndexData instanceof GcdpDwsIndexEconomics) {
            GcdpDwsIndexEconomics gcdpDwsIndexEconomics = (GcdpDwsIndexEconomics) projectIndexData;

            return gcdpDwsIndexEconomics.getFzValue() != null
                    || gcdpDwsIndexEconomics.getFmValue() != null
                    || (!"-".equals(gcdpDwsIndexEconomics.getIndexValue()) && !"0".equals(gcdpDwsIndexEconomics.getIndexValue()));
        }

        return true;
    }

    /**
     * 移除数据为0的数据
     *
     * @param jmIndexValueIncludeTaxList
     * @param jmSampleIds
     * @param jmCalculateValues
     */
    protected void removeZeroData(List<BigDecimal> jmIndexValueIncludeTaxList, List<String> jmSampleIds,
                                  List<String> jmCalculateValues) {
        List<BigDecimal> indexValueList = new ArrayList<>();
        List<String> jmSampleIdList = new ArrayList<>();
        List<String> jmCalculateValueList = new ArrayList<>();
        for (int i = 0; i < jmIndexValueIncludeTaxList.size(); i++) {
            BigDecimal item = jmIndexValueIncludeTaxList.get(i);
            if (BigDecimal.ZERO.compareTo(item) != 0) {
                indexValueList.add(item);
                jmSampleIdList.add(jmSampleIds.get(i));
                if (CollUtil.isNotEmpty(jmCalculateValues)) {
                    jmCalculateValueList.add(jmCalculateValues.get(i));
                }
            }
        }
        jmIndexValueIncludeTaxList.clear();
        jmIndexValueIncludeTaxList.addAll(indexValueList);
        jmSampleIds.clear();
        jmSampleIds.addAll(jmSampleIdList);
        if (CollUtil.isNotEmpty(jmCalculateValues)) {
            jmCalculateValues.clear();
            jmCalculateValues.addAll(jmCalculateValueList);
        }
    }

    /**
     * 获取合并后的单方值列表
     *
     * @param map
     * @param swlIndexValueIncludeTaxList
     * @param swlSampleIds
     * @param swlCalculateValues
     * @return
     */
    protected List<BigDecimal> getNewIndexValueList(Map<String, List<DwsIndexProjectNote>> map,
                                                    List<BigDecimal> swlIndexValueIncludeTaxList,
                                                    List<String> swlSampleIds,
                                                    List<String> swlCalculateValues) {
        List<Integer> removeList = new ArrayList<>();
        List<BigDecimal> newList = new ArrayList<>();
        for (Map.Entry<String, List<DwsIndexProjectNote>> entry : map.entrySet()) {
            if (entry == null || entry.getValue() == null || entry.getValue().size() == 1) {
                continue;
            }
            List<DwsIndexProjectNote> list = entry.getValue();
            BigDecimal amount = BigDecimal.ZERO;
            BigDecimal calculate = BigDecimal.ZERO;
            for (DwsIndexProjectNote note : list) {
                int i = swlSampleIds.indexOf(note.getId().toString());
                if (i >= 0) {
                    BigDecimal calculateValue = new BigDecimal(swlCalculateValues.get(i));
                    BigDecimal indexValue = swlIndexValueIncludeTaxList.get(i);
                    amount = amount.add(indexValue.multiply(calculateValue)).setScale(6, RoundingMode.HALF_DOWN);
                    calculate = calculateValue;
                    removeList.add(i);
                }
            }
            if (calculate != null && BigDecimal.ZERO.compareTo(calculate) != 0) {
                newList.add(amount.divide(calculate, 6, RoundingMode.HALF_DOWN));
            }
        }
        for (int i = 0; i < swlIndexValueIncludeTaxList.size(); i++) {
            if (removeList.contains(i)) {
                continue;
            }
            newList.add(swlIndexValueIncludeTaxList.get(i));
        }
        return newList.stream().sorted(Comparator.comparing(item -> item)).collect(Collectors.toList());
    }

    /**
     * 获取最新的平均值
     *
     * @param count
     * @param swlIndexValueIncludeTaxList
     * @return
     */
    protected String getAvg(Integer count, List<BigDecimal> swlIndexValueIncludeTaxList) {
        if (CollUtil.isNotEmpty(swlIndexValueIncludeTaxList) && count != null && count != 0) {
            BigDecimal jmAvg = swlIndexValueIncludeTaxList.stream().reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(count), 6, RoundingMode.HALF_DOWN);
            return jmAvg.toString();
        }
        return Constant.LINE;
    }

}
