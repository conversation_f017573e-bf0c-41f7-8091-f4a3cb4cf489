package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdp.common.domain.Page;
import com.glodon.gcdp.common.domain.PageData;
import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdp.common.domain.consts.StringConst;
import com.glodon.gcdp.common.domain.consts.ValueConst;
import com.glodon.gcdp.common.domain.enums.ArchiveTypeEnum;
import com.glodon.gcdp.common.domain.enums.DwsNoteTypeEnums;
import com.glodon.gcdp.common.domain.enums.ProductSource;
import com.glodon.gcdp.common.domain.enums.StageEnum;
import com.glodon.gcdp.common.utils.CaliberUtil;
import com.glodon.gcdp.common.utils.CopyUtils;
import com.glodon.gcdp.common.utils.DateUtils;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dimservice.domain.dao.entity.DimTbArea;
import com.glodon.gcdp.dimservice.domain.dao.entity.DimZbStandardsTrade;
import com.glodon.gcdp.dimservice.domain.dao.service.DimProjectCategoryService;
import com.glodon.gcdp.dimservice.domain.dao.service.IDimTradeInfoService;
import com.glodon.gcdp.dimservice.domain.service.DimTbAreaService;
import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject;
import com.glodon.gcdp.dwdservice.domain.model.OriginalContractProject;
import com.glodon.gcdp.dwdservice.domain.service.project.IDwdProjectAuthEntInfoService;
import com.glodon.gcdp.dwdservice.domain.vo.OrgAuthVo;
import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsBuildStandardIndex;
import com.glodon.gcdp.dwsindexservice.domain.dao.mapper.DwsBuildStandardIndexMapper;
import com.glodon.gcdpindexsearch.common.constant.BusinessConstants;
import com.glodon.gcdpindexsearch.common.enums.JADetailsEnums;
import com.glodon.gcdpindexsearch.common.util.FilterUtil;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.service.ZbkResIdAndOriginalProjectIdRelationService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwsNewArchiveDataMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.Constants;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.SumTypeEnum;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwdDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.CommonHandler;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ProjectNoteService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.utils.EscapeUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.*;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.glodon.gcdpindexsearch.infrastructure.annotation.TakeTime;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.glodon.gcdp.common.domain.enums.DwsNoteTypeEnums.*;
import static com.glodon.gcdp.common.domain.enums.ProductSource.*;
import static com.glodon.gcdpindexsearch.common.constant.ProjectConst.*;


/**
 * <AUTHOR>
 * @description: 工程数据接口Service
 * @date 2022/10/24 11:01
 */
@Service
@Slf4j
public class ProjectNoteServiceImpl implements ProjectNoteService {

    @Autowired
    private IDwsDataRepository dwsDataRepository;
    @Autowired
    private IDwdDataRepository dwdDataRepository;
    @Autowired
    private IDwdProjectAuthEntInfoService projectAuthEntInfoService;
    @Autowired
    private ZbkResIdAndOriginalProjectIdRelationService zbkResIdAndOriginalProjectIdRelationService;
    @Autowired
    private DimTbAreaService dimTbAreaService;
    @Autowired
    private DwsNewArchiveDataMapper newArchiveDataMapper;
    @Autowired
    private DimProjectCategoryService projectCategoryService;
    @Autowired
    private IDimTradeInfoService dimTradeInfoService;
    @Autowired
    private DwsBuildStandardIndexMapper buildStandardIndexMapper;
    /**
     * 系统内置项目信息
     */
    private static final List<String> DEFAULT_PROJECTINFO_FIELD = Arrays.asList(
            "项目名称",
            "项目地点",
            "项目规模",
            "项目规模单位",
            "项目分类",
            "开工时间",
            "竣工时间",
            "产品定位",
            "建设性质",
            "建设单位");
    /**
     * 需要特殊处理的，跳过的信息
     */
    private static final List<String> SKIP_PROJECTINFO_FIELD = Arrays.asList(
            "项目名称",
            "项目地点");
    /**
     * 系统内置项目信息字段名
     */
    private static final List<String> DEFAULT_PROJECTINFO_FIELDNAME = Arrays.asList(
            "ProjectName",
            "ProjectArea",
            "ProjectScale",
            "ProjectScaleUnit",
            "ProjectCategoryName",
            "StartTime",
            "EndTime",
            "ProductPositioning",
            "Jianshexingzhi",
            "ConstructionUnit");

    private static final LinkedHashMap<String, String> PROJECTINFO_FIELDNAME_MAP = new LinkedHashMap<>();
    static {
        PROJECTINFO_FIELDNAME_MAP.put("projectCategoryName", "工程分类");
        PROJECTINFO_FIELDNAME_MAP.put("projectName", "项目名称");
        PROJECTINFO_FIELDNAME_MAP.put("projectArea", "项目地点");
        PROJECTINFO_FIELDNAME_MAP.put("projectScale", "项目规模");
        PROJECTINFO_FIELDNAME_MAP.put("projectScaleUnit", "项目规模单位");
        PROJECTINFO_FIELDNAME_MAP.put("startTime", "开工时间");
        PROJECTINFO_FIELDNAME_MAP.put("endTime", "竣工时间");
        PROJECTINFO_FIELDNAME_MAP.put("productPositioning", "产品定位");
        PROJECTINFO_FIELDNAME_MAP.put("jianshexingzhi", "建设性质");
        PROJECTINFO_FIELDNAME_MAP.put("constructionUnit", "建设单位");
    }


    /**
     * @param enterpriseId
     * @param sumCondition
     * @param orgIds
     * @return com.glodon.gcdpindexsearch.dwsdatasearch.application.dto.ConditionDto
     * @description: 反推筛选条件-工程分类、造价类型
     * <AUTHOR>
     * @date 2022/10/25 18:36
     */
    @Override
    public ConditionDto getCondition(String enterpriseId,
                                     Integer sumCondition,
                                     List<String> orgIds,
                                     List<String> sharedEnterpriseIds,
                                     List<String> productSource,
                                     List<String> authControlProjectCodes) {
        ConditionDto conditionDto = new ConditionDto();

        // 获取类别type   按项目：type=所有；按业态：type=业态+楼栋；按单体：type=楼栋
        List<Integer> typeList = SumTypeEnum.getTypeListByCode(sumCondition);
        // 查库
        OrgAuthVo orgAuthVo = projectAuthEntInfoService.countByEnterpriseId(enterpriseId, orgIds);

        List<ConditionResultDto> conditionResultDtoList = dwsDataRepository.selectCondition(enterpriseId, typeList,
                orgAuthVo.getOrgIds(), orgAuthVo.getIsVirtualOrg(), sharedEnterpriseIds, productSource, authControlProjectCodes);

        Set<String> categoryCodeAndPhaseSet = new HashSet<>();
        Set<String> areaSet = new HashSet<>();
        Set<String> productSourceSet = new HashSet<>();
        List<DwsIndexProjectNote> categoryCodeAndPhaseList = new ArrayList<>();
        List<ConditionResultDto> areaList = new ArrayList<>();
        Set<String> tradeNameSet = new HashSet<>();

        for (ConditionResultDto condition : conditionResultDtoList) {
            String categoryAndPhaseCombination = condition.getProjectCategoryCode() + condition.getProjectCategoryName() + condition.getPhase();
            if (!categoryCodeAndPhaseSet.contains(categoryAndPhaseCombination)) {
                categoryCodeAndPhaseSet.add(categoryAndPhaseCombination);
                categoryCodeAndPhaseList.add(condition);
            }
            String areaCombination = condition.getProvinceId() + condition.getCityId() + condition.getDistrictId();
            if (!areaSet.contains(areaCombination)) {
                areaSet.add(areaCombination);
                areaList.add(condition);
            }
            productSourceSet.add(condition.getProductSource());
            // 专业
            if (StringUtils.isNotBlank(condition.getTradeName())){
                tradeNameSet.add(condition.getTradeName());
            }
        }

        // 业态、造价阶段
        if (CollUtil.isNotEmpty(categoryCodeAndPhaseList)) {
            categoryCodeAndPhaseList = categoryCodeAndPhaseList.stream().filter(Objects::nonNull).collect(Collectors.toList());
            // 造价类型/测算阶段  排序规则：造价类型的放前面，测算阶段的放后面，重名的按测算阶段处理
            List<String> phaseList = this.getPhaseList(categoryCodeAndPhaseList);
            conditionDto.setPhase(phaseList);

            // 工程分类根据名称转成父子结构   排序规则：按code排序
            List<CategoryDto> categoryDto = CategoryDto.buildTree(categoryCodeAndPhaseList);
            conditionDto.setCategory(categoryDto);
        }
        // 地区
        if (CollUtil.isNotEmpty(areaList)) {
            List<TbAreaDto> tbAreaDtoWithLevel = getTbAreaDto(areaList);
            conditionDto.setTbArea(tbAreaDtoWithLevel);
        }
        // 数据来源
        conditionDto.setProductSource(Lists.newArrayList(productSourceSet));
        // 专业名称
        conditionDto.setTradeName(Lists.newArrayList(tradeNameSet));
        return conditionDto;
    }

    private List<TbAreaDto> getTbAreaDto(List<ConditionResultDto> areaList) {
        List<TbAreaDto> tbAreaDtoWithLevel = Lists.newArrayList();
        List<String> areaIds = areaList.stream()
                .map(dto -> {
                    List<String> ids = new ArrayList<>();
                    ids.add(dto.getProvinceId());
                    ids.add(dto.getCityId());
                    ids.add(dto.getDistrictId());
                    return ids;
                })
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        List<DimTbArea> dimTbAreas = dimTbAreaService.selectByAreaIds(areaIds);
        if (CollUtil.isNotEmpty(dimTbAreas)) {
            tbAreaDtoWithLevel = buildHierarchy(CopyUtils.copyList(dimTbAreas, TbAreaDto::new));
        }
        return tbAreaDtoWithLevel;
    }

    public List<TbAreaDto> buildHierarchy(List<TbAreaDto> areaList) {
        Map<String, TbAreaDto> areaMap = areaList.stream()
                .collect(Collectors.toMap(TbAreaDto::getAreaid, area -> area));
        List<TbAreaDto> rootAreas = new ArrayList<>();
        areaList.forEach(area -> {
            String parentId = area.getPid();
            if (parentId != null && areaMap.containsKey(parentId)) {
                TbAreaDto parentArea = areaMap.get(parentId);
                parentArea.getChildren().add(area);
            } else {
                rootAreas.add(area);
            }
        });
        return rootAreas;
    }


    /**
     * @param categoryCodeAndPhaseList
     * @return java.util.List<java.lang.String>
     * @description: 造价类型/测算阶段  排序规则：造价类型的放前面，测算阶段的放前面，重名的按测算阶段处理
     */
    private List<String> getPhaseList(List<DwsIndexProjectNote> categoryCodeAndPhaseList) {
        List<String> phaseList = categoryCodeAndPhaseList.stream().map(DwsIndexProjectNote::getPhase).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> stageList = StageEnum.getList().stream().filter(phaseList::contains).collect(Collectors.toList());
        phaseList = phaseList.stream().filter(x -> !stageList.contains(x)).collect(Collectors.toList());
        phaseList.addAll(stageList);
        return phaseList;
    }

    /**
     * @param enterpriseId
     * @param filterConditionVO
     * @param orgIds
     * @return com.glodon.gcdp.common.domain.Page<com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ProjectSearchDto>
     * @description: 工程维度列表
     * <AUTHOR>
     * @date 2022/11/2 14:21
     */
    @Override
    public Page<ProjectSearchDto> projectAnalyzeList(String enterpriseId, FilterConditionVO filterConditionVO, List<String> orgIds) throws Exception {
        Page<ProjectSearchDto> projectSearchDtoPage = new Page<>();
        if (CollectionUtils.isEmpty(filterConditionVO.getSharedEnterpriseId()) && Constant.NOT_INCLUDE_SELF.equals(filterConditionVO.getIncludeSelfFlag())) {
            return projectSearchDtoPage;
        }
        List<DwdProjectInfo> validProjects = this.getProjectsByDetailCondition(filterConditionVO, enterpriseId, orgIds);

        if (CollUtil.isEmpty(validProjects)) {
            return projectSearchDtoPage;
        }
        // 查询数据库
        List<DwsIndexProjectNote> dwsIndexProjectNotes = this.selectSharedNoteByDb(filterConditionVO,validProjects);

        if (CollUtil.isEmpty(dwsIndexProjectNotes)) {
            return projectSearchDtoPage;
        }

        // 非建安
        Map<String, DwsIndexProjectNote> nonConstructionMap = new HashMap<>();
        if (SumTypeEnum.PROJECT.getCode().equals(filterConditionVO.getSumCondition())) {
            // 非建安
            nonConstructionMap = dwsIndexProjectNotes.stream()
                    .filter(x -> NONE.getIndex().equals(x.getType()) && x.getNonConstruction())
                    .collect(Collectors.toMap(x ->x.getEnterpriseId() + x.getProjectCode() + x.getPhase(), Function.identity(), (v1, v2) -> v2));
        }

        List<DwsIndexProjectNote> validNotes = this.getNoteByCondition(filterConditionVO, dwsIndexProjectNotes);

        if (CollUtil.isEmpty(validNotes)) {
            return projectSearchDtoPage;
        }

        // 数据合并
        Map<String, List<DwsIndexProjectNote>> groupNote = this.mergeNoteBySumCondition(filterConditionVO.getSumCondition(), validNotes);

        // 构建返回体
        List<ProjectSearchDto> result = this.convertToDto(filterConditionVO, groupNote, validProjects, nonConstructionMap);

        // 排序、分页
        return this.convertPageDto(filterConditionVO.getCurrentPage(), filterConditionVO.getPageSize(), result);
    }

    @Override
    public ContractProjectDto contractProjectList(String enterpriseId, FilterConditionVO conditionVO, List<String> orgIds) throws Exception {
        // 获取有效的note
        Map<String, DwdProjectInfo> projectInfoMap = Maps.newHashMap();
        List<DwsIndexProjectNote> validNotes = this.getValidNotes(enterpriseId, conditionVO, orgIds, projectInfoMap);

        // 合并并构建返回体
        List<ContractProjectDto.NoteMergeDto> noteMergeDtoList =
                this.convertToNoteMergeDto(conditionVO.getSumCondition(), conditionVO.getSimpleNoteSumFlagV2(), validNotes, projectInfoMap);

        // 基于结果的处理
        this.mergeAfterFilter(conditionVO, noteMergeDtoList);

        // 排序分页
        return this.convertContractProjectDto(conditionVO.getCurrentPage(), conditionVO.getPageSize(), noteMergeDtoList, conditionVO.getSimpleNoteSumFlagV2(), conditionVO.getFetchAll());
    }

    /**
     * getValidNotes
     * @param enterpriseId
     * @param filterConditionVO
     * @param orgIds
     * @param projectInfoMap 如果调用处需要项目信息，则传入一个空的map，否则不传
     * @return
     * @throws Exception
     */
    @Override
    public List<DwsIndexProjectNote> getValidNotes(String enterpriseId,
                                                    FilterConditionVO filterConditionVO,
                                                    List<String> orgIds,
                                                    Map<String, DwdProjectInfo> projectInfoMap) throws Exception{

        // 共享业务时，不查自己，又没给共享的企业id，结束
        if(Constant.NOT_INCLUDE_SELF.equals(filterConditionVO.getIncludeSelfFlag()) && CollectionUtils.isEmpty(filterConditionVO.getSharedEnterpriseId())){
            return Collections.emptyList();
        }

        // 项目级筛选（sql过滤+代码过滤）
        List<DwdProjectInfo> validProjects = this.getProjectsByDetailCondition(filterConditionVO, enterpriseId, orgIds);
        if (CollUtil.isEmpty(validProjects)) {
            return Collections.emptyList();
        }

        // 项目信息的map
        Map<String, DwdProjectInfo> validProjectInfoMap = validProjects.parallelStream()
                .collect(Collectors.toMap(x -> this.generateProjectCodeAndInfoMapKey(x.getEnterpriseId(), x.getProjectCode()), Function.identity(), (v1, v2) -> v2));
        if (projectInfoMap != null){
            projectInfoMap.putAll(validProjectInfoMap);
        }

        // 单项级筛选（sql过滤）（order by archive_date）
        long start = System.currentTimeMillis();
        List<DwsIndexProjectNote> dwsIndexProjectNotes = this.selectSharedNoteByDb(filterConditionVO, validProjects);
        long end = System.currentTimeMillis();
        log.info("selectSharedNoteByDb 查库: {} ms", end - start);
        if (CollUtil.isEmpty(dwsIndexProjectNotes)) {
            return Collections.emptyList();
        }

        // 单项级筛选（代码过滤）
        List<DwsIndexProjectNote> validNotes = this.getNoteByCondition(filterConditionVO, dwsIndexProjectNotes);
        if (CollUtil.isEmpty(validNotes)) {
            return Collections.emptyList();
        }

        // 复查完整数据
        List<DwsIndexProjectNote> wholeNote = getWholeNoteList(filterConditionVO.getSumCondition(), validNotes, filterConditionVO.getProductSource());
        if (CollUtil.isEmpty(wholeNote)) {
            return Collections.emptyList();
        }

        // 同步相同楼栋的工程分类，取非空最新
        CommonHandler.sameLdCategorySync(wholeNote);

        // 再进行一次业态筛选
        wholeNote = categoryFilter(filterConditionVO, wholeNote, validProjectInfoMap);

        return wholeNote;
    }

    private List<DwsIndexProjectNote> categoryFilter(FilterConditionVO filterConditionVO, List<DwsIndexProjectNote> wholeNote, Map<String, DwdProjectInfo> projectInfoMap) {
        if (CollUtil.isEmpty(filterConditionVO.getCategoryCode())) {
            return wholeNote;
        }

        // 工程分类筛选：先和单体的工程分类匹配，如果单体的工程分类为空，再和项目的工程分类匹配。
        Predicate<DwsIndexProjectNote> categoryPredicate = note -> isProjectInCategory(note, filterConditionVO, projectInfoMap);
        List<DwsIndexProjectNote> filteredNotes = wholeNote.stream().filter(categoryPredicate).collect(Collectors.toList());

        // 如果过滤维度是按项目，则进一步根据项目哈希获取
        if (SumTypeEnum.PROJECT.getCode().equals(filterConditionVO.getSumCondition())) {
            Set<String> projectHashes = filteredNotes.stream().map(DwsIndexProjectNote::getProjectHash).collect(Collectors.toSet());
            return wholeNote.stream().filter(note -> projectHashes.contains(note.getProjectHash())).collect(Collectors.toList());
        } else {
            return filteredNotes;
        }
    }

    /**
     * 工程分类筛选：先和单体的工程分类匹配，如果单体的工程分类为空，再和项目的工程分类匹配。
     */
    private boolean isProjectInCategory(DwsIndexProjectNote note, FilterConditionVO filterConditionVO, Map<String, DwdProjectInfo> projectInfoMap) {
        if (StrUtil.isNotBlank(note.getProjectCategoryName())) {
            return filterConditionVO.getCategoryCode().contains(note.getProjectCategoryName());
        } else {
            if (projectInfoMap == null){
                return false;
            }
            DwdProjectInfo projectInfo = projectInfoMap.get(generateProjectCodeAndInfoMapKey(note.getEnterpriseId(), note.getProjectCode()));
            return projectInfo != null && filterConditionVO.getCategoryCode().contains(projectInfo.getProjectCategoryName());
        }
    }

    public List<DwsIndexProjectNote> getWholeNoteList(Integer sumCondition, List<DwsIndexProjectNote> validNotes, List<String> productSource) {
        return dwsDataRepository.selectWholeNoteByHash(sumCondition, validNotes, productSource);
    }

    /**
     * 基于结果的处理
     * 1.业态和单体维度时按照合并后的单体或业态，对建筑面积进行过滤
     * 2.按单体，【工程造价】区间筛选：当用户列表合并后数据中全费用含税合价、全费用不含税合价、非全费用含税合价、非全费用不含税合价命中区间值时，即筛选出此结果，列表出值不变
     * 3.按单体，【工程规模】区间筛选：当用户列表合并后数据中全费用/非全费用工程规模命中区间值时，即筛选出此结果，列表出值不变
     */
    private void mergeAfterFilter(FilterConditionVO filterConditionVO, List<ContractProjectDto.NoteMergeDto> result) {
        if (SumTypeEnum.PROJECT.getCode().equals(filterConditionVO.getSumCondition())) {
            return;
        }
        filterByBuildArea(filterConditionVO.getBuildArea(), result);
        filterByTotal(filterConditionVO.getTotal(), result);
        filterByScale(filterConditionVO.getScale(), result);
    }

    private void filterByBuildArea(String buildArea, List<ContractProjectDto.NoteMergeDto> result) {
        if (StringUtils.isEmpty(buildArea)) {
            return;
        }
        filterResult(result, dto -> FilterUtil.matchValueByNumber(buildArea, dto.getBuildArea()));
    }

    private void filterByTotal(String total, List<ContractProjectDto.NoteMergeDto> result) {
        if (StringUtils.isEmpty(total)) {
            return;
        }
        filterResult(result, dto -> FilterUtil.matchValueByNumber(total, dto.getFullCostTotal())
                || FilterUtil.matchValueByNumber(total, dto.getFullCostTotalIncludeTax())
                || FilterUtil.matchValueByNumber(total, dto.getNonFullCostTotal())
                || FilterUtil.matchValueByNumber(total, dto.getNonFullCostTotalIncludeTax()));
    }

    private void filterByScale(String scale, List<ContractProjectDto.NoteMergeDto> result) {
        if (StringUtils.isEmpty(scale)) {
            return;
        }
        filterResult(result, dto -> FilterUtil.matchValueByNumber(scale, dto.getScale()));
    }

    private void filterResult(List<ContractProjectDto.NoteMergeDto> result, Predicate<ContractProjectDto.NoteMergeDto> predicate) {
        result.removeIf(dto -> {
            try {
                return !predicate.test(dto);
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        });
    }

    public String generateProjectCodeAndInfoMapKey(String enterpriseId, String projectCode) {
        StringJoiner stringJoiner = new StringJoiner(CharConst.DOUBLE_AT);
        stringJoiner.add(enterpriseId)
                .add(projectCode);

        return stringJoiner.toString();
    }

    /**
     * 组织查询数据库参数
     * @param filterConditionVO
     * @return
     */
    private List<DwsIndexProjectNote> selectSharedNoteByDb(FilterConditionVO filterConditionVO, List<DwdProjectInfo> validProjects ) {
        List<SharedEnterpriseVo> sharedEnterpriseList = getSharedEnterpriseList(validProjects);
        // 获取类别type
        List<Integer> typeList = SumTypeEnum.getTypeListByCode(filterConditionVO.getSumCondition());
        // 单体名称特殊字符转义
        filterConditionVO.setLdNameIdentify(EscapeUtil.escapeChar(filterConditionVO.getLdNameIdentify()));
        // 造价类型
        List<String> phaseList = StringUtils.isNotBlank(filterConditionVO.getPhase()) ? Arrays.asList(filterConditionVO.getPhase().split(",")) : null;
        // 大字段会有效率问题，判断此处是否需要查大字段【工程特征】【合同信息】。这里查出的数据只用于筛选，后面还会根据哈希反查，所以如果没有对应的筛选条件就不需要对应的大字段。
        boolean isNeedContractInfo = StringUtils.isNotBlank(filterConditionVO.getContractStartEndTime()) || CollUtil.isNotEmpty(filterConditionVO.getContractInfo());
        boolean isNeedFeatureInfo = CollUtil.isNotEmpty(filterConditionVO.getFeature());
        return dwsDataRepository.selectSharedListWithBlobField(sharedEnterpriseList, typeList, filterConditionVO, phaseList, isNeedContractInfo, isNeedFeatureInfo);
    }

    /**
     * 组织互信企业列表参数
     * @param validProjects
     * @return
     */
    private List<SharedEnterpriseVo> getSharedEnterpriseList(List<DwdProjectInfo> validProjects) {
        List<SharedEnterpriseVo> sharedEnterpriseList = new ArrayList<>();
        Map<String, Set<String>> sharedMap = new HashMap<>();
        validProjects.forEach(item -> {
            String enterpriseId = item.getEnterpriseId();
            Set<String> shareSet = sharedMap.get(enterpriseId);
            if (shareSet != null) {
                shareSet.add(item.getProjectCode());
            } else {
                SharedEnterpriseVo sharedEnterpriseVo = new SharedEnterpriseVo();
                sharedEnterpriseVo.setEnterpriseId(enterpriseId);
                Set<String> set = new HashSet<>();
                set.add(item.getProjectCode());
                sharedEnterpriseVo.setProjectCodeList(set);
                sharedEnterpriseList.add(sharedEnterpriseVo);
                sharedMap.put(enterpriseId, set);
            }
        });
        return sharedEnterpriseList;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public List<ProjectAttrDto> getProjectAttr(String enterpriseId, List<Long> noteIds, List<String> originalIds) {
        // 兼容归档库查询工程特征
        List<Long> contractProjectIds = Lists.newArrayList();
        if (CollUtil.isNotEmpty(originalIds)) {
            contractProjectIds = getContractProjectIds(originalIds);
        }

        List<DwsIndexProjectNote> projectNotes = dwsDataRepository.selectProjectAttrByIds(noteIds, contractProjectIds, enterpriseId);
        if (CollectionUtils.isEmpty(projectNotes)) {
            return Lists.newArrayList();
        }

        boolean ldPresent = projectNotes.stream().anyMatch(x -> x.getType() != null && LD.getIndex().equals(x.getType()));

        Map<String, String> ldNameIdentifyAndNameMap = CommonHandler.getLdNameIdentifyAndNameMap(projectNotes);
        List<ProjectAttrDto> attrDtoList = projectNotes.stream()
                .filter(x -> !StringUtils.isEmpty(x.getProjectAttrJson()))
                .map(x -> {
                    List<ProjectAttrDto> projectAttrDtoList = JSONObject.parseArray(x.getProjectAttrJson(), ProjectAttrDto.class);
                    String key = CommonHandler.generateSameLdKey(x);
                    for (ProjectAttrDto projectAttrDto : projectAttrDtoList) {
                        List<ColsDto> cols = projectAttrDto.getCols();
                        cols.forEach(item -> {
                            if (Objects.equals(x.getType(), LD.getIndex()) && Objects.equals(x.getLdNameIdentify(), item.getCaption()) && ldNameIdentifyAndNameMap.containsKey(key)) {
                                item.setOriginalCaption(ldNameIdentifyAndNameMap.get(key));
                            } else {
                                item.setOriginalCaption(item.getCaption());
                            }
                            item.setBuildArea(x.getBuildArea());
                        });
                        projectAttrDto.setArchiveDate(x.getArchiveDate());
                        projectAttrDto.setCaption(x.getLdNameIdentify());
                        projectAttrDto.setBuildArea(x.getBuildArea());
                        //市场化计价来源特殊处理，去掉最后一个括号
                        if (ProductSource.QYQD.getIndex().equals(x.getProductSource())
                                || ProductSource.YSTZ.getIndex().equals(x.getProductSource())
                                || ProductSource.JSTZ.getIndex().equals(x.getProductSource())) {
                            List<LinkedHashMap> data = projectAttrDto.getData();
                            data.forEach(item -> item.put(StringConst.ATTR_NAME,  CaliberUtil.caliberConvertV2(item.get(StringConst.ATTR_NAME) + "").getCaliber()));
                        }
                    }
                    return projectAttrDtoList;
                })
                .flatMap(Collection::stream).collect(Collectors.toList());

        Map<String, List<ProjectAttrDto>> map = attrDtoList.stream().collect(Collectors.groupingBy(ProjectAttrDto::getTradeCode));
        List<String> orderTradeCodeList = attrDtoList.stream().map(ProjectAttrDto::getTradeCode).filter(tradeCode -> !Constant.otherTradeCode.equals(tradeCode))
                .distinct().sorted(Comparator.comparing(Function.identity())).collect(Collectors.toList());
        orderTradeCodeList.add(Constant.otherTradeCode);
        // 按专业合并，楼栋和业态去重  productSource暂时做造价、成本指标不同逻辑兼容
        return mergeProjectAttrData(map, orderTradeCodeList, isCostZbk(projectNotes), ldPresent);
    }

    private List<Long> getContractProjectIds(List<String> originalIds) {
        List<Long> contractProjectIds = Lists.newArrayList();
        List<DwdContractProject> dwdContractProjects = dwdDataRepository.selectProjectListByOriginalIds(originalIds);
        if (CollUtil.isNotEmpty(dwdContractProjects)) {
            contractProjectIds = dwdContractProjects.stream().map(DwdContractProject::getId).collect(Collectors.toList());
        }
        return contractProjectIds;
    }

    /**
     * 按专业合并，楼栋和业态去重
     */
    @SuppressWarnings("rawtypes")
    private List<ProjectAttrDto> mergeProjectAttrData(Map<String, List<ProjectAttrDto>> map, List<String> orderTradeCodeList, Boolean isCbzbk, boolean ldPresent) {
        List<ProjectAttrDto> list = new ArrayList<>();
        for (String code : orderTradeCodeList) {
            List<ProjectAttrDto> attrs = map.get(code);
            if (CollUtil.isEmpty(attrs)) {
                continue;
            }
            // 造价指标库 暂时兼容 后面会统一逻辑
            List<ColsDto> cols = distinctCol(attrs, ldPresent);
            List<LinkedHashMap> data;
            //if (!isCbzbk) {
            //    data = distinctData(attrs, cols);
            //} else {
            //    //成本指标库
            //    data = distinctDataByArchiveDate(attrs);
            //}
            data = distinctDataByArchiveDate(attrs, ldPresent);
            List<LinkedHashMap> newData = mergeData(data);
            ProjectAttrDto projectAttrDto = new ProjectAttrDto();
            // 列头去重
            projectAttrDto.setCols(cols);
            projectAttrDto.setData(newData);
            projectAttrDto.setTradeCode(code);
            projectAttrDto.setTradeName(CollUtil.getFirst(attrs).getTradeName());
            list.add(projectAttrDto);
        }
        return list;
    }

    /**
     * 对列头进行去重
     *
     * @param attrs
     * @param ldPresent
     * @return
     */
    private List<ColsDto> distinctCol(List<ProjectAttrDto> attrs, boolean ldPresent) {
//        List<ColsDto> colsDtoList = attrs.stream()
//                .map(ProjectAttrDto::getCols)
//                .flatMap(Collection::stream)
//                .sorted(Comparator.comparing(ColsDto::getCaption))
//                .collect(Collectors.collectingAndThen(
//                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ColsDto::getCaption))), ArrayList::new));
        return attrs.stream()
                .map(ProjectAttrDto::getCols)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(d -> {
                    if (ldPresent) {
                        return d.getCaption() + d.getBuildArea();
                    }
                    return d.getCaption();
                }))
                //.collect(Collectors.groupingBy(ColsDto::getCaption))
                .values()
                .stream()
                .map(colsDtos -> {
                    List<String> codeList = colsDtos.stream().map(ColsDto::getCode).collect(Collectors.toList());
                    ColsDto colsDto = CollUtil.getFirst(colsDtos);
                    colsDto.setCodeList(codeList);
                    // 其余的列统一返回code
                    for (ColsDto sameCol : colsDtos){
                        sameCol.setAttrColCode(colsDto.getCode());
                    }
                    return colsDto;
                })
                .sorted(Comparator.comparing(ColsDto::getCaption))
                .collect(Collectors.toList());
    }

    private List<String> getColsCode(List<ProjectAttrDto> attrs) {
        return attrs.stream()
                .map(ProjectAttrDto::getCols)
                .flatMap(Collection::stream)
                .sorted(Comparator.comparing(ColsDto::getCaption))
                .map(ColsDto::getCode)
                .collect(Collectors.toList());
    }
    /**
     * 对数据去重
     *
     * @param attrs
     * @param cols
     * @return
     */
    private List<LinkedHashMap> distinctData(List<ProjectAttrDto> attrs, List<ColsDto> cols) {
        //数据合并
        List<String> colCodes = cols.stream().map(ColsDto::getCode).collect(Collectors.toList());
        List<LinkedHashMap> data = attrs.stream()
                .map(ProjectAttrDto::getData)
                .flatMap(Collection::stream)
                .filter(item -> {
                    Set<String> keys = item.keySet();
                    for (String key : keys) {
                        if (colCodes.contains(key)) {
                            return true;
                        }
                    }
                    return false;
                })
                .collect(Collectors.toList());
        return data;
    }


    @SuppressWarnings("all")
    private List<LinkedHashMap> distinctDataByArchiveDate(List<ProjectAttrDto> attrs, boolean ldPresent) {
        //数据合并,收集所有col的code(列的编码 dws_index_project_note表中的id字段)
        List<String> colsCodeList = attrs.stream()
                .map(ProjectAttrDto::getCols)
                .flatMap(Collection::stream)
                .collect(Collectors.toList()).stream()
                .map(ColsDto::getCode)
                .collect(Collectors.toList());

        List<LinkedHashMap> newAttrMap = Lists.newArrayList();
        attrs.stream()
                .peek(c -> {
                    List<LinkedHashMap> attrMap = c.getData();
                    for (LinkedHashMap attr : attrMap) {
                        attr.put(StringConst.ARCHIVE_DATE, c.getArchiveDate());
                        attr.put(StringConst.CAPTION, c.getCaption());
                        attr.put(StringConst.BUILD_AREA, c.getBuildArea());
                        //获取对应列的code
                        if (CollUtil.isNotEmpty(c.getCols())) {
                            attr.put(StringConst.ATTR_COL_CODE, CollUtil.getFirst(c.getCols()).getAttrColCode());
                        }
                    }
                })
                .map(ProjectAttrDto::getData)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingByConcurrent(
                        //d -> "" + CaliberUtil.caliberConvertV2(d.get(StringConst.ATTR_NAME) + "").getCaliber() + d.get(StringConst.CAPTION) + d.get(StringConst.BUILD_AREA)))
                        //d -> StrUtil.EMPTY + d.get(StringConst.ATTR_NAME) + d.get(StringConst.CAPTION) + d.get(StringConst.BUILD_AREA)))
                        // 取非空最新的时候去除建筑面积这个维度
                        d -> {
                            if(ldPresent) {
                                return StrUtil.EMPTY + d.get(StringConst.ATTR_NAME) + d.get(StringConst.CAPTION) + d.get(StringConst.BUILD_AREA);
                            }
                            return StrUtil.EMPTY + d.get(StringConst.ATTR_NAME) + d.get(StringConst.CAPTION);
                        }
                ))
                .forEach((groupKey, attrMap) -> {
                    attrMap.sort((map1, map2) -> {
                        Date dateTime1 = (Date) map1.get(StringConst.ARCHIVE_DATE);
                        Date dateTime2 = (Date) map2.get(StringConst.ARCHIVE_DATE);
                        return dateTime2.compareTo(dateTime1);
                    });
                    LinkedHashMap finalAttr = CollUtil.getFirst(attrMap);
                    outerLoop:
                    for (LinkedHashMap attr : attrMap) {
                        Set<String> keys = attr.keySet();
                        for (String key : keys) {
                            // 特征值非空最新
                            if (colsCodeList.contains(key) && !StrUtil.isBlankIfStr(attr.get(key))) {
                                finalAttr = attr;
                                // 将相同列对应的key调整为一致
                                String attrColCode = String.valueOf(attr.get(StringConst.ATTR_COL_CODE));
                                if (!StringUtils.equals(key, attrColCode)){
                                    finalAttr.put(attrColCode, finalAttr.get(key));
                                    finalAttr.remove(key);
                                }
                                //finalAttr.put(StringConst.ATTR_NAME, CaliberUtil.caliberConvertV2(finalAttr.get(StringConst.ATTR_NAME) + "").getCaliber());
                                break outerLoop;
                            }
                        }
                    }
                    finalAttr.remove(StringConst.ARCHIVE_DATE);
                    finalAttr.remove(StringConst.CAPTION);
                    finalAttr.remove(StringConst.BUILD_AREA);
                    finalAttr.remove(StringConst.ATTR_COL_CODE);
                    newAttrMap.add(finalAttr);
                });
//        newAttrMap.sort(Comparator.comparingInt(o -> (int) o.get("orderNum")));
//        return newAttrMap;
        return newAttrMap.stream()
                .sorted(Comparator.comparing(o -> (Integer) o.getOrDefault("orderNum", Integer.MAX_VALUE), Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    /**
     * 对数据进行合并
     *
     * @param data 特征数据
     * @return 取非空的特征数据
     */
    @SuppressWarnings({"rawtypes", "unchecked", "SuspiciousMethodCalls"})
    private List<LinkedHashMap> mergeData(List<LinkedHashMap> data) {
        List<LinkedHashMap> newData = Lists.newArrayList();
        if (CollUtil.isEmpty(data)) {
            return newData;
        }
        Map<String, List<LinkedHashMap>> dataMap = data.stream().collect(Collectors.groupingBy(item -> item.get(Constants.ATTR_NAME).toString()));
        for (Map.Entry<String, List<LinkedHashMap>> dataEntry : dataMap.entrySet()) {
            List<LinkedHashMap> subDatas = dataEntry.getValue();
            LinkedHashMap jsonObject = new LinkedHashMap();
            subDatas.forEach(item ->
                item.keySet().forEach(y -> {
                    if (jsonObject.get(y) == null) {
                        jsonObject.put(y, item.get(y));
                    }
                })
            );
            newData.add(jsonObject);
        }
        List<LinkedHashMap> ordData = Lists.newArrayList();
        Map<String, LinkedHashMap> map = newData.stream().collect(Collectors.toMap(item -> item.get(Constants.ATTR_NAME).toString(), item -> item));
        data.forEach(item -> {
            LinkedHashMap hashMap = map.get(item.get(Constants.ATTR_NAME));
            if (!ordData.contains(hashMap)) {
                ordData.add(hashMap);
            }
        });
        return ordData;
    }

    /**
     * 根据过滤条件获取有效的项目ID集合（项目信息）
     */
    @TakeTime
    @Override
    public List<DwdProjectInfo> getProjectsByDetailCondition(FilterConditionVO filterConditionVO, String enterpriseId, List<String> orgIds) throws Exception {
        List<FilterItemVO> projectInfo = filterConditionVO.getProjectInfo();

        // 项目地点的特殊处理，value是任意级的areaId
        List<String> areaId = null;
        if (CollUtil.isNotEmpty(projectInfo)) {
            areaId = projectInfo
                    .stream()
                    .filter(x -> PROJECT_AREA.equals(x.getName()))
                    .map(FilterItemVO::getValue)
                    .flatMap(area -> Stream.of(area.split(CharConst.COMMA)))
                    .collect(Collectors.toList());
        }

        OrgAuthVo orgAuthVo = projectAuthEntInfoService.countByEnterpriseId(enterpriseId, orgIds);
        List<String> sharedEnterpriseId = filterConditionVO.getSharedEnterpriseId();
        String includeSelfFlg = filterConditionVO.getIncludeSelfFlag();

        // 查询项目基本信息和自定义信息
        List<DwdProjectInfo> projects = dwdDataRepository.listDetailProjectInfo(enterpriseId, orgAuthVo.getOrgIds(), filterConditionVO.getProjectCodeList(),
                filterConditionVO.getName(), areaId, orgAuthVo.getIsVirtualOrg(), sharedEnterpriseId, includeSelfFlg, filterConditionVO.getAuthControlProjectCodeList());

        // 只要用户在项目信息中填写的【开工时间】、【竣工时间】与用户执行的筛选的区间值有重叠，即将此行数据筛选出来
        filterProjectStartEndTime(projects, filterConditionVO.getProjectStartEndTime());

        // 项目信息的筛选
        filterProjectInfo(projects, projectInfo);

        return projects;
    }

    public void filterProjectInfo(List<DwdProjectInfo> projects, List<FilterItemVO> projectInfo) throws Exception{
        if (CollUtil.isEmpty(projectInfo)) {
            return;
        }
        Iterator<DwdProjectInfo> iterator = projects.iterator();
        while (iterator.hasNext()) {
            if (!this.matchProjectInfoCondition(iterator.next(), projectInfo)) {
                iterator.remove();
            }
        }
    }

    /**
     * 【开竣工时间】筛选：支持用户进行区间筛选
     * 可能出现的几种情况：（当用户填写筛选区间时，只填了一个值，认为另一个值无限长即可）
     * 当客户A项目填写了【开工时间】、【竣工时间】时，在执行筛选时，只要用户在项目信息中填写的【开工时间】、【竣工时间】与用户执行的筛选的区间值有重叠，即将此行数据筛选出来
     * 当客户X项目没有填写【开工时间】、【竣工时间】时，在执行筛选时，此数据不可被筛选出来
     * 当客户Y项目只填写了【开工时间】，没有填写【竣工时间】时，可能为未完工项目，所以可认为【竣工时间】为执行筛选的时间，因此当【开工时间】-【当前时间】与用户执行的筛选的区间值有重叠，即将此行数据筛选出来
     * 当客户Z项目只填写了【竣工时间】，没有填写【开工时间】时，可能是漏填，所以可认为【开工时间】无线延展，因此当【竣工时间】与用户执行的筛选的区间值有重叠，即将此行数据筛选出来
     * @param projects
     * @param projectStartEndTime
     */
    public void filterProjectStartEndTime(List<DwdProjectInfo> projects, String projectStartEndTime) {
        if (StringUtils.isBlank(projectStartEndTime)) {
            return;
        }

        String[] startEndTime = projectStartEndTime.split(StrUtil.COMMA);
        Date filterStartTime = DateUtils.parseDate(startEndTime[0], DATE_FORMAT_YDM);
        Date filterEndTime = DateUtils.parseDate(startEndTime.length > 1 ? startEndTime[1] : null, DATE_FORMAT_YDM);

        projects.removeIf(project -> !DateUtils.isOverlap(filterStartTime, filterEndTime, project.getStartTime(), project.getEndTime()));
    }

    /**
     * 根据项目信息匹配
     */
    private boolean matchProjectInfoCondition(DwdProjectInfo project, List<FilterItemVO> projectInfoFilter) throws Exception {
        if (CollUtil.isEmpty(projectInfoFilter)) {
            return true;
        }

        for (FilterItemVO filterInfo : projectInfoFilter) {
            // 是否系统内置
            boolean sysDefault = filterInfo.getSysDefault() != null && filterInfo.getSysDefault() == 1;
            if (sysDefault && SKIP_PROJECTINFO_FIELD.contains(filterInfo.getName())) {
                continue;
            }
            // 获取数据的实际值
            Object actualValue = this.getActualValue(filterInfo.getName(), sysDefault, project);
            // 根据数据类型匹配
            boolean isMatch = FilterUtil.matchValue(filterInfo.getTypeCode(), filterInfo.getValue(), actualValue);
            if (!isMatch) {
                return false;
            }
        }

        return true;
    }

    /**
     * 根据项目信息匹配
     */
    private Object getActualValue(String fieldName, boolean sysDefaultFlag, DwdProjectInfo project) throws Exception {
        Object actualValue;

        if (sysDefaultFlag) {
            // 系统内置的从固定字段里取值
            String methodName = "get" + DEFAULT_PROJECTINFO_FIELDNAME.get(DEFAULT_PROJECTINFO_FIELD.indexOf(fieldName));
            actualValue = DwdProjectInfo.class.getMethod(methodName).invoke(project);
        } else {
            // 自定义的是key-value存储
            if (CollUtil.isEmpty(project.getProjectDetail())) {
                return null;
            }
            Map<String, String> map = new HashMap<>();
            project.getProjectDetail().forEach(item -> {
                if (!StringUtils.isEmpty(item.getFieldName()) && !StringUtils.isEmpty(item.getFieldValue())) {
                    map.putIfAbsent(item.getFieldName(), item.getFieldValue());
                }
            });
            if (!map.containsKey(fieldName)) {
                return null;
            }
            actualValue = map.get(fieldName);
        }

        // 处理[]的特殊字符
        if (actualValue != null && actualValue.toString().startsWith(StrUtil.BRACKET_START) && actualValue.toString().endsWith(StrUtil.BRACKET_END)) {
            JSONArray arr = JSONArray.parseArray(actualValue.toString());
            return String.join(StrUtil.COMMA, arr.toJavaList(String.class));
        }
        return actualValue;
    }

    /**
     * 根据合同信息，工程特征，指标类型，项目户口簿ID，业态，企业ID 获取原始指标数据
     */
    @TakeTime
    @Override
    public List<DwsIndexProjectNote> getNoteByCondition(FilterConditionVO filterConditionVO, List<DwsIndexProjectNote> noteList) throws Exception {
        if (CollUtil.isEmpty(noteList)) {
            return Collections.emptyList();
        }

        // 合同信息、工程特征过滤
        noteList = this.filterByCondition(noteList, filterConditionVO);

        return noteList;
    }

    /**
     * 根据合同信息、工程特征 进行过滤
     *
     * @param noteList
     * @param filterConditionVO : 过滤条件
     * @return
     * @throws Exception
     */
    private List<DwsIndexProjectNote> filterByCondition(List<DwsIndexProjectNote> noteList, FilterConditionVO filterConditionVO) throws Exception {
        if (CollUtil.isEmpty(noteList) || (
                StringUtils.isEmpty(filterConditionVO.getContractStartEndTime()) &&
                        CollUtil.isEmpty(filterConditionVO.getContractInfo()) &&
                        CollUtil.isEmpty(filterConditionVO.getFeature()))) {
            return noteList;
        }

        List<DwsIndexProjectNote> result = new ArrayList<>();

        // 合同信息、工程特征过滤
        for (DwsIndexProjectNote note : noteList) {
            if (this.matchContractInfo(note, filterConditionVO) && this.matchProjectAttr(note.getProjectAttrJson(), filterConditionVO)) {
                result.add(note);
            }
        }

        return result;
    }

    /**
     * 工程特征过滤
     */
    private boolean matchProjectAttr(String attrJson, FilterConditionVO filterConditionVO) throws Exception {
        if (CollUtil.isEmpty(filterConditionVO.getFeature())){
            return true;
        }

        List<AttrFilterVO> filterInfos = filterConditionVO.getFeature();
        // 解析工程特征的json串
        Map<String, Map<String, Object>> map = this.parseProjectAttrValue(attrJson);
        // 匹配
        return this.matchFilterAttrInfoByTrade(map, filterInfos);
    }

    /**
     * [
     * {
     * "tradeName": "土建工程",
     * "tradeCode": "1001",
     * "data": [
     * {
     * "attrName": "道路等级",
     * "123456789": "1.23mm",
     * "987654321": "100mm",
     * "654321789": "-",
     * "type": "text"
     * }
     * ],
     * "cols": [
     * {
     * "code": "123456789",
     * "caption": "1#"
     * }
     * ]
     * }
     * ]
     *
     * @param attrJson
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2022/11/3 18:01
     */
    public Map<String, Map<String, Object>> parseProjectAttrValue(String attrJson) {
        try {
            List<ProjectAttrDto> projectAttrDtos = JSON.parseArray(attrJson, ProjectAttrDto.class);
            if (projectAttrDtos == null) {
                return Collections.emptyMap();
            }

            Map<String, Map<String, Object>> tradeMap = new HashMap<>();
            for (ProjectAttrDto attrDto : projectAttrDtos) {
                String tradeCode = attrDto.getTradeCode();
                Map<String, Object> map = new HashMap<>();

                List<ColsDto> cols = attrDto.getCols();

                if (CollUtil.isEmpty(cols) || CollUtil.isEmpty(attrDto.getData())) {
                    continue;
                }
                String colCode = cols.get(0).getCode();

                for (LinkedHashMap jsonObject : attrDto.getData()) {
                    String key = jsonObject.get("attrName").toString();
                    map.put(key, jsonObject.get(colCode));
                }

                tradeMap.put(tradeCode, map);
            }

            return tradeMap;
        } catch (Exception e) {
            log.error("解析工程特征失败 {}", attrJson, e);
            return Collections.emptyMap();
        }
    }

    public Map<String, Map<String, Object>> parseProjectAttrValueOfTradeName(String attrJson) {
        try {
            List<ProjectAttrDto> projectAttrDtos = JSON.parseArray(attrJson, ProjectAttrDto.class);
            if (projectAttrDtos == null) {
                return Collections.emptyMap();
            }
            Map<String, Map<String, Object>> tradeMap = new HashMap<>();
            for (ProjectAttrDto attrDto : projectAttrDtos) {
                Map<String, Object> map = new HashMap<>();

                List<ColsDto> cols = attrDto.getCols();

                if (CollUtil.isEmpty(cols) || CollUtil.isEmpty(attrDto.getData())) {
                    continue;
                }
                String colCode = cols.get(0).getCode();

                for (LinkedHashMap jsonObject : attrDto.getData()) {
                    String key = jsonObject.get("attrName").toString();
                    map.put(key, jsonObject.get(colCode));
                }
                tradeMap.put(attrDto.getTradeName(), map);
            }
            return tradeMap;
        } catch (Exception e) {
            log.error("解析工程特征失败 {}", attrJson, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 合同信息过滤
     */
    private boolean matchContractInfo(DwsIndexProjectNote note, FilterConditionVO filterConditionVO) throws Exception {
        if (StringUtils.isEmpty(filterConditionVO.getContractStartEndTime()) && CollUtil.isEmpty(filterConditionVO.getContractInfo())){
            return true;
        }
        // 解析合同信息的json串
        Map<String, Object> contractMap = this.parseProjectInfoValue(note.getProjectInfoJson());
        // 2024年10月9日19:04:36 TODO 为了适配查询时候的特殊问题 需要根据数据源先进行一次判断后再走工程特征的匹配逻辑 代总已经记录了技术债后续统一处理
        renovateProductSourceAndCondition(note, filterConditionVO.getContractInfo(), contractMap);
        // 匹配
        return filterContractStartTime(filterConditionVO, contractMap) && this.matchFilterInfo(contractMap, filterConditionVO.getContractInfo());
    }

    private void renovateProductSourceAndCondition(DwsIndexProjectNote note, List<FilterItemVO> filterInfos, Map<String, Object> contractMap) {
        if (CollUtil.isEmpty(filterInfos)) {
            return;
        }
        if (JSTZ.getIndex().equals(note.getProductSource()) || ZBGX.getIndex().equals(note.getProductSource()) || ZBW.getIndex().equals(note.getProductSource())) {
            filterInfos.forEach(x -> {
                // 如果筛选条件是编制时间的话, 根据数据来源进行数据重置
                if (COMPILE_TIME.equals(x.getName())) {
                    if (JSTZ.getIndex().equals(note.getProductSource()) || ZBGX.getIndex().equals(note.getProductSource())) {
                        contractMap.put(COMPILE_TIME, DateUtil.beginOfDay(note.getArchiveDate()));
                    } else {
                        // 指标网数据需要进行重置
                        Object orDefault = contractMap.getOrDefault(COMPILE_TIME, contractMap.get(COMPILE_DATE));
                        contractMap.put(COMPILE_TIME, orDefault instanceof Date ? DateUtil.beginOfDay((Date) orDefault) : orDefault);
                    }
                }
            });
        }
    }

    /**
     * 合同信息开竣工时间过滤
     * @param filterConditionVO
     * @param contractMap
     * @return
     */
    private static boolean filterContractStartTime(FilterConditionVO filterConditionVO, Map<String, Object> contractMap) {
        String contractStartEndTime = filterConditionVO.getContractStartEndTime();
        if (!StringUtils.isEmpty(contractStartEndTime)){
            Date beginDate = DateUtils.parseDate((String) contractMap.get("开工时间"), DATE_FORMAT_YDM);
            Date endDate = DateUtils.parseDate((String) contractMap.get("竣工时间"), DATE_FORMAT_YDM);
            String[] startEndTime = contractStartEndTime.split(StrUtil.COMMA);
            Date filterStartTime = DateUtils.parseDate(startEndTime[0], DATE_FORMAT_YDM);
            Date filterEndTime = DateUtils.parseDate(startEndTime.length > 1 ? startEndTime[1] : null, DATE_FORMAT_YDM);
            if (!DateUtils.isOverlap(filterStartTime, filterEndTime, beginDate, endDate)){
                return false;
            }
        }

        return true;
    }

    /**
     * 解析合同信息的json串
     * [{"id": 2374408,
     * "name": "建筑面积",
     * "selectList": "null",
     * "value": "12",
     * "valueType": 3}]
     *
     * @param contractInfoJson
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2022/11/3 18:00
     */
    public Map<String, Object> parseProjectInfoValue(String contractInfoJson) {
        try {
            JSONArray arr = JSON.parseArray(contractInfoJson);
            if (arr == null) {
                return Collections.emptyMap();
            }
            return arr.stream().map(JSONObject.class::cast).filter(i -> i.get("value") != null)
                    .collect(Collectors.toMap(i -> i.get("name").toString(), i -> i.get("value"), (v1, v2) -> v1));
        } catch (Exception e) {
            log.error("解析工程信息失败 {}", contractInfoJson, e);
            return Collections.emptyMap();
        }
    }

    public Map<String, ContractInfoItem> parseProjectInfo(String contractInfoJson) {
        try {
            List<ContractInfoItem> contractInfoItems = JSON.parseArray(contractInfoJson, ContractInfoItem.class);
            if (contractInfoItems == null) {
                return Collections.emptyMap();
            }
            return contractInfoItems.stream().filter(i -> i.getValue() != null)
                    .collect(Collectors.toMap(ContractInfoItem::getName, Function.identity(), (v1, v2) -> v1));
        } catch (Exception e) {
            log.error("解析工程信息失败 {}", contractInfoJson, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 匹配
     */
    private boolean matchFilterInfo(Map<String, Object> map, List<FilterItemVO> filterInfos) throws Exception {
        if (CollUtil.isNotEmpty(filterInfos)) {
            if (CollUtil.isEmpty(map)) {
                return false;
            }

            for (FilterItemVO filterInfo : filterInfos) {
                String fieldName = filterInfo.getName();

                Object actualValue = map.get(fieldName);
                boolean isMatch = FilterUtil.matchValue(filterInfo.getTypeCode(), filterInfo.getValue(), actualValue);

                // 兼容处理，拼上单位再匹配一次
                boolean spliceUnitIsMatch = false;
                if (NUMBER_DATA_TYPE.equals(filterInfo.getTypeCode()) && StringUtils.isNotEmpty(filterInfo.getUnit())) {
                    actualValue = map.get(fieldName + "(" + filterInfo.getUnit() + ")");
                    spliceUnitIsMatch = FilterUtil.matchValue(filterInfo.getTypeCode(), filterInfo.getValue(), actualValue);
                }

                if (!isMatch && !spliceUnitIsMatch) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 工程特征需分专业匹配
     */
    private boolean matchFilterAttrInfoByTrade(Map<String, Map<String, Object>> map, List<AttrFilterVO> attrFilterInfos) throws Exception {
        if (attrFilterInfos != null && attrFilterInfos.size() > 0) {
            if (map == null || map.size() == 0) {
                return false;
            }

            // 检查是否是全量专业筛选
            if (attrFilterInfos.size() == 1 && Objects.equals(attrFilterInfos.get(0).getTradeId(), null)){
                // 依次检查每个专业特征是否能匹配到
                List<FilterItemVO> features = attrFilterInfos.get(0).getFeatures();
                if (CollUtil.isEmpty(features)){
                    return true;
                }

                for (FilterItemVO featureItem : features){
                    Boolean findSameFeature = false;
                    for (Map<String, Object> featureValueMap : map.values()){
                        // 依次检查每个特征项是否满足
                        if (this.matchFilterInfo(featureValueMap, Lists.newArrayList(featureItem))) {
                            findSameFeature = true;
                        }
                    }

                    if (!findSameFeature){
                        return false;
                    }
                }

                return true;
            } else {
                // 按专业筛选
                for (AttrFilterVO attrFilterVO : attrFilterInfos) {
                    // 分专业
                    String tradeId = attrFilterVO.getTradeId();

                    if (!StringUtils.isEmpty(tradeId) && !map.containsKey(tradeId)) {
                        return false;
                    }

                    Map<String, Object> stringStringMap = map.get(tradeId);
                    if (!this.matchFilterInfo(stringStringMap, attrFilterVO.getFeatures())) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 合并业态指标数据
     */
    public Map<String, List<DwsIndexProjectNote>> mergeNoteBySumCondition(Integer sumCondition, List<DwsIndexProjectNote> validNotes) {
        Map<String, List<DwsIndexProjectNote>> groupNote = new LinkedHashMap<>();

        if (CollUtil.isEmpty(validNotes)) {
            return groupNote;
        }

        //如果造价阶段为空，则单独一组，平铺展开
        if (SumTypeEnum.PROJECT.getCode().equals(sumCondition) || SumTypeEnum.VIRTUAL_NODE.getCode().equals(sumCondition)) {
            // 按项目 【项目+阶段】 按照项目创建时间排序
            groupNote = validNotes.stream()
                    .collect(Collectors.groupingBy(x -> x.getEnterpriseId() + x.getProjectCode() + x.getPhase(),
                            LinkedHashMap::new, Collectors.toList()));
        } else if (SumTypeEnum.CATEGORY.getCode().equals(sumCondition)) {
            // 业态  【项目+阶段+业态】 按照项目创建时间排序，项目下按工程分类的编码
            groupNote = validNotes.stream()
                    .filter(x -> StringUtils.isNotEmpty(x.getProjectCategoryName()))
                    .sorted(Comparator.comparing(DwsIndexProjectNote::getProjectCategoryCode, Comparator.nullsFirst(Comparator.naturalOrder())))
                    .collect(Collectors.groupingBy(x -> x.getEnterpriseId() + x.getProjectCode() + x.getPhase() + x.getProjectCategoryName(),
                            LinkedHashMap::new, Collectors.toList()));
        } else if (SumTypeEnum.DT.getCode().equals(sumCondition)) {
            // 单体 【项目+阶段+业态+楼栋】  按照项目创建时间排序，项目下楼栋名称排序，【楼栋名相同，面积相同，才算同一个楼】
            groupNote = validNotes.stream()
                    .sorted(Comparator.comparing(DwsIndexProjectNote::getLdNameIdentify, Comparator.nullsFirst(Comparator.naturalOrder())))
                    .collect(Collectors.groupingBy(x -> x.getEnterpriseId() + x.getProjectCode() + x.getPhase() + x.getProjectCategoryName() + x.getLdNameIdentify() + x.getBuildArea(),
                            LinkedHashMap::new, Collectors.toList()));
        } else if (SumTypeEnum.LD_TRADE.getCode().equals(sumCondition)){
            // 按专业合并
            // 专业 【项目+阶段+业态+楼栋+专业名称】  按照项目创建时间排序，项目下楼栋名称排序，【楼栋名相同，面积相同，才算同一个楼】
            groupNote = validNotes.stream()
                    .sorted(Comparator.comparing(DwsIndexProjectNote::getLdNameIdentify, Comparator.nullsFirst(Comparator.naturalOrder())))
                    .collect(Collectors.groupingBy(x -> x.getEnterpriseId() + x.getProjectCode() + x.getPhase() + x.getProjectCategoryName() + x.getLdNameIdentify() + x.getTradeName() + x.getBuildArea(),
                            LinkedHashMap::new, Collectors.toList()));
        } else if (SumTypeEnum.XNLD_TRADE.getCode().equals(sumCondition)){
            // 虚拟楼栋专业(项目+阶段+专业),notes必须是经过业态或项目筛选完之后聚合出来的虚拟楼栋专业
            groupNote = validNotes.stream()
                    .sorted(Comparator.comparing(DwsIndexProjectNote::getLdNameIdentify, Comparator.nullsFirst(Comparator.naturalOrder())))
                    .collect(Collectors.groupingBy(CommonHandler::generateSameXnldTrade, LinkedHashMap::new, Collectors.toList()));
        }

        return groupNote;
    }

    /**
     * 构建返回体
     */
    @TakeTime
    private List<ProjectSearchDto> convertToDto(FilterConditionVO filterConditionVO,
                                                Map<String, List<DwsIndexProjectNote>> groupNoteMap,
                                                List<DwdProjectInfo> validProjects,
                                                Map<String, DwsIndexProjectNote> nonConstructionMap) {
        boolean initFlag = filterConditionVO.getSimpleNoteSumFlag() == ValueConst.FALSE;
        List<ProjectSearchDto> result = new ArrayList<>();

        // 项目信息的map
        Map<String, DwdProjectInfo> projectCodeAndInfoMap = validProjects.parallelStream()
                .collect(Collectors.toMap(item -> item.getEnterpriseId()+item.getProjectCode(), Function.identity(), (v1, v2) -> v2));


        List<Long> contractProjectIds = Lists.newArrayList();
        boolean isTzgsbz = ProductSource.TZGSBZ.getIndex().equals(filterConditionVO.getCallingParty()) &&
                SumTypeEnum.DT.getCode().equals(filterConditionVO.getSumCondition());
        // 构建返回体
        for (List<DwsIndexProjectNote> noteList : groupNoteMap.values()) {

            noteList.sort(Comparator.comparing(DwsIndexProjectNote::getArchiveDate).reversed().thenComparing(DwsIndexProjectNote::getId));

            DwsIndexProjectNote note = noteList.get(0);

            if (!projectCodeAndInfoMap.containsKey(note.getEnterpriseId()+note.getProjectCode())) {
                continue;
            }

            // 获取项目信息
            DwdProjectInfo projectInfo = projectCodeAndInfoMap.get(note.getEnterpriseId()+note.getProjectCode());

            // 基本信息
            ProjectSearchDto dto = new ProjectSearchDto();
            dto.setProjectCode(projectInfo.getProjectCode());
            dto.setProjectName(projectInfo.getProjectName());
            dto.setProvinceId(projectInfo.getProvinceId());
            dto.setProvinceName(projectInfo.getProvinceName());
            dto.setCityId(projectInfo.getCityId());
            dto.setCityName(projectInfo.getCityName());
            dto.setDistrictId(projectInfo.getDistrictId());
            dto.setDistrictName(projectInfo.getDistrictName());
            dto.setCreateDate(projectInfo.getCreateDate());
            dto.setArchiveDate(note.getArchiveDate());
            dto.setEnterpriseId(note.getEnterpriseId());
            dto.setPhase(note.getPhase());
            dto.setInitFlag(initFlag ? 1 : 0);

            // 兼容投资估算批量载入单体列表接口
            if (isTzgsbz) {
                // 合并/未合并  统一唯一标识
                setMergedLdInfo(contractProjectIds, noteList, note, dto);
            }

            // 非建安
            DwsIndexProjectNote nonConstructionNote = nonConstructionMap.get(note.getEnterpriseId() + note.getProjectCode() + note.getPhase());
            // 需要处理的信息
            this.convertSummaryProps(initFlag, projectInfo.getProjectScale(), noteList, dto, nonConstructionNote);
            result.add(dto);
        }

        // 投资估算指标神器 历史数据唯一标识处理
        if (isTzgsbz) {
            setZbProjectNoteResId(result, contractProjectIds);
        }

        // 业态和单体维度时按照合并后的单体或业态建筑面积进行过滤
        if (!SumTypeEnum.PROJECT.getCode().equals(filterConditionVO.getSumCondition())) {
            result = result.stream().filter(dto -> {
                try {
                    return FilterUtil.matchValue(NUMBER_DATA_TYPE, filterConditionVO.getBuildArea(), dto.getBuildArea());
                } catch (Exception e) {
                    e.printStackTrace();
                    return false;
                }
            }).collect(Collectors.toList());
        }

        return result;
    }

    /**
     * 构建返回体
     */
    @TakeTime
    public List<ContractProjectDto.NoteMergeDto> convertToNoteMergeDto(Integer sumCondition,
                                                                        int simpleNoteSumFlag,
                                                                        List<DwsIndexProjectNote> validNotes,
                                                                        Map<String, DwdProjectInfo> projectCodeAndInfoMap) {
        List<ContractProjectDto.NoteMergeDto> noteMergeDtoList = Lists.newArrayList();

        // 根据汇算类型分组合并
        Map<String, List<DwsIndexProjectNote>> groupNoteMap = CommonHandler.noteGroupBySumCondition(sumCondition, validNotes);

        // 构建返回体
        for (List<DwsIndexProjectNote> noteList : groupNoteMap.values()) {
            // 拿出组内归档时间最新的一个节点，用于获取共性信息及最新归档时间
            DwsIndexProjectNote newestNote = CollUtil.getFirst(noteList);

            // 获取项目信息
            String projectCodeAndInfoMapKey = this.generateProjectCodeAndInfoMapKey(newestNote.getEnterpriseId(), newestNote.getProjectCode());
            if (!projectCodeAndInfoMap.containsKey(projectCodeAndInfoMapKey)) {
                continue;
            }
            DwdProjectInfo projectInfo = projectCodeAndInfoMap.get(projectCodeAndInfoMapKey);

            // 构建dto
            ContractProjectDto.NoteMergeDto noteMergeDto = this.buildNoteMergeDto(sumCondition, simpleNoteSumFlag, noteList, newestNote, projectInfo);
            noteMergeDtoList.add(noteMergeDto);
        }

        // 按项目，将相同项目不同阶段的数据进行合并
        if (SumTypeEnum.PROJECT.getCode().equals(sumCondition)) {
            return projectMergeResult(noteMergeDtoList);
        }

        return noteMergeDtoList;
    }

    /**
     * 结构化输出篱笆
     * @param sumCondition
     * @param simpleNoteSumFlag
     * @param projectCodeAndInfoMap
     * @return
     */
    public ContractProjectDto.NoteMergeDto convertToSingleNoteMergeDto(Integer sumCondition,
                                                                       int simpleNoteSumFlag,
                                                                       List<DwsIndexProjectNote> noteList,
                                                                       Map<String, DwdProjectInfo> projectCodeAndInfoMap) {
        // 拿出组内归档时间最新的一个节点，用于获取共性信息及最新归档时间
        DwsIndexProjectNote newestNote = CollUtil.getFirst(noteList);

        // 获取项目信息
        String projectCodeAndInfoMapKey = this.generateProjectCodeAndInfoMapKey(newestNote.getEnterpriseId(), newestNote.getProjectCode());
        if (!projectCodeAndInfoMap.containsKey(projectCodeAndInfoMapKey)) {
            return null;
        }
        DwdProjectInfo projectInfo = projectCodeAndInfoMap.get(projectCodeAndInfoMapKey);

        // 构建dto
        return this.buildNoteMergeDto(sumCondition, simpleNoteSumFlag, noteList, newestNote, projectInfo);
    }

    private List<ContractProjectDto.NoteMergeDto> projectMergeResult(List<ContractProjectDto.NoteMergeDto> noteMergeDtoList) {
        // 使用Stream API进行分组
        Map<String, List<ContractProjectDto.NoteMergeDto>> groupMap = noteMergeDtoList.stream()
                .collect(Collectors.groupingBy(note -> note.getEnterpriseId() + note.getProjectCode(), LinkedHashMap::new, Collectors.toList()));

        // 使用Stream API进行数据合并`
        return groupMap.values().stream()
                .map(noteList -> {
                    ContractProjectDto.NoteMergeDto firstNote = CollUtil.getFirst(noteList);
                    firstNote.setPhases(noteList.stream().map(ContractProjectDto.NoteMergeDto::getPhase).collect(Collectors.toList()));
                    firstNote.setAllTempNoteIds(noteList.stream().flatMap(note -> note.getTempNoteIds().stream()).collect(Collectors.toList()));
                    firstNote.setMergeInfos(noteList.stream().flatMap(note -> note.getMergeInfos().stream()).collect(Collectors.toList()));
                    return firstNote;
                })
                .collect(Collectors.toList());
    }


    private ContractProjectDto.NoteMergeDto buildNoteMergeDto(Integer sumCondition,
                                                              int simpleNoteSumFlag,
                                                              List<DwsIndexProjectNote> noteList,
                                                              DwsIndexProjectNote newestNote,
                                                              DwdProjectInfo projectInfo) {
        ContractProjectDto.NoteMergeDto noteMergeDto =
                this.assembleNoteMergeDto(sumCondition, simpleNoteSumFlag, noteList, newestNote, projectInfo);

        this.assembleNoteMergeDtoAfterHandler(noteList, noteMergeDto);

        return noteMergeDto;
    }

    private ContractProjectDto.NoteMergeDto assembleNoteMergeDto(Integer sumCondition,
                                                                 int simpleNoteSumFlag,
                                                                 List<DwsIndexProjectNote> noteList,
                                                                 DwsIndexProjectNote newestNote,
                                                                 DwdProjectInfo projectInfo) {
        ContractProjectDto.NoteMergeDto noteMergeDto = new ContractProjectDto.NoteMergeDto();
        // 基本信息
        this.assembleBaseInfo(newestNote, noteMergeDto);
        // 项目信息
        this.assembleProjectInfo(projectInfo, noteMergeDto);
        // 合并信息
        this.assembleMergeInfo(sumCondition, noteList, noteMergeDto);
        // 建筑面积
        this.assembleBuildArea(sumCondition, simpleNoteSumFlag, noteList, projectInfo.getProjectScale(), noteMergeDto);
        // 工程规模
        this.assembleScale(noteMergeDto);
        // 造价金额
        this.calculateTotal(simpleNoteSumFlag, noteList, noteMergeDto);

        return noteMergeDto;
    }

    private void assembleBaseInfo(DwsIndexProjectNote newestNote, ContractProjectDto.NoteMergeDto dto) {
        dto.setPhase(newestNote.getPhase());
        dto.setArchiveDate(newestNote.getArchiveDate());
        dto.setEnterpriseId(newestNote.getEnterpriseId());
    }

    private void assembleNoteMergeDtoAfterHandler(List<DwsIndexProjectNote> noteList, ContractProjectDto.NoteMergeDto dto) {
        // 单方造价=总造价/总面积
        dto.setDfIndexValue(MathUtil.div(dto.getTotal(), dto.getBuildArea()));
        dto.setDfIndexValueIncludeTax(MathUtil.div(dto.getTotalIncludeTax(), dto.getBuildArea()));

        // 全费、非全费
        dto.setFullCostDfIndexValue(MathUtil.div(dto.getFullCostTotal(), dto.getBuildArea()));
        dto.setFullCostDfIndexValueIncludeTax(MathUtil.div(dto.getFullCostTotalIncludeTax(), dto.getBuildArea()));
        dto.setNonFullCostDfIndexValue(MathUtil.div(dto.getNonFullCostTotal(), dto.getBuildArea()));
        dto.setNonFullCostDfIndexValueIncludeTax(MathUtil.div(dto.getNonFullCostTotalIncludeTax(), dto.getBuildArea()));

        // 重新计算总造价 单方造价
        dto.setTotalDfIndexValue(MathUtil.div(dto.getTotalAmount(), dto.getBuildArea()));
        dto.setTotalDfIndexValueIncludeTax(MathUtil.div(dto.getTotalAmountIncludeTax(), dto.getBuildArea()));

        dto.setTempNoteIds(noteList.stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList()));
        // 合并项明细信息
        dto.setMergeInfos(this.assembleMergeInfos(noteList));
    }

    public List<ContractProjectDto.NoteInfoDto> assembleMergeInfos(List<DwsIndexProjectNote> noteList) {
        List<ContractProjectDto.NoteInfoDto> mergeInfos = Lists.newArrayList();
        for (DwsIndexProjectNote note : noteList) {
            ContractProjectDto.NoteInfoDto noteInfoDto = new ContractProjectDto.NoteInfoDto();
            noteInfoDto.setTempNoteId(String.valueOf(note.getId()));
            noteInfoDto.setProductSource(note.getProductSource());
            noteInfoDto.setContractProjectId(String.valueOf(note.getContractProjectId()));
            noteInfoDto.setType(DwsNoteTypeEnums.getNameByIndex(note.getType(), note.getNonConstruction()));
            noteInfoDto.setName(note.getLdNameIdentify());
            noteInfoDto.setDisplayName(note.getName());
            noteInfoDto.setItemCostType(note.getItemCostType());
            noteInfoDto.setContractInfoDetail(getContractInfoJson(note.getProjectInfoJson()));
            noteInfoDto.setFeatureDetail(getAttrJson(note.getProjectAttrJson()));
            noteInfoDto.setArchiveDate(note.getArchiveDate());
            noteInfoDto.setTradeName(note.getTradeName());
            noteInfoDto.setPhase(note.getPhase());
            mergeInfos.add(noteInfoDto);
        }
        return mergeInfos;
    }

    public static List<JSONObject> getContractInfoJson(String contractInfoJson) {
        try {
            // 检查输入的 JSON 字符串是否为空
            if (StringUtils.isBlank(contractInfoJson)) {
                return Collections.emptyList();
            }

            // 解析 JSON 字符串为 ContractInfoItem 对象列表
            List<ContractInfoItem> contractInfoList = JSON.parseArray(contractInfoJson, ContractInfoItem.class);

            // 过滤掉 value 为空的 ContractInfoItem 对象
            return contractInfoList.stream()
                    .filter(x -> StringUtils.isNotBlank(x.getValue()))
                    .map(x -> {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("name", x.getName());
                        jsonObject.put("value", x.getValue());
                        return jsonObject;
                    }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("解析合同信息失败 {}", contractInfoJson, e);
            return new ArrayList<>();
        }
    }

    public static List<ProjectAttrDto> getAttrJson(String attrJson) {
        try {
            // 检查输入的 JSON 字符串是否为空
            if (StringUtils.isBlank(attrJson)) {
                return new ArrayList<>();
            }

            // 解析 JSON 字符串为 ProjectAttrDto 对象列表
            List<ProjectAttrDto> projectAttrDtoList = JSON.parseArray(attrJson, ProjectAttrDto.class);

            // 遍历并过滤 ProjectAttrDto 对象
            projectAttrDtoList.removeIf(projectAttrDto -> {
                List<LinkedHashMap> data = projectAttrDto.getData();
                List<ColsDto> cols = projectAttrDto.getCols();

                // 检查 data 和 cols 是否为空
                if (data != null && cols != null) {
                    // 提取 cols 中的 code 值
                    List<String> codes = cols.stream().map(ColsDto::getCode).collect(Collectors.toList());

                    // 过滤 data 列表，保留那些在 codes 列表中对应的值不为空且不为空字符串的项
                    projectAttrDto.setData(data.stream()
                            .filter(map -> codes.stream()
                                    .anyMatch(code -> map.get(code) != null && !map.get(code).toString().trim().isEmpty()))
                            .peek(x -> {
                                x.remove("type");
                                x.remove("selectList");
                            })
                            .collect(Collectors.toList()));

                    // 如果过滤后的 data 列表为空，返回 true 以剔除该 ProjectAttrDto
                    return projectAttrDto.getData().isEmpty();
                }

                // 如果 data 或 cols 为 null，剔除该 ProjectAttrDto
                return true;
            });

            return projectAttrDtoList;

        } catch (Exception e) {
            log.error("解析工程信息失败 {}", attrJson, e);
            return new ArrayList<>();
        }
    }

    private void assembleProjectInfo(DwdProjectInfo projectInfo, ContractProjectDto.NoteMergeDto dto) {
        dto.setProjectCode(projectInfo.getProjectCode());
        dto.setProjectName(projectInfo.getProjectName());
        dto.setProjectId(String.valueOf(projectInfo.getId()));
        dto.setProvinceId(projectInfo.getProvinceId());
        dto.setProvinceName(projectInfo.getProvinceName());
        dto.setCityId(projectInfo.getCityId());
        dto.setCityName(projectInfo.getCityName());
        dto.setDistrictId(projectInfo.getDistrictId());
        dto.setDistrictName(projectInfo.getDistrictName());
        dto.setProjectCreateDate(projectInfo.getCreateDate());
        dto.setProjectStartTime(projectInfo.getStartTime());
        dto.setProjectEndTime(projectInfo.getEndTime());
        dto.setProjectScale(projectInfo.getProjectScale());
        dto.setProjectScaleUnit(projectInfo.getProjectScaleUnit());
        dto.setProjectProductPositioning(projectInfo.getProductPositioning());
        dto.setProjectJianshexingzhi(projectInfo.getJianshexingzhi());
        dto.setProjectConstructionUnit(projectInfo.getConstructionUnit());
        dto.setProjectInfoDetail(getProjectInfoJson(projectInfo));
    }

    private List<JSONObject> getProjectInfoJson(DwdProjectInfo projectInfo) {
        List<JSONObject> result = new ArrayList<>();
        // 主表中的项目信息
        JSONObject projectInfoJsonObj = (JSONObject) JSONObject.toJSON(projectInfo);
        PROJECTINFO_FIELDNAME_MAP.forEach((fieldKey, fieldName) -> {
            if (projectInfoJsonObj.containsKey(fieldKey)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("key", fieldKey);
                jsonObject.put("name", fieldName);
                String value = this.extractFieldValue(projectInfoJsonObj.get(fieldKey), fieldKey, projectInfo);
                jsonObject.put("value", value);
                if (StringUtils.isNotEmpty(value)) {
                    result.add(jsonObject);
                }
            }
        });
        // 自定义项目信息
        List<DwdProjectDetail> projectDetail = projectInfo.getProjectDetail();
        List<JSONObject> customProjectInfo = projectDetail.stream()
                .filter(x -> StringUtils.isNotBlank(x.getFieldValue()) && !("checkbox".equals(x.getFieldType()) && "[]".equals(x.getFieldValue())))
                .map(x -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("key", x.getFieldKey());
                    jsonObject.put("name", x.getFieldName());
                    jsonObject.put("value", x.getFieldValue());
                    return jsonObject;
                }).collect(Collectors.toList());
        result.addAll(customProjectInfo);
        return result;
    }

    private String extractFieldValue(Object value, String key, DwdProjectInfo projectInfo) {
        String valueStr = null;
        if (key.equals("projectArea")) {
            List<String> areaList = Stream.of(projectInfo.getProvinceName(), projectInfo.getCityName(), projectInfo.getDistrictName())
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            valueStr = StringUtils.join(areaList, "/");
            return valueStr;
        }
        if (value != null) {
            if (value instanceof Date) {
                valueStr = (DateUtil.format((Date) value, "yyyy-MM-dd"));
            } else if (value instanceof BigDecimal) {
                BigDecimal result = (BigDecimal) value;
                result = result.setScale(6,  RoundingMode.HALF_UP).stripTrailingZeros();
                valueStr = result.toPlainString();
            } else {
                valueStr = value.toString();
            }
        }
        return valueStr;
    }

    /**
     * 只进行项目层级的筛选：建造面积取项目信息中用户填写的项目规模。
     * 否则取单体汇总值。
     *      如只有“虚拟楼栋”，先做专业标签的聚合，再取归档时间最晚、结构第一个的专业对应的建筑面积值
     *      否则取标签为“真实楼栋”的建筑面积汇总值
     * @param simpleNoteSumFlag
     * @param noteList
     * @param projectScale
     * @param dto
     */
    public void assembleBuildArea(int sumCondition,
                                  int simpleNoteSumFlag,
                                  List<DwsIndexProjectNote> noteList,
                                  BigDecimal projectScale,
                                  ContractProjectDto.NoteMergeDto dto) {
        if (simpleNoteSumFlag == ValueConst.FALSE) {
            // 直接取项目规模
            dto.setBuildArea(MathUtil.valueOf(projectScale));
        } else if (Objects.equals(SumTypeEnum.XNLD_TRADE.getCode(), sumCondition)) {
            // 按虚拟楼栋合并
            dto.setBuildArea(CommonHandler.calcTradeBuildArea(noteList));
        } else{
            // 项目维度 带筛选条件
            dto.setBuildArea(CommonHandler.calcBuildArea(noteList));
        }
    }

    /**
     * todo 当前取值同建筑面积，后续不同业态会取不同的口径值
     * @param dto
     */
    public void assembleScale(ContractProjectDto.NoteMergeDto dto) {
        dto.setScale(dto.getBuildArea());
    }

    private void calculateTotal(int simpleNoteSumFlag,
                                List<DwsIndexProjectNote> noteList,
                                ContractProjectDto.NoteMergeDto dto) {
        BigDecimal[] totalArr = CommonHandler.calculateTotal(simpleNoteSumFlag, noteList);
        dto.setTotal(totalArr[0]);
        dto.setTotalIncludeTax(totalArr[1]);

        dto.setFullCostTotal(totalArr[2]);
        dto.setFullCostTotalIncludeTax(totalArr[3]);
        dto.setNonFullCostTotal(totalArr[4]);
        dto.setNonFullCostTotalIncludeTax(totalArr[5]);

        dto.setTotalAmount(totalArr[6]);
        dto.setTotalAmountIncludeTax(totalArr[7]);
    }

    private void setZbProjectNoteResId(List<ProjectSearchDto> result, List<Long> contractProjectIds) {
        List<OriginalContractProject> dwdContractProjects = dwdDataRepository.selectOriginalProjectIdByContractProjectIds(contractProjectIds);
        List<String> originalProjectIds = dwdContractProjects
                .stream()
                .map(OriginalContractProject::getOriginalProjectId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(originalProjectIds)) {
            Map<String, Long> resIdAndOriginalProjectIdRelationMap =
                    zbkResIdAndOriginalProjectIdRelationService.getOriginalProjectIdAndResIdRelation(originalProjectIds);

            Map<Long, OriginalContractProject> dwdContractProjectMap = dwdContractProjects
                    .stream()
                    .collect(Collectors.toMap(OriginalContractProject::getId, a -> a, (a, b) -> a));

            result.stream()
                    .map(ProjectSearchDto::getDtInfoDtoList)
                    .flatMap(Collection::stream)
                    .forEach(c ->{
                        OriginalContractProject original = dwdContractProjectMap.get(c.getContractProjectId());
                        if (original != null) {
                            Long zbProjectNoteResId = resIdAndOriginalProjectIdRelationMap
                                    .get(getRelationMapKey(original.getCustomerCode(), original.getProjectCode(), original.getOriginalProjectId()));
                            if (zbProjectNoteResId != null) {
                                c.setZbProjectNoteResId(zbProjectNoteResId);
                            }
                        }
                    });
        }
    }

    private void setMergedLdInfo(List<Long> contractProjectIds, List<DwsIndexProjectNote> noteList, DwsIndexProjectNote note, ProjectSearchDto dto) {
        String originalIdentity = SecureUtil.md5(String.format("%s_%s_%s_%s_%s_%s",
                note.getEnterpriseId(), note.getProjectCode(), note.getPhase(), note.getProjectCategoryName(), note.getLdNameIdentify(), note.getBuildArea()));
        dto.setDtIdentity(originalIdentity);

        List<DtInfoDto> dtInfoDtos = Lists.newArrayList();
        Set<String> productSource = Sets.newHashSet();
        noteList.forEach(c -> {
            DtInfoDto dtInfoDto = new DtInfoDto();
            dtInfoDto.setProductSource(c.getProductSource());
            dtInfoDto.setOriginalDtName(c.getName());
            dtInfoDto.setTempNoteId(c.getId());
            dtInfoDto.setContractProjectId(c.getContractProjectId());
            dtInfoDto.setItemCostType(c.getItemCostType());
            dtInfoDtos.add(dtInfoDto);
            // 记录指标神器工程id
            if (ProductSource.ZBSQ.getIndex().equals(c.getProductSource())) {
                contractProjectIds.add(c.getContractProjectId());
            }
            // 投资估算 楼栋名称取未优化过的
            if (ProductSource.TZGSBZ.getIndex().equals(c.getProductSource())) {
                c.setLdNameIdentify(c.getName());
            }
            productSource.add(c.getProductSource());
        });
        dto.setProductSource(Lists.newArrayList(productSource));
        dto.setDtInfoDtoList(dtInfoDtos);
    }


    public String getRelationMapKey(String customerCode, String projectCode, String originalProjectIdList){
        return String.format("%s@@%s@@%s", customerCode, projectCode, originalProjectIdList);
    }

    /**
     * 处理聚合字段
     */
    private void convertSummaryProps(boolean initFlag,
                                     BigDecimal projectScale,
                                     List<DwsIndexProjectNote> noteList,
                                     ProjectSearchDto projectSearchDto,
                                     DwsIndexProjectNote nonConstructionNote) {
        // id集合
        List<Long> ids = noteList.stream().map(DwsIndexProjectNote::getId).collect(Collectors.toList());
        projectSearchDto.setIds(ids);

        // 一个项目下只有一个非建安，这个非建安是最新归档文件的非建安数据。造价类型也是跟着最新归档文件的。初始状态下，需要给项目下各造价类型的目标成本文件都来一份非建安数据。
        if (nonConstructionNote != null && ids.contains(nonConstructionNote.getId())) {
            nonConstructionNote = null;
        }

        // 业态 顿号隔开
        String categoryStr = noteList.stream().filter(item -> !item.getNonConstruction() && StringUtils.isNotEmpty(item.getProjectCategoryName())).map(
                        DwsIndexProjectNote::getProjectCategoryName).distinct()
                .map(x -> x.lastIndexOf(CharConst.DOUBLE_AT) == -1 ? x : x.substring(x.lastIndexOf(CharConst.DOUBLE_AT) + 2)).distinct().collect(Collectors.joining(STOP_SIGN));
        projectSearchDto.setCategory(categoryStr);

        // 单体工程 顿号隔开
        String ldNameStr = noteList.stream().map(DwsIndexProjectNote::getLdNameIdentify)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(STOP_SIGN));
        projectSearchDto.setLdNameIdentify(ldNameStr);

        // 处理需要计算的属性
        this.calculationProperties(initFlag, projectScale, noteList, projectSearchDto, nonConstructionNote);
    }

    /**
     * 处理合并信息
     */
    private void assembleMergeInfo(Integer sumCondition, List<DwsIndexProjectNote> noteList, ContractProjectDto.NoteMergeDto noteMergeDto) {
        List<String> categoryName = new ArrayList<>();
        List<String> categoryPath = new ArrayList<>();
        List<String> ldNameIdentify = new ArrayList<>();
        List<String> ldNameOriginal = new ArrayList<>();
        List<Date> compileDate = new ArrayList<>();
        List<String> fileNames = new ArrayList<>();
        List<String> tradeName = new ArrayList<>();
        List<Integer> itemCostType = new ArrayList<>();

        for (DwsIndexProjectNote note : noteList) {
            // 业态名称
            this.assembleCategoryName(categoryName, categoryPath, note);
            // 楼栋名称
            this.assembleLdName(ldNameIdentify, ldNameOriginal, note);
            // 文件名称
            this.assembleContractName(fileNames, note);
            // 专业名称
            this.assembleTradeName(tradeName, note);
            // 综合单价取费
            this.assembleItemCostType(itemCostType, note);
            // 编制时间
            this.assembleCompileDate(compileDate, note);
        }

        noteMergeDto.setCategoryName(categoryName);
        noteMergeDto.setCategoryPath(categoryPath);
        noteMergeDto.setLdNameIdentify(ldNameIdentify);
        noteMergeDto.setLdNameOriginal(ldNameOriginal);
        noteMergeDto.setFileName(fileNames);
        noteMergeDto.setTradeName(tradeName);
        noteMergeDto.setItemCostType(itemCostType);

        compileDate.sort(Comparator.reverseOrder());
        noteMergeDto.setCompileDate(compileDate);
    }

    /**
     * TODO 编制时间理论上需要在dwd和dws单独做一个编制时间的字段 根据不同的业务阶段进行赋值（产品给规则） 最终在查询以及筛选两个地方需要进行重新处理
     */
    private void assembleCompileDate(List<Date> compileDate, DwsIndexProjectNote note) {
        Map<String, ContractInfoItem> stringObjectMap = this.parseProjectInfo(note.getProjectInfoJson());
        String productSource = note.getProductSource();
        // 结算台账、指标更新：编制时间为归档时间
        if (JSTZ.getIndex().equals(productSource) || ZBGX.getIndex().equals(productSource)) {
            if (!compileDate.contains(note.getArchiveDate())) {
                compileDate.add(DateUtil.beginOfDay(note.getArchiveDate()));
            }
        } else {
            ContractInfoItem compileDateItem;
            if (ZBW.getIndex().equals(productSource)) {
                // 指标网历史数据为编制日期，新数据为编制日期
                compileDateItem = Optional.ofNullable(stringObjectMap.get(COMPILE_DATE)).orElse(stringObjectMap.get(COMPILE_TIME));
            } else {
                compileDateItem = stringObjectMap.get(COMPILE_TIME);
            }
            if (compileDateItem != null) {
                Date itemCompileDate = DateUtils.parseData(compileDateItem.getValue());
                if (itemCompileDate != null && !compileDate.contains(itemCompileDate)) {
                    compileDate.add(DateUtil.beginOfDay(itemCompileDate));
                }
            }
        }
    }

    private void assembleLdName(List<String> ldNameIdentify, List<String> ldNameOriginal, DwsIndexProjectNote note) {
        if (Objects.equals(note.getType(), LD.getIndex()) || Objects.equals(note.getType(), XN_LD.getIndex())) {
            // 楼栋标识
            String ldIdentify = note.getLdNameIdentify();
            if (StringUtils.isNotEmpty(ldIdentify) && !ldNameIdentify.contains(ldIdentify)) {
                ldNameIdentify.add(ldIdentify);
            }

            // 原始楼栋名称
            String ldOriginal = note.getName();
            if (StringUtils.isNotEmpty(ldOriginal) && !ldNameOriginal.contains(ldOriginal)) {
                ldNameOriginal.add(ldOriginal);
            }
        }
    }

    /**
     * 合同文件名合并
     * @param fileNames: 合同名称列表
     * @param note: dws的projectNote
     */
    private void assembleContractName(List<String> fileNames, DwsIndexProjectNote note) {
        if (StringUtils.isNotEmpty(note.getFileName())) {
            String contractName = note.getFileName();
            if (!fileNames.contains(contractName)) {
                fileNames.add(contractName);
            }
        }
    }

    private void assembleCategoryName(List<String> categoryName, List<String> categoryPath, DwsIndexProjectNote note) {
        if ((Objects.equals(note.getType(), LD.getIndex()) || Objects.equals(note.getType(), XN_LD.getIndex())) && StringUtils.isNotEmpty(note.getProjectCategoryName())) {
            String categoryNamePath = note.getProjectCategoryName();
            int index = categoryNamePath.lastIndexOf(CharConst.DOUBLE_AT);
            String categoryLastName = index == -1 ? categoryNamePath : categoryNamePath.substring(index + CharConst.DOUBLE_AT.length());
            if (!categoryName.contains(categoryLastName)) {
                categoryName.add(categoryLastName);
            }
            if (!categoryPath.contains(categoryNamePath)) {
                categoryPath.add(categoryNamePath);
            }
        }
    }

    private void assembleTradeName(List<String> tradeNameList, DwsIndexProjectNote note) {
        if (StringUtils.isNotBlank(note.getTradeName())) {
            String tradeName = note.getTradeName();
            if (!tradeNameList.contains(tradeName)) {
                tradeNameList.add(tradeName);
            }
        }
    }

    private void assembleItemCostType(List<Integer> itemCostTypeList, DwsIndexProjectNote note) {
        if (note.getItemCostType() != null) {
            Integer itemCostType = note.getItemCostType();
            if (!itemCostTypeList.contains(itemCostType)) {
                itemCostTypeList.add(itemCostType);
            }
        }
    }

    /**
     * 处理需要计算的属性
     */
    private void calculationProperties(boolean initFlag,
                                       BigDecimal projectScale,
                                       List<DwsIndexProjectNote> noteList,
                                       ProjectSearchDto projectSearchDto,
                                       DwsIndexProjectNote nonConstructionNote) {
        BigDecimal buildAreaTotal = BigDecimal.valueOf(0);
        BigDecimal amountTotal = BigDecimal.valueOf(0);
        BigDecimal amountTotalIncludeTax = BigDecimal.valueOf(0);

        BigDecimal fullCostTotal = BigDecimal.valueOf(0);
        BigDecimal fullCostTotalIncludeTax = BigDecimal.valueOf(0);
        BigDecimal nonFullCostTotal = BigDecimal.valueOf(0);
        BigDecimal nonFullCostTotalIncludeTax = BigDecimal.valueOf(0);
        Set<String> ldSet = new HashSet<>();

        for (DwsIndexProjectNote item : noteList) {
            // 名称相同，面积相同的楼栋只计一次面积
            if (!(LD.getIndex().equals(item.getType()) && !ldSet.add(item.getLdNameIdentify() + CharConst.DOUBLE_AT + item.getBuildArea()))) {
                buildAreaTotal = MathUtil.add(buildAreaTotal, item.getBuildArea());
            }
            amountTotal = MathUtil.add(amountTotal, item.getTotal());
            amountTotalIncludeTax = MathUtil.add(amountTotalIncludeTax, item.getTotalIncludeTax());

            fullCostTotal = MathUtil.add(fullCostTotal, item.getFullCostTotal());
            fullCostTotalIncludeTax = MathUtil.add(fullCostTotalIncludeTax, item.getFullCostTotalIncludeTax());
            nonFullCostTotal = MathUtil.add(nonFullCostTotal, item.getNonFullCostTotal());
            nonFullCostTotalIncludeTax = MathUtil.add(nonFullCostTotalIncludeTax, item.getNonFullCostTotalIncludeTax());

        }

        // 初始化时,①项目规模取户口簿的项目规模；②市场化计价的总造价直接取合约总造价；③计算是需要加非建安
        if (initFlag) {
            buildAreaTotal = MathUtil.valueOf(projectScale);
            amountTotal = this.amountTotalInitDeal(noteList, amountTotal, nonConstructionNote, projectSearchDto, false, 0);
            amountTotalIncludeTax = this.amountTotalInitDeal(noteList, amountTotalIncludeTax, nonConstructionNote, projectSearchDto, true, 0);

            fullCostTotal = this.amountTotalInitDeal(noteList, fullCostTotal, nonConstructionNote, projectSearchDto, false, 1);
            fullCostTotalIncludeTax = this.amountTotalInitDeal(noteList, fullCostTotalIncludeTax, nonConstructionNote, projectSearchDto, true, 1);
            nonFullCostTotal = this.amountTotalInitDeal(noteList, nonFullCostTotal, nonConstructionNote, projectSearchDto, false, 2);
            nonFullCostTotalIncludeTax = this.amountTotalInitDeal(noteList, nonFullCostTotalIncludeTax, nonConstructionNote, projectSearchDto, true, 2);
        }

        projectSearchDto.setBuildArea(buildAreaTotal);
        projectSearchDto.setAmount(amountTotal);
        projectSearchDto.setAmountIncludeTax(amountTotalIncludeTax);

        // 单方造价=总造价/总面积
        projectSearchDto.setDfIndexValue(MathUtil.div(amountTotal, buildAreaTotal));
        projectSearchDto.setDfIndexValueIncludeTax(MathUtil.div(amountTotalIncludeTax, buildAreaTotal));

        // 全费、非全费
        projectSearchDto.setFullCostAmount(fullCostTotal);
        projectSearchDto.setFullCostAmountIncludeTax(fullCostTotalIncludeTax);
        projectSearchDto.setNonFullCostAmount(nonFullCostTotal);
        projectSearchDto.setNonFullCostAmountIncludeTax(nonFullCostTotalIncludeTax);

        projectSearchDto.setFullCostDfIndexValue(MathUtil.div(fullCostTotal, buildAreaTotal));
        projectSearchDto.setFullCostDfIndexValueIncludeTax(MathUtil.div(fullCostTotalIncludeTax, buildAreaTotal));
        projectSearchDto.setNonFullCostDfIndexValue(MathUtil.div(nonFullCostTotal, buildAreaTotal));
        projectSearchDto.setNonFullCostDfIndexValueIncludeTax(MathUtil.div(nonFullCostTotalIncludeTax, buildAreaTotal));

    }

    /**
     * 初始化时,②市场化计价的总造价直接取合约总造价；③计算是需要加非建安
     */
    private BigDecimal amountTotalInitDeal(List<DwsIndexProjectNote> noteList,
                                           BigDecimal amountTotal,
                                           DwsIndexProjectNote nonConstructionNote,
                                           ProjectSearchDto projectSearchDto,
                                           boolean includeTaxFlag,
                                           int costType) {
        boolean hasQyqd = noteList.stream()
                .allMatch(x -> QYQD.getIndex().equals(x.getProductSource()) || ProductSource.YSTZ.getIndex().equals(x.getProductSource()) ||
                        JSTZ.getIndex().equals(x.getProductSource()));
        if (hasQyqd) {
            amountTotal = BigDecimal.valueOf(0);
            Set<Long> contractProjectIdSet = new HashSet<>();

            for (DwsIndexProjectNote note : noteList) {
                String productSource = note.getProductSource();
                if (QYQD.getIndex().equals(productSource) || ProductSource.YSTZ.getIndex().equals(productSource)
                || JSTZ.getIndex().equals(productSource)) {
                    if (contractProjectIdSet.add(note.getContractProjectId())) {
                        amountTotal = MathUtil.add(amountTotal, includeTaxFlag ? note.getContractProjectTotalIncludeTax() : note.getContractProjectTotal());
                    }
                } else {
                    if (costType == 0) {
                        amountTotal = MathUtil.add(amountTotal, includeTaxFlag  ?  note.getTotalIncludeTax() : note.getTotal());

                    }
                    if (costType == 1) {
                        amountTotal = MathUtil.add(amountTotal, includeTaxFlag  ?  note.getFullCostTotalIncludeTax() : note.getFullCostTotal());
                    }

                    if (costType == 2) {
                        amountTotal = MathUtil.add(amountTotal, includeTaxFlag  ?  note.getNonFullCostTotalIncludeTax() : note.getNonFullCostTotal());
                    }
                }
            }
        }

        if (nonConstructionNote != null) {
            amountTotal = MathUtil.add(amountTotal, includeTaxFlag ? nonConstructionNote.getTotalIncludeTax() : nonConstructionNote.getTotal());

            List<Long> ids = CollUtil.isNotEmpty(projectSearchDto.getIds()) ? projectSearchDto.getIds() : new ArrayList<>();
            if (!ids.contains(nonConstructionNote.getId())){
                ids.add(nonConstructionNote.getId());
                projectSearchDto.setIds(ids);
            }
        }

        return amountTotal;
    }

    @TakeTime
    private Page<ProjectSearchDto> convertPageDto(Integer currentPage, Integer pageSize, List<ProjectSearchDto> mergeData) {
        int pageNum = currentPage == null ? 1 : currentPage;
        int size = pageSize == null ? 10 : pageSize;
        int total = Optional.of(mergeData.size()).orElse(0);

        mergeData = mergeData.stream().sorted(Comparator.comparing(ProjectSearchDto::getArchiveDate, Comparator.nullsFirst(Comparator.naturalOrder())).reversed())
                .skip((long) (pageNum - 1) * size).limit(size).collect(Collectors.toList());

        Page<ProjectSearchDto> pageInfo = new Page<>();
        pageInfo.setList(mergeData);
        pageInfo.setPage(new PageData(pageNum, size, total));

        return pageInfo;
    }

    private ContractProjectDto convertContractProjectDto(Integer currentPage, Integer pageSize, List<ContractProjectDto.NoteMergeDto> mergeData, Integer simpleNoteSumFlag, Boolean fetchAll) {
        int pageNum = currentPage == null ? 1 : currentPage;
        int size = pageSize == null ? 10 : pageSize;
        int total = Optional.of(mergeData.size()).orElse(0);

        ContractProjectDto contractProjectDto = new ContractProjectDto();
        if (Boolean.TRUE.equals(fetchAll)){
            mergeData = mergeData.stream()
                    .sorted(Comparator.comparing(ContractProjectDto.NoteMergeDto::getArchiveDate, Comparator.nullsFirst(Comparator.naturalOrder())).reversed())
                    .collect(Collectors.toList());
            contractProjectDto.setPage(new PageData(pageNum, total, total));
        } else {
            // 分页查询
            mergeData = mergeData.stream()
                    .sorted(Comparator.comparing(ContractProjectDto.NoteMergeDto::getArchiveDate, Comparator.nullsFirst(Comparator.naturalOrder())).reversed())
                    .skip((long) (pageNum - 1) * size).limit(size).collect(Collectors.toList());
            contractProjectDto.setPage(new PageData(pageNum, size, total));
        }

        contractProjectDto.setContractProjectList(mergeData);
        contractProjectDto.setSimpleNoteSumFlag(simpleNoteSumFlag);

        return contractProjectDto;
    }

    @Override
    public List<SampleNoteDto> ldNoteList(String enterpriseId, SampleNoteIndexDataVO sampleNoteIndexDataVO) {
        List<Long> sampleNoteIds = sampleNoteIndexDataVO.getSampleNoteIds().stream().map(Long::valueOf).collect(Collectors.toList());
        if (CollUtil.isEmpty(sampleNoteIds)) {
            return null;
        }

        List<SampleNoteDto> sampleNoteDtoList = dwsDataRepository.selectNoteAndProjByIds(enterpriseId, sampleNoteIds);

        sampleNoteDtoList.forEach(item -> {
            // 单方造价=总造价/总面积
            item.setDfIndexValue(MathUtil.div(item.getAmount(), item.getBuildArea()));
            item.setDfIndexValueIncludeTax(MathUtil.div(item.getAmountIncludeTax(), item.getBuildArea()));
            item.setFullCostDfIndexValue(MathUtil.div(item.getFullCostAmount(), item.getBuildArea()));
            item.setFullCostDfIndexValue(MathUtil.div(item.getFullCostAmountIncludeTax(), item.getBuildArea()));
            item.setNonFullCostDfIndexValue(MathUtil.div(item.getNonFullCostAmount(), item.getBuildArea()));
            item.setNonFullCostDfIndexValue(MathUtil.div(item.getNonFullCostDfIndexValueIncludeTax(), item.getBuildArea()));
            // 工程分类名称只要末级
            String categoryName = item.getCategoryName();
            if (StringUtils.isNotEmpty(categoryName)) {
                item.setCategoryName(categoryName.lastIndexOf(CharConst.DOUBLE_AT) == -1 ? categoryName : categoryName.substring(categoryName.lastIndexOf(CharConst.DOUBLE_AT) + 2));
            }
        });

        return sampleNoteDtoList;
    }

    @Override
    public List<MakeUpIndexData> getMakeUpIndexDataList(String enterpriseId, List<Long> sampleNoteIds) {
        if (CollUtil.isEmpty(sampleNoteIds)) {
            return null;
        }
        log.info("查询单体id参数为：{}", sampleNoteIds.size());
        return dwsDataRepository.selectNoteInfoByIds(sampleNoteIds);
    }

    @Override
    public List<NoteExistVO> noteExist(String enterpriseId, List<NoteExistVO> noteExistVOS) {
        if (CollectionUtils.isEmpty(noteExistVOS)) {
            return Collections.emptyList();
        }
        return noteExistVOS.stream().map(noteExistVO -> dwsDataRepository.existNoteJudge(noteExistVO.getEnterpriseId(), noteExistVO))
                .collect(Collectors.toList());
    }

    @Override
    public ProjectDetailRangeDto detailRange(SingleProjectReqVO reqVO) {
        ProjectDetailRangeDto dto = new ProjectDetailRangeDto();
        List<DwsIndexProjectNote> noteList = dwsDataRepository.selectNoteBySingleProjectReqVO(reqVO);
        if (CollUtil.isEmpty(noteList)){
            return dto;
        }

        this.includeIndexData(reqVO, dto, noteList);

        this.includeDesignIndexAndBuildStandard(dto, noteList, reqVO.getEnterpriseId(), reqVO.getProjectCode(), reqVO.getPhase());

        // 按项目时才区分建安和非建安
        if (Objects.equals(SumTypeEnum.PROJECT.getCode(), reqVO.getSumCondition())) {
            this.projectSumTypeDetailRange(reqVO, dto, noteList);
        }

        boolean isCostZbk = isCostZbk(noteList);

        if ((Objects.equals(SumTypeEnum.CATEGORY.getCode(), reqVO.getSumCondition())
                || Objects.equals(SumTypeEnum.DT.getCode(), reqVO.getSumCondition())
                || Objects.equals(SumTypeEnum.PROJECT.getCode(), reqVO.getSumCondition())) || isCostZbk) {
            this.includeAttr(dto, noteList);
        }

        return dto;
    }

    private Boolean isCostZbk(List<DwsIndexProjectNote> noteList) {
        return noteList.stream()
                .map(DwsIndexProjectNote::getProductSource)
                .anyMatch(x -> GCDP_COST_ZBSQ_WEB.getIndex().equals(x) || SGCBCS2.getIndex().equals(x));
    }

    private Boolean isIncludeSGFMBCBCS(List<DwsIndexProjectNote> noteList) {
        return noteList.stream()
                .map(DwsIndexProjectNote::getProductSource)
                .anyMatch(v -> SGCBCS2.getIndex().equals(v));
    }

    private void includeAttr(ProjectDetailRangeDto dto, List<DwsIndexProjectNote> noteList) {
        boolean includeProjectAttr = noteList.stream().anyMatch(DwsIndexProjectNote::getIncludeProjectAttr);
        dto.setIncludeProjectAttr(includeProjectAttr);
    }

    private void projectSumTypeDetailRange(SingleProjectReqVO reqVO, ProjectDetailRangeDto dto, List<DwsIndexProjectNote> noteList) {
        // 非建安
        boolean includeFJAIndex = noteList.stream().anyMatch(x -> Objects.equals(NONE.getIndex(), x.getType()) && x.getNonConstruction());
        dto.setIncludeFJAIndex(includeFJAIndex);
        // 建安
        List<DwsIndexProjectNote> jaNote = noteList.stream().filter(x -> !x.getNonConstruction()).collect(Collectors.toList());
        List<ProjectDetailRangeDto.MakeUp> jaIndexMakeUp = this.jaIndexNodeDetails(reqVO, jaNote);
        dto.setJAIndex(jaIndexMakeUp);
        // 主要工料指标、经济技术指标，按项目时无业务意义
        dto.setIncludeIndexMainRes(false);
        dto.setIncludeIndexEconomics(false);
    }

    private void includeIndexData(SingleProjectReqVO reqVO, ProjectDetailRangeDto dto, List<DwsIndexProjectNote> noteList) {
        for (DwsIndexProjectNote note : noteList) {
            // 造价阶段的过滤，按项目时，目前实现会把最新的非建安给每个造价阶段都放置一份，造价阶段的值不太对
            if (!Objects.equals(note.getPhase(), reqVO.getPhase())){
                continue;
            }
            dto.setIncludeJMDF(dto.getIncludeJMDF() || note.getIncludeIndexJmdf());
            dto.setIncludeSWLDF(dto.getIncludeSWLDF() || note.getIncludeIndexSwldf());
            dto.setIncludeDFZB(dto.getIncludeDFZB() || note.getIncludeIndexDfzb());
            dto.setIncludeZYLZB(dto.getIncludeZYLZB() || note.getIncludeIndexZylzb());
            dto.setIncludeJMHLZB(dto.getIncludeJMHLZB() || note.getIncludeIndexJmhlzb());
            dto.setIncludeIndexMainRes(dto.getIncludeIndexMainRes() || note.getIncludeIndexMainRes());
            dto.setIncludeIndexEconomics(dto.getIncludeIndexEconomics() || note.getIncludeIndexEconomics());
        }

        // ①成本测算来源的数据无建面单方、建面含量指标，因此查询指标时查询有效指标即可
        // ②当成本测算和成本指标分析数据合并时，建面单方、建面含量的总成本只有一半，因此这两类指标不应展示。
        // 只要包含成本测算来源，就不查建面单方、建面含量
        if (isIncludeSGFMBCBCS(noteList)) {
            dto.setIncludeJMDF(false);
            dto.setIncludeJMHLZB(false);
        }
    }

    private void includeDesignIndexAndBuildStandard(ProjectDetailRangeDto dto,
                                                    List<DwsIndexProjectNote> noteList,
                                                    String enterpriseId,
                                                    String projectCode,
                                                    String phase){
        // 建造标准数据迁移了
        List<Long> noteIds = noteList.stream().map(DwsIndexProjectNote::getId).collect(Collectors.toList());
        List<DwsBuildStandardIndex> buildStandardIndexList = buildStandardIndexMapper.selectByIndexProjectNoteIds(noteIds);
        dto.setIncludeBuildStandard(CollUtil.isNotEmpty(buildStandardIndexList));
        // 查询dws归档数据-设计指标、建造标准数据表，1：设计指标；2：建造标准
        List<DwsNewArchiveData> dwsNewArchiveDataList = newArchiveDataMapper.getIncludeByProjectCode(enterpriseId, projectCode, phase);
        if (CollUtil.isEmpty(dwsNewArchiveDataList)) {
            return;
        }

        // 设计指标
        Optional<DwsNewArchiveData> designIndexOptional = dwsNewArchiveDataList.stream()
                .filter(a -> Objects.equals(a.getType(), ArchiveTypeEnum.DESIGN_INDEX.getType()))
                .findAny();
        dto.setIncludeDesignIndex(designIndexOptional.isPresent());

        // 建造标准，需要比较业态是否存在
//        Optional<DwsNewArchiveData> buildStandardOptional = dwsNewArchiveDataList.stream()
//                .filter(a -> Objects.equals(a.getType(), ArchiveTypeEnum.BUILD_STANDARD.getType()))
//                .findAny();
//        if (buildStandardOptional.isPresent()) {
//            Set<String> projectCategoryCodeSet = noteList.parallelStream()
//                    .map(DwsIndexProjectNote::getProjectCategoryCode).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
//
//            JSONObject jsonData = JSON.parseObject(buildStandardOptional.get().getDataJson());
//            JSONArray colsInfo = jsonData.getJSONArray(Constant.colsInfo);
//
//            boolean includeBuildStandard = colsInfo.stream()
//                    .map(JSONObject.class::cast)
//                    .anyMatch(target -> projectCategoryCodeSet.contains(target.getString(Constant.targetCode)));
//
//            dto.setIncludeBuildStandard(includeBuildStandard);
//        }
    }

    private List<ProjectDetailRangeDto.MakeUp> jaIndexNodeDetails(SingleProjectReqVO reqVO, List<DwsIndexProjectNote> noteList) {
        if (!Objects.equals(SumTypeEnum.PROJECT.getCode(), reqVO.getSumCondition()) || CollUtil.isEmpty(noteList)) {
            return Lists.newArrayList();
        }

        CommonHandler.sameLdCategorySync(noteList);

        // 查询基础数据
        Map<String, Integer> dimCategoryNamePathAndOrd = projectCategoryService.getDimCategoryNamePathAndOrd(reqVO.getEnterpriseId());
        Map<String, DimZbStandardsTrade> tradeNameMap = dimTradeInfoService.tradeNameMapByEnterpriseId(reqVO.getEnterpriseId());

        List<ProjectDetailRangeDto.MakeUpNode> makeUpList = this.convertMakeUpList(noteList, dimCategoryNamePathAndOrd, tradeNameMap);

        return this.mergeAndSort(makeUpList);
    }

    private List<ProjectDetailRangeDto.MakeUpNode> convertMakeUpList(List<DwsIndexProjectNote> noteList,
                                                                 Map<String, Integer> dimCategoryNamePathAndOrd,
                                                                 Map<String, DimZbStandardsTrade> tradeNameMap) {
        List<ProjectDetailRangeDto.MakeUpNode> makeUpNodeList = Lists.newArrayList();

        for (int i = 0; i < noteList.size(); i++) {
            DwsIndexProjectNote note = noteList.get(i);

            DwsNoteTypeEnums noteType = DwsNoteTypeEnums.getEnumByIndex(note.getType(), note.getNonConstruction());
            if (noteType == null) {
                continue;
            }

            ProjectDetailRangeDto.MakeUpNode makeUpNode = new ProjectDetailRangeDto.MakeUpNode();
            makeUpNode.setArchiveOrd(i);
            makeUpNode.setArchiveDate(note.getArchiveDate());
            makeUpNode.setTempNoteId(note.getId());
            makeUpNode.setOriginalType(noteType.getName());

            switch (noteType) {
                case LD:
                    makeUpNode.setSameLdGroupKey(note.getLdNameIdentify() + StrPool.BRACKET_START + note.getBuildArea() + StrPool.BRACKET_END);
                    makeUpNode.setOriginalName(note.getName());
                    makeUpNode.setIdentifyName(note.getLdNameIdentify());
                    if (StringUtils.isNotEmpty(note.getProjectCategoryCode())) {
                        makeUpNode.setTypeOrd(JADetailsEnums.YT_LD.getOrd());
                        if (Objects.nonNull(dimCategoryNamePathAndOrd) && dimCategoryNamePathAndOrd.containsKey(note.getProjectCategoryName())){
                            makeUpNode.setBasicInfoOrd(dimCategoryNamePathAndOrd.get(note.getProjectCategoryName()));
                        }
                        makeUpNode.setGroupType(JADetailsEnums.YT_LD.getType());
                        makeUpNode.setGroupKey(note.getProjectCategoryName());
                        makeUpNode.setDisplayName(CommonHandler.getLastCategoryName(note.getProjectCategoryName()));
                    } else {
                        makeUpNode.setTypeOrd(JADetailsEnums.WYT_LD.getOrd());
                        makeUpNode.setGroupType(JADetailsEnums.WYT_LD.getType());
                        makeUpNode.setGroupKey(note.getLdNameIdentify() + StrPool.BRACKET_START + note.getBuildArea() + StrPool.BRACKET_END);
                        makeUpNode.setDisplayName(note.getName());
                    }
                    break;
                case VIRTUAL:
                    makeUpNode.setTypeOrd(JADetailsEnums.XNJD.getOrd());
                    makeUpNode.setGroupType(JADetailsEnums.XNJD.getType());
                    makeUpNode.setGroupKey(BusinessConstants.QTJA_NAME);
                    makeUpNode.setDisplayName(BusinessConstants.QTJA_NAME);
                    break;
                case XN_LD:
                    makeUpNode.setSameLdGroupKey(note.getLdNameIdentify() + StrPool.BRACKET_START + note.getTradeName() + StrPool.BRACKET_END);
                    makeUpNode.setOriginalName(note.getName());
                    makeUpNode.setIdentifyName(note.getLdNameIdentify());
                    if (StringUtils.isNotEmpty(note.getProjectCategoryCode())) {
                        makeUpNode.setTypeOrd(JADetailsEnums.YT_XNLD.getOrd());
                        if (Objects.nonNull(dimCategoryNamePathAndOrd) && dimCategoryNamePathAndOrd.containsKey(note.getProjectCategoryName())){
                            makeUpNode.setBasicInfoOrd(dimCategoryNamePathAndOrd.get(note.getProjectCategoryName()));
                        }
                        makeUpNode.setGroupType(JADetailsEnums.YT_XNLD.getType());
                        makeUpNode.setGroupKey(note.getProjectCategoryName());
                        makeUpNode.setDisplayName(CommonHandler.getLastCategoryName(note.getProjectCategoryName()));
                    } else {
                        makeUpNode.setTypeOrd(JADetailsEnums.WYT_XNLD.getOrd());
                        makeUpNode.setGroupType(JADetailsEnums.WYT_XNLD.getType());
                        if (Objects.nonNull(tradeNameMap) && tradeNameMap.containsKey(note.getTradeName())){
                            makeUpNode.setBasicInfoOrd(tradeNameMap.get(note.getTradeName()).getOrd());
                        }
                        makeUpNode.setGroupKey(note.getTradeName());
                        makeUpNode.setDisplayName(note.getTradeName());
                    }
                    break;
                default:
            }
            makeUpNodeList.add(makeUpNode);
        }
        return makeUpNodeList;
    }

    private List<ProjectDetailRangeDto.MakeUp> mergeAndSort(List<ProjectDetailRangeDto.MakeUpNode> makeUpList) {
        Map<String, ProjectDetailRangeDto.MakeUp> makeUpMap = Maps.newHashMap();

        for (ProjectDetailRangeDto.MakeUpNode makeUpNode : makeUpList) {
            String key = makeUpNode.getGroupType() + makeUpNode.getGroupKey();
            if (makeUpMap.containsKey(key)) {
                ProjectDetailRangeDto.MakeUp existingMakeUp = makeUpMap.get(key);
                existingMakeUp.getDisplayNameSet().add(makeUpNode.getDisplayName());
                existingMakeUp.setArchiveOrd(Math.min(makeUpNode.getArchiveOrd(), existingMakeUp.getArchiveOrd()));
                existingMakeUp.setDisplayName(String.join(STOP_SIGN, existingMakeUp.getDisplayNameSet()));
                existingMakeUp.getMergeInfos().add(makeUpNode);
            } else {
                ProjectDetailRangeDto.MakeUp newMakeUp = new ProjectDetailRangeDto.MakeUp();
                newMakeUp.setGroupType(makeUpNode.getGroupType());
                newMakeUp.setGroupKey(makeUpNode.getGroupKey());
                newMakeUp.setDisplayName(makeUpNode.getDisplayName());
                newMakeUp.setTypeOrd(makeUpNode.getTypeOrd());
                newMakeUp.setBasicInfoOrd(makeUpNode.getBasicInfoOrd());
                newMakeUp.setArchiveOrd(makeUpNode.getArchiveOrd());
                newMakeUp.setArchiveDate(makeUpNode.getArchiveDate());
                newMakeUp.getDisplayNameSet().add(makeUpNode.getDisplayName());
                newMakeUp.getMergeInfos().add(makeUpNode);
                makeUpMap.put(key, newMakeUp);
            }
        }

        List<ProjectDetailRangeDto.MakeUp> sortedList = new ArrayList<>(makeUpMap.values());
        sortedList.sort(Comparator.comparing(ProjectDetailRangeDto.MakeUp::getTypeOrd, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(ProjectDetailRangeDto.MakeUp::getBasicInfoOrd, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(ProjectDetailRangeDto.MakeUp::getArchiveOrd, Comparator.nullsFirst(Comparator.naturalOrder())));
        return sortedList;
    }

}
