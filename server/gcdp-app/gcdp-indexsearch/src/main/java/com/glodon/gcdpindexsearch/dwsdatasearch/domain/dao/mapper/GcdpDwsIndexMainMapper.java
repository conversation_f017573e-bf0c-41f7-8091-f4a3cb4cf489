package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMainRes;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ProjectIndexData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GcdpDwsIndexMainMapper {
    List<ProjectIndexData> getIndex(@Param(value = "ids") List<String> ids);

    List<ProjectIndexData> getItemIndex(@Param(value = "ids")List<String> ids, Integer showAll);

    List<DwsIndexMainRes> selectByItemIds(@Param("ids") List<Long> itemIdList);
}