package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsIndex;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndex;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.SingleProjectDbIndex;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.SubjectIndexDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GcdpDwsIndexMapper {

    List<SubjectIndexDto> selectCostAndUsageIndexByNoteId(@Param("noteIds") List<Long> noteIds,
                                                          @Param("itemCostType") Integer itemCostType);

    List<GcdpDwsIndex> selectItemIndexByNoteId(@Param("indexTypePrefix") String indexTypePrefix,
                                               @Param("calcType") String calcType,
                                               @Param("ids") List<Long> ids,
                                               @Param("itemCostType") Integer itemCostType);

    List<SingleProjectDbIndex> selectDwsIndexByNoteId(@Param("indexTypePrefix") String indexTypePrefix,
                                                      @Param("calcType") String calcType,
                                                      @Param("ids") List<Long> ids,
                                                      @Param("itemCostType") Integer itemCostType,
                                                      @Param("onlyXNLDFlag") Boolean onlyXNLDFlag,
                                                      @Param("baseTradeName") String baseTradeName);

    List<DwsIndex> selectByItemIds(@Param("itemIdList") List<Long> itemIdList);
}