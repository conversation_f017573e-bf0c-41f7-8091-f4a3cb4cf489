package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.costData;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.domain.enums.ProductSource;
import com.glodon.gcdpindexsearch.common.constant.CommonConstants;
import com.glodon.gcdpindexsearch.common.constant.CostDataConstants;
import com.glodon.gcdpindexsearch.common.enums.IndexTypeEnums;
import com.glodon.gcdpindexsearch.common.util.EmptyUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.utils.StringToListUtils;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexListVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexMakeupVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 全成本指标库-查询数据
 * @date 2023/5/16 9:15
 */
@Service(CostDataConstants.COST_DATA_SERVICE_PREFIX + "qcbzb")
@Slf4j
public class CostDataQCBZBServiceImpl extends CostDataAbsService {
    @Autowired
    @Qualifier("queryDataServiceExecutor")
    private ThreadPoolTaskExecutor queryDataServiceExecutor;

    @Override
    public List<CostIndexBaseDto> costIndexList(CostIndexListVO costIndexListVO) throws Exception{
        costIndexListVO.setProductSource(Lists.newArrayList(ProductSource.MBCB.getIndex(),ProductSource.TZGSBZ.getIndex()));
        //获取一级业态
        getOneCategoryName(costIndexListVO);
        long time = System.currentTimeMillis();
        //根据组织权限和数据来源，查询条件筛选样本量
        List<DwsIndexProjectNote> indexProjectNotes = filterProjectNoteByCondition(costIndexListVO);
        log.info("查询样本量id耗时={}",System.currentTimeMillis()-time);
        if(CollUtil.isEmpty(indexProjectNotes)){
            return Lists.newArrayList();
        }
        List<Long> ids = indexProjectNotes.stream().map(DwsIndexProjectNote::getId).collect(Collectors.toList());
        //1.首先查询成本指标数据
        long time1 = System.currentTimeMillis();
        List<CostIndexBaseDto> costIndexList = indexDataRepository.selectCostIndexMergeByItemHash(ids);
        if(CollUtil.isEmpty(costIndexList)){
            return costIndexList;
        }
        costIndexList.stream().forEach(item->{
            CostIndexQCBZBLibDto qcbzbLibDto =  (CostIndexQCBZBLibDto)item;
            item.setIds(qcbzbLibDto.getCostIds());
        });
        log.info("查询指标耗时={}",System.currentTimeMillis()-time1);
        makePid(costIndexList);
        return costIndexList;
    }

    /**
     * 将一级业态添加到业态列表中
     * @param costIndexListVO
     */
    private void getOneCategoryName(CostIndexListVO costIndexListVO) {
        List<String> categoryNameList = costIndexListVO.getCategoryName();
        if(CollUtil.isEmpty(categoryNameList)){
            return;
        }
        List<String> oneCategoryList = new ArrayList<>();
        categoryNameList.stream().forEach(item -> {
            String[] categories = item.split(CommonConstants.SPLIT_FLAG);
            if(categories.length>1&&!oneCategoryList.contains(categories[0])){
                oneCategoryList.add(categories[0]);
            }
        });
        categoryNameList.addAll(oneCategoryList);
    }
    /**
     * @Description: 建面单方
     * @param costIndexMakeupVO
     * @return java.util.List<T>
     * @Author: zhangj-cl
     * @Date: 2023/5/18 16:38
     */
    @SuppressWarnings("all")
    protected List<CostIndexMakeupBaseDto> getCostIndex(CostIndexMakeupVO costIndexMakeupVO) {
        List<Long> itemIds = StringToListUtils.convertToLongList(costIndexMakeupVO.getItemIds());
        if (EmptyUtil.isEmpty(itemIds)) {
            return Collections.emptyList();
        }

        List<? extends CostIndexMakeupBaseDto> indexDtos = null;
        IndexTypeEnums indexTypeEnum = IndexTypeEnums.fromIndex(costIndexMakeupVO.getIndexType());

        switch (indexTypeEnum) {
            // 建面单方
            case JMDF: {
                List<CostIndexMakeupQCBZBLibJMDFDto> jmdfDtos = dwsCostDataRepository.selectQCBZBLibJmdfCostIndexByIds(itemIds);
                indexDtos = jmdfDtos.stream().filter(dto -> isValidValue(dto.getJmIndexValue(), dto.getJmIndexValueIncludeTax())).collect(Collectors.toList());
                break;
            }
            // 单方含量
            case DFHL: {
                List<CostIndexMakeupQCBZBLibDFHLDto> dfhlDtos = dwsCostDataRepository.selectQCBZBLibDfhlCostIndexByIds(itemIds);
                indexDtos = dfhlDtos.stream().filter(dto -> isValidValue(dto.getZylIndexValue(), null)).collect(Collectors.toList());
                break;
            }
            // 单方造价
            case DFZJ: {
                List<CostIndexMakeupQCBZBLibDFZJDto> dfzjDtos = dwsCostDataRepository.selectQCBZBlibDfzjCostIndexByIds(itemIds);
                indexDtos = dfzjDtos.stream().filter(dto -> isValidValue(dto.getDfIndexValue(), dto.getDfIndexValueIncludeTax())).collect(Collectors.toList());
                break;
            }
            // 综合单价
            case ZHDJ: {
                List<CostIndexMakeupQCBZBLibZHDJDto> zhdjDtos = dwsCostDataRepository.selectQCBZBlibZhdjCostIndexByIds(itemIds);
                indexDtos = zhdjDtos.stream().filter(dto -> isValidValue(dto.getSwlIndexValue(), dto.getSwlIndexValueIncludeTax())).collect(Collectors.toList());
                break;
            }
            default:
                return new ArrayList<>();
        }
        return (List<CostIndexMakeupBaseDto>) indexDtos;
    }

    /**
     * @Description: 是否为有效数据
     * @param indexValue
     * @return boolean
     * @Author: zhangj-cl
     * @Date: 2023/5/23 20:48
     */
    private boolean isValidValue(BigDecimal indexValue, BigDecimal indexValueIncludeTax) {
        boolean indexValueValid = indexValue != null && indexValue.compareTo(BigDecimal.ZERO) > 0;
        boolean indexValueIncludeTaxValid = indexValueIncludeTax != null && indexValueIncludeTax.compareTo(BigDecimal.ZERO) > 0;

        return indexValueValid || indexValueIncludeTaxValid;
    }

}
