package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ItemIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.ItemIndexQueryReqVO;

/**
 * @author: luoml-b
 * @date: 2023/11/17 10:36
 * @description: 科目维度查询service
 */
public interface ItemIndexDataService {


    ItemIndexData getItemIndexData(ItemIndexQueryReqVO indexQueryReqVO) throws Exception;

    ItemIndexData getItemIndexDataDetails(ItemIndexQueryReqVO itemIndexQueryReqVO) throws Exception;
}
