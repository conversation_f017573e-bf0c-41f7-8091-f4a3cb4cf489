package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 建造做法指标库-单方造价-指标区间组成-Dto
 * @date 2023-05-16 15:01
 */
@Data
@SuppressWarnings("squid:S1068") // 忽略sonar规则： Unused "private" fields should be removed
public class CostIndexMakeupJZZFZBLibDFZJDto extends CostIndexMakeupBaseDto {
    /**
     * 标准名称
     */
    private String standardName;
    /**
     * 单方造价（不含税）
     */
    private BigDecimal dfIndexValue;
    /**
     * 单方造价（含税）
     */
    private BigDecimal dfIndexValueIncludeTax;
    /**
     * 指标值单位
     */
    private String indexUnit;
    /**
     * 造价金额（不含税）
     */
    private BigDecimal amount;
    /**
     * 造价金额（含税）
     */
    private BigDecimal amountIncludeTax;
    /**
     * 计算口径名称
     */
    private String calculateName;
    /**
     * 计算口径值
     */
    private BigDecimal calculateValue;
}
