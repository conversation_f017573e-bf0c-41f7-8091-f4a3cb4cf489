package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsIndex;
import com.glodon.gcdpindexsearch.common.enums.FilterTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMainRes;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MainResDjTaxMakeupIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMainMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.MakeupIndexDetailService;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service("makeupIndexDetailServiceZYGLDJZB_TAX")
public class MainResDjTaxMakeupIndexDetailServiceImpl implements MakeupIndexDetailService {
    @Autowired
    private GcdpDwsIndexMainMapper dwsIndexMainMapper;

    @Override
    public List<MakeUpIndexData> listMakeupIndexDetail(List<MakeUpIndexData> indexDataList, String itemIds, FilterTypeEnums filterType) {
        if (CollUtil.isEmpty(indexDataList)) {
            return Lists.newArrayList();
        }
        //查询科目信息
        List<DwsIndexMainRes> mainRes = assembleIndexes(itemIds);
        if (CollUtil.isEmpty(mainRes)) {
            return Lists.newArrayList();
        }
        return getIndexData(indexDataList, mainRes);
    }

    /**
     * 查询指标数据
     * @param itemIds
     * @return
     */
    List<DwsIndexMainRes> assembleIndexes(String itemIds){
        List<Long> itemIdList = Arrays.stream(itemIds.split(Constant.COMMA)).map(Long::new).collect(Collectors.toList());
        List<DwsIndexMainRes> mainRes = dwsIndexMainMapper.selectByItemIds(itemIdList);
        if (CollUtil.isEmpty(mainRes)){
            return Lists.newArrayList();
        }

        return mainRes.stream()
                .filter(x -> (x.getDjIndexValueIncludeTax() != null && x.getDjIndexValueIncludeTax().compareTo(BigDecimal.ZERO) != 0)).collect(Collectors.toList());
    }

    private List<MakeUpIndexData> getIndexData(List<MakeUpIndexData> indexDataList, List<DwsIndexMainRes> mainRes) {
        List<MakeUpIndexData> data = new ArrayList<>();
        Map<String, List<DwsIndexMainRes>> calculateValueMap = mainRes.stream().collect(Collectors.groupingBy(item -> item.getSubjectLdMergeHash()));
        for (Map.Entry<String,List<DwsIndexMainRes>> entry : calculateValueMap.entrySet()){
            List<DwsIndexMainRes> dwsIndexMainReses = entry.getValue();
            DwsIndexMainRes dwsIndexMainRes = dwsIndexMainReses.get(0);
            Optional<MakeUpIndexData> optional = indexDataList.stream().filter(item->item.getIds().contains(String.valueOf(dwsIndexMainRes.getIndexProjectNoteId()))).findFirst();
            if(!optional.isPresent()){
                continue;
            }
            String ids = dwsIndexMainReses.stream().map(item-> String.valueOf(item.getIndexProjectNoteId())).collect(Collectors.joining(","));
            MakeUpIndexData makeUpIndexData = optional.get();
            MainResDjTaxMakeupIndexData djTaxMakeupIndexData = new MainResDjTaxMakeupIndexData();
            BeanUtils.copyProperties(makeUpIndexData, djTaxMakeupIndexData);
            djTaxMakeupIndexData.setIds(ids);
            setIndexDataField(djTaxMakeupIndexData, dwsIndexMainReses);
            data.add(djTaxMakeupIndexData);
        }
        return data;
    }

    private void setIndexDataField(MainResDjTaxMakeupIndexData djTaxMakeupIndexData, List<DwsIndexMainRes> dwsIndexMainRes) {
        DwsIndexMainRes indexMainRes = dwsIndexMainRes.get(0);
        String name = indexMainRes.getName();
        BigDecimal djCalculateValue = dwsIndexMainRes.stream().map(DwsIndexMainRes::getDjCalculateValue).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(6, RoundingMode.HALF_DOWN);
        BigDecimal taxMarketAmount = dwsIndexMainRes.stream().filter(x -> x.getDjCalculateValue() != null && x.getDjIndexValueIncludeTax() != null)
                .map(x -> x.getDjCalculateValue().multiply(x.getDjIndexValueIncludeTax())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(6, RoundingMode.HALF_DOWN);

        djTaxMakeupIndexData.setName(name);
        djTaxMakeupIndexData.setUnit(indexMainRes.getDjIndexUnit());
        djTaxMakeupIndexData.setQuantity(djCalculateValue == null ? Constant.LINE : djCalculateValue.toString());
        djTaxMakeupIndexData.setTaxMarketAmount(taxMarketAmount == null ? Constant.LINE : taxMarketAmount.toString());
        if (taxMarketAmount != null && djCalculateValue != null && BigDecimal.ZERO.compareTo(djCalculateValue) != 0) {
            djTaxMakeupIndexData.setDjTaxIndexValue(taxMarketAmount.divide(djCalculateValue, 6, RoundingMode.HALF_DOWN).toString());
        } else {
            djTaxMakeupIndexData.setDjTaxIndexValue(Constant.LINE);
        }
        String unit = StringUtils.isNotBlank(indexMainRes.getUnit()) ? indexMainRes.getUnit() : Constant.LINE;
        djTaxMakeupIndexData.setUnit(Constant.YUAN + Constant.Slash + unit);
    }

}
