package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import com.alibaba.fastjson.JSON;
import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsIndex;
import com.glodon.gcdpindexsearch.common.enums.FilterTypeEnums;
import com.glodon.gcdpindexsearch.common.util.EmptyUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.StandardDescription;
import org.springframework.beans.BeanUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 指标组成实现接口类
 * <AUTHOR>
 * @date 2022-11-23
 */
public interface MakeupIndexDetailService {

    List<MakeUpIndexData> listMakeupIndexDetail(List<MakeUpIndexData> indexDataList , String itemIds, FilterTypeEnums filterType);

    default MakeUpIndexData setSharedFields(List<MakeUpIndexData> indexDataList, Map.Entry<String, List<DwsIndex>> entry) {
        List<DwsIndex> dwsIndexList = entry.getValue();
        DwsIndex dwsIndex = dwsIndexList.get(0);
        Optional<MakeUpIndexData> optional = indexDataList.stream().filter(item -> item.getIds().contains(String.valueOf(dwsIndex.getIndexProjectNoteId()))).findFirst();
        if (!optional.isPresent()) {
            return null;
        }
        MakeUpIndexData makeUpIndexData = new MakeUpIndexData();
        BeanUtils.copyProperties(optional.get(), makeUpIndexData);

        // 有楼栋名称，则显示单项名称/无单项名称（末级业态数据）则显示文件名称
        String noteOrFileName = EmptyUtil.isEmpty(makeUpIndexData.getLdNameIdentify())? makeUpIndexData.getFileName() : makeUpIndexData.getNoteName();
        List<String> noteOrFileNames = Arrays.stream(noteOrFileName.split(StrPool.COMMA)).distinct().collect(Collectors.toList());
        makeUpIndexData.setNoteName(CharSequenceUtil.join("、", noteOrFileNames));
        // 设置ids
        String ids = dwsIndexList.stream().map(item -> String.valueOf(item.getIndexProjectNoteId())).collect(Collectors.joining(StrPool.COMMA));
        makeUpIndexData.setIds(ids);
        // 设置建造做法
        List<StandardDescription> standardDescriptions = JSON.parseArray(dwsIndex.getStandardDescription(), StandardDescription.class);
        makeUpIndexData.setStandardDescription(standardDescriptions);

        return makeUpIndexData;
    }
}
