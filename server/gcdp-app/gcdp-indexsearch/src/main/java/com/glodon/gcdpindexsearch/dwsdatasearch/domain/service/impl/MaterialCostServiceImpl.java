package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdp.common.utils.HashUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.CostDetailDTO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MakeupDetailDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MaterialDTO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexMakeupRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.MaterialCostService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostDetailVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.MaterialVO;
import com.glodon.gcdpindexsearch.infrastructure.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program ebq
 * @create 2023/8/17 10:03
 */

@Service
@Slf4j
public class MaterialCostServiceImpl implements MaterialCostService {

    @Autowired
    IDwsIndexMakeupRepository dwsIndexMakeupRepositoryImpl;

    private static final String LMM_BASE_PID = "-1";

    @Override
    public JSONObject bqItemMaterialList(String id) {
        JSONObject result = new JSONObject();
        if (StringUtils.isBlank(id)) {
            return result;
        }
        try {
            Long bqItemId = Long.parseLong(id);
            // 查询清单组价
            List<CostDetailVO> costDetail = getCostDetail(bqItemId);

            // 查询人材机组成
            List<MaterialDTO> materialDTOS = dwsIndexMakeupRepositoryImpl.selectMaterialByBqItemId(bqItemId);
            if (CollectionUtils.isEmpty(materialDTOS)) { // 为空查定额
                materialDTOS = getNormItemMaterials(bqItemId);
            }

            List<MaterialVO> materials = convertMaterials(materialDTOS);

            result.put("costDetail", costDetail);
            result.put("materialDetail", materials);
        } catch (Exception e) {
            log.error("bqItemMaterialList查询清单人材机组价失败,清单id={}", id, e);
            throw new BusinessException("查询清单人材机组价失败", e);
        }
        return result;
    }

    private List<MaterialDTO> getNormItemMaterials(Long bqItemId) {
        List<MaterialDTO> result = new ArrayList<>();

        // 查找清单下的所有定额id
        List<Long> normItemIds = dwsIndexMakeupRepositoryImpl.selectNormItemIdsByBqItemId(bqItemId);
        if (CollectionUtils.isEmpty(normItemIds)) {
            return result;
        }
        // 查询定额下人材机
        List<MaterialDTO> materialDTOS = dwsIndexMakeupRepositoryImpl.selectMaterialByNormItemId(normItemIds);
        if (CollectionUtils.isEmpty(materialDTOS)) {
            return result;
        }
        // 查询清单工程量
        BigDecimal bqItemQuality = null;
        List<MakeupDetailDto> makeupDetailList = dwsIndexMakeupRepositoryImpl.selectBqitemById(Collections.singletonList(bqItemId));
        if (!CollectionUtils.isEmpty(makeupDetailList)) {
            bqItemQuality = makeupDetailList.get(0).getQuantity();
        }

        // 父id默认-1
        materialDTOS.forEach(item -> item.setPid(item.getPid() == null ? LMM_BASE_PID : item.getPid()));
        treeMaterialDTO(materialDTOS); // 树化
        dgSetMergeId(materialDTOS, LMM_BASE_PID); // 递归设置合并id
        tilingMaterialDTO(materialDTOS); // 铺平
        // 计算合并含量
        result = calculateMergeUsage(materialDTOS, bqItemQuality);
        // 合并后排序
        sortMaterialDTO(result);
        return result;
    }

    /***
     * @Description: 计算含量
     * @date 2024/1/17
     * @return
     */
    private List<MaterialDTO> calculateMergeUsage(List<MaterialDTO> materialDTOS, BigDecimal bqItemQuality) {
        List<MaterialDTO> result = new ArrayList<>();
        Map<String, List<MaterialDTO>> hashMaterialMap = materialDTOS.stream().collect(Collectors.groupingBy(MaterialDTO::getId));

        for (Map.Entry<String, List<MaterialDTO>> entry : hashMaterialMap.entrySet()) {
            List<MaterialDTO> materialList = entry.getValue();
            MaterialDTO materialDTO = new MaterialDTO();
            BeanUtils.copyProperties(materialList.get(0), materialDTO);
            BigDecimal usageSum = materialList.stream().map(MaterialDTO::getUsage).filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal quantitySum = materialList.stream().map(MaterialDTO::getQuantity).filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            materialDTO.setQuantity(quantitySum);
            // 工料机含量算法修改: 含量=∑数量/清单工程量
            materialDTO.setUsage(bqItemQuality != null && bqItemQuality.compareTo(BigDecimal.ZERO) != 0 ?
                    quantitySum.divide(bqItemQuality, 6, RoundingMode.HALF_UP) : usageSum);
            result.add(materialDTO);
        }

        return result;
    }

    /***
     * @Description: 组织人材机
     * @date 2020/6/18
     * @return
     */
    private void formatMaterialDTO(List<MaterialDTO> list) {
        // 父子级紧挨着处理
        treeMaterialDTO(list); // 树化
        tilingMaterialDTO(list); // 铺平
    }

    /***
     * @Description: 树化人材机
     * @date 2020/6/18
     * @return
     */
    private void treeMaterialDTO(List<MaterialDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, List<MaterialDTO>> treeMap = list.stream().filter(x -> !LMM_BASE_PID.equals(x.getPid()))
                .collect(Collectors.groupingBy(MaterialDTO::getPid));
        list.forEach(x -> x.setChildren(treeMap.get(x.getId())));
        List<MaterialDTO> tmpList = list.stream().filter(x -> LMM_BASE_PID.equals(x.getPid())).collect(Collectors.toList());
        list.clear();
        list.addAll(tmpList);
    }

    /***
     * @Description: 铺平人材机
     * @date 2024/1/17
     * @return
     */
    private void tilingMaterialDTO(List<MaterialDTO> list) {
        List<MaterialDTO> tileDTOS = new ArrayList<>();
        dgAddMaterialDTO(list, tileDTOS);
        tileDTOS.forEach(item -> item.setChildren(null)); // 置空children
        list.clear();
        list.addAll(tileDTOS);
    }

    /***
     * @Description: 递归添加子节点
     * @date 2024/1/17
     * @return
     */
    private void dgAddMaterialDTO(List<MaterialDTO> list, List<MaterialDTO> result) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (MaterialDTO item : list) {
            result.add(item);
            dgAddMaterialDTO(item.getChildren(), result);
        }
    }

    /***
     * @Description: 递归设置合并id
     * @date 2024/1/17
     * @return
     */
    private void dgSetMergeId(List<MaterialDTO> list, String pid) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (MaterialDTO item : list) {
            item.setPid(pid);
            // 定额含量合并，规则：名称+编码+单价相同
            item.setId(HashUtil.hash64WithFnv1a(item.getPid() + "_" + item.getCode() + item.getName() +
                    item.getMarketRate() + item.getMarketTaxRate()));

            dgSetMergeId(item.getChildren(), item.getId());
        }
    }

    /***
     * @Description: 人材机排序
     * @date 2024/1/17
     * @return
     */
    private void sortMaterialDTO(List<MaterialDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.sort((item1, item2) -> {
            // 先按工料机类别排序
            int type1 = item1.getType() == null || item1.getType() == 0 ? 1000 : item1.getType();
            int type2 = item2.getType() == null || item2.getType() == 0 ? 1000 : item2.getType();
            if (type1 != type2) {
                return type1 - type2;
            }

            // 再按编码排序
            String code1 = item1.getCode() == null ? "" : item1.getCode();
            String code2 = item2.getCode() == null ? "" : item2.getCode();
            return code1.compareTo(code2);
        });
    }

    private List<CostDetailVO> getCostDetail(Long id) {
        List<CostDetailVO> costDetail = new ArrayList<>();
        List<CostDetailDTO> costDetails = dwsIndexMakeupRepositoryImpl.selectCostDetailByBqItemId(id);
        if (CollectionUtils.isEmpty(costDetails)) {
            log.info("bqItemMaterialList.getCostDetail查询清单{}单价组成为空", id);
            return costDetail;
        }
        initCostDetail(costDetail, costDetails.get(0));
        // 按code排序插入
        costDetails.sort(Comparator.comparing(CostDetailDTO::getCostIdentity));
        Collections.reverse(costDetails);
        costDetails.forEach(detail -> costDetail.add(2,
                new CostDetailVO(detail.getCostIdentity(), detail.getCostName(), detail.getRate().stripTrailingZeros().toPlainString(),
                        detail.getBaseAmount(), detail.getBaseAmountRemark())));

        return costDetail;
    }

    /**
     * @Description 转换人材机数据
     * @param materialDTOS
     * @date 2023/8/18
     **/
    @Override
    public List<MaterialVO> convertMaterials(List<MaterialDTO> materialDTOS) {
        List<MaterialVO> materials = new ArrayList<>();
        if (!CollectionUtils.isEmpty(materialDTOS)) {
            // 父子级紧挨着处理
            formatMaterialDTO(materialDTOS);
            // 组装返回结构
            materialDTOS.forEach(materialDTO -> {
                MaterialVO materialVO = new MaterialVO();
                BeanUtils.copyProperties(materialDTO, materialVO);
                BigDecimal marketRate = materialDTO.getMarketRate();
                BigDecimal marketTaxRate = materialDTO.getMarketTaxRate();
                materialVO.setRate(marketRate != null && marketRate.compareTo(BigDecimal.ZERO) != 0 ? marketRate : marketTaxRate);
                materialVO.setType(materialDTO.getType());
                materialVO.setSupplyType(materialDTO.getSupplyType());
                materials.add(materialVO);
            });
        }
        // 过滤无效数据，规则:材料名称、单位、含税价/除税价均不为空
        return materials.stream().filter(material ->
                        StringUtils.isNotBlank(material.getName()) &&
                                StringUtils.isNotBlank(material.getUnit()) &&
                                material.getRate() != null && material.getRate().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());
    }

    @SuppressWarnings("squid:S1192")
    private void initCostDetail(List<CostDetailVO> costDetail, CostDetailDTO detail) {
        costDetail.add(new CostDetailVO("code", "编码", detail.getItemCode(), "", ""));
        costDetail.add(new CostDetailVO("name", "清单名称", detail.getItemName(), "", ""));
        costDetail.add(new CostDetailVO("remark", "备注", detail.getRemark(), "", ""));
    }

    @Override
    public JSONObject normItemMaterialList(String id) {
        JSONObject result = new JSONObject();
        if (StringUtils.isBlank(id)) {
            return result;
        }
        Long normItemId = Long.parseLong(id);
        try {
            // 定额组价

            // 定额人材机
            List<Long> ids = Collections.singletonList(normItemId);
            List<MaterialDTO> materialDTOS = dwsIndexMakeupRepositoryImpl.selectMaterialByNormItemId(ids);
            List<MaterialVO> materials = convertMaterials(materialDTOS);
            result.put("costDetail", null);
            result.put("materialDetail", materials);
        } catch (Exception e) {
            log.error("bqItemMaterialList查询定额人材机组价失败,清单id={}", id);
            throw new BusinessException("查询定额人材机组价失败", e);
        }
        return result;
    }
}
