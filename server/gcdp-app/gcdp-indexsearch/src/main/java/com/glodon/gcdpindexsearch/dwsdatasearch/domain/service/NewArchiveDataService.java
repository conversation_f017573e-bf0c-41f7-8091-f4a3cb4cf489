package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import com.glodon.gcdp.common.domain.model.StandardBuilderDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsNewArchiveData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ProjectPlanDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.BuildStandardVO;

import java.util.List;

/**
 * 设计指标服务接口类
 *
 * <AUTHOR>
 * @date 2022-11-1
 */
public interface NewArchiveDataService {
    /**
     * 查询设计指标数据
     *
     * @param enterpriseId
     * @param projectCode
     * @return
     */
    List<ProjectPlanDto> getProjectPlan(String enterpriseId, String projectCode, String phase);

    /**
     * 根据projectCode查询建造标准数据
     *
     * @param projectCode
     * @return
     */
    DwsNewArchiveData getDwsNewArchiveDataByProjectCode(String enterpriseId, String projectCode);

    /**
     * 查询建造标准数据
     *
     * @param buildStandardVO
     * @return
     */
    StandardBuilderDto getBuildStandard(BuildStandardVO buildStandardVO);
}
