package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsIndex;
import com.glodon.gcdpindexsearch.common.enums.FilterTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexEconomics;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.UsageZylMakeupIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.MakeupIndexDetailService;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("DuplicatedCode")
@Service("makeupIndexDetailServiceZYLZB")
public class UsageZylMakeupIndexDetailServiceImpl implements MakeupIndexDetailService {

    @Autowired
    private GcdpDwsIndexMapper gcdpDwsIndexMapper;

    @Override
    public List<MakeUpIndexData> listMakeupIndexDetail(List<MakeUpIndexData> indexDataList, String itemIds, FilterTypeEnums filterType) {
        if (CollUtil.isEmpty(indexDataList)) {
            return Lists.newArrayList();
        }
        //查询科目信息
        List<DwsIndex> dwsIndices = assembleIndexes(itemIds);
        if (CollUtil.isEmpty(dwsIndices)) {
            return Lists.newArrayList();
        }
        return getIndexData(indexDataList, dwsIndices);
    }

    /**
     * 查询指标数据
     * @param itemIds
     */
    List<DwsIndex> assembleIndexes(String itemIds){
        List<Long> itemIdList = Arrays.stream(itemIds.split(Constant.COMMA)).map(Long::new).collect(Collectors.toList());
        List<DwsIndex> dwsIndices = gcdpDwsIndexMapper.selectByItemIds(itemIdList);
        if (CollUtil.isEmpty(dwsIndices)){
            return Lists.newArrayList();
        }

        return dwsIndices.stream()
                .filter(x -> x.getZylIndexValue() != null &&
                        x.getZylIndexValue().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
    }

    private List<MakeUpIndexData> getIndexData(List<MakeUpIndexData> indexDataList, List<DwsIndex> dwsIndices) {
        List<MakeUpIndexData> data = new ArrayList<>();
        Map<String, List<DwsIndex>> calculateValueMap = dwsIndices.stream().collect(Collectors.groupingBy(DwsIndex::getZylIndexWithCalcMergeHash));
        for (Map.Entry<String, List<DwsIndex>> entry : calculateValueMap.entrySet()) {
            List<DwsIndex> dwsIndexList = entry.getValue();
            UsageZylMakeupIndexData usageZylMakeupIndexData = new UsageZylMakeupIndexData();
            MakeUpIndexData makeUpIndexData = this.setSharedFields(indexDataList, entry);
            if (makeUpIndexData == null) {
                continue;
            }
            BeanUtils.copyProperties(makeUpIndexData, usageZylMakeupIndexData);
            setIndexDataField(usageZylMakeupIndexData, dwsIndexList);
            data.add(usageZylMakeupIndexData);
        }
        return data;
    }

    private void setIndexDataField(UsageZylMakeupIndexData usageZylMakeupIndexData, List<DwsIndex> dwsIndexList) {
        DwsIndex dwsIndex = dwsIndexList.get(0);
        String name = dwsIndex.getName();
        BigDecimal zylCalculateValue = dwsIndexList.stream().filter(item -> MathUtil.notNullAndNotZero(item.getZylIndexValue()))
                .sorted(Comparator.comparing(DwsIndex::getArchiveDate).reversed())
                .map(DwsIndex::getZylCalculateValue).findFirst().orElse(null);
        String calculateName = dwsIndex.getZylCalculateName();
        BigDecimal quantity = dwsIndexList.stream().filter(x -> x.getZylIndexValue() != null && x.getZylCalculateValue() != null)
                .map(x -> MathUtil.isGreaterZero(x.getQuantity()) ? x.getQuantity() : x.getZylIndexValue().multiply(x.getZylCalculateValue())).reduce(BigDecimal.ZERO, BigDecimal::add);
        usageZylMakeupIndexData.setName(name);
        usageZylMakeupIndexData.setUnit(dwsIndex.getZylIndexUnit());
        usageZylMakeupIndexData.setQuantity(quantity.toString());
        if (zylCalculateValue != null && BigDecimal.ZERO.compareTo(zylCalculateValue) != 0) {
            usageZylMakeupIndexData.setZylIndexValue(quantity.divide(zylCalculateValue, 6, RoundingMode.HALF_DOWN).toString());
        } else {
            usageZylMakeupIndexData.setZylIndexValue(Constant.LINE);
        }
        usageZylMakeupIndexData.setCaliberValue(zylCalculateValue == null ? Constant.LINE : zylCalculateValue.toString());
        usageZylMakeupIndexData.setCaliber(calculateName);
    }

}
