package com.glodon.gcdpindexsearch;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = {"com.glodon.*"}, exclude = {MongoAutoConfiguration.class})
@MapperScan({"com.glodon.gcdpindexsearch.*.domain.dao.mapper",
              "com.glodon.gcdp.common.*.mapper",
               "com.glodon.gcdp.*.domain.dao.mapper"
             })
@Slf4j
@EnableFeignClients
@EnableScheduling
public class GcdpIndexSearchApplication {

    public static void main(String[] args) {
        log.info("项目启动了");
        SpringApplication.run(GcdpIndexSearchApplication.class, args);
    }
}
