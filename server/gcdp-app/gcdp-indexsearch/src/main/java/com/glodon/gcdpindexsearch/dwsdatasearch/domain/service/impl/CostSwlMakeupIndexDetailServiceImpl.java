package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsIndex;
import com.glodon.gcdpindexsearch.common.enums.FilterTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.CostSwlMakeupIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.MakeupIndexDetailService;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("DuplicatedCode")
@Service("makeupIndexDetailServiceSWLDF")
public class CostSwlMakeupIndexDetailServiceImpl implements MakeupIndexDetailService {
    @Autowired
    private GcdpDwsIndexMapper gcdpDwsIndexMapper;

    @Override
    public List<MakeUpIndexData> listMakeupIndexDetail(List<MakeUpIndexData> indexDataList, String itemIds, FilterTypeEnums filterType) {
        if (CollUtil.isEmpty(indexDataList)) {
            return Lists.newArrayList();
        }
        //查询科目信息
        List<DwsIndex> dwsIndices = assembleIndexes(itemIds, filterType);
        if (CollUtil.isEmpty(dwsIndices)) {
            return Lists.newArrayList();
        }
        return getIndexData(indexDataList, dwsIndices);
    }

    /**
     * 查询科目数据
     * @param itemIds
     * @param filterType
     * @return
     */
    List<DwsIndex> assembleIndexes(String itemIds, FilterTypeEnums filterType){
        List<Long> itemIdList = Arrays.stream(itemIds.split(Constant.COMMA)).map(Long::new).collect(Collectors.toList());
        List<DwsIndex> dwsIndices = gcdpDwsIndexMapper.selectByItemIds(itemIdList);
        if (CollUtil.isEmpty(dwsIndices)){
            return Lists.newArrayList();
        }

        switch (filterType){
            case NO_TAX_VALID:
                return dwsIndices.stream()
                        .filter(x -> (x.getSwlIndexValue() != null && x.getSwlIndexValue().compareTo(BigDecimal.ZERO) != 0)).collect(Collectors.toList());
            case INCLUDE_TAX_VALID:
                return dwsIndices.stream()
                        .filter(x -> (x.getSwlIndexValueIncludeTax() != null && x.getSwlIndexValueIncludeTax().compareTo(BigDecimal.ZERO) != 0)).collect(Collectors.toList());
            default:
                return dwsIndices.stream()
                        .filter(x -> (x.getSwlIndexValueIncludeTax() != null && x.getSwlIndexValueIncludeTax().compareTo(BigDecimal.ZERO) != 0)
                                || (x.getSwlIndexValue() != null && x.getSwlIndexValue().compareTo(BigDecimal.ZERO) != 0)).collect(Collectors.toList());
        }
    }

    private List<MakeUpIndexData> getIndexData(List<MakeUpIndexData> indexDataList, List<DwsIndex> dwsIndices) {
        List<MakeUpIndexData> data = new ArrayList<>();
        Map<String, List<DwsIndex>> calculateValueMap = dwsIndices.stream().collect(Collectors.groupingBy(DwsIndex::getSwlIndexWithCalcMergeHash));
        for (Map.Entry<String, List<DwsIndex>> entry : calculateValueMap.entrySet()) {
            List<DwsIndex> dwsIndexList = entry.getValue();
            CostSwlMakeupIndexData costSwlMakeupIndexData = new CostSwlMakeupIndexData();
            MakeUpIndexData makeUpIndexData = this.setSharedFields(indexDataList, entry);
            if (makeUpIndexData == null) {
                continue;
            }
            BeanUtils.copyProperties(makeUpIndexData, costSwlMakeupIndexData);
            setIndexDataField(costSwlMakeupIndexData, dwsIndexList);
            data.add(costSwlMakeupIndexData);
        }
        return data;
    }

    private void setIndexDataField(CostSwlMakeupIndexData costSwlMakeupIndexData, List<DwsIndex> dwsIndices) {
        DwsIndex dwsIndex = dwsIndices.get(0);
        costSwlMakeupIndexData.setName(dwsIndex.getName());
        costSwlMakeupIndexData.setUnit(dwsIndex.getSwlIndexUnit());
        //计算口径取和
        setValue(costSwlMakeupIndexData, dwsIndices, dwsIndices.stream().map(DwsIndex::getSwlCalculateValue).reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    private void setValue(CostSwlMakeupIndexData costSwlMakeupIndexData, List<DwsIndex> dwsIndices, BigDecimal swlCalculateValue) {
        BigDecimal amount = dwsIndices.stream().filter(x -> x.getSwlCalculateValue() != null && x.getSwlIndexValue() != null)
                .map(item -> MathUtil.isGreaterZero(item.getAmount()) ? item.getAmount() : item.getSwlIndexValue().multiply(item.getSwlCalculateValue()).setScale(6, RoundingMode.HALF_DOWN))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal amountIncludeTax = dwsIndices.stream().filter(x -> x.getSwlCalculateValue() != null && x.getSwlIndexValueIncludeTax() != null)
                .map(item -> MathUtil.isGreaterZero(item.getAmountIncludeTax()) ? item.getAmountIncludeTax() : item.getSwlIndexValueIncludeTax().multiply(item.getSwlCalculateValue()).setScale(6, RoundingMode.HALF_DOWN))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        costSwlMakeupIndexData.setAmount(amount.toString());
        costSwlMakeupIndexData.setAmountIncludeTax(amountIncludeTax.toString());

        if (swlCalculateValue == null) {
            costSwlMakeupIndexData.setQuantity(Constant.LINE);
            costSwlMakeupIndexData.setSwlIndexValue(Constant.LINE);
        } else if (BigDecimal.ZERO.compareTo(swlCalculateValue) == 0) {
            costSwlMakeupIndexData.setSwlIndexValue(Constant.LINE);
            costSwlMakeupIndexData.setQuantity(swlCalculateValue.toString());
        } else {
            costSwlMakeupIndexData.setSwlIndexValue(amount.divide(swlCalculateValue, 6, RoundingMode.HALF_DOWN).toString());
            costSwlMakeupIndexData.setSwlIndexValueIncludeTax(amountIncludeTax.divide(swlCalculateValue, 6, RoundingMode.HALF_DOWN).toString());
            costSwlMakeupIndexData.setQuantity(swlCalculateValue.toString());
        }
    }

}
