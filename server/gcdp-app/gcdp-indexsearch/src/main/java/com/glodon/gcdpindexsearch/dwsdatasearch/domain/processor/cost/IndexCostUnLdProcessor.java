package com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.cost;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dimservice.domain.dao.enums.IndexCategoryDictEnum;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexCost;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.AbstractProcessor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @packageName: com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.indexcost
 * @className: IndexCostUnLdProcessor
 * @author: yany<PERSON><PERSON> <EMAIL>
 * @date: 2023/4/18 14:05
 * @description: 非楼栋维度处理器
 */
@SuppressWarnings("all")
@Slf4j
public class IndexCostUnLdProcessor extends AbstractProcessor<GcdpDwsItemIndexCost> {

    @Override
    public List<GcdpDwsItemIndexCost> process(List<GcdpDwsItemIndexCost> indexCosts) {
        StopWatch stopwatch = new StopWatch();
        stopwatch.start();
        List<GcdpDwsItemIndexCost> returnResult = Collections.synchronizedList(new LinkedList<>());
        Map<String, List<GcdpDwsItemIndexCost>> hashCostMap = indexCosts.parallelStream().collect(Collectors.groupingBy(GcdpDwsItemIndexCost::getSubjectUnLdMergeHash));
        log.info("非楼栋hashCostMap数量:[{}]", hashCostMap.size());
        hashCostMap.entrySet().parallelStream().forEach(node -> {
            List<GcdpDwsItemIndexCost> costs = node.getValue();
            GcdpDwsItemIndexCost source = CollUtil.getFirst(costs);
            GcdpDwsItemIndexCost dest = new GcdpDwsItemIndexCost();
            BeanUtil.copyProperties(source, dest);
            combineReturnField(costs, dest);
            returnResult.add(dest);
        });
        stopwatch.stop();
        log.info("IndexCostUnLdProcessor合并处理耗时:[{}]秒", stopwatch.getLastTaskInfo().getTimeSeconds());
        return processNext(returnResult);
    }


    private void combineReturnField(List<GcdpDwsItemIndexCost> costs, GcdpDwsItemIndexCost dest) {
        List<String> ids = new ArrayList<>(costs.size());
        List<String> swlSampleIds = new ArrayList<>(costs.size());
        List<String> jmSampleIds = new ArrayList<>(costs.size());
        List<String> dfSampleIds = new ArrayList<>(costs.size());
        String jmUnit = StrUtil.EMPTY;
        String swlUnit = StrUtil.EMPTY;
        String dfUnit = StrUtil.EMPTY;
        int jmCount = 0;
        int swlCount = 0;
        int dfCount = 0;
        BigDecimal jmSum = BigDecimal.ZERO;
        BigDecimal jmSumIncludeTax = BigDecimal.ZERO;
        BigDecimal swlSum = BigDecimal.ZERO;
        BigDecimal swlSumIncludeTax = BigDecimal.ZERO;
        BigDecimal dfSum = BigDecimal.ZERO;
        BigDecimal dfSumIncludeTax = BigDecimal.ZERO;
        BigDecimal jmAmount = BigDecimal.ZERO;
        BigDecimal jmAmountIncludeTax = BigDecimal.ZERO;
        BigDecimal swlAmount = BigDecimal.ZERO;
        BigDecimal swlAmountIncludeTax = BigDecimal.ZERO;
        BigDecimal dfAmount = BigDecimal.ZERO;
        BigDecimal dfAmountIncludeTax = BigDecimal.ZERO;
        BigDecimal jmSumCalculateValue = BigDecimal.ZERO;
        BigDecimal swlSumCalculateValue = BigDecimal.ZERO;
        BigDecimal dfSumCalculateValue = BigDecimal.ZERO;

        for (GcdpDwsItemIndexCost cost : costs) {
            ids.add(cost.getIds());
            swlSampleIds.add(cost.getSwlSampleIds());
            jmSampleIds.add(cost.getJmSampleIds());
            dfSampleIds.add(cost.getDfSampleIds());
            if (StrUtil.isEmpty(jmUnit) && StrUtil.isNotEmpty(cost.getJmUnit())) {
                jmUnit = cost.getJmUnit();
            }
            if (StrUtil.isEmpty(swlUnit) && StrUtil.isNotEmpty(cost.getSwlUnit())) {
                swlUnit = cost.getSwlUnit();
            }
            if (StrUtil.isEmpty(dfUnit) && StrUtil.isNotEmpty(cost.getDfUnit())) {
                dfUnit = cost.getDfUnit();
            }

            if (MathUtil.notNullAndNotZero(cost.getJmIndexValue())) {
                jmSum = NumberUtil.add(jmSum, cost.getJmIndexValue());
            }
            if (MathUtil.notNullAndNotZero(cost.getJmIndexValueIncludeTax())) {
                jmSumIncludeTax = NumberUtil.add(jmSumIncludeTax, cost.getJmIndexValueIncludeTax());
            }
            if (MathUtil.notNullAndNotZero(cost.getJmAmount())) {
                jmAmount = NumberUtil.add(jmAmount, cost.getJmAmount());
            }
            if (MathUtil.notNullAndNotZero(cost.getJmAmountIncludeTax())) {
                jmAmountIncludeTax = NumberUtil.add(jmAmountIncludeTax, cost.getJmAmountIncludeTax());
            }
            if (MathUtil.notNullAndNotZero(cost.getJmIndexValue()) || MathUtil.notNullAndNotZero(cost.getJmIndexValueIncludeTax())){
                jmCount++;
                if (MathUtil.notNullAndNotZero(cost.getJmCalculateValue())){
                    jmSumCalculateValue = NumberUtil.add(jmSumCalculateValue, cost.getJmCalculateValue());
                }
            }

            if (MathUtil.notNullAndNotZero(cost.getSwlIndexValue())) {
                swlSum = NumberUtil.add(swlSum, cost.getSwlIndexValue());
            }
            if (MathUtil.notNullAndNotZero(cost.getSwlIndexValueIncludeTax())) {
                swlSumIncludeTax = NumberUtil.add(swlSumIncludeTax, cost.getSwlIndexValueIncludeTax());
            }
            if (MathUtil.notNullAndNotZero(cost.getSwlAmount())) {
                swlAmount = NumberUtil.add(swlAmount, cost.getSwlAmount());
            }
            if (MathUtil.notNullAndNotZero(cost.getSwlAmountIncludeTax())) {
                swlAmountIncludeTax = NumberUtil.add(swlAmountIncludeTax, cost.getSwlAmountIncludeTax());
            }
            if (MathUtil.notNullAndNotZero(cost.getSwlIndexValue()) || MathUtil.notNullAndNotZero(cost.getSwlIndexValueIncludeTax())) {
                swlCount++;
                if (MathUtil.notNullAndNotZero(cost.getSwlCalculateValue())) {
                    swlSumCalculateValue = NumberUtil.add(swlSumCalculateValue, cost.getSwlCalculateValue());
                }
            }

            if (MathUtil.notNullAndNotZero(cost.getDfIndexValue())) {
                dfSum = NumberUtil.add(dfSum, cost.getDfIndexValue());
            }
            if (MathUtil.notNullAndNotZero(cost.getDfIndexValueIncludeTax())) {
                dfSumIncludeTax = NumberUtil.add(dfSumIncludeTax, cost.getDfIndexValueIncludeTax());
            }
            if (MathUtil.notNullAndNotZero(cost.getDfAmount())) {
                dfAmount = NumberUtil.add(dfAmount, cost.getDfAmount());
            }
            if (MathUtil.notNullAndNotZero(cost.getDfAmountIncludeTax())) {
                dfAmountIncludeTax = NumberUtil.add(dfAmountIncludeTax, cost.getDfAmountIncludeTax());
            }
            if (MathUtil.notNullAndNotZero(cost.getDfIndexValue()) || MathUtil.notNullAndNotZero(cost.getDfIndexValueIncludeTax())) {
                dfCount++;
                if (MathUtil.notNullAndNotZero(cost.getDfCalculateValue())) {
                    dfSumCalculateValue = NumberUtil.add(dfSumCalculateValue, cost.getDfCalculateValue());
                }
            }
        }

        setItemMaxAndMin(costs, dest);

        dest.setIds(StrUtil.join(StrUtil.COMMA, ids));
        dest.setSwlSampleIds(StrUtil.join(StrUtil.COMMA, swlSampleIds));
        dest.setJmSampleIds(StrUtil.join(StrUtil.COMMA, jmSampleIds));
        dest.setDfSampleIds(StrUtil.join(StrUtil.COMMA, dfSampleIds));

        dest.setJmUnit(jmUnit);
        dest.setSwlUnit(swlUnit);
        dest.setDfUnit(dfUnit);

        dest.setJmAvg(MathUtil.divStrZeroDashed(jmSum, new BigDecimal(jmCount)));
        dest.setJmAvgIncludeTax(MathUtil.divStrZeroDashed(jmSumIncludeTax, new BigDecimal(jmCount)));
        dest.setJmWeightedAvg(MathUtil.divStrZeroDashed(jmAmount, jmSumCalculateValue));
        dest.setJmWeightedAvgIncludeTax(MathUtil.divStrZeroDashed(jmAmountIncludeTax, jmSumCalculateValue));
        dest.setJmSampleCount(jmCount);

        dest.setSwlAvg(MathUtil.divStrZeroDashed(swlSum, new BigDecimal(swlCount)));
        dest.setSwlAvgIncludeTax(MathUtil.divStrZeroDashed(swlSumIncludeTax, new BigDecimal(swlCount)));
        dest.setSwlWeightedAvg(MathUtil.divStrZeroDashed(swlAmount, swlSumCalculateValue));
        dest.setSwlWeightedAvgIncludeTax(MathUtil.divStrZeroDashed(swlAmountIncludeTax, swlSumCalculateValue));
        dest.setSwlSampleCount(swlCount);

        dest.setDfAvg(MathUtil.divStrZeroDashed(dfSum, new BigDecimal(dfCount)));
        dest.setDfAvgIncludeTax(MathUtil.divStrZeroDashed(dfSumIncludeTax, new BigDecimal(dfCount)));
        dest.setDfWeightedAvg(MathUtil.divStrZeroDashed(dfAmount, dfSumCalculateValue));
        dest.setDfWeightedAvgIncludeTax(MathUtil.divStrZeroDashed(dfAmountIncludeTax, dfSumCalculateValue));
        dest.setDfSampleCount(dfCount);

        dest.setJmDictCode(IndexCategoryDictEnum.JMDF.getCode());
        dest.setJmDictCodeIncludeTax(IndexCategoryDictEnum.JMDF_TAX.getCode());
        dest.setSwlDictCode(IndexCategoryDictEnum.SWLDF.getCode());
        dest.setSwlDictCodeIncludeTax(IndexCategoryDictEnum.SWLDF_TAX.getCode());
        dest.setDfDictCode(IndexCategoryDictEnum.DF.getCode());
        dest.setDfDictCodeIncludeTax(IndexCategoryDictEnum.DF_TAX.getCode());

        String itemIds = orderedIds(dest.getIds());
        dest.setItemIds(itemIds);
        dest.setIds(itemIds);
    }

    private void setItemMaxAndMin(List<GcdpDwsItemIndexCost> costs, GcdpDwsItemIndexCost dest) {
        List<BigDecimal> jmIndexs = costs.stream().map(GcdpDwsItemIndexCost::getJmIndexValue).filter(Objects::nonNull).collect(Collectors.toList());
        List<BigDecimal> jmIndexIncludeTaxs = costs.stream().map(GcdpDwsItemIndexCost::getJmIndexValueIncludeTax).filter(Objects::nonNull).collect(Collectors.toList());
        List<BigDecimal> swlIndexs = costs.stream().map(GcdpDwsItemIndexCost::getSwlIndexValue).filter(Objects::nonNull).collect(Collectors.toList());
        List<BigDecimal> swlIndexIncludeTaxs = costs.stream().map(GcdpDwsItemIndexCost::getSwlIndexValueIncludeTax).filter(Objects::nonNull).collect(Collectors.toList());
        List<BigDecimal> dfIndexs = costs.stream().map(GcdpDwsItemIndexCost::getDfIndexValue).filter(Objects::nonNull).collect(Collectors.toList());
        List<BigDecimal> dfIndexIncludeTaxs = costs.stream().map(GcdpDwsItemIndexCost::getDfIndexValueIncludeTax).filter(Objects::nonNull).collect(Collectors.toList());

        String jmMaxAndMin = MathUtil.combineMinAndMax(MathUtil.min(jmIndexs), MathUtil.max(jmIndexs));
        String jmMaxAndMinIncludeTax = MathUtil.combineMinAndMax(MathUtil.min(jmIndexIncludeTaxs), MathUtil.max(jmIndexIncludeTaxs));
        String swlMaxAndMin = MathUtil.combineMinAndMax(MathUtil.min(swlIndexs), MathUtil.max(swlIndexs));
        String swlMaxAndMinIncludeTax = MathUtil.combineMinAndMax(MathUtil.min(swlIndexIncludeTaxs), MathUtil.max(swlIndexIncludeTaxs));
        String dfMaxAndMin = MathUtil.combineMinAndMax(MathUtil.min(dfIndexs), MathUtil.max(dfIndexs));
        String dfMaxAndMinIncludeTax = MathUtil.combineMinAndMax(MathUtil.min(dfIndexIncludeTaxs), MathUtil.max(dfIndexIncludeTaxs));

        dest.setJmMaxAndMin(jmMaxAndMin);
        dest.setJmMaxAndMinIncludeTax(jmMaxAndMinIncludeTax);
        dest.setSwlMaxAndMin(swlMaxAndMin);
        dest.setSwlMaxAndMinIncludeTax(swlMaxAndMinIncludeTax);
        dest.setDfMaxAndMin(dfMaxAndMin);
        dest.setDfMaxAndMinIncludeTax(dfMaxAndMinIncludeTax);
    }
}
