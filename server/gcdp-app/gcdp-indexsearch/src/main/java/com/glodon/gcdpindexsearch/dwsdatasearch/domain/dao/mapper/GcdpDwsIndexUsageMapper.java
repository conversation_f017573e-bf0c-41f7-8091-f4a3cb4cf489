package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexUsage;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexUsage;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ProjectIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexBaseDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GcdpDwsIndexUsageMapper {
    List<ProjectIndexData> getIndex(@Param(value = "ids") List<String> ids);

    List<GcdpDwsItemIndexUsage> getItemIndex(@Param(value = "ids")List<String> ids, Integer showAll);

    List<DwsIndexUsage> selectByItemIds(@Param(value = "itemIdList")List<Long> itemIdList);

    List<CostIndexBaseDto> selectUsageIndexMergeByItemHash(@Param(value = "ids")List<Long> ids);
}
