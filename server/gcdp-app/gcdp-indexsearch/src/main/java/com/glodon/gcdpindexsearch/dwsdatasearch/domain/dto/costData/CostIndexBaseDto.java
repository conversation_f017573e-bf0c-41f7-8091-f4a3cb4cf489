package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 全成本指标库和建造做法指标库返回实体基类
 * <AUTHOR>
 * @date 2023-5-16
 */
@Data
public class CostIndexBaseDto {
    @ApiModelProperty(value = "主键id", required = true)
    private Long id;
    @ApiModelProperty(value = "父id", required = true)
    private Long pid;
    @ApiModelProperty(value = "科目名称", required = true)
    private String name;
    @ApiModelProperty(value = "样本量列表", required = true)
    private String sampleIds;

    private String ids;

}
