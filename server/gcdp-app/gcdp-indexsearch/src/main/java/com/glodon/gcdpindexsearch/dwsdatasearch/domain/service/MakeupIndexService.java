package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdp.common.utils.SpringUtil;
import com.glodon.gcdp.dimservice.domain.dao.enums.IndexCategoryDictEnum;
import com.glodon.gcdpindexsearch.common.enums.FilterTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndexCategoryDynamicColDict;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexCategoryDynamicColDictMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MakeupIndexDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MakeupIndexDynamicColDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.SumTypeEnum;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.ProjectNoteServiceImpl;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.MakeUpIndexVo;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 指标组调度类
 * <AUTHOR>
 * @date 2022-2
 */
@Service
public class MakeupIndexService {
    @Autowired
    private IDwsDataRepository dwsDataRepository;
    @Autowired
    private GcdpDwsIndexCategoryDynamicColDictMapper dictMapper;
    @Autowired
    private ProjectNoteServiceImpl projectNoteService;

    public MakeupIndexDto selectMakeupIndexList(MakeUpIndexVo makeUpIndexVo){
        String ids = makeUpIndexVo.getIds();
        String itemIds = makeUpIndexVo.getItemIds();
        String dictCode = makeUpIndexVo.getDictCode();
        MakeupIndexDto makeupIndexDto = new MakeupIndexDto();
        if(StringUtils.isBlank(ids)||StringUtils.isBlank(itemIds)){
            return makeupIndexDto;
        }
        List<Long> idList = Arrays.stream(ids.split(Constant.COMMA)).map(Long::new).collect(Collectors.toList());
        // 查询楼栋信息
        List<MakeUpIndexData> indexDataList = dwsDataRepository.selectNoteByIdsGroupByPhaseAndName(idList);
        if(CollUtil.isEmpty(indexDataList)){
            return makeupIndexDto;
        }
        indexDataList.forEach(item-> {
            String category = item.getCategoryName();
            if(StringUtils.isNotBlank(category)){
                String[] categories = category.split("@@");
                item.setCategoryName(categories[categories.length-1]);
            }
        });
        //查询表头配置信息
        GcdpDwsIndexCategoryDynamicColDict colDict = dictMapper.selectByDictId(Integer.valueOf(dictCode));
        if (colDict == null){
            throw new RuntimeException("没有配置列头信息");
        }
        String colJson = colDict.getDynamicColJson();
        List<MakeupIndexDynamicColDto> colDtos = JSONObject.parseArray(colJson, MakeupIndexDynamicColDto.class);
        makeupIndexDto.setDynamicCol(colDtos);
        String type = IndexCategoryDictEnum.getTypeByCode(makeUpIndexVo.getDictCode());
        MakeupIndexDetailService makeupIndexDetailService = SpringUtil.getBean("makeupIndexDetailService" + type);
        List<MakeUpIndexData> data = makeupIndexDetailService.listMakeupIndexDetail(indexDataList,itemIds, FilterTypeEnums.getByType(makeUpIndexVo.getFilterType()));
        // 根据样本量初始化勾选状态
        String rationNoteIds = makeUpIndexVo.getRationalNoteIds();
        if (!StringUtils.isEmpty(rationNoteIds)){
            data.forEach((makeUpIndexData -> {
                String idStr = makeUpIndexData.getIds();
                String[] sameLdIds = idStr.split(StrUtil.COMMA);
                boolean isChecked = true;
                for (String noteId : sameLdIds){
                    if (!rationNoteIds.contains(noteId)){
                        isChecked = false;
                        break;
                    }
                }
                makeUpIndexData.setIsChecked(isChecked);
            }));
        }
        setAreaName(data);
        setWholeId(data);
        makeupIndexDto.setData(data);
        return makeupIndexDto;
    }

    private void setWholeId(List<MakeUpIndexData> data) {
        if (CollUtil.isEmpty(data)) {
            return;
        }

        // 提取所有ID字符串，并去重后转换为Long类型列表
        List<Long> idList = data.stream()
                .map(MakeUpIndexData::getIds) // 获取所有ID字符串
                .flatMap(idStr -> Arrays.stream(idStr.split(","))) // 按逗号分割ID字符串
                .distinct() // 去重
                .map(Long::parseLong) // 将字符串转换为Long类型
                .collect(Collectors.toList()); // 收集为List

        // 根据单体规则反查完整数据
        List<DwsIndexProjectNote> validNotes = dwsDataRepository.selectNotesByIds(idList);
        List<DwsIndexProjectNote> projectWholeNote = projectNoteService.getWholeNoteList(SumTypeEnum.DT.getCode(), validNotes, null);

        // 按dtHash对单体进行分组
        Map<String, List<DwsIndexProjectNote>> dtHasGroup = projectWholeNote.stream().collect(Collectors.groupingBy(DwsIndexProjectNote::getDtHash));

        // 创建一个map，根据dtHash进行，单体id和完整单体id的映射
        Map<Long, List<Long>> noteIdMap = validNotes.stream()
                .collect(Collectors.toMap(
                        DwsIndexProjectNote::getId,
                        validNote -> dtHasGroup.getOrDefault(validNote.getDtHash(), Collections.emptyList()).stream().map(DwsIndexProjectNote::getId).collect(Collectors.toList())
                ));

        // 遍历原始数据列表，设置每个item的wholeIds
        data.forEach(item -> {
            List<Long> ids = Arrays.stream(item.getIds().split(",")).map(Long::parseLong)
                    .flatMap(id -> noteIdMap.getOrDefault(id, Collections.emptyList()).stream())
                    .distinct()
                    .collect(Collectors.toList());

            item.setWholeIds(ids.stream().map(String::valueOf).collect(Collectors.joining(",")));
        });
    }

    public MakeupIndexDto selectAllMakeupIndexList(MakeUpIndexVo makeUpIndexVo, List<MakeUpIndexData> indexDataList){
        String ids = makeUpIndexVo.getIds();
        String itemIds = makeUpIndexVo.getItemIds();
        MakeupIndexDto makeupIndexDto = new MakeupIndexDto();
        if(StringUtils.isBlank(ids) || StringUtils.isBlank(itemIds)){
            return makeupIndexDto;
        }
        indexDataList.forEach(item->{
            String category = item.getCategoryName();
            if(StringUtils.isNotBlank(category)){
                String[] categories = category.split("@@");
                item.setCategoryName(categories[categories.length-1]);
            }
        });
        String type = IndexCategoryDictEnum.getTypeByCode(makeUpIndexVo.getDictCode());
        MakeupIndexDetailService makeupIndexDetailService = SpringUtil.getBean("makeupIndexDetailService"+type);
        List<MakeUpIndexData> data = makeupIndexDetailService.listMakeupIndexDetail(indexDataList, itemIds, FilterTypeEnums.getByType(makeUpIndexVo.getFilterType()));
        setAreaName(data);
        makeupIndexDto.setData(data);
        return makeupIndexDto;
    }

    private void setAreaName(List<MakeUpIndexData> data) {
        if(CollUtil.isNotEmpty(data)){
            data.forEach(item->{
                List<String> areaNameList = new ArrayList<>();
                if(StringUtils.isNotBlank(item.getProvinceName())){
                    areaNameList.add(item.getProvinceName());
                }
                if(StringUtils.isNotBlank(item.getCityName())){
                    areaNameList.add(item.getCityName());
                }
                if(StringUtils.isNotBlank(item.getDistrictName())){
                    areaNameList.add(item.getDistrictName());
                }
                String areaName = StrUtil.join(StrUtil.SLASH, areaNameList);
                item.setArea(areaName);
            });
        }
    }
}
