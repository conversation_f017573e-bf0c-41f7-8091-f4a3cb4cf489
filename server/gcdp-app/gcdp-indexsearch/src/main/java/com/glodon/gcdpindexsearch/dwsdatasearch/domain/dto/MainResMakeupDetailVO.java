package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 材料明细
 */
@Data
@ApiModel(value = "主要工料明细", description = "主要工料明细")
@SuppressWarnings("squid:S1068") // 忽略sonar规则： Unused "private" fields should be removed
public class MainResMakeupDetailVO extends MakeupDetailBaseVO {
    @ApiModelProperty(value = "规格型号", example = "6.30")
    private String spec;
    @ApiModelProperty(value = "市场价（含税）", example = "6.30")
    private BigDecimal marketRateIncludeTax;
    @ApiModelProperty(value = "市场价合计（含税）", example = "6.30")
    private BigDecimal marketAmountIncludeTax;
    @ApiModelProperty(value = "市场价（不含税）", example = "106230.80")
    private BigDecimal marketRate;
    @ApiModelProperty(value = "市场价合计（不含税）", example = "106230.80")
    private BigDecimal marketAmount;
    @ApiModelProperty(value = "材料类型", example = "6.30")
    private Integer resourceType;
    @ApiModelProperty(value = "材料类型名称", example = "人")
    private String resourceTypeName;
    @ApiModelProperty(value = "材料类型排序", example = "1")
    private Integer resourceTypeOrd;
}
