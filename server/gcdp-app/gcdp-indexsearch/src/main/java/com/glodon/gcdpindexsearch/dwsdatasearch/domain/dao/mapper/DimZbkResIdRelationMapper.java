package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DimZbkProjectNoteRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 指标库-resId与业务端原始工程ID对应关系（临时表-处理历史数据使用）
 * <AUTHOR>
 * @date 2023/5/9 17:45
 */
@Mapper
public interface DimZbkResIdRelationMapper extends BaseMapper<DimZbkProjectNoteRes> {
    List<DimZbkProjectNoteRes> getResIdAndOriginalProjectIdRelation(@Param(value = "resIdList") List<Long> resIdList);
}
