package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl;

import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject;
import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdItemIndexTemplate;
import com.glodon.gcdp.dwdservice.domain.dao.mapper.DwdContractProjectMapper;
import com.glodon.gcdp.dwdservice.domain.dao.mapper.DwdItemIndexTemplateMapper;
import com.glodon.gcdp.dwdservice.domain.model.OriginalContractProject;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwdProjectInfo;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwdProjectInfoQueryMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwdDataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class DwdDataRepositoryImpl implements IDwdDataRepository {

    @Autowired
    DwdProjectInfoQueryMapper dwdProjectInfoQueryMapper;

    @Autowired
    DwdContractProjectMapper dwdContractProjectMapper;

    @Autowired
    DwdItemIndexTemplateMapper dwdItemIndexTemplateMapper;

    /**
     * 获取全部项目列表，包含项目信息
     */
    public List<DwdProjectInfo> listDetailProjectInfo(String enterpriseId,
                                                      List<String> orgIds,
                                                      List<String> projectCodes,
                                                      String projectName,
                                                      List<String> areaId,
                                                      String isVirtualOrg,
                                                      List<String> sharedEnterpriseId,
                                                      String includeSelfFlg,
                                                      List<String> authControlProjectCodes) {
        return dwdProjectInfoQueryMapper.listDetailProjectInfo(enterpriseId, orgIds, projectCodes, projectName, areaId, isVirtualOrg,sharedEnterpriseId,includeSelfFlg, authControlProjectCodes);
    }

    public DwdProjectInfo selectByProjectCode(String enterpriseId, String projectCode) {
        return dwdProjectInfoQueryMapper.selectByProjectCode(enterpriseId, projectCode);
    }

    public List<DwdProjectInfo> selectByProjectCodes(String enterpriseId, List<String> projectCodes) {
        return dwdProjectInfoQueryMapper.selectByProjectCodes(enterpriseId, projectCodes);
    }

    public List<OriginalContractProject> selectOriginalProjectIdByContractProjectIds(List<Long> contractProjectIds) {
        return dwdContractProjectMapper.selectOriginalProjectIdByContractProjectIds(contractProjectIds);
    }

    public List<DwdItemIndexTemplate> selectItemIndexTemplateByBidNodeIds(List<Long> bidNodeIds) {
        return dwdItemIndexTemplateMapper.selectByBidNodeIds(bidNodeIds);
    }

    @Override
    public List<DwdContractProject> selectProjectListByOriginalIds(List<String> originalIds) {
        return dwdContractProjectMapper.selectProjectListByOriginalIds(originalIds);
    }

}

