package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.glodon.gcdp.common.domain.PageData;
import com.glodon.gcdp.common.domain.consts.ValueConst;
import com.glodon.gcdp.common.domain.enums.DwsNoteTypeEnums;
import com.glodon.gcdp.common.utils.DateUtils;
import com.glodon.gcdpindexsearch.common.constant.BusinessConstants;
import com.glodon.gcdpindexsearch.common.entity.ContractInfoVO;
import com.glodon.gcdpindexsearch.common.enums.JADetailsEnums;
import com.glodon.gcdpindexsearch.common.util.EmptyUtil;
import com.glodon.gcdpindexsearch.common.util.ProjectAttrRecommendUtils;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.dao.entity.SimilarNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwdProjectInfo;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ColsDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ContractProjectDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ProjectAttrDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.StructContractProjectDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.SumTypeEnum;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.CommonHandler;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterConditionVO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.glodon.gcdp.common.domain.enums.DwsNoteTypeEnums.*;

/**
 * 结构化工程列表
 */
@Service
@Slf4j
public class StructProjectNoteServiceImpl{
    private static final String PROJECT_TYPE = "PROJECT";

    @Autowired
    ProjectNoteServiceImpl projectNoteService;

    /**
     * 返回结构化的项目列表
     * @param enterpriseId
     * @param conditionVO
     * @param orgIds
     * @return
     * @throws Exception
     */
    public StructContractProjectDto structProjectList(String enterpriseId, FilterConditionVO conditionVO, ContractInfoVO standardCondition, List<String> orgIds) throws Exception {
        // 1.查询符合条件的单体
        Map<String, DwdProjectInfo> projectInfoMap = Maps.newHashMap();
        conditionVO.setSumCondition(SumTypeEnum.STRUCT_PROJECT.getCode());
        long start = System.currentTimeMillis();
        List<DwsIndexProjectNote> validNotes = projectNoteService.getValidNotes(enterpriseId, conditionVO, orgIds, projectInfoMap);
        long end = System.currentTimeMillis();
        log.info("getValidNotes 查库：{}ms", end - start);
        // 2.构建结构化列表
        List<StructContractProjectDto.NoteMergeItem> projectList = buildStructProjectList(conditionVO, standardCondition, validNotes, projectInfoMap);
        long end2 = System.currentTimeMillis();
        log.info("构建结构化列表总耗时：{}ms", end2 - end);


        // 3.分数统计和排序
        structProjectDtoSort(projectList);

        // 4.筛选特殊的合并单体
        projectList = filterMergeNote(conditionVO.getSearchIds(), projectList);

        return convertStructProjectDto(conditionVO.getCurrentPage(), conditionVO.getPageSize(), projectList, conditionVO.getSimpleNoteSumFlag());
    }

    /**
     * 分页返回
     * @param currentPage
     * @param pageSize
     * @param mergeData
     * @param simpleNoteSumFlag
     * @return
     */
    private StructContractProjectDto convertStructProjectDto(Integer currentPage, Integer pageSize, List<StructContractProjectDto.NoteMergeItem> mergeData, Integer simpleNoteSumFlag) {
        int pageNum = currentPage == null ? 1 : currentPage;
        int size = pageSize == null ? 10 : pageSize;
        int total = Optional.of(mergeData.size()).orElse(0);
        StructContractProjectDto structContractProjectDto = new StructContractProjectDto();
        // 分页查询
        mergeData = mergeData.stream().skip((long) (pageNum - 1) * size).limit(size).collect(Collectors.toList());
        structContractProjectDto.setPage(new PageData(pageNum, size, total));
        structContractProjectDto.setProjectList(mergeData);
        structContractProjectDto.setSimpleNoteSumFlag(simpleNoteSumFlag);
        return structContractProjectDto;
    }

    private void setMaxScore(List<StructContractProjectDto.NoteMergeItem> childNotes, StructContractProjectDto.NoteMergeItem parentNote){
        childNotes.stream().max(Comparator.comparing(StructContractProjectDto.NoteMergeItem::getRecommendScore))
                .ifPresent(x -> {
                    if (x.getRecommendScore() > parentNote.getRecommendScore()){
                        parentNote.setRecommendScore(x.getRecommendScore());
                    }
                });
    }

    /**
     * 生成结构化的项目列表
     * @param conditionVO
     * @param validNotes
     * @param projectCodeAndInfoMap
     * @return
     */
    public List<StructContractProjectDto.NoteMergeItem> buildStructProjectList(FilterConditionVO conditionVO,
                                                                               ContractInfoVO standardCondition,
                                                                               List<DwsIndexProjectNote> validNotes,
                                                                               Map<String, DwdProjectInfo> projectCodeAndInfoMap) {
        List<StructContractProjectDto.NoteMergeItem> dtoList = new ArrayList<>();
        // 根据项目哈希，找出完整的项目数据
        List<DwsIndexProjectNote> projectWholeNote = projectNoteService.getWholeNoteList(SumTypeEnum.PROJECT.getCode(), validNotes, conditionVO.getProductSource());
        // 同步相同楼栋的工程分类，取非空最新
        CommonHandler.sameLdCategorySync(projectWholeNote);
        // 找补合同信息和工程特征
        Set<Long> noteIdSet = validNotes.stream().map(DwsIndexProjectNote::getId).collect(Collectors.toSet());
        validNotes = projectWholeNote.stream().filter(x -> noteIdSet.contains(x.getId())).collect(Collectors.toList());
        // 按项目合并
        Map<String, List<DwsIndexProjectNote>> projectWholeNoteGroupMap = projectNoteService.mergeNoteBySumCondition(SumTypeEnum.PROJECT.getCode(), projectWholeNote);
        Map<String, List<DwsIndexProjectNote>> groupNoteMap = projectNoteService.mergeNoteBySumCondition(SumTypeEnum.PROJECT.getCode(), validNotes);

        for (Map.Entry<String, List<DwsIndexProjectNote>> entry : projectWholeNoteGroupMap.entrySet()) {
            // 项目节点使用按项目哈希反查出的完整数据；下级节点用查询出的完整数据；
            List<DwsIndexProjectNote> projectWholeNoteList = entry.getValue();
            List<DwsIndexProjectNote> noteList = groupNoteMap.get(entry.getKey());
            if (CollUtil.isEmpty(noteList)) {
                continue;
            }
            // 1.合并项目节点
            StructContractProjectDto.NoteMergeItem dto = processNoteMerge(SumTypeEnum.PROJECT.getCode(), conditionVO, standardCondition, projectWholeNoteList, projectCodeAndInfoMap);
            dto.setMergeInfos(null);
            // 2.处理有业态的楼栋
            List<StructContractProjectDto.NoteMergeItem> categoryNotes = processCategoryNotes(noteList, conditionVO, standardCondition, projectCodeAndInfoMap, projectWholeNoteList);
            if (CollUtil.isNotEmpty(categoryNotes)) {
                dto.setCategoryList(categoryNotes);

                setMaxScore(categoryNotes, dto);
            }

            // 3.处理无业态的真实楼栋
            List<DwsIndexProjectNote> ldWithoutCategoryNotes = filterNotesByTypeAndCategory(noteList, LD, false);
            List<StructContractProjectDto.NoteMergeItem> ldNotes = processLdAndZyNotes(ldWithoutCategoryNotes, conditionVO, standardCondition, projectCodeAndInfoMap, SumTypeEnum.DT, projectWholeNoteList);
            if (CollUtil.isNotEmpty(ldNotes)) {
                dto.setLdList(ldNotes);
                setMaxScore(ldNotes, dto);
            }

            // 4.处理无业态的虚拟楼栋
            List<DwsIndexProjectNote> xnldWithoutCategoryNotes = filterNotesByTypeAndCategory(noteList, XN_LD, false);
            List<StructContractProjectDto.NoteMergeItem> zyNotes = processZyMerge(xnldWithoutCategoryNotes, conditionVO, standardCondition, projectCodeAndInfoMap, SumTypeEnum.XNLD_TRADE, projectWholeNoteList);
            if (CollUtil.isNotEmpty(zyNotes)) {
                dto.setZyList(zyNotes);
                setMaxScore(zyNotes, dto);
            }

            // 5. 处理虚拟节点的数据
            List<DwsIndexProjectNote> xnJdWithoutProjectNotes = filterNotesByTypeAndCategory(noteList, VIRTUAL, Boolean.TRUE);
            conditionVO.setSource(FilterConditionVO.XNLD_SUM_CONDITION);
            List<StructContractProjectDto.NoteMergeItem> xnLdNotes = processXnjdMerge(xnJdWithoutProjectNotes, conditionVO, standardCondition, projectCodeAndInfoMap);
            conditionVO.setSource(StrUtil.EMPTY);
            if (CollUtil.isNotEmpty(xnLdNotes)) {
                dto.setXnjdList(xnLdNotes);
                setMaxScore(xnLdNotes, dto);
            }

            // 6.如果项目下存在非空的节点那么保留
            if (CollUtil.isNotEmpty(dto.getCategoryList()) || CollUtil.isNotEmpty(dto.getLdList()) || CollUtil.isNotEmpty(dto.getZyList()) || CollUtil.isNotEmpty(dto.getXnjdList())) {
                dtoList.add(dto);
            }
        }

        return dtoList;
    }

    /**
     * 推荐节点排序和推荐状态设置
     * @param list
     * @param maxRecommendScore
     */
    private void sortAndSetRecommendStatus(List<StructContractProjectDto.NoteMergeItem> list, Integer maxRecommendScore) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        // 按 recommendScore 进行排序
        list.sort((o1, o2) -> o1.compare(o1, o2));

        // 设置推荐状态
        if (EmptyUtil.isNotEmpty(list) && maxRecommendScore.equals(list.get(0).getRecommendScore())) {
            for (StructContractProjectDto.NoteMergeItem item : list) {
                if (item.getRecommendScore() < maxRecommendScore) {
                    break;
                }
                item.setRecommend(true);

                // 递归调用，设置子节点的推荐状态
                sortAndSetRecommendStatus(item.getLdList(), maxRecommendScore);
                sortAndSetRecommendStatus(item.getZyList(), maxRecommendScore);
            }
        }
    }

    /**
     * 按得分和归档时间排序
     * @param projectList
     */
    private void structProjectDtoSort(List<StructContractProjectDto.NoteMergeItem> projectList){
        if (CollUtil.isEmpty(projectList)){
            return;
        }

        // 1.项目按归档时间和得分排序
        projectList.sort((o1, o2) -> o1.compare(o1, o2));

        Integer maxRecommendScore = projectList.get(0).getRecommendScore() >= 0 ? projectList.get(0).getRecommendScore() : 0;
        for (StructContractProjectDto.NoteMergeItem projectNote: projectList){
            if (projectNote.getRecommendScore() < maxRecommendScore){
                break;
            }

            // 1. 项目下业态节点排序和推荐状态
            sortAndSetRecommendStatus(projectNote.getCategoryList(), maxRecommendScore);

            // 2.项目下真实楼栋排序和推荐状态
            sortAndSetRecommendStatus(projectNote.getLdList(), maxRecommendScore);

            // 3.项目下专业排序和推荐状态
            sortAndSetRecommendStatus(projectNote.getZyList(), maxRecommendScore);
        }
    }

    /**
     * 处理业态结构:(业态节点、真实楼栋(有业态)、真实楼栋(有专业和业态)、虚拟楼栋(有业态))
     * @param noteList
     * @param conditionVO
     * @param projectCodeAndInfoMap
     */
    private List<StructContractProjectDto.NoteMergeItem> processCategoryNotes(List<DwsIndexProjectNote> noteList,
                                                                              FilterConditionVO conditionVO,
                                                                              ContractInfoVO standardCondition,
                                                                              Map<String, DwdProjectInfo> projectCodeAndInfoMap,
                                                                              List<DwsIndexProjectNote> projectWholeNote) {
        List<StructContractProjectDto.NoteMergeItem> categoryMergeNotes = new ArrayList<>();
        List<DwsIndexProjectNote> categoryNotes = filterNotesByCategory(noteList);
        Map<String, List<DwsIndexProjectNote>> categoryWholeNoteGroupMap = projectNoteService.mergeNoteBySumCondition(SumTypeEnum.CATEGORY.getCode(), projectWholeNote);
        Map<String, List<DwsIndexProjectNote>> categoryNoteMap = projectNoteService.mergeNoteBySumCondition(SumTypeEnum.CATEGORY.getCode(), categoryNotes);
        for (Map.Entry<String, List<DwsIndexProjectNote>> entry : categoryWholeNoteGroupMap.entrySet()) {
            // 项目节点使用按项目哈希反查出的完整数据；下级节点用查询出的完整数据；
            List<DwsIndexProjectNote> categoryWholeNoteList = entry.getValue();
            List<DwsIndexProjectNote> notes = categoryNoteMap.get(entry.getKey());
            if (CollUtil.isEmpty(notes)){
                continue;
            }
            // 1.合并出业态节点
            StructContractProjectDto.NoteMergeItem categoryDto = processNoteMerge(SumTypeEnum.CATEGORY.getCode(), conditionVO, standardCondition, categoryWholeNoteList, projectCodeAndInfoMap);
            categoryDto.setMergeInfos(null);
            categoryDto.setProjectInfoDetail(null);

            // 2.处理有业态的真实楼栋
            List<DwsIndexProjectNote> ldWithCategoryNotes = filterNotesByTypeAndCategory(notes, LD, true);
            List<StructContractProjectDto.NoteMergeItem> ldMergeNotes = processLdAndZyNotes(ldWithCategoryNotes, conditionVO, standardCondition, projectCodeAndInfoMap, SumTypeEnum.DT, projectWholeNote);
            if (CollUtil.isNotEmpty(ldMergeNotes)){
                categoryDto.setLdList(ldMergeNotes);
                ldMergeNotes.stream().max(Comparator.comparing(StructContractProjectDto.NoteMergeItem::getRecommendScore))
                        .ifPresent(x -> {
                            if (x.getRecommendScore() > categoryDto.getRecommendScore()){
                                categoryDto.setRecommendScore(x.getRecommendScore());
                            }
                        });
            }

            // 3.处理有业态的虚拟楼栋
            List<DwsIndexProjectNote> xnldWithCategoryNotes = filterNotesByTypeAndCategory(notes, XN_LD, true);
            List<StructContractProjectDto.NoteMergeItem> zyMergeNotes = processZyMerge(xnldWithCategoryNotes, conditionVO, standardCondition, projectCodeAndInfoMap, SumTypeEnum.XNLD_TRADE, projectWholeNote);
            if (CollUtil.isNotEmpty(zyMergeNotes)){
                categoryDto.setZyList(zyMergeNotes);
                zyMergeNotes.stream().max(Comparator.comparing(StructContractProjectDto.NoteMergeItem::getRecommendScore))
                        .ifPresent(x -> {
                            if (x.getRecommendScore() > categoryDto.getRecommendScore()){
                                categoryDto.setRecommendScore(x.getRecommendScore());
                            }
                        });
            }

            // 4.如果有楼栋或专业那么保留
            if (CollUtil.isNotEmpty(ldMergeNotes) || CollUtil.isNotEmpty(zyMergeNotes)){
                categoryMergeNotes.add(categoryDto);
            }
        }

        return categoryMergeNotes;
    }

    /**
     * 真实楼栋合并、真实专业楼栋合并
     * @param ldNotes
     * @param conditionVO
     * @param standardCondition
     * @param projectCodeAndInfoMap
     * @param sumType
     */
    private List<StructContractProjectDto.NoteMergeItem> processLdAndZyNotes(List<DwsIndexProjectNote> ldNotes,
                                                                             FilterConditionVO conditionVO,
                                                                             ContractInfoVO standardCondition,
                                                                             Map<String, DwdProjectInfo> projectCodeAndInfoMap,
                                                                             SumTypeEnum sumType,
                                                                             List<DwsIndexProjectNote> projectWholeNote) {
        List<StructContractProjectDto.NoteMergeItem> ldMergeNotes = new ArrayList<>();
        Map<String, List<DwsIndexProjectNote>> ldMergeGroupMap = projectNoteService.mergeNoteBySumCondition(sumType.getCode(), ldNotes);
        for (List<DwsIndexProjectNote> notes : ldMergeGroupMap.values()) {
            StructContractProjectDto.NoteMergeItem ldDto = processNoteMerge(sumType.getCode(), conditionVO, standardCondition, notes, projectCodeAndInfoMap);

            // 筛选真实楼栋下专业
            List<DwsIndexProjectNote> zyNotesOfLd = filterNotesByTypeAndTrade(notes, LD.getIndex());
            if (!CollUtil.isEmpty(zyNotesOfLd)) {
                List<StructContractProjectDto.NoteMergeItem> zyMergeNotes = processZyMerge(zyNotesOfLd, conditionVO, standardCondition, projectCodeAndInfoMap, SumTypeEnum.LD_TRADE, projectWholeNote);
                if (!CollUtil.isEmpty(zyMergeNotes)) {
                    ldDto.setZyList(zyMergeNotes);
                }
            }

            ldMergeNotes.add(ldDto);
        }

        return ldMergeNotes;
    }

    /**
     * 专业级楼栋合并: 先做专业标签聚合,建筑面积取楼栋建筑面积
     * @param notes
     * @param conditionVO
     * @param projectCodeAndInfoMap
     * @param sumType
     */
    private List<StructContractProjectDto.NoteMergeItem> processZyMerge(List<DwsIndexProjectNote> notes,
                                                                        FilterConditionVO conditionVO,
                                                                        ContractInfoVO standardCondition,
                                                                        Map<String, DwdProjectInfo> projectCodeAndInfoMap,
                                                                        SumTypeEnum sumType,
                                                                        List<DwsIndexProjectNote> projectWholeNote) {
        List<StructContractProjectDto.NoteMergeItem> zyMergeNotes = new ArrayList<>();
        List<DwsIndexProjectNote> typeWholeNotes = projectWholeNote.stream().filter(x -> sumType.getTypeList().contains(x.getType())).collect(Collectors.toList());
        Map<String, List<DwsIndexProjectNote>> zyWholeNoteGroupMap = projectNoteService.mergeNoteBySumCondition(sumType.getCode(), typeWholeNotes);
        Map<String, List<DwsIndexProjectNote>> zyMergeGroupMap = projectNoteService.mergeNoteBySumCondition(sumType.getCode(), notes);

        for (Map.Entry<String, List<DwsIndexProjectNote>> entry : zyWholeNoteGroupMap.entrySet()) {
            // 项目节点使用按项目哈希反查出的完整数据；下级节点用查询出的完整数据；
            List<DwsIndexProjectNote> categoryWholeNoteList = entry.getValue();
            List<DwsIndexProjectNote> zyNotes = zyMergeGroupMap.get(entry.getKey());
            if (CollUtil.isEmpty(zyNotes)){
                continue;
            }
            zyMergeNotes.add(processNoteMerge(sumType.getCode(), conditionVO, standardCondition, categoryWholeNoteList, projectCodeAndInfoMap));
        }

        return zyMergeNotes;
    }


    /**
     * 虚拟节点的数据汇总: 先做专业标签聚合,建筑面积取楼栋建筑面积
     * @param notes 单体数据
     * @param conditionVO 条件数据
     * @param projectCodeAndInfoMap 项目映射数据
     */
    private List<StructContractProjectDto.NoteMergeItem> processXnjdMerge(List<DwsIndexProjectNote> notes, FilterConditionVO conditionVO,
            ContractInfoVO standardCondition, Map<String, DwdProjectInfo> projectCodeAndInfoMap) {
        List<StructContractProjectDto.NoteMergeItem> xnJdMerge = new ArrayList<>();
        Map<String, List<DwsIndexProjectNote>> xuJdMergeNotes = projectNoteService.mergeNoteBySumCondition(SumTypeEnum.VIRTUAL_NODE.getCode(), notes);
        for (List<DwsIndexProjectNote> xnjdNotes : xuJdMergeNotes.values()) {
            StructContractProjectDto.NoteMergeItem targetDTO = processNoteMerge(SumTypeEnum.VIRTUAL_NODE.getCode(), conditionVO, standardCondition, xnjdNotes, projectCodeAndInfoMap);
            // 计算评分
            Map<String, List<String>> featureMap = convertFeatureToMap(targetDTO.getFeatures());
            Integer score = calcRecommendScore(standardCondition, targetDTO, featureMap);
            targetDTO.setRecommendScore(score);
            xnJdMerge.add(targetDTO);
        }
        return xnJdMerge;
    }

    /**
     * 合并节点
     * @param sumType
     * @param conditionVO
     * @param notes
     * @param projectCodeAndInfoMap
     * @return
     */
    // 通用的合并处理
    private StructContractProjectDto.NoteMergeItem processNoteMerge(
            Integer sumType,
            FilterConditionVO conditionVO,
            ContractInfoVO standardCondition,
            List<DwsIndexProjectNote> notes,
            Map<String, DwdProjectInfo> projectCodeAndInfoMap) {
        StructContractProjectDto.NoteMergeItem targetDto = new StructContractProjectDto.NoteMergeItem();

        // 搜索条件时建筑面积求和
        int simpleNoteSumFlag = Objects.equals(sumType, SumTypeEnum.PROJECT.getCode()) ? ValueConst.FALSE : ValueConst.TRUE;

        ContractProjectDto.NoteMergeDto mergeNote = projectNoteService.convertToSingleNoteMergeDto(sumType, simpleNoteSumFlag, notes, projectCodeAndInfoMap);

        // 基本信息赋值
        BeanUtils.copyProperties(mergeNote, targetDto);

        // 非项目节点按单体的合同信息填充合同信息字段
        fillStructNoteInfo(sumType, notes, projectCodeAndInfoMap, targetDto, mergeNote);

        // 真实楼栋、业态下虚拟楼栋、项目下虚拟楼栋根据特征过滤并计算评分
        if (Objects.equals(sumType, SumTypeEnum.DT.getCode()) || Objects.equals(sumType, SumTypeEnum.XNLD_TRADE.getCode())){
            // 计算评分
            Map<String, List<String>> featureMap = convertFeatureToMap(targetDto.getFeatures());
            Integer score = calcRecommendScore(standardCondition, targetDto, featureMap);
            targetDto.setRecommendScore(score);
        }

        return targetDto;
    }

    private int calculateSumFlag(Integer sumType, FilterConditionVO conditionVO) {
        if (!Objects.equals(sumType, SumTypeEnum.PROJECT.getCode())) {
            conditionVO.setSumCondition(2);
            return conditionVO.getSimpleNoteSumFlag();
        } else {
            return 0;
        }
    }

    /**
     * 转换为<特征项, [特征值]>的形式
     * @param features 特征数据
     * @return 特征值的映射
     */
    private Map<String, List<String>> convertFeatureToMap(List<StructContractProjectDto.Feature> features) {
        Map<String, List<String>> featureMap = new HashMap<>();
        if (EmptyUtil.isEmpty(features)) {
            return featureMap;
        }
        features.forEach(trade -> trade.getValues().forEach(value -> {
            String featureName = value.getFeatureName();
            String featureValue = Optional.ofNullable(value.getValue()).orElse(StringUtils.EMPTY).trim();
            List<String> featureValues = featureMap.get(featureName);
            if (EmptyUtil.isEmpty(featureValues)) {
                featureValues = new ArrayList<>();
                featureValues.add(featureValue);
                featureMap.put(featureName, featureValues);
            } else {
                featureValues.add(featureValue);
            }
        }));
        return featureMap;
    }

    /**
     * 填充节点的信息
     * @param sumType
     * @param notes
     * @param projectCodeAndInfoMap
     * @param targetDto
     * @param mergeNote
     */
    private void fillStructNoteInfo(
            Integer sumType,
            List<DwsIndexProjectNote> notes, Map<String, DwdProjectInfo> projectCodeAndInfoMap,
            StructContractProjectDto.NoteMergeItem targetDto, ContractProjectDto.NoteMergeDto mergeNote) {
        DwsIndexProjectNote firstNote = notes.get(0);

        // 1.产品来源字段赋值,指标查询接口需要
        targetDto.setProductSource(
                Optional.ofNullable(mergeNote.getMergeInfos())
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(ContractProjectDto.NoteInfoDto::getProductSource)
                        .distinct()
                        .collect(Collectors.toList())
        );

        // 2.按节点类型赋值其他字段
        if (Objects.equals(SumTypeEnum.PROJECT.getCode(), sumType) || Objects.equals(SumTypeEnum.VIRTUAL_NODE.getCode(), sumType)) {
            String projectKey = projectNoteService.generateProjectCodeAndInfoMapKey(firstNote.getEnterpriseId(), firstNote.getProjectCode());
            DwdProjectInfo projectInfo = projectCodeAndInfoMap.get(projectKey);
            // 工程分类
            targetDto.setProjectCategoryCode(projectInfo.getProjectCategoryCode());
            targetDto.setProjectCategoryName(projectInfo.getProjectCategoryName());
            if (Objects.equals(SumTypeEnum.PROJECT.getCode(), sumType)){
                // 显示名称为项目名称
                targetDto.setDisplayName(projectInfo.getProjectName());
                targetDto.setType(PROJECT_TYPE);
                // 开工时间取项目开工时间
                targetDto.setBeginDate(projectInfo.getStartTime());
                // 竣工时间取项目竣工时间
                targetDto.setCompletionDate(projectInfo.getEndTime());
            }else if (Objects.equals(SumTypeEnum.VIRTUAL_NODE.getCode(), sumType)){
                // 设置虚拟节点的类型
                targetDto.setDisplayName(BusinessConstants.QTJA_NAME);
                targetDto.setType(JADetailsEnums.XNJD.getType());
            }
        } else {
            targetDto.setProjectCategoryCode(firstNote.getProjectCategoryCode());
            targetDto.setProjectCategoryName(firstNote.getProjectCategoryName());
            // 开竣工时间补齐
            assembleContractInfo(notes, targetDto);
            if (Objects.equals(SumTypeEnum.CATEGORY.getCode(), sumType)){
                targetDto.setDisplayName(firstNote.getProjectCategoryName());
                targetDto.setType(CATEGORY.getName());
            } else if (Objects.equals(SumTypeEnum.DT.getCode(), sumType)){
                targetDto.setDisplayName(firstNote.getLdNameIdentify());
                fillLatestProjectAttr(mergeNote, targetDto);
                targetDto.setType(LD.getName());
            } else {
                targetDto.setDisplayName(firstNote.getTradeName());
                fillLatestProjectAttr(mergeNote, targetDto);
                targetDto.setType(Objects.equals(SumTypeEnum.XNLD_TRADE.getCode(), sumType)? XN_LD.getName() : LD.getName());
            }
        }

        // 生成每个节点的关键路径
        String searchIds = buildSearchIds(sumType, firstNote);
        targetDto.setSearchIds(searchIds);
    }

    /**
     * 筛选业态下楼栋或虚拟楼栋
     * @param noteList: 楼栋列表
     * @param type: 节点类型(LD、XNLD)
     * @param withCategory: 是否包含业态
     * @return
     */
    private List<DwsIndexProjectNote> filterNotesByTypeAndCategory(List<DwsIndexProjectNote> noteList, DwsNoteTypeEnums type, boolean withCategory) {
        return noteList.stream()
                .filter(x -> ObjectUtil.equals(type.getIndex(), x.getType()) && (withCategory ? !StringUtils.isEmpty(x.getProjectCategoryName()) : StringUtils.isEmpty(x.getProjectCategoryName())))
                .collect(Collectors.toList());
    }

    /**
     * 按楼栋类型过滤出专业级真实楼栋或虚拟楼栋
     * @param noteList
     * @param type
     * @return
     */
    private List<DwsIndexProjectNote> filterNotesByTypeAndTrade(List<DwsIndexProjectNote> noteList, int type) {
        return noteList.stream()
                .filter(x -> Objects.equals(x.getType(), type) && !StringUtils.isEmpty(x.getTradeCode()))
                .collect(Collectors.toList());
    }

    /***
     * 筛选出列表中有业态的单体列表
     * @param noteList
     * @return
     */
    private List<DwsIndexProjectNote> filterNotesByCategory(List<DwsIndexProjectNote> noteList) {
        return noteList.stream()
                .filter(x -> !StringUtils.isEmpty(x.getProjectCategoryName()))
                .collect(Collectors.toList());
    }

    /**
     * 从工程特征提取审核接口需要的最新的工程特征
     * 提取的特征: "结构类型", "抗震设防烈度", "基础类型", "装修标准", "标准层层高", "标准层层高(m)"
     * @param noteMergeDto
     */
    private void fillLatestProjectAttr(ContractProjectDto.NoteMergeDto noteMergeDto, StructContractProjectDto.NoteMergeItem targetDto){
        List<ContractProjectDto.NoteInfoDto> noteMergeDtos = noteMergeDto.getMergeInfos();
        if (CollUtil.isEmpty(noteMergeDtos)){
            return;
        }

        // 根据归档时间取最新的,倒序排列
        noteMergeDtos.sort((o1, o2) -> o2.getArchiveDate().compareTo(o1.getArchiveDate()));


        List<String> featureList = Arrays.asList("结构类型", "抗震设防烈度", "基础类型", "装修标准", "标准层层高", "标准层层高(m)");

        // 每个专业最新的特征
        Map<String, Map<String, StructContractProjectDto.Feature.FeatureValueOfTrade>> latestAttributes = new LinkedHashMap<>();
        for (ContractProjectDto.NoteInfoDto note : noteMergeDtos){
            List<ProjectAttrDto> featureDetails = note.getFeatureDetail();
            if (CollUtil.isEmpty(featureDetails)){
                continue;
            }
            for (ProjectAttrDto attrDto : featureDetails){
                String tradeName = attrDto.getTradeName();
                latestAttributes.putIfAbsent(tradeName, new HashMap<>());

                List<ColsDto> cols = attrDto.getCols();
                if (CollUtil.isEmpty(cols)){
                    continue;
                }
                String code = cols.get(0).getCode();
                // 如果属性值不为空且之前未存储该属性或该属性没有值
                List<LinkedHashMap> attrItems = attrDto.getData();
                if (CollUtil.isEmpty(attrItems)){
                    continue;
                }

                // 当前专业下的特征用非空最新补齐
                Map<String, StructContractProjectDto.Feature.FeatureValueOfTrade> featureValues = latestAttributes.get(tradeName);
                for (LinkedHashMap attrItem : attrItems){
                    String attrName = (String)attrItem.get("attrName");
                    if (featureList.contains(attrName)){
                        Object attrValue = attrItem.get(code);
                        String unit = (String)attrItem.get("unit");

                        featureValues.putIfAbsent(attrName, new StructContractProjectDto.Feature.FeatureValueOfTrade(attrName, (String) attrValue,  unit));
                        // 如果特征值并且没有添加到特征列表,那么添加
                        if ((attrValue != null) && (featureValues.get(attrName).getValue() == null)){
                            featureValues.put(attrName, new StructContractProjectDto.Feature.FeatureValueOfTrade(attrName, (String) attrValue,  unit));
                        }
                    }
                }
            }
        }

        // 生成最新的工程特征
        for (String tradeName : latestAttributes.keySet()){
            Map<String, StructContractProjectDto.Feature.FeatureValueOfTrade> tradeFeatures = latestAttributes.get(tradeName);
            if (!CollUtil.isEmpty(tradeFeatures)){
                StructContractProjectDto.Feature feature = buildTradeFeature(tradeName, tradeFeatures);
                targetDto.addFeature(feature);
            }
        }
    }

    /**
     * 生成专业级的特征
     * @param tradeName
     * @param latestAttributes
     * @return
     */
    private static StructContractProjectDto.Feature buildTradeFeature(String tradeName, Map<String, StructContractProjectDto.Feature.FeatureValueOfTrade> latestAttributes) {
        StructContractProjectDto.Feature feature = new StructContractProjectDto.Feature();
        feature.setTradeName(tradeName);
        List<StructContractProjectDto.Feature.FeatureValueOfTrade> values = new ArrayList<>();
        for (Map.Entry<String, StructContractProjectDto.Feature.FeatureValueOfTrade> attrItem : latestAttributes.entrySet()){
            StructContractProjectDto.Feature.FeatureValueOfTrade featureValueOfTrade = attrItem.getValue();
            values.add(featureValueOfTrade);
        }
        feature.setValues(values);

        return feature;
    }

    /**
     * 计算单体特征匹配得分
     * @param standardCondition: 用户当前工程的合同信息
     * @param targetNoteDto 当前数据单体
     * @return 汇算的分值
     */
    private Integer calcRecommendScore(
            ContractInfoVO standardCondition,
            StructContractProjectDto.NoteMergeItem targetNoteDto,
            Map<String, List<String>> featureMap){
        SimilarNote similarNote = new SimilarNote();
        if (!CollUtil.isEmpty(targetNoteDto.getCategoryPath())){
            similarNote.setCategoryNamePath(targetNoteDto.getCategoryPath().get(0));
        }
        similarNote.setBeginDate(targetNoteDto.getBeginDate());
        similarNote.setFeatureMap(featureMap);

        return ProjectAttrRecommendUtils.getRecommendScore(standardCondition, targetNoteDto, similarNote);
    }
    /**
     * 用合同信息填充节点
     * @param noteList
     * @param targetDto
     */
    private void assembleContractInfo(List<DwsIndexProjectNote> noteList, StructContractProjectDto.NoteMergeItem targetDto) {
        List<String> beginDateList = new ArrayList<>();
        List<String> completionDateList = new ArrayList<>();
        for (DwsIndexProjectNote note : noteList){
            // 提取合同信息
            Map<String, Object> contractInfoMap = projectNoteService.parseProjectInfoValue(note.getProjectInfoJson());

            // 提取开工时间
            String beginDate = (String) contractInfoMap.get("开工时间");
            if (StringUtils.isNotEmpty(beginDate)){
                beginDateList.add(beginDate);
            }

            // 提取竣工时间
            String completionDate = (String) contractInfoMap.get("竣工时间");
            if (StringUtils.isNotEmpty(completionDate)) {
                completionDateList.add(completionDate);
            }
        }

        // 开工时间取非空最早
        if (beginDateList.size() > 0){
            String beginDate = Collections.min(beginDateList);
            targetDto.setBeginDate(DateUtils.parseData(beginDate));
        }

        // 竣工时间
        if (completionDateList.size() > 0){
            String endDate = Collections.max(completionDateList);
            targetDto.setCompletionDate(DateUtils.parseData(endDate));
        }
    }

    /**
     * 按合并类型生成每个节点的关键路径,方便后续基于此字段来定位记录
     * @param type: 合并类型
     * @param note: dws中的单体
     * @return
     */
    private String buildSearchIds(Integer type, DwsIndexProjectNote note){
        String searchIds = null;
        if (Objects.equals(type, SumTypeEnum.PROJECT.getCode()) || Objects.equals(type, SumTypeEnum.VIRTUAL_NODE.getCode())){
            // 项目节点
            searchIds = CharSequenceUtil.join(StrPool.COMMA, note.getProjectCode(), note.getPhase());
        } else if (Objects.equals(type, SumTypeEnum.CATEGORY.getCode())){
            searchIds = CharSequenceUtil.join(StrPool.COMMA, note.getProjectCode(), note.getPhase(), note.getProjectCategoryName());
        } else if (Objects.equals(type, SumTypeEnum.DT.getCode())){
            // 有业态,无业态
            searchIds = Stream.of(
                            note.getProjectCode(),
                            note.getPhase(),
                            note.getProjectCategoryName(),
                            note.getLdNameIdentify(),
                            note.getBuildArea())
                    .filter(ObjectUtil::isNotNull)  // 过滤掉 null 和空字符串
                    .map(String::valueOf)
                    .collect(Collectors.joining(StrPool.COMMA));  // 使用逗号连接
        } else if (Objects.equals(type, SumTypeEnum.LD_TRADE.getCode())) {
            searchIds = Stream.of(
                            note.getProjectCode(),
                            note.getPhase(),
                            note.getProjectCategoryName(),
                            note.getLdNameIdentify(),
                            note.getBuildArea(),
                            note.getTradeName()
                            )
                    .filter(ObjectUtil::isNotNull)  // 过滤掉 null 和空字符串
                    .map(String::valueOf)
                    .collect(Collectors.joining(StrPool.COMMA));  // 使用逗号连接
        }else if (Objects.equals(type, SumTypeEnum.XNLD_TRADE.getCode())) {
            searchIds = Stream.of(
                            note.getProjectCode(),
                            note.getPhase(),
                            note.getProjectCategoryName(),
                            note.getTradeName())
                    .filter(ObjectUtil::isNotNull)  // 过滤掉 null 和空字符串
                    .map(String::valueOf)
                    .collect(Collectors.joining(StrPool.COMMA));  // 使用逗号连接
        }

        return searchIds;
    }

    /**
     * 按searchIds
     * @param searchIds: 特殊筛选某条数据时使用
     * @param projectList: 合并出来的全部列表
     * @return
     */
    public List<StructContractProjectDto.NoteMergeItem> filterMergeNote(
            String searchIds,
            List<StructContractProjectDto.NoteMergeItem> projectList) {
        if (StringUtils.isEmpty(searchIds)){
            return projectList;
        }

        // 筛选出符合条件的记录
        StructContractProjectDto.NoteMergeItem searchItem = filterNoteBySearchIds(searchIds, projectList);
        if (searchItem != null){
            return Collections.singletonList(searchItem);
        }

        return Collections.emptyList();
    }

    /**
     * 递归遍历节点找到符合的单体
     * @param searchIds: 传入的搜索条件
     * @param noteList: 合并的列表
     * @return
     */
    private StructContractProjectDto.NoteMergeItem filterNoteBySearchIds(
            String searchIds, List<StructContractProjectDto.NoteMergeItem> noteList) {

        for (StructContractProjectDto.NoteMergeItem item : noteList) {
            // 检查当前层的 searchIds 是否匹配
            if (StringUtils.equals(searchIds, item.getSearchIds())) {
                return item;
            }

            // 从业态及业态下的节点筛选
            if (item.getCategoryList() != null) {
                StructContractProjectDto.NoteMergeItem match = filterNoteBySearchIds(searchIds, item.getCategoryList());
                if (match != null) {
                    return match;
                }
            }

            // 从楼栋级楼栋下的节点筛选
            if (item.getLdList() != null) {
                StructContractProjectDto.NoteMergeItem match = filterNoteBySearchIds(searchIds, item.getLdList());
                if (match != null) {
                    return match;
                }
            }

            // 检查专业节点
            if (item.getZyList() != null) {
                StructContractProjectDto.NoteMergeItem match = filterNoteBySearchIds(searchIds, item.getZyList());
                if (match != null) {
                    return match;
                }
            }
        }

        return null;
    }
}
