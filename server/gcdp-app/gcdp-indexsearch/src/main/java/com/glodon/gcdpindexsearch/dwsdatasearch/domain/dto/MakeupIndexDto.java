package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 指标组成实体类
 */
@ApiModel(value = "指标组成实体类",description = "指标组成实体类")
@Data
public class MakeupIndexDto {
    @ApiModelProperty(value = "动态列列表")
    private List<MakeupIndexDynamicColDto> dynamicCol;
    private List<MakeUpIndexData> data;
}
