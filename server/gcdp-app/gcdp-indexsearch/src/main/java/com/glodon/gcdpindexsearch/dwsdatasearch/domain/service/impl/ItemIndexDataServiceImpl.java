package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdpindexsearch.common.enums.IndexTypeCalcEnums;
import com.glodon.gcdpindexsearch.common.enums.IndexTypePrefixEnums;
import com.glodon.gcdpindexsearch.common.util.EmptyUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MakeupIndexDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.SumTypeEnum;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.ProcessFactory;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.ItemIndexQueryReqVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.MakeUpIndexVo;
import com.glodon.gcdpindexsearch.infrastructure.exception.BusinessException;
import com.glodon.gcdpindexsearch.infrastructure.util.ListUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @author: luoml-b
 * @date: 2023/11/17 10:37
 * @description: 科目维度查询service
 */
@Service
@Slf4j
public class ItemIndexDataServiceImpl extends ItemDetailService implements ItemIndexDataService {

    @Autowired
    private IDwsIndexDataRepository indexDataRepository;

    @Autowired
    private MakeupIndexService makeupIndexService;

    @Autowired
    private ProjectNoteService projectNoteService;

    @Autowired
    private ItemSortService itemSortService;

    @Autowired
    private SingleProjectIndexDataServiceImpl projectIndexDataService;

    @Override
    public ItemIndexData getItemIndexData(ItemIndexQueryReqVO indexQueryReqVO) throws Exception {
        preCheck(indexQueryReqVO);
        List<DwsIndexProjectNote> dwsIndexProjectNotes = getIdsByFilter(indexQueryReqVO);
        if (CollectionUtils.isEmpty(dwsIndexProjectNotes)) {
            return new ItemIndexData(Lists.newArrayList(), indexQueryReqVO.getTempNodeIds(), null);
        }
        // 构建科目模板信息
        List<Template> templateList = projectIndexDataService.getTemplateList(dwsIndexProjectNotes);
        List<String> ids = dwsIndexProjectNotes.stream().map(x -> x.getId().toString()).collect(Collectors.toList());
        Set<String> matchCategoryCodeList = dwsIndexProjectNotes.stream()
                .filter(e -> Objects.nonNull(e.getProjectCategoryCode()))
                .map(e -> Strings.substring(e.getProjectCategoryCode(), 0, 3))
                .collect(Collectors.toSet());
        indexQueryReqVO.setMatchCategory(matchCategoryCodeList);
        List<ItemIndex> indexDataList = Lists.newArrayList();
        List<String> indexTypeList = indexQueryReqVO.getIndexType().stream().distinct().collect(Collectors.toList());
        for (String indexType : indexTypeList) {
            List<GcdpDwsIndex> indexData = concurrentGetIndex(ids, indexType, indexQueryReqVO.getItemCostType());

            // 调用指标责任链进行数据组装
            indexData= ProcessFactory.indexProcessor(indexData, indexType, this);

            makePid(indexData);

            //主要量指标需要按照基础信息库的主要量排序
            indexData = itemSortService.subjectQueryItemSort(indexData, indexQueryReqVO, indexType);

            makeCode(indexData);
            //indexData.sort(Comparator.comparing(ItemData::getCode));
            buildReturnValue(indexDataList, indexType, indexData);
        }
        return new ItemIndexData(templateList, ids, indexDataList);
    }


    @Override
    public ItemIndexData getItemIndexDataDetails(ItemIndexQueryReqVO itemIndexQueryReqVO) throws Exception {
        ItemIndexData itemIndexData = getItemIndexData(itemIndexQueryReqVO);
        List<ItemIndex> indexData = itemIndexData.getIndexData();
        if (CollUtil.isEmpty(indexData)) {
            return new ItemIndexData(Lists.newArrayList(), itemIndexQueryReqVO.getTempNodeIds(), null);
        }
        indexData.forEach(item -> {
            try {
                setMakeup(itemIndexData, itemIndexQueryReqVO.getEnterpriseId(), item);
            } catch (Exception e) {
                throw new BusinessException("设置指标组成出现异常");
            }
        });
        return itemIndexData;
    }


    private List<GcdpDwsIndex> concurrentGetIndex(List<String> ids, String indexType, Integer itemCostType) throws InterruptedException {
        List<List<String>> batchIds = ListUtil.partition(ids, DEFAULT_NOTE_BATCH_SIZE);
        List<GcdpDwsIndex> result = Collections.synchronizedList(new ArrayList<>(ids.size() * DEFAULT_NOTE_BATCH_SIZE));
        CountDownLatch countDownLatch = new CountDownLatch(batchIds.size());
        AtomicReference<Exception> exceptionRef = new AtomicReference<>();
        for (List<String> bIds : batchIds) {
            selectDataServiceExecutor.execute(() -> {
                try {
                    //查询dws_index_data表
                    List<GcdpDwsIndex> gcdpDwsIndices = indexDataRepository.selectItemIndexByNoteId(IndexTypePrefixEnums
                                    .getEnumByType(indexType).getPrefix(),
                            IndexTypeCalcEnums.getEnumByType(indexType).getCalc(),
                            bIds.stream().map(Long::parseLong).collect(Collectors.toList()),itemCostType);
                    result.addAll(gcdpDwsIndices);
                } catch (Exception e) {
                    exceptionRef.set(e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        // 如果发生异常，将异常重新抛出
        if (exceptionRef.get() != null) {
            log.error("查询指标数据出错: ", exceptionRef.get());
            throw new BusinessException("查询指标数据出错");
        }
        return result;
    }


    private void preCheck(ItemIndexQueryReqVO indexQueryReqVO) {
        if (indexQueryReqVO.isMergeMultiIndex()) {
            throw new BusinessException("暂不支持指标合并");
        }
        boolean dtInSearch = SumTypeEnum.DT.getCode().equals(indexQueryReqVO.getSumCondition());
        boolean categoryInSearch = SumTypeEnum.CATEGORY.getCode().equals(indexQueryReqVO.getSumCondition());
        if (!(dtInSearch || categoryInSearch)) {
            throw new BusinessException("只持支单体及末级业态查询");
        }
    }


    private void buildReturnValue(List<ItemIndex> indexDataList, String indexType, List<GcdpDwsIndex> indexData) {
        List<ItemValue> itemValueList = Lists.newArrayList();
        indexData.forEach(item -> {
            IndexValueData indexValueData = new IndexValueData();
            BeanUtils.copyProperties(item, indexValueData);
            Map<String, IndexValueData> indexes = Maps.newHashMap();
            indexes.put(indexType, indexValueData);
            ItemValue itemValue = new ItemValue();
            BeanUtils.copyProperties(item, itemValue);
            itemValue.setIndexes(indexes);
            itemValueList.add(itemValue);
        });
        ItemIndex itemIndex = new ItemIndex(Collections.singletonList(indexType), itemValueList);
        indexDataList.add(itemIndex);
    }


    @Override
    public boolean isShow(ItemData itemData) {
        GcdpDwsIndex index = (GcdpDwsIndex) itemData;
        return MathUtil.notNullAndNotZero(index.getIndexValue()) || MathUtil.notNullAndNotZero(index.getIndexValueIncludeTax());
    }


    private void setMakeup(ItemIndexData itemIndexData, String enterpriseId, ItemIndex itemIndex) throws Exception {
        // 查询所有样本量实体
        List<Long> ids = itemIndexData.getTempNoteIds().stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<MakeUpIndexData> makeUpIndexDataList = projectNoteService.getMakeUpIndexDataList(enterpriseId, ids);
        log.info("数据库中查询到的单体size={}", makeUpIndexDataList.size());
        int defaultSize = 10;
        int size = Math.min(itemIndex.getIndexList().size(), defaultSize);
        CountDownLatch countDownLatch = new CountDownLatch(size);
        AtomicReference<Exception> exceptionRef = new AtomicReference<>();
        for (ItemDataResp itemData : itemIndex.getIndexList()) {
            selectDataServiceExecutor.execute(() -> {
                try {
                    long startTimeInFor = System.currentTimeMillis();
                    //再没有多指标合并的情况下，itemIndex.getType()只有一个指标type
                    String indexType = itemIndex.getType().stream().findFirst().orElse(null);
                    if (StringUtils.isNotBlank(indexType)) {
                        setMakeUpIndexData(itemData, makeUpIndexDataList, indexType);
                    }
                    log.info("科目id={},耗时{}", itemData.getId(), System.currentTimeMillis() - startTimeInFor);
                } catch (Exception e) {
                    log.error("填充科目详情出错 科目数量为={}", itemIndex.getIndexList().size(), e);
                    exceptionRef.set(e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        // 如果发生异常，将异常重新抛出
        if (exceptionRef.get() != null) {
            log.error("标准指标库设置指标组成出错: ", exceptionRef.get());
            throw new BusinessException("标准指标库设置指标组成出错");
        }
    }

    private void setMakeUpIndexData(ItemDataResp itemData, List<MakeUpIndexData> makeUpIndexDataList, String indexType) {
        ItemValue itemValue = (ItemValue) itemData;
        IndexValueData value = itemValue.getIndexes().get(indexType);
        List<MakeUpIndexData> makeUpIndexDataListRes = getMakeUpIndexList(itemData, value.getTempNoteIds(), value.getItemIds(), value.getDictCode(), makeUpIndexDataList);
        value.setResInfoList(makeUpIndexDataListRes);
    }

    private List<MakeUpIndexData> getMakeUpIndexList(ItemDataResp itemData, String ids, String itemIds, String dictCode, List<MakeUpIndexData> makeUpIndexDataList) {
        MakeUpIndexVo makeUpIndexVo = new MakeUpIndexVo();
        makeUpIndexVo.setIds(ids);
        makeUpIndexVo.setItemIds(itemIds);
        makeUpIndexVo.setDictCode(dictCode);
        try {
            List<MakeUpIndexData> dictCodeMakeUpIndexDataList = getListByIds(ids, makeUpIndexDataList);
            MakeupIndexDto makeupIndexDto = makeupIndexService.selectAllMakeupIndexList(makeUpIndexVo, dictCodeMakeUpIndexDataList);
            return makeupIndexDto.getData();
        } catch (Exception e) {
            log.error("处理科目组成数据出错，科目信息为：{}", itemData);
        }
        return Collections.emptyList();
    }

    private List<MakeUpIndexData> getListByIds(String ids, List<MakeUpIndexData> allMakeUpIndexDataList) throws IOException, ClassNotFoundException {
        List<String> idList = Arrays.stream(ids.split(StrPool.COMMA)).collect(Collectors.toList());
        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }
        List<MakeUpIndexData> returnList = new ArrayList<>();
        List<MakeUpIndexData> newMakeUpIndexDataList = allMakeUpIndexDataList.stream().filter(makeUpIndexData -> idList.contains(makeUpIndexData.getIds())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(newMakeUpIndexDataList)) {
            List<MakeUpIndexData> makeUpIndexDataList = ListUtils.deepCopy(newMakeUpIndexDataList);
            // 按照note.phase,note.ld_name_identify/name,note.project_code,note.build_area做合并
            Map<String, MakeUpIndexData> map = new HashMap<>();
            for (MakeUpIndexData makeUpIndexData : makeUpIndexDataList) {
                String ldOrCategoryName = EmptyUtil.isEmpty(makeUpIndexData.getLdNameIdentify())? makeUpIndexData.getNoteName(): makeUpIndexData.getLdNameIdentify();
                String[] keyArr = {makeUpIndexData.getPhase(), ldOrCategoryName, makeUpIndexData.getProjectCode(), makeUpIndexData.getBuildArea()};
                String keyStr = CharSequenceUtil.join(StrPool.COMMA, (Object) keyArr);
                if (!map.containsKey(keyStr)) {
                    map.put(keyStr, makeUpIndexData);
                    returnList.add(makeUpIndexData);
                } else {
                    MakeUpIndexData innerMakeUpIndexData = map.get(keyStr);
                    String itemIds = innerMakeUpIndexData.getIds();
                    String noteName = innerMakeUpIndexData.getNoteName();
                    itemIds = itemIds + StrPool.COMMA + makeUpIndexData.getIds();
                    noteName = noteName + StrPool.COMMA + makeUpIndexData.getNoteName();
                    if (StringUtils.isBlank(innerMakeUpIndexData.getCategoryName())) {
                        innerMakeUpIndexData.setCategoryName(makeUpIndexData.getCategoryName());
                    }
                    innerMakeUpIndexData.setIds(itemIds);
                    innerMakeUpIndexData.setNoteName(noteName);
                }
            }
            return returnList;
        }
        return Collections.emptyList();
    }
}
