package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdp.common.domain.consts.ValueConst;
import com.glodon.gcdp.common.domain.enums.DwsNoteTypeEnums;
import com.glodon.gcdp.common.domain.enums.ItemCostTypeEnums;
import com.glodon.gcdp.common.domain.enums.ProductSource;
import com.glodon.gcdp.common.domain.enums.TypeEnum;
import com.glodon.gcdp.common.domain.model.BaseIndex;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dwdservice.domain.enums.DwdContractProjectExtendJsonFiledNameEnum;
import com.glodon.gcdpindexsearch.common.constant.BusinessConstants;
import com.glodon.gcdpindexsearch.common.enums.IndexTypeCalcEnums;
import com.glodon.gcdpindexsearch.common.enums.IndexTypeEnums;
import com.glodon.gcdpindexsearch.common.enums.IndexTypePrefixEnums;
import com.glodon.gcdpindexsearch.common.enums.JADetailsEnums;
import com.glodon.gcdpindexsearch.common.util.EmptyUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.SumTypeEnum;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwdDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.CommonHandler;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ItemDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.SingleProjectIndexDataService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.SingleProjectIndexReqVO;
import com.glodon.gcdpindexsearch.infrastructure.business.itemapportion.ApportionCalculate;
import com.glodon.gcdpindexsearch.infrastructure.exception.BusinessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.glodon.gcdp.common.domain.enums.DwsNoteTypeEnums.*;
import static com.glodon.gcdpindexsearch.common.constant.ProjectConst.STOP_SIGN;
import static com.glodon.gcdpindexsearch.common.enums.JADetailsEnums.WYT_XNLD;

/**
 * @author: caidj
 * @date: 2023/11/24 15:37
 * @description: 科目维度查询service
 */
@SuppressWarnings("DuplicatedCode")
@Service
@Slf4j
public class SingleProjectIndexDataServiceImpl extends ItemDetailService implements SingleProjectIndexDataService {
    @Resource
    private IDwsIndexDataRepository indexDataRepository;
    @Resource
    private IDwsDataRepository dwsDataRepository;
    @Resource
    private IDwdDataRepository dwdDataRepository;

    @Autowired
    DwsIndexEconomicsDataDetailServiceImpl economicsDataDetailService;

    @Autowired
    DwsIndexMainDataDetailServiceImpl indexMainDataDetailService;

    @Resource()
    private SingleProjectDbIndexPostProcessService indexPostProcessService;

    @Override
    public SingleProjectIndexDataDto getSingleProjectIndexData(SingleProjectIndexReqVO reqVO) {
        this.preCheck(reqVO);
        // 判断是否成本指标库的调用
        boolean isCostZbk = isCostZbk(reqVO);

        if (reqVO.getItemCostType() == null && !isCostZbk) {
            reqVO.setItemCostType(ItemCostTypeEnums.FULL_COST.getItemCostType());
        }

        SingleProjectIndexDataDto singleProjectIndexDataDto = new SingleProjectIndexDataDto();
        // 样本note
        List<DwsIndexProjectNote> noteList = this.getProjectNotes(reqVO);
        // 工程组成
        List<SingleProjectMakeUp> mergeMakeUp = this.getSingleProjectMakeUps(reqVO, noteList);
        singleProjectIndexDataDto.setIndex(mergeMakeUp);

        // 获取全部总造价
        this.calculateAllTotal(noteList, reqVO.getSimpleNoteSumFlag(), singleProjectIndexDataDto);
        List<String> indexTypeList = reqVO.getIndexType().stream().distinct().collect(Collectors.toList());
        // 项目级汇总节点，计算口径是【建造面积】的，值从户口薄的项目规模里取
        setProjectScale(mergeMakeUp, reqVO.getProjectCode(), reqVO.getEnterpriseId());

        // 工程结构
        for (SingleProjectMakeUp makeUp : mergeMakeUp) {
            // 获取部分总造价
            SingleProjectMakeUp totalMakeUp = calculatePartTotal(reqVO.getSumCondition(), singleProjectIndexDataDto, noteList);
            makeUp.setTotalMakeUp(totalMakeUp);
            // 指标数据
            List<ItemIndex> itemIndices = this.assembleIndexData(reqVO, indexTypeList, makeUp, noteList, isCostZbk);
            makeUp.setIndexData(itemIndices);
        }
        // 模版
        singleProjectIndexDataDto.setTemplate(this.getTemplateList(noteList));
        // 样本note
        singleProjectIndexDataDto.setTempNoteIds(this.getTempNoteIds(noteList));
        return singleProjectIndexDataDto;
    }

    private boolean isCostZbk(SingleProjectIndexReqVO reqVO) {
        List<String> productSourceList = reqVO.getProductSource();

        // 有成本指标分析来源、施工成本测算来源，则认为是成本指标的调用
        return CollUtil.isNotEmpty(productSourceList) &&
                (productSourceList.contains(ProductSource.GCDP_COST_ZBSQ_WEB.getIndex()) ||
                        productSourceList.contains(ProductSource.SGCBCS2.getIndex()));
    }


    public List<Template> getTemplateList(List<DwsIndexProjectNote> dwsIndexProjectNotes) {
        List<Template> templates = new ArrayList<>();
        Map<String, String> templateUuidMap = new HashMap<>();
        dwsIndexProjectNotes.forEach(item -> {
            String originalTemplateUuid = item.getOriginalTemplateUuid();
            if (EmptyUtil.isEmpty(originalTemplateUuid)) {
                return;
            }
            String productSource = item.getProductSource();
            if (!templateUuidMap.containsKey(originalTemplateUuid) && EmptyUtil.isNotEmpty(productSource)) {
                templateUuidMap.put(originalTemplateUuid, productSource);
                templates.add(new Template(originalTemplateUuid, productSource));
            }
        });
        return templates;
    }

    private List<ItemIndex> assembleIndexData(SingleProjectIndexReqVO reqVO, List<String> indexTypeList, SingleProjectMakeUp makeUp,
                                              List<DwsIndexProjectNote> noteList, boolean isCostZbk) {

        List<ItemIndex> indexDataList = Lists.newArrayList();
        for (String indexType : indexTypeList) {
            // 如果是单指标
            ItemIndex itemIndex = null;
            if (IndexTypeEnums.INDEX_MAIN.getName().equals(indexType)){
                itemIndex = indexMainDataDetailService.buildItemIndex(reqVO, makeUp);
            } else if (IndexTypeEnums.INDEX_ECONOMIC.getName().equals(indexType)){
                itemIndex = economicsDataDetailService.buildItemIndex(reqVO, makeUp);
            } else {
                // 单指标场景
                itemIndex = buildItemIndex(reqVO, makeUp, noteList, isCostZbk, indexType);
            }
            if (itemIndex == null) {
                continue;
            }

            indexDataList.add(itemIndex);
        }

        return indexDataList;
    }

    /**
     * 单指标查询
     * @param reqVO
     * @param makeUp
     * @param noteList
     * @param isCostZbk
     * @param indexType
     * @return
     */
    private ItemIndex buildItemIndex(SingleProjectIndexReqVO reqVO, SingleProjectMakeUp makeUp, List<DwsIndexProjectNote> noteList, boolean isCostZbk, String indexType) {

        // 含税和不含税指标合并,需要清除含税列出值
        boolean isMultiTypeMerge = needClearIncludeTax(reqVO, noteList, isCostZbk, makeUp);
        // 查询dws_index_data表
        String prefix = IndexTypePrefixEnums.getEnumByType(indexType).getPrefix();
        String calcType = IndexTypeCalcEnums.getEnumByType(indexType).getCalc();
        List<SingleProjectDbIndex> sigleIndexData = indexDataRepository
                .selectDwsIndexByNoteId(prefix, calcType, makeUp.getTempNodeIds(), reqVO.getItemCostType(), makeUp.getOnlyXNLDFlag(), makeUp.getBaseTradeName());
        if (CollUtil.isEmpty(sigleIndexData)) {
            return null;
        }
        // 构建子父级
        super.makePid(sigleIndexData);
        if (!isCostZbk) {
            // 后置处理
            indexPostProcessService.processParentIndex(sigleIndexData, indexType);
        }

        // 计算指标值
        this.calculateIndexValue(sigleIndexData, makeUp, indexType, reqVO.getProductSource(), isMultiTypeMerge, reqVO.getSimpleNoteSumFlag());

        // 生成科目合计行
        SingleProjectDbIndex summaryRowData = calcIndexSummary(makeUp, indexType, noteList, isCostZbk, reqVO.getItemCostType());

        // 过滤无效数据
        if (!Objects.equals(reqVO.getShowAll(), ValueConst.TRUE)) {
            sigleIndexData = super.filterInvalidData(sigleIndexData);
        }

        // 生成编码
        super.makeCode(sigleIndexData);

        // 设置合计行人材机信息
        calcIndexSummaryManMachineMaterial(summaryRowData, sigleIndexData, makeUp, reqVO.getItemCostType(), reqVO.getProductSource());

        // 构造返回结构
        ItemIndex itemIndex = this.buildReturnValue(indexType, sigleIndexData, summaryRowData);
        return itemIndex;
    }

    /**
     * 判断是否需要清除含税标识
     * @param reqVO
     * @param noteList
     * @param isCostZbsq
     * @param makeUp : 合并单体信息
     * @return
     */
    private boolean needClearIncludeTax(SingleProjectIndexReqVO reqVO, List<DwsIndexProjectNote> noteList, boolean isCostZbsq, SingleProjectMakeUp makeUp) {
        if (isCostZbsq){
            return false;
        }

        boolean isMultiTaxMerge = false;
        // 检查是否是含税+不含税指标合并(排除虚拟节点,同一个工程会存在虚拟节点不含税(其他或措施)+其他节点含税+不含税的场景,按产品需求忽略虚拟节点影响,考虑让用户做费用分摊)
        List<DwsIndexProjectNote> makeupNoteList = noteList.stream().filter(noteItem -> makeUp.getTempNodeIds().contains(noteItem.getId()) &&
                (!Objects.equals(noteItem.getType(), TypeEnum.VIRTUAL_NODE.getIndex()))).collect(Collectors.toList());
        if (CollUtil.isEmpty(makeupNoteList)){
            return false;
        }
        List<Integer> flags = makeupNoteList.stream().map(dwsIndexProjectNote ->{
            String extendJson = dwsIndexProjectNote.getExtendDataJson();
            if (!StringUtils.isEmpty(extendJson)){
                JSONObject object = JSON.parseObject(extendJson);
                if (Objects.equals(ItemCostTypeEnums.FULL_COST.getItemCostType(), reqVO.getItemCostType())){
                    return object.getInteger(DwdContractProjectExtendJsonFiledNameEnum.FULL_COST_TAX_FLAG.getName());
                } else if (Objects.equals(ItemCostTypeEnums.NON_FULL_COST.getItemCostType(), reqVO.getItemCostType())){
                    return object.getInteger(DwdContractProjectExtendJsonFiledNameEnum.NON_FULL_COST_TAX_FLAG.getName());
                }

                return null;
            }

            return null;
        }).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        if (flags.size() > 1){
            isMultiTaxMerge = true;
        }

        return isMultiTaxMerge;
    }

    private void calcIndexSummaryManMachineMaterial(SingleProjectDbIndex summaryRowData, List<SingleProjectDbIndex> sigleIndexData,
                                                    SingleProjectMakeUp makeUp, Integer itemCostType, List<String> productSource) {
        if (summaryRowData == null) {
            return;
        }
        ApportionCalculate calculate = ApportionCalculate.apply(productSource);
        if (!calculate.isApportion()) {
            // 设置默认值
            summaryRowData.setPercentLaborAmount(CharConst.LINE);
            summaryRowData.setPercentMachineAmount(CharConst.LINE);
            summaryRowData.setPercentMaterialAmount(CharConst.LINE);
            summaryRowData.setPercentOtherAmount(CharConst.LINE);
            return;
        }
        summaryRowData.setItemCostType(itemCostType);
        Map<Long, List<SingleProjectDbIndex>> idMap = sigleIndexData.stream().collect(Collectors.groupingBy(BaseIndex::getPid));
        for (SingleProjectDbIndex sigleIndexDatum : sigleIndexData) {
            List<SingleProjectDbIndex> singleProjectDbIndices = idMap.get(sigleIndexDatum.getId());
            if (CollUtil.isEmpty(singleProjectDbIndices)) {
                summaryRowData.setLaborAmount((MathUtil.addWithNoDeal(summaryRowData.getLaborAmount(), sigleIndexDatum.getLaborAmount())));
                summaryRowData.setMaterialAmount(MathUtil.addWithNoDeal(summaryRowData.getMaterialAmount(), sigleIndexDatum.getMaterialAmount()));
                summaryRowData.setMachineAmount(MathUtil.addWithNoDeal(summaryRowData.getMachineAmount(), sigleIndexDatum.getMachineAmount()));
                summaryRowData.setOtherAmount(MathUtil.addWithNoDeal(summaryRowData.getOtherAmount(), sigleIndexDatum.getOtherAmount()));
            }
        }
        BigDecimal costTotal;
        BigDecimal costTotalIncludeTax;
        if (ItemCostTypeEnums.FULL_COST.getItemCostType() == itemCostType) {
            costTotal = makeUp.getFullCostTotal();
            costTotalIncludeTax = makeUp.getFullCostTotalIncludeTax();
        } else {
            costTotal = makeUp.getNonFullCostTotal();
            costTotalIncludeTax = makeUp.getNonFullCostTotalIncludeTax();
        }
        summaryRowData.setPercentLaborAmount(MathUtil.toPercent(calculate.calculateApportion(summaryRowData.getLaborAmount(), MathUtil.zeroToDefault(costTotalIncludeTax, costTotal))));
        summaryRowData.setPercentMachineAmount(MathUtil.toPercent(calculate.calculateApportion(summaryRowData.getMachineAmount(), MathUtil.zeroToDefault(costTotalIncludeTax, costTotal))));
        summaryRowData.setPercentMaterialAmount(MathUtil.toPercent(calculate.calculateApportion(summaryRowData.getMaterialAmount(), MathUtil.zeroToDefault(costTotalIncludeTax, costTotal))));
        summaryRowData.setPercentOtherAmount(MathUtil.toPercent(calculate.calculateApportion(summaryRowData.getOtherAmount(), MathUtil.zeroToDefault(costTotalIncludeTax, costTotal))));
    }

    private SingleProjectMakeUp calculatePartTotal(Integer sumCondition, SingleProjectIndexDataDto singleProjectIndexDataDto, List<DwsIndexProjectNote> noteList) {
        SingleProjectMakeUp totalMakeUp = new SingleProjectMakeUp();
        if (Objects.equals(SumTypeEnum.PROJECT.getCode(), sumCondition)){
            totalMakeUp.setTotal(singleProjectIndexDataDto.getTotal());
            totalMakeUp.setTotalIncludeTax(singleProjectIndexDataDto.getTotalIncludeTax());
            totalMakeUp.setFullCostTotal(singleProjectIndexDataDto.getFullCostTotal());
            totalMakeUp.setFullCostTotalIncludeTax(singleProjectIndexDataDto.getFullCostTotalIncludeTax());
            totalMakeUp.setNonFullCostTotal(singleProjectIndexDataDto.getNonFullCostTotal());
            totalMakeUp.setNonFullCostTotalIncludeTax(singleProjectIndexDataDto.getNonFullCostTotalIncludeTax());
            return totalMakeUp;
        }

        // 累加总造价
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal totalIncludeTax = BigDecimal.ZERO;
        BigDecimal fullCostTotal = BigDecimal.ZERO;
        BigDecimal fullCostTotalIncludeTax = BigDecimal.ZERO;
        BigDecimal nonFullCostTotal = BigDecimal.ZERO;
        BigDecimal nonFullCostTotalIncludeTax = BigDecimal.ZERO;
        for (DwsIndexProjectNote note : noteList) {
            total = MathUtil.add(total, note.getTotal());
            totalIncludeTax = MathUtil.add(totalIncludeTax, note.getTotalIncludeTax());
            fullCostTotal = MathUtil.add(fullCostTotal, note.getFullCostTotal());
            fullCostTotalIncludeTax = MathUtil.add(fullCostTotalIncludeTax, note.getFullCostTotalIncludeTax());
            nonFullCostTotal = MathUtil.add(nonFullCostTotal, note.getNonFullCostTotal());
            nonFullCostTotalIncludeTax = MathUtil.add(nonFullCostTotalIncludeTax, note.getNonFullCostTotalIncludeTax());
        }
        totalMakeUp.setTotal(total);
        totalMakeUp.setTotalIncludeTax(totalIncludeTax);
        totalMakeUp.setFullCostTotal(fullCostTotal);
        totalMakeUp.setFullCostTotalIncludeTax(fullCostTotalIncludeTax);
        totalMakeUp.setNonFullCostTotal(nonFullCostTotal);
        totalMakeUp.setNonFullCostTotalIncludeTax(nonFullCostTotalIncludeTax);
        return totalMakeUp;
    }

    /**
     * 处理需要合并的工程节点
     * @param reqVO
     * @param noteList: gcdp_dws_index_project_note中筛选出的记录
     * @return
     */
    private List<SingleProjectMakeUp> getSingleProjectMakeUps(SingleProjectIndexReqVO reqVO, List<DwsIndexProjectNote> noteList) {
        // 工程组成
        List<SingleProjectMakeUp> singleProjectMakeUps = this.convertSingleProjectMakeUpList(reqVO.getSumCondition(), noteList);
        // 过滤组成
        singleProjectMakeUps = this.filterMakeUp(reqVO, singleProjectMakeUps);
        // 工程组成聚合
        return this.mergeMakeUp(singleProjectMakeUps);
    }

    /**
     * 按请求类型从gcdp_dws_index_project_note表筛选符合条件的工程节点,
     * 建安的工程节点需要与请求参数的造价阶段一致
     * @param reqVO: 请求参数
     * @return
     */
    private List<DwsIndexProjectNote> getProjectNotes(SingleProjectIndexReqVO reqVO) {
        // note过滤校验
        List<DwsIndexProjectNote> noteList = dwsDataRepository.selectNoteBySingleProjectReqVO(reqVO);

        // 相同楼栋工程分类同步
        CommonHandler.sameLdCategorySync(noteList);
        return noteList;
    }

    private List<SingleProjectMakeUp> filterMakeUp(SingleProjectIndexReqVO reqVO, List<SingleProjectMakeUp> singleProjectMakeUps) {
        List<SingleProjectIndexReqVO.MakeUp> queryChildren = reqVO.getQueryChildren();
        if (CollUtil.isNotEmpty(queryChildren)){
            Set<String> typeNameSet = queryChildren.stream().map(x -> x.getType() + CharConst.DOUBLE_AT + x.getName()).collect(Collectors.toSet());
            singleProjectMakeUps = singleProjectMakeUps.stream().filter(x -> typeNameSet.contains(x.getType() + CharConst.DOUBLE_AT + x.getName())).collect(Collectors.toList());
        }
        return singleProjectMakeUps;
    }


    /**
     * 计算科目的指标数据: 指标值、父级占比、科目合价
     *
     * @param singleIndexData
     * @param makeUp
     * @param indexType
     * @param productSource
     */
    private void calculateIndexValue(List<SingleProjectDbIndex> singleIndexData,
                                     SingleProjectMakeUp makeUp,
                                     String indexType,
                                     List<String> productSource,
                                     boolean isMultiTypeMerge,
                                     Integer simpleNoteSumFlag) {
        // 父项合价
        Map<Long, SingleProjectDbIndex> idAndAmount = singleIndexData.stream()
                .collect(Collectors.toMap(SingleProjectDbIndex::getId, Function.identity(), (v1, v2) -> v2));

//        Map<Long, List<ItemData>> parentIndexMap = singleIndexData.stream().collect(Collectors.groupingBy(ItemData::getPid));
        SingleProjectMakeUp totalMakeUp = makeUp.getTotalMakeUp();
        boolean jaNodeFlag = Objects.equals(makeUp.getType(), JA.getName());
        BigDecimal projectScale = makeUp.getProjectScale();

        // 工程维度:项目规模
        // 业态维度累加
        singleIndexData.forEach(item -> {
            BigDecimal calcValue = getCalcValue(item);
            // 项目级汇总节点，计算口径是【建造面积】的，值从户口薄的项目规模里取；实物量单方的分母是工程量，目标成本的工程量名称可以自定义，也可能叫【建筑面积】
            // 成本指标含量取
            if (jaNodeFlag && simpleNoteSumFlag == ValueConst.FALSE && CharConst.BUILD_AREA.equals(item.getCalcName()) && !Objects.equals(indexType, IndexTypeCalcEnums.SWLDF.getType())) {
                calcValue = projectScale;
            }
            // 计算口径重新赋值
            item.setCalcValue(calcValue);

            // 指标值 = 科目合价/计算口径
            if (MathUtil.notNullAndNotZero(item.getIndexFZ()) && MathUtil.notNullAndNotZero(calcValue)){
                item.setIndexValue(MathUtil.div(item.getIndexFZ(), calcValue));
            }

            if (isMultiTypeMerge){
                item.setAmountIncludeTax(null);
            } else {
                if (MathUtil.notNullAndNotZero(item.getIndexFZIncludeTax()) && MathUtil.notNullAndNotZero(calcValue)){
                    item.setIndexValueIncludeTax(MathUtil.div(item.getIndexFZIncludeTax(), calcValue));
                }
            }

            // 父项合价
            BigDecimal parentAmount = item.getPid().equals(-1L) ? item.getAmount() : idAndAmount.get(item.getPid()).getAmount();
            BigDecimal parentAmountIncludeTax = item.getPid().equals(-1L) ? item.getAmountIncludeTax() : idAndAmount.get(item.getPid()).getAmountIncludeTax();
            item.setParentAmount(parentAmount);
            item.setParentAmountIncludeTax(parentAmountIncludeTax);
            // 父级占比指标 科目合价/父级科目合价
            item.setParentPercent(MathUtil.toPercent(MathUtil.divStr(item.getAmount(), parentAmount)));
            item.setParentPercentIncludeTax(MathUtil.toPercent(MathUtil.divStr(item.getAmountIncludeTax(), parentAmountIncludeTax)));

            // 总造价占比指标 科目合价/总造价
            item.setTotalPercent(MathUtil.toPercent(MathUtil.divStr(item.getAmount(), totalMakeUp.getTotal())));
            item.setTotalPercentIncludeTax(MathUtil.toPercent(MathUtil.divStr(item.getAmountIncludeTax(), totalMakeUp.getTotalIncludeTax())));
            // 全费非全费
            item.setFullCostTotalPercent(MathUtil.toPercent(MathUtil.divStr(item.getAmount(), totalMakeUp.getFullCostTotal())));
            item.setFullCostTotalPercentIncludeTax(MathUtil.toPercent(MathUtil.divStr(item.getAmountIncludeTax(), totalMakeUp.getFullCostTotalIncludeTax())));
            item.setNonFullCostTotalPercent((MathUtil.toPercent(MathUtil.divStr(item.getAmount(), totalMakeUp.getNonFullCostTotal()))));
            item.setNonFullCostTotalPercentIncludeTax((MathUtil.toPercent(MathUtil.divStr(item.getAmountIncludeTax(), totalMakeUp.getNonFullCostTotalIncludeTax()))));

            //樣本Id
            item.setItemIds(item.getItemIds());
            // 设置费用组成
            setManMachineMaterial(productSource, item);
            // 累计末级科目合价
//            if (!parentIndexMap.containsKey(item.getId())){
//                makeUp.setAmountOfIndex(MathUtil.add(makeUp.getAmountOfIndex(), item.getAmount()));
//                makeUp.setAmountIncludeTaxOfIndex(MathUtil.add(makeUp.getAmountIncludeTaxOfIndex(), item.getAmountIncludeTax()));
//            }
        });
    }

    private static BigDecimal getCalcValue(SingleProjectDbIndex item) {
        if (item.getCalcValue() != null) {
            return item.getCalcValue();
        }

        if (item.getTradeNames() == null || StringUtil.isBlank(item.getBaseTradeName())) {
            return null;
        }

        BigDecimal xnldCalcValue = BigDecimal.ZERO;
        String[] tradeNameArr = item.getTradeNames().split(CharConst.COMMA);
        String[] xnldCalcValueArr = item.getXnldCalcValues().split(CharConst.COMMA);

        for (int i = 0; i < tradeNameArr.length; i++) {
            if (tradeNameArr[i].equals(item.getBaseTradeName())) {
                BigDecimal bigDecimal = StringUtils.isBlank(xnldCalcValueArr[i]) ? BigDecimal.ZERO : new BigDecimal(xnldCalcValueArr[i]);
                xnldCalcValue = xnldCalcValue.add(bigDecimal);
            }
        }

        return xnldCalcValue;
    }

    private void setManMachineMaterial(List<String> productSource, SingleProjectDbIndex item) {
        ApportionCalculate calculate = ApportionCalculate.apply(productSource);
        if (!calculate.isApportion()) {
            item.setLaborAmount(null);
            item.setMachineAmount(null);
            item.setMaterialAmount(null);
            item.setOtherAmount(null);
        }
        item.setPercentLaborAmount(MathUtil.toPercent(calculate.calculateApportion(item.getLaborAmount(), MathUtil.zeroToDefault(item.getAmountIncludeTax(), item.getAmount()))));
        item.setPercentMachineAmount(MathUtil.toPercent(calculate.calculateApportion(item.getMachineAmount(), MathUtil.zeroToDefault(item.getAmountIncludeTax(), item.getAmount()))));
        item.setPercentMaterialAmount(MathUtil.toPercent(calculate.calculateApportion(item.getMaterialAmount(), MathUtil.zeroToDefault(item.getAmountIncludeTax(), item.getAmount()))));
        item.setPercentOtherAmount(MathUtil.toPercent(calculate.calculateApportion(item.getOtherAmount(), MathUtil.zeroToDefault(item.getAmountIncludeTax(), item.getAmount()))));
    }

    /**
     * 计算科目合计行数据
     * @param makeUp
     * @param indexType
     * @return
     */
    private SingleProjectDbIndex calcIndexSummary(SingleProjectMakeUp makeUp, String indexType, List<DwsIndexProjectNote> noteList, Boolean isCostZbsq, Integer itemCostType) {
        // 成本指标不需要合计行
        if (isCostZbsq){
            return null;
        }

        // 只处理"建模单方"、"实物量单方"、"单方指标"
        if (!IndexTypeCalcEnums.JMDF.getType().equals(indexType) && !IndexTypeCalcEnums.DFZB.getType().equals(indexType)
                && !IndexTypeCalcEnums.SWLDF.getType().equals(indexType)){
            return null;
        }

        BigDecimal buildArea = null;
        String calcName = "建筑面积";
        String calcUnit = "m2";
        // 建安、非建安、其他建安取项目规模
        if (Objects.equals(makeUp.getType(), JA.getName()) ||
                (Objects.equals(makeUp.getType(), FJA.getName())) ||
                (Objects.equals(makeUp.getType(), BusinessConstants.QTJA))){
            calcName = "项目规模";
            calcUnit = makeUp.getProjectScaleUnit();
            buildArea = makeUp.getProjectScale();
        } else {
            // 计算建筑面积
            if (ItemCostTypeEnums.FULL_COST.getItemCostType() == itemCostType) {
                List<DwsIndexProjectNote> makeupNodeList = noteList.stream()
                        .filter(projectNote -> makeUp.getTempNodeIds().contains(projectNote.getId()))
                        .filter(projectNote -> projectNote.getItemCostType() == ItemCostTypeEnums.FULL_COST.getItemCostType() ||
                                 projectNote.getItemCostType() == ItemCostTypeEnums.FULL_AND_NOT_FULL_COST.getItemCostType())
                        .collect(Collectors.toList());
                buildArea = CommonHandler.calcBuildArea(makeupNodeList);
            }
            if (ItemCostTypeEnums.NON_FULL_COST.getItemCostType() == itemCostType) {
                List<DwsIndexProjectNote> makeupNodeList = noteList.stream()
                        .filter(projectNote -> makeUp.getTempNodeIds().contains(projectNote.getId()))
                        .filter(projectNote -> projectNote.getItemCostType() == ItemCostTypeEnums.NON_FULL_COST.getItemCostType() ||
                                projectNote.getItemCostType() == ItemCostTypeEnums.FULL_AND_NOT_FULL_COST.getItemCostType())
                        .collect(Collectors.toList());
                buildArea = CommonHandler.calcBuildArea(makeupNodeList);
            }
        }

        SingleProjectDbIndex summaryDetail = new SingleProjectDbIndex();

        if (ItemCostTypeEnums.FULL_COST.getItemCostType() == itemCostType) {
            summaryDetail.setAmount(makeUp.getFullCostTotal());
            summaryDetail.setAmountIncludeTax(makeUp.getFullCostTotalIncludeTax());
        }
        if (ItemCostTypeEnums.NON_FULL_COST.getItemCostType() == itemCostType) {
            summaryDetail.setAmount(makeUp.getNonFullCostTotal());
            summaryDetail.setAmountIncludeTax(makeUp.getNonFullCostTotalIncludeTax());
        }

        // 实物量单方不需要计算指标
        if (IndexTypeCalcEnums.SWLDF.getType().equals(indexType)){
            return summaryDetail;
        } else {
            summaryDetail.setCalcName(calcName);
            summaryDetail.setCalcValue(buildArea);
            summaryDetail.setCalcUnit(calcUnit);
            summaryDetail.setIndexUnit(String.format("元/%s", calcUnit));
        }
        summaryDetail.setIndexValue(MathUtil.div(summaryDetail.getAmount(), summaryDetail.getCalcValue()));
        summaryDetail.setIndexValueIncludeTax(MathUtil.div(summaryDetail.getAmountIncludeTax(), summaryDetail.getCalcValue()));
        return summaryDetail;
    }

    /**
     * 根据工程查询维度标识,确定需要合并的工程列表,合并规则: (YT|LD)+@@+工程节点名称
     * @param makeUpList
     * @return
     */
    private List<SingleProjectMakeUp> mergeMakeUp(List<SingleProjectMakeUp> makeUpList) {
        Map<String, SingleProjectMakeUp> makeUpMap = Maps.newHashMap();
        for (SingleProjectMakeUp makeUp : makeUpList) {
            String key = makeUp.getType() + CharConst.DOUBLE_AT + makeUp.getName();
            if (makeUpMap.containsKey(key)) {
                SingleProjectMakeUp existingMakeUp = makeUpMap.get(key);
                existingMakeUp.getTempNodeIds().add(makeUp.getTempNodeId());
                existingMakeUp.getDisplayNameSet().add(makeUp.getDisplayName());
                existingMakeUp.setDisplayName(String.join(STOP_SIGN, existingMakeUp.getDisplayNameSet()));
                setAmount(existingMakeUp, makeUp);
            } else {
                makeUp.getTempNodeIds().add(makeUp.getTempNodeId());
                makeUp.getDisplayNameSet().add(makeUp.getDisplayName());
                setAmount(new SingleProjectMakeUp() , makeUp);
                makeUpMap.put(key, makeUp);
            }
        }

        setBaseTradeName(makeUpList, makeUpMap);

        return new ArrayList<>(makeUpMap.values());
    }

    private void setBaseTradeName(List<SingleProjectMakeUp> makeUpList, Map<String, SingleProjectMakeUp> makeUpMap) {
        Map<String, List<SingleProjectMakeUp>> group = makeUpList.stream().collect(Collectors.groupingBy(x -> x.getType() + CharConst.DOUBLE_AT + x.getName()));

        for (Map.Entry<String, List<SingleProjectMakeUp>> entry : group.entrySet()) {
            SingleProjectMakeUp makeUp = makeUpMap.get(entry.getKey());
            boolean allXNLDFlag = entry.getValue().stream().allMatch(x -> x.getOriginalType().equals(XN_LD.getName()));
            makeUp.setOnlyXNLDFlag(allXNLDFlag);
            if (allXNLDFlag) {
                SingleProjectMakeUp first = CollUtil.getFirst(entry.getValue());
                makeUp.setBaseTradeName(first.getTradeName());
            }
        }
    }

    private void setAmount(SingleProjectMakeUp existingMakeUp,SingleProjectMakeUp makeUp) {
        existingMakeUp.setTotal(MathUtil.add(existingMakeUp.getTotal(), makeUp.getTotal()));
        existingMakeUp.setTotalIncludeTax(MathUtil.add(existingMakeUp.getTotalIncludeTax(), makeUp.getTotalIncludeTax()));
        existingMakeUp.setFullCostTotal(MathUtil.add(existingMakeUp.getFullCostTotal(), makeUp.getFullCostTotal()));
        existingMakeUp.setFullCostTotalIncludeTax(MathUtil.add(existingMakeUp.getFullCostTotalIncludeTax(), makeUp.getFullCostTotalIncludeTax()));
        existingMakeUp.setNonFullCostTotal(MathUtil.add(existingMakeUp.getNonFullCostTotal(), makeUp.getNonFullCostTotal()));
        existingMakeUp.setNonFullCostTotalIncludeTax(MathUtil.add(existingMakeUp.getNonFullCostTotalIncludeTax(), makeUp.getNonFullCostTotalIncludeTax()));
    }

    private List<SingleProjectMakeUp> convertSingleProjectMakeUpList(Integer sumCondition, List<DwsIndexProjectNote> noteList) {
        List<SingleProjectMakeUp> makeUpList = Lists.newArrayList();

        for (DwsIndexProjectNote note : noteList) {
            DwsNoteTypeEnums noteType = getEnumByIndex(note.getType(), note.getNonConstruction());
            if (noteType == null) {
                continue;
            }

            SingleProjectMakeUp makeUp = new SingleProjectMakeUp();
            makeUp.setTempNodeId(note.getId());
            makeUp.setOriginalType(noteType.getName());

            switch (noteType) {
//                case CATEGORY:
//                    makeUp.setType(JADetailsEnums.YT.getType());
//                    makeUp.setName(note.getProjectCategoryName());
//                    makeUp.setDisplayName(getLastCategoryName(note.getProjectCategoryName()));
//                    break;
                case LD:
                    if (Objects.equals(SumTypeEnum.PROJECT.getCode(), sumCondition)) {
                        if (StringUtils.isNotEmpty(note.getProjectCategoryCode())) {
                            makeUp.setType(JADetailsEnums.YT_LD.getType());
                            makeUp.setName(note.getProjectCategoryName());
                            makeUp.setDisplayName(CommonHandler.getLastCategoryName(note.getProjectCategoryName()));
                        } else {
                            makeUp.setType(JADetailsEnums.WYT_LD.getType());
                            makeUp.setName(note.getLdNameIdentify() + StrPool.BRACKET_START + note.getBuildArea() + StrPool.BRACKET_END);
                            makeUp.setDisplayName(note.getName());
                        }
                    }else if (Objects.equals(SumTypeEnum.CATEGORY.getCode(), sumCondition)){
                        makeUp.setType(CATEGORY.getName());
                        makeUp.setName(note.getProjectCategoryName());
                        makeUp.setDisplayName(CommonHandler.getLastCategoryName(note.getProjectCategoryName()));
                    }else if (Objects.equals(SumTypeEnum.DT.getCode(), sumCondition)){
                        makeUp.setType(LD.getName());
                        makeUp.setName(note.getLdNameIdentify() + StrPool.BRACKET_START + note.getBuildArea() + StrPool.BRACKET_END);
                        makeUp.setDisplayName(note.getName());
                    }
                    break;
//                case TRADE:
//                case CLASSIFY:
                case VIRTUAL:
                    makeUp.setType(BusinessConstants.QTJA);
                    makeUp.setName(BusinessConstants.QTJA_NAME);
                    makeUp.setDisplayName(BusinessConstants.QTJA_NAME);
                    break;
                case FJA:
                    makeUp.setType(FJA.getName());
                    break;
                case XN_LD:
                    makeUp.setTradeName(note.getTradeName());
                    if (Objects.equals(SumTypeEnum.PROJECT.getCode(), sumCondition)) {
                        if (StringUtils.isNotEmpty(note.getProjectCategoryCode())) {
                            makeUp.setType(JADetailsEnums.YT_XNLD.getType());
                            makeUp.setName(note.getProjectCategoryName());
                            makeUp.setDisplayName(CommonHandler.getLastCategoryName(note.getProjectCategoryName()));
                        } else {
                            makeUp.setType(WYT_XNLD.getType());
                            makeUp.setName(note.getTradeName());
                            makeUp.setDisplayName(note.getTradeName());
                        }
                    }else if (Objects.equals(SumTypeEnum.CATEGORY.getCode(), sumCondition)){
                        makeUp.setType(CATEGORY.getName());
                        makeUp.setName(note.getProjectCategoryName());
                        makeUp.setDisplayName(CommonHandler.getLastCategoryName(note.getProjectCategoryName()));
                    }else if (Objects.equals(SumTypeEnum.DT.getCode(), sumCondition)){
                        makeUp.setType(XN_LD.getName());
                        makeUp.setName(note.getLdNameIdentify() + StrPool.BRACKET_START + note.getTradeName() + StrPool.BRACKET_END);
                        makeUp.setDisplayName(note.getName());
                    }
                    break;
                default:
            }
            setTotal(note, makeUp);
            makeUpList.add(makeUp);

            // 建安
            if (Objects.equals(SumTypeEnum.PROJECT.getCode(), sumCondition) && noteType != FJA){
                SingleProjectMakeUp jaMakeUp = new SingleProjectMakeUp();
                jaMakeUp.setTempNodeId(note.getId());
                jaMakeUp.setOriginalType(noteType.getName());
                jaMakeUp.setType(JA.getName());
                jaMakeUp.setTradeName(note.getTradeName());
                setTotal(note, jaMakeUp);
                makeUpList.add(jaMakeUp);
            }
        }

        return makeUpList;
    }

    private void setTotal(DwsIndexProjectNote note, SingleProjectMakeUp makeUp) {
        makeUp.setTotal(note.getTotal());
        makeUp.setTotalIncludeTax(note.getTotalIncludeTax());
        makeUp.setFullCostTotal(note.getFullCostTotal());
        makeUp.setFullCostTotalIncludeTax(note.getFullCostTotalIncludeTax());
        makeUp.setNonFullCostTotal(note.getNonFullCostTotal());
        makeUp.setNonFullCostTotalIncludeTax(note.getNonFullCostTotalIncludeTax());
    }

    /**
     * 计算单指标数据的总造价
     * @param noteList
     * @param simpleNoteSumFlag
     * @param singleProjectIndexDataDto
     */
    private void calculateAllTotal(List<DwsIndexProjectNote> noteList, Integer simpleNoteSumFlag, SingleProjectIndexDataDto singleProjectIndexDataDto) {
        if (CollUtil.isEmpty(noteList)) {
            return;
        }
        BigDecimal[] totalArr = CommonHandler.calculateTotal(simpleNoteSumFlag, noteList);
        singleProjectIndexDataDto.setTotal(totalArr[0]);
        singleProjectIndexDataDto.setTotalIncludeTax(totalArr[1]);
        singleProjectIndexDataDto.setFullCostTotal(totalArr[2]);
        singleProjectIndexDataDto.setFullCostTotalIncludeTax(totalArr[3]);
        singleProjectIndexDataDto.setNonFullCostTotal(totalArr[4]);
        singleProjectIndexDataDto.setNonFullCostTotalIncludeTax(totalArr[5]);
    }


    @Override
    public boolean isShow(ItemData itemData) {
        SingleProjectDbIndex indexValue = (SingleProjectDbIndex) itemData;
        return (MathUtil.notNullAndNotZero(indexValue.getIndexValue()))
                || (MathUtil.notNullAndNotZero(indexValue.getIndexValueIncludeTax()));
    }

    private void preCheck(SingleProjectIndexReqVO reqVO) {
        if (reqVO.isMergeMultiIndex()) {
            throw new BusinessException("暂不支持指标合并");
        }
    }

    private List<String> getTemplateUuid(List<DwsIndexProjectNote> dwsIndexProjectNotes) {
        return dwsIndexProjectNotes.stream().map(DwsIndexProjectNote::getOriginalTemplateUuid).filter(Objects::nonNull).collect(Collectors.toList());
    }
    private List<Long> getTempNoteIds(List<DwsIndexProjectNote> dwsIndexProjectNotes) {
        return dwsIndexProjectNotes.stream().map(DwsIndexProjectNote::getId).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private ItemIndex buildReturnValue(String indexType, List<SingleProjectDbIndex> indexData, SingleProjectDbIndex summaryRowData) {
        List<SingleProjectItemValue> itemValueList = Lists.newArrayList();
        indexData.forEach(item -> {
            SingleProjectIndexValueDto indexValueDto = new SingleProjectIndexValueDto();
            BeanUtils.copyProperties(item, indexValueDto);
            Map<String, SingleProjectIndexValueDto> indexes = Maps.newHashMap();
            indexes.put(indexType, indexValueDto);
            SingleProjectItemValue itemValue = new SingleProjectItemValue();
            BeanUtils.copyProperties(item, itemValue);
            itemValue.setIndexes(indexes);
            itemValueList.add(itemValue);
        });

        // 补充合计行信息
        List<IndexSummary> indexSummaries = null;
        if (summaryRowData != null){
            indexSummaries = new ArrayList<>();
            IndexSummary summary = new IndexSummary();
            Map<String, IndexSummaryValueDto> summaryDetail = new HashMap<>();
            IndexSummaryValueDto indexValue = new IndexSummaryValueDto();
            BeanUtils.copyProperties(summaryRowData, indexValue);
            summaryDetail.put(indexType, indexValue);
            summary.setIndexes(summaryDetail);
            indexSummaries.add(summary);
        }

        return new ItemIndex(Collections.singletonList(indexType), itemValueList, indexSummaries);
    }

    /**
     * 更新"项目户口簿"中的"工程规模"到工程节点
     * @param mergeMakeUp
     * @param projectCode
     * @param enterpriseId
     */
    private void setProjectScale(List<SingleProjectMakeUp> mergeMakeUp, String projectCode, String enterpriseId) {
        // 参数校验
        if (projectCode == null || enterpriseId == null) {
            return;
        }

        // 查询项目信息
        DwdProjectInfo projectInfo = dwdDataRepository.selectByProjectCode(enterpriseId, projectCode);
        if (projectInfo == null){
            return;
        }

        mergeMakeUp.stream().forEach(x->{x.setProjectScale(projectInfo.getProjectScale());x.setProjectScaleUnit(projectInfo.getProjectScaleUnit());});
    }
}
