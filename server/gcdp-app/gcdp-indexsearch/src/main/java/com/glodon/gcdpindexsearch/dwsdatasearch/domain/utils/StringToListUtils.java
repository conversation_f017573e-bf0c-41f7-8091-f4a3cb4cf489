package com.glodon.gcdpindexsearch.dwsdatasearch.domain.utils;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class StringToListUtils {
    private StringToListUtils() {}
    public static List<Long> convertToLongList(String idsString){
        return convertToLongList(idsString, StrUtil.COMMA);
    }

    public static List<Long> convertToLongList(String idsString, String sign){
        if (StringUtils.isEmpty(idsString)){
            return new ArrayList<>();
        }

        return Arrays.stream(idsString.split(sign)).map(Long::parseLong).collect(Collectors.toList());
    }
}
