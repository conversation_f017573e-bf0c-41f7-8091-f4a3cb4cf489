package com.glodon.gcdpindexsearch.infrastructure.advice;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * 耗时统计
 */
@Slf4j
@Aspect
@Component
public class TakeTimeAdvice {

    /**
     * 带有@TakeTime注解的方法
     */
    @Pointcut("@annotation(com.glodon.gcdpindexsearch.infrastructure.annotation.TakeTime)")
    public void logPoint() {
    }

    @Around("logPoint()")
    public Object aroundTakeTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        log.info("方法名为:[{}],方法执行时间为:[{}] ms", className + StrUtil.COLON + methodName, System.currentTimeMillis() - startTime);
        return result;
    }
}
