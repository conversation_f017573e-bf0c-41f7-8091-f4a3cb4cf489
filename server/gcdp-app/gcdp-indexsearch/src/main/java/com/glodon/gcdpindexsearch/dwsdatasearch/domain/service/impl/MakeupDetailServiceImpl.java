package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdp.common.domain.consts.ValueConst;
import com.glodon.gcdp.common.domain.enums.AmountSourceFlagEnum;
import com.glodon.gcdp.common.domain.enums.ProjectCalcStyleOption;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject;
import com.glodon.gcdp.dwdservice.domain.dao.enums.IndexMakeupType;
import com.glodon.gcdp.dwdservice.domain.dao.enums.IndexType;
import com.glodon.gcdp.dwdservice.domain.dao.enums.ResourceType;
import com.glodon.gcdpindexsearch.common.constant.CommonConstants;
import com.glodon.gcdpindexsearch.common.enums.BqItemTypeEnums;
import com.glodon.gcdpindexsearch.common.enums.CBZBKMakeupDetailTypeEnums;
import com.glodon.gcdpindexsearch.common.enums.NormItemTypeEnums;
import com.glodon.gcdpindexsearch.common.enums.ResourceCostType;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.BqItemDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMakeup;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.BqEstiTypeEnum;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexMakeupRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.MakeupDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.MakeupQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.glodon.gcdp.common.domain.enums.ProductSource.SGCBCS2;
import static com.glodon.gcdp.dwdservice.domain.dao.enums.IndexMakeupType.RCJ;
import static com.glodon.gcdpindexsearch.common.enums.CBZBKMakeupDetailTypeEnums.convertType;
import static com.glodon.gcdpindexsearch.dwsdatasearch.domain.utils.StringToListUtils.convertToLongList;

/**
 * <AUTHOR>
 * @date 2022/11/18 13:58
 */
@Service
@Slf4j
public class MakeupDetailServiceImpl implements MakeupDetailService {

    @Autowired
    IDwsIndexMakeupRepository dwsIndexMakeupRepositoryImpl;

    @Override
    public List<MakeupDetailBaseVO> makeupDetail(MakeupQueryVO makeupQueryVO) {
        // 根据不同的指标类型，查询组成的构成的id集合
        List<DwsIndexMakeup> makeupList = makeupByType(makeupQueryVO);
        if (CollUtil.isEmpty(makeupList)) {
            return Collections.emptyList();
        }

        // 根据id查询组成的详细信息
        Map<Integer, Map<Long, MakeupDetailDto>> typeMakeupDetailMap = detailByTypeAndDwdId(makeupList, false);

        if (CollUtil.isEmpty(typeMakeupDetailMap)) {
            log.error("数据制作有误，id有引用，根据id却查不到数据，{}", makeupQueryVO);
            return Collections.emptyList();
        }

        // 组装分配数据
        List<MakeupDetailDto> makeupDetailDtoList = assembleMakeupDetailDto(makeupList, typeMakeupDetailMap);

        // 工料指标组成明细的VO转换和规则处理
        if (isZYYLZB(makeupQueryVO.getIndexType())) {
            return convertMainResMakeupDetailVO(makeupDetailDtoList);
        }

        return new ArrayList<>(makeupDetailDtoList);
    }

    private static boolean isZYYLZB(Integer indexType) {
        return indexType != null && IndexType.ZYYLZB == IndexType.fromIndex(indexType);
    }

    private List<MakeupDetailBaseVO> convertMainResMakeupDetailVO(List<MakeupDetailDto> makeupDetailDtoList) {
        if (CollUtil.isEmpty(makeupDetailDtoList)) {
            return Collections.emptyList();
        }

        return makeupDetailDtoList.stream().map(x -> {
                    MainResMakeupDetailVO mainResMakeupDetailVO = new MainResMakeupDetailVO();
                    BeanUtils.copyProperties(x, mainResMakeupDetailVO);
                    return mainResMakeupDetailVO;
                })
                // 无效数据过滤规则保持不变：①材料名称、单位均不为空；②含税价/除税价不为0/-
                .filter(x -> StringUtils.isNotBlank(x.getName())
                        && StringUtils.isNotBlank(x.getUnit())
                        && (MathUtil.notNullAndNotZero(x.getMarketRate()) || MathUtil.notNullAndNotZero(x.getMarketRateIncludeTax())))
                // 明细数据先按照类别排序（人工、材料、机械、设备、主材、其他），同类别按照编码排列
                .sorted(Comparator.comparing(MainResMakeupDetailVO::getResourceTypeOrd).thenComparing(MainResMakeupDetailVO::getCode))
                .collect(Collectors.toList());
    }

    /**
     * 按楼栋维度返回科目关联的清单明细组成
     * @param makeupQueryVO
     * @return
     */
    @Override
    public List<BqItemDto> ldMakeupDetail(MakeupQueryVO makeupQueryVO) {
        // 根据不同的指标类型，查询组成的构成的id集合
        List<DwsIndexMakeup> dwsIndexMakeupList = this.makeupByType(makeupQueryVO);
        if (CollUtil.isEmpty(dwsIndexMakeupList)) {
            return null;
        }

        // 初始化合同工程的取值字段选项
        Map<Long, ProjectCalcStyleOption> optionsMap = initProjectOptions(dwsIndexMakeupList);

        // key 清单类型 value  dwd_id 清单具体数据
        Map<Integer, Map<Long, MakeupDetailDto>> typeMakeupDetailMap = detailByTypeAndDwdId(dwsIndexMakeupList, true);
        List<BqItemDto> bpItems = new ArrayList<>();
        // 按楼栋id和名称分组
        Map<String, List<DwsIndexMakeup>> dwsBpItemMapping = dwsIndexMakeupList.stream().collect(Collectors.groupingBy(x -> x.getIndexProjectNoteId() + CharConst.DOUBLE_AT + x.getName()));
        for (Map.Entry<String, List<DwsIndexMakeup>> node : dwsBpItemMapping.entrySet()) {
            BqItemDto bqItemDto = new BqItemDto();
            String[] combineKey = node.getKey().split(CharConst.DOUBLE_AT);
            bqItemDto.setId(Long.valueOf(combineKey[0]));
            bqItemDto.setNoteName(combineKey[1]);
            ArrayList<BqItemDto.MakeUp> makeUpList = new ArrayList<>();
            for (DwsIndexMakeup item : node.getValue()) {
                ProjectCalcStyleOption calOption = optionsMap.get(item.getContractProjectId());
                BqItemDto.MakeUp mu = new BqItemDto.MakeUp();
                Long dwdId = item.getDwdId();
                Integer type = item.getType();
                // dws层科目id重新赋值
                mu.setIndexId(item.getDwsIndexId().toString());
                // 赋值工程总造价相关字段
                bqItemDto.setFullCostAmount(item.getFullCostAmount());
                bqItemDto.setFullCostAmountIncludeTax(item.getFullCostAmountIncludeTax());
                bqItemDto.setNonFullCostAmount(item.getNonFullCostAmount());
                bqItemDto.setNonFullCostAmountIncludeTax(item.getNonFullCostAmountIncludeTax());

                if (!typeMakeupDetailMap.containsKey(type)) {
                    continue;
                }
                Map<Long, MakeupDetailDto> dwdMappingMap = typeMakeupDetailMap.get(type);
                if (!dwdMappingMap.containsKey(dwdId)) {
                    continue;
                }
                MakeupDetailDto dwdMakeupDetail = dwdMappingMap.get(dwdId);
                if (dwdMakeupDetail == null) {
                    continue;
                }
                // 计算具体科目数据  只有定额有这种计价类型
                if (BqEstiTypeEnum.DEJJGLF.getCode().equals(dwdMakeupDetail.getEstiType()) || BqEstiTypeEnum.DEJJFQDF.getCode().equals(dwdMakeupDetail.getEstiType())) {
                    if ((calOption != null) && (AmountSourceFlagEnum.NORM_AMOUNT == calOption.getAmountFlag())){
                        dwdMakeupDetail.setRate(dwdMakeupDetail.getGlRate());
                        dwdMakeupDetail.setAmount(dwdMakeupDetail.getGlAmount());
                    } else {
                        // 默认情况下用rate、amount
                        dwdMakeupDetail.setRate(dwdMakeupDetail.getFqdRate());
                        dwdMakeupDetail.setAmount(dwdMakeupDetail.getFqdAmount());
                    }
                }

                BigDecimal ldQuantity = item.getLdQuantity();
                // 市场化计价来源的清单，清单上的工程量是总的工程量，不是楼栋的工程量。需要从_makeup表里的ld_quantity楼栋工程量取值，重新计算。
                // 约定根据ld_quantity是否为空来判断从dws层取还是从dwd层取,楼栋具体工程量从dws层而来
                if (ldQuantity != null) {
                    dwdMakeupDetail.setQuantity(ldQuantity);
                    dwdMakeupDetail.setAmountIncludeTax(MathUtil.mul(ldQuantity, dwdMakeupDetail.getRateIncludeTax()));
                    dwdMakeupDetail.setAmount(MathUtil.mul(ldQuantity, dwdMakeupDetail.getRate()));
                }
                assembleMakeUp(mu, dwdMakeupDetail, type, optionsMap.get(item.getContractProjectId()));

                List<BqItemDto.MakeUp> children = buildDetailMakeupItems(dwdMakeupDetail, calOption, ldQuantity);
                mu.setChildren(children);
                makeUpList.add(mu);
            }
            bqItemDto.setMakeUpList(makeUpList);
            bpItems.add(bqItemDto);
        }
        return bpItems;
    }

    /**
     * 初始化工程的选项信息,用于确定子目、材料的取值字段
     * @param dwsBqItemDtos
     * @return
     */
    private Map<Long, ProjectCalcStyleOption> initProjectOptions(List<DwsIndexMakeup> dwsBqItemDtos) {
        List<Long> contractProjectIds = dwsBqItemDtos.stream().map(DwsIndexMakeup::getContractProjectId).collect(Collectors.toList());
        List<DwdContractProject> contractExtendOptions = dwsIndexMakeupRepositoryImpl.selectContractProjectInfo(contractProjectIds);
        Map<Long, ProjectCalcStyleOption> optionsMap = new HashMap<>();
        contractExtendOptions.forEach(contractProject->{
            ProjectCalcStyleOption option = ProjectCalcStyleOption.init(contractProject.getExtendDataJson());
            if (option != null){
                optionsMap.put(contractProject.getId(), option);
            }
        });
        return optionsMap;
    }

    /**
     * 转换明细组成为makeup
     * @param dwdMakeupDetail
     * @param option: contract_project表中的扩展选项
     * @return
     */
    List<BqItemDto.MakeUp> buildDetailMakeupItems(MakeupDetailDto dwdMakeupDetail, ProjectCalcStyleOption option, BigDecimal ldQuantity){
        if (!CollUtil.isEmpty(dwdMakeupDetail.getNormItems())){
            return convertNormItemToMakeUp(dwdMakeupDetail.getNormItems(), option);
        } else if (!CollUtil.isEmpty(dwdMakeupDetail.getLmmDetailItems())){
            return convertLmmDetailToMakeUp(dwdMakeupDetail.getLmmDetailItems(), option, ldQuantity);
        }

        return null;
    }

    /**
     * 转化工料消耗为组成明细
     * @param lmmItems
     * @param option: contract_project表中的扩展选项
     * @param ldQuantity: 市场化计价的楼栋工程量
     * @return
     */
    List<BqItemDto.MakeUp> convertLmmDetailToMakeUp(List<MaterialDTO> lmmItems, ProjectCalcStyleOption option, BigDecimal ldQuantity){
        List<BqItemDto.MakeUp> lmmMakeupItems = new ArrayList<>();
        for (MaterialDTO materialDTO : lmmItems){
            BqItemDto.MakeUp makeUp = new BqItemDto.MakeUp();
            makeUp.setCode(materialDTO.getCode());
            makeUp.setId(Long.parseLong(materialDTO.getId()));
            makeUp.setPid(Long.parseLong(materialDTO.getPid()));
            makeUp.setSpec(materialDTO.getSpec());
            makeUp.setUnit(materialDTO.getUnit());
            if ((option == null) || (option.getResourceFlag() == null)){
                makeUp.setRate(materialDTO.getMarketRate());
            } else {
                switch (option.getResourceFlag()){
                    case BUDGET:
                        makeUp.setRate(materialDTO.getBudgetRate());
                        break;
                    case TAX_BUDGET:
                        makeUp.setRate(materialDTO.getBudgetTaxRate());
                        break;
                    case TAX_MARKET:
                        makeUp.setRate(materialDTO.getMarketTaxRate());
                        break;
                    case MARKET:
                    default:
                        makeUp.setRate(materialDTO.getMarketRate());
                }
            }
            if (ldQuantity != null){
                makeUp.setQuantity(MathUtil.mul(ldQuantity, materialDTO.getUsage()));
            } else {
                makeUp.setQuantity(materialDTO.getQuantity());
            }
            makeUp.setAmount(MathUtil.mul(makeUp.getQuantity(), makeUp.getRate()));
            makeUp.setBqItemType(ResourceCostType.getByIndex(materialDTO.getType()).getName());
            makeUp.setMakeupType(IndexMakeupType.LMM.getIndex());
            makeUp.setItemType(materialDTO.getType());
            makeUp.setName(materialDTO.getName());
            lmmMakeupItems.add(makeUp);
        }

        return lmmMakeupItems;
    }

    /**
     * 子目转换为makeup
     * @param normItems
     * @param option: contract_project表中的扩展选项
     * @return
     */
    List<BqItemDto.MakeUp> convertNormItemToMakeUp(List<MakeupDetailDto> normItems, ProjectCalcStyleOption option){
        List<BqItemDto.MakeUp> normMakeupItems = new ArrayList<>();
        for (MakeupDetailDto normItem : normItems){
            BqItemDto.MakeUp makeUp = new BqItemDto.MakeUp();
            makeUp.setName(normItem.getName());
            assembleMakeUp(makeUp, normItem, IndexMakeupType.NORM.getIndex(), option);
            // 转换工料明细为makeup
            if (!CollUtil.isEmpty(normItem.getLmmDetailItems())){
                List<BqItemDto.MakeUp> lmmMakeUp = convertLmmDetailToMakeUp(normItem.getLmmDetailItems(), option, null);
                makeUp.setChildren(lmmMakeUp);
            }

            normMakeupItems.add(makeUp);
        }

        return normMakeupItems;
    }

    private void assembleMakeUp(BqItemDto.MakeUp mu, MakeupDetailDto dwdMakeupDetail, Integer type, ProjectCalcStyleOption option) {
        mu.setId(dwdMakeupDetail.getId());
        mu.setQuantity(dwdMakeupDetail.getQuantity());
        mu.setName(dwdMakeupDetail.getName());

        mu.setRate(dwdMakeupDetail.getRate());
        mu.setUnit(dwdMakeupDetail.getUnit());
        mu.setSpec(dwdMakeupDetail.getSpec());
        mu.setAmount(dwdMakeupDetail.getAmount());
        //mu.setIndexId();

        switch (IndexMakeupType.getTypeName(type)) {
            case CSXM:
            case FBFX:
                // 清单、措施
                if (dwdMakeupDetail.getBqItemType() == null){
                    mu.setBqItemType(BqItemTypeEnums.XIANG.getName());
                    mu.setItemType(BqItemTypeEnums.XIANG.getIndex());
                } else {
                    mu.setBqItemType(BqItemTypeEnums.getTypeName(dwdMakeupDetail.getBqItemType()).getName());
                    mu.setItemType(dwdMakeupDetail.getBqItemType());
                }
                // 设置当前行的数据类型
                mu.setMakeupType(type);
                break;
            case NORM:
                // 定额
                mu.setBqItemType(NormItemTypeEnums.getTypeName(dwdMakeupDetail.getNormItemType()).getName());
                mu.setItemType(dwdMakeupDetail.getNormItemType());
                // 设置当前行的数据类型
                mu.setMakeupType(type);
                break;
            default:
                break;
        }
        mu.setCode(dwdMakeupDetail.getCode());
    }

    /**
     * 根据不同的指标类型，查询组成的构成
     *
     * @param makeupQueryVO 查询参数
     * @return dws make up关系
     */
    private List<DwsIndexMakeup> makeupByType(MakeupQueryVO makeupQueryVO) {
        Integer indexType = Optional.ofNullable(makeupQueryVO.getIndexType()).orElse(IndexType.CBZB.getIndex());
        List<Long> noteIds = convertToLongList(makeupQueryVO.getIds());
        List<Long> itemIds = convertToLongList(makeupQueryVO.getItemIds());

        if (CollUtil.isEmpty(noteIds) || CollUtil.isEmpty(itemIds)) {
            return new ArrayList<>();
        }

        List<DwsIndexMakeup> makeupList = new ArrayList<>();
        // 根据不同的指标类型，查询组成
        switch (IndexType.fromIndex(indexType)) {
            case CBZB:
            case HLZB:
                // 成本指标、含量指标 -> gcdp_dws_index、gcdp_dws_index_makeup
                makeupList = dwsIndexMakeupRepositoryImpl.selectMakeupByNoteIdAndSubjectId(noteIds, itemIds);
                break;
            case ZYYLZB:
                // 主要工料指标 -> gcdp_dws_index_main_res、gcdp_dws_index_main_res_makeup
                makeupList = dwsIndexMakeupRepositoryImpl.selectMainResMakeupByNoteIdAndSubjectId(noteIds, itemIds);
                break;
            default:
                break;
        }

        return makeupList;
    }

    /**
     * 查询组成的详细信息
     *
     * @param makeupList
     * @param includeDetail : 是否包含明细信息
     * @return
     */
    private Map<Integer, Map<Long, MakeupDetailDto>> detailByTypeAndDwdId(List<DwsIndexMakeup> makeupList, boolean includeDetail) {
        // <type，<dwdId， 详细数据>>
        Map<Integer, Map<Long, MakeupDetailDto>> typeMakeupDetailMap = new HashMap<>();

        // 组成的类型：清单、措施、定额、材料
        Map<Integer, List<DwsIndexMakeup>> typeGroup = makeupList.stream().collect(Collectors.groupingBy(DwsIndexMakeup::getType));

        // 根据不同的类型，去不同的表里查数据
        typeGroup.forEach((type, value) -> {
            List<MakeupDetailDto> makeupDetailList = new ArrayList<>();
            List<Long> dwdIds = value.stream().map(DwsIndexMakeup::getDwdId).distinct().collect(Collectors.toList());

            switch (IndexMakeupType.getTypeName(type)) {
                case FBFX:
                case CSXM:
                case QTQD:
                    // 清单、措施 -> gcdp_dwd_bqitem
                    makeupDetailList = dwsIndexMakeupRepositoryImpl.selectBqitemById(dwdIds);
                    if (includeDetail){
                        buildChildrenItems(makeupDetailList, IndexMakeupType.getTypeName(type));
                    }
                    break;
                case NORM:
                    // 定额 -> gcdp_dwd_norm_item
                    makeupDetailList = dwsIndexMakeupRepositoryImpl.selectNormItemById(dwdIds);
                    if (includeDetail){
                        buildChildrenItems(makeupDetailList, IndexMakeupType.getTypeName(type));
                    }
                    break;
                case RCJ:
                    // 材料 -> gcdp_dwd_resource
                    makeupDetailList = dwsIndexMakeupRepositoryImpl.selectResourceItemById(dwdIds);
                    break;
                default:
                    break;
            }

            if (CollUtil.isNotEmpty(makeupDetailList)) {
                typeMakeupDetailMap.put(type, makeupDetailList.parallelStream().collect(Collectors.toMap(MakeupDetailDto::getId, Function.identity())));
            }
        });

        return typeMakeupDetailMap;
    }

    private void buildChildrenItems(List<MakeupDetailDto> makeupDetailList, IndexMakeupType type) {
        switch (type){
            case FBFX:
            case CSXM:
                // (Collectors.toMap(DwsIndexProjectNote::getBidnodeId, Function.identity(), (a, b) -> a));
                LinkedHashMap<Long, MakeupDetailDto> makeupDetailMap = makeupDetailList.stream()
                        .collect(Collectors.toMap(
                                MakeupDetailDto::getId,
                                makeupDetailDto -> makeupDetailDto,
                                (oldValue, newValue) -> oldValue,
                                LinkedHashMap::new
                        ));
                // 查清单下的子目
                List<MakeupDetailDto> normItems = dwsIndexMakeupRepositoryImpl.selectNormItemByBqItemId(makeupDetailMap.keySet().stream().collect(Collectors.toList()));
                // 查子目下的工料消耗
                initNormItemLmmDetails(normItems);
                initNormItemOfBqItem(normItems, makeupDetailMap);

                // 查询清单关联的工料明细
                List<MaterialDTO> bqLmmDetails = dwsIndexMakeupRepositoryImpl.selectMaterialByBqItemId(makeupDetailMap.keySet().stream().collect(Collectors.toList()));
                initLmmDetailOfBqItem(bqLmmDetails, makeupDetailMap);

                break;
            case NORM:
                // 查子目下的工料消耗
                initNormItemLmmDetails(makeupDetailList);
        }
    }

    private static void initNormItemOfBqItem(List<MakeupDetailDto> normItems, LinkedHashMap<Long, MakeupDetailDto> makeupDetailMap) {
        // 子目添加到清单下
        for (MakeupDetailDto normItem : normItems){
            MakeupDetailDto bqItem = makeupDetailMap.get(normItem.getBqtemId());
            List<MakeupDetailDto> children = bqItem.getNormItems();
            if (children == null){
                children = new ArrayList<>();
                bqItem.setNormItems(children);
            }
            children.add(normItem);
        }
    }

    private static void initLmmDetailOfBqItem(List<MaterialDTO> lmmDetails, LinkedHashMap<Long, MakeupDetailDto> makeupDetailMap) {
        // 子目添加到清单下
        for (MaterialDTO lmmDetail : lmmDetails){
            MakeupDetailDto bqItem = makeupDetailMap.get(lmmDetail.getBqItemId());
            List<MaterialDTO> children = bqItem.getLmmDetailItems();
            if (children == null){
                children = new ArrayList<>();
                bqItem.setLmmDetailItems(children);
            }
            children.add(lmmDetail);
        }
    }

    private void initNormItemLmmDetails(List<MakeupDetailDto> normItems) {
        LinkedHashMap<Long, MakeupDetailDto> normItemIdMap = normItems.stream()
                .collect(Collectors.toMap(
                        MakeupDetailDto::getId,
                        makeupDetailDto -> makeupDetailDto,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));
        List<Long> normItemIds = normItems.stream().map(MakeupDetailDto::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(normItemIds)){
            return;
        }

        List<MaterialDTO> lmmDetails = dwsIndexMakeupRepositoryImpl.selectNormItemLmmDetailItems(normItemIds);
        // 转一层按pid不为空的分组
        Map<String, List<MaterialDTO>> materialMap = lmmDetails.stream().filter(material -> !"-1".equals(material.getPid()))
                .collect(Collectors.groupingBy(MaterialDTO::getPid));


        lmmDetails.stream()
                .filter(materialDTO -> materialDTO.getNormItemId() != null && "-1".equals(materialDTO.getPid()))
                .forEach(materialDTO -> {
                    MakeupDetailDto normItem = normItemIdMap.get(materialDTO.getNormItemId());
                    if (normItem != null) {
                        List<MaterialDTO> children = normItem.getLmmDetailItems();
                        if (children == null) {
                            children = new ArrayList<>();
                            normItem.setLmmDetailItems(children);
                        }
                        children.add(materialDTO);

                        // 添加当前材料的明细材料
                        List<MaterialDTO> mixresDetails = materialMap.get(materialDTO.getId());
                        if (mixresDetails != null) {
                            children.addAll(mixresDetails);
                        }
                    }
                });
    }

    private List<MakeupDetailDto> assembleMakeupDetailDto(List<DwsIndexMakeup> dwsIndexMakeupList, Map<Integer, Map<Long, MakeupDetailDto>> typeMakeupDetailMap) {
        List<MakeupDetailDto> makeupDetailDtoList = new ArrayList<>();

        for (DwsIndexMakeup dwsIndexMakeup : dwsIndexMakeupList) {
            Integer type = dwsIndexMakeup.getType();
            Long dwdId = dwsIndexMakeup.getDwdId();
            BigDecimal ldQuantity = dwsIndexMakeup.getLdQuantity();

            if (!typeMakeupDetailMap.containsKey(type) || !typeMakeupDetailMap.get(type).containsKey(dwdId)) {
                continue;
            }

            Map<Long, MakeupDetailDto> dwdIdMap = typeMakeupDetailMap.get(type);

            MakeupDetailDto dwdMakeupDetail = dwdIdMap.get(dwdId);

            MakeupDetailDto makeupDetailDto = new MakeupDetailDto();
            makeupDetailDtoList.add(makeupDetailDto);
            BeanUtils.copyProperties(dwdMakeupDetail, makeupDetailDto);
            // 添加dws侧科目的id数据填充
            makeupDetailDto.setIndexId(dwsIndexMakeup.getDwsIndexId().toString());
            makeupDetailDto.setType(type);
            makeupDetailDto.setIsCalcQuantity(dwsIndexMakeup.getIsCalcQuantity());
            makeupDetailDto.setIsCalcAmount(dwsIndexMakeup.getIsCalcAmount());
            makeupDetailDto.setFactor(dwsIndexMakeup.getFactor());

            if (RCJ.equals(IndexMakeupType.getTypeName(type))){
                ResourceType resourceType = ResourceType.getTypeName(makeupDetailDto.getResourceType());
                makeupDetailDto.setResourceTypeName(resourceType.getName());
                makeupDetailDto.setResourceTypeOrd(getTypeOrd(resourceType));
            }else {
                // 非清单计价（即定额计价）
                if (BqEstiTypeEnum.DEJJGLF.getCode().equals(makeupDetailDto.getEstiType()) || BqEstiTypeEnum.DEJJFQDF.getCode().equals(makeupDetailDto.getEstiType())) {
                    makeupDetailDto.setRate(makeupDetailDto.getFqdRate());
                    makeupDetailDto.setAmount(makeupDetailDto.getFqdAmount());
                    makeupDetailDto.setRateIncludeTax(makeupDetailDto.getFqdRateIncludeTax());
                    makeupDetailDto.setAmountIncludeTax(makeupDetailDto.getFqdAmountIncludeTax());
                }

                // 市场化计价来源的清单，清单上的工程量是总的工程量，不是楼栋的工程量。需要从_makeup表里的ld_quantity楼栋工程量取值，重新计算。
                // 约定根据ld_quantity是否为空来判断从dws层取还是从dwd层取
                if (ldQuantity != null) {
                    makeupDetailDto.setQuantity(ldQuantity);
                    makeupDetailDto.setAmountIncludeTax(MathUtil.mul(ldQuantity, makeupDetailDto.getRateIncludeTax()));
                    makeupDetailDto.setAmount(MathUtil.mul(ldQuantity, makeupDetailDto.getRate()));
                }
            }
        }

        return makeupDetailDtoList;
    }

    private static int getTypeOrd(ResourceType resourceType) {
        switch (resourceType) {
            case RGF:
                return 1;
            case CLF:
                return 2;
            case JXF:
                return 3;
            case SBF:
                return 4;
            case ZCF:
                return 5;
            default:
                return 6;
        }
    }

    @Override
    public List<CBZBKMakeupDetailDto> costIndexDetail(MakeupQueryVO makeupQueryVO) {
        // 查询makeup
        List<DwsIndexMakeup> dwsIndexMakeupList = this.makeupByType(makeupQueryVO);
        if (CollUtil.isEmpty(dwsIndexMakeupList)) {
            return null;
        }
        // 查询dwd明细
        Map<String, CBZBKMakeupDetailDto> dwdDetailMap = this.getDwdDetailMap(dwsIndexMakeupList);
        // makeup和dwd明细是多对一的关系。遍历makeup，组织返回数据
        return this.assembleDetailDto(dwsIndexMakeupList, dwdDetailMap);
    }

    /**
     * makeup和dwd明细是多对一的关系。遍历makeup，组织返回数据
     */
    private List<CBZBKMakeupDetailDto> assembleDetailDto(List<DwsIndexMakeup> dwsIndexMakeupList, Map<String, CBZBKMakeupDetailDto> dwdDetailMap) {
        List<CBZBKMakeupDetailDto> resultList = new ArrayList<>();

        for (DwsIndexMakeup makeup : dwsIndexMakeupList) {
            CBZBKMakeupDetailDto detailDto = dwdDetailMap.get(generateDetailKey(makeup.getType(), makeup.getDwdId()));
            if (detailDto == null) {
                continue;
            }
            CBZBKMakeupDetailDto resultDto = new CBZBKMakeupDetailDto();
            BeanUtils.copyProperties(detailDto, resultDto);
            resultDto.setNoteId(makeup.getIndexProjectNoteId());
            // 施工方成本测算默认勾选工程量是否计入、合价是否计入
            boolean isSGFMBCBCS = SGCBCS2.getIndex().equals(makeup.getProductSource());
            resultDto.setIsCalcQuantity(isSGFMBCBCS ? ValueConst.TRUE : makeup.getIsCalcQuantity());
            resultDto.setIsCalcAmount(isSGFMBCBCS ? ValueConst.TRUE : makeup.getIsCalcAmount());
            resultDto.setFactor(makeup.getFactor());
            // 类型转换
            CBZBKMakeupDetailTypeEnums typeEnum = convertType(makeup.getType(), resultDto.getType() != null ? Integer.parseInt(resultDto.getType()) : null);
            resultDto.setType(typeEnum.getType());
            resultDto.setTypeOrd(typeEnum.getOrd());
            // 归档时间用于排序
            resultDto.setArchiveDate(makeup.getArchiveDate());
            // 项目特征/工作内容：项目特征、工作内容、规格型号，中间用“；”隔开
            String specWork = Stream.of(resultDto.getCharacteristic(), resultDto.getWorkScope(), resultDto.getSpec()).filter(Objects::nonNull).collect(Collectors.joining("；"));
            resultDto.setSpecWork(specWork);
            // 明细里的工程量可能是总的工程量，需要从makeup表里取量
            if (makeup.getLdQuantity() != null) {
                resultDto.setQuantity(makeup.getLdQuantity());
                resultDto.setAmount(MathUtil.mul(makeup.getLdQuantity(), resultDto.getRate()));
                resultDto.setAmountIncludeTax(MathUtil.mul(makeup.getLdQuantity(), resultDto.getRateIncludeTax()));
            }
            resultList.add(resultDto);
        }

        // 排序：明细数据先按照类别排序（人、专、主、材、机、设、间接费、税金、其他），同类别内再按照名称排列，同类别、同名称再按照项目特征/工作内容/规格型号排序，如果前三者一致再按归档时间倒序排序
        resultList.sort(Comparator.comparing(CBZBKMakeupDetailDto::getTypeOrd)
                .thenComparing(CBZBKMakeupDetailDto::getName)
                .thenComparing(CBZBKMakeupDetailDto::getSpecWork)
                .thenComparing(Comparator.comparing(CBZBKMakeupDetailDto::getArchiveDate).reversed()));

        return resultList;
    }

    private Map<String, CBZBKMakeupDetailDto> getDwdDetailMap(List<DwsIndexMakeup> dwsIndexMakeupList) {
        Map<String, CBZBKMakeupDetailDto> dwdDetailMap = new HashMap<>();
        // 根据type分组
        Map<Integer, List<DwsIndexMakeup>> typeGroup = dwsIndexMakeupList.stream().collect(Collectors.groupingBy(DwsIndexMakeup::getType));
        typeGroup.forEach((type, makupList) -> {
            Set<Long> dwdIdSet = makupList.stream().map(DwsIndexMakeup::getDwdId).collect(Collectors.toSet());
            // 根据type从不同的表里查数据
            List<CBZBKMakeupDetailDto> makeupDetailList = getCbzbkMakeupDetailByType(type, dwdIdSet);
            if (CollUtil.isNotEmpty(makeupDetailList)) {
                dwdDetailMap.putAll(makeupDetailList.stream().collect(Collectors.toMap(x -> generateDetailKey(type, x.getId()), Function.identity(), (v1, v2) -> v2)));
            }
        });
        return dwdDetailMap;
    }

    private static String generateDetailKey(Integer type, Long id) {
        return type + CommonConstants.SPLIT_FLAG + id;
    }

    private List<CBZBKMakeupDetailDto> getCbzbkMakeupDetailByType(Integer type, Set<Long> ids) {
        List<CBZBKMakeupDetailDto> makeupDetailList = new ArrayList<>();

        switch (IndexMakeupType.getTypeName(type)) {
            case FBQD:
                // 分包清单 -> gcdp_dwd_expend_sub_bqitem
                makeupDetailList = dwsIndexMakeupRepositoryImpl.selectExpendSubBqItemById(ids);
                break;
            case RCJ:
                // 材料 -> gcdp_dwd_resource
                makeupDetailList = dwsIndexMakeupRepositoryImpl.selectResourceById(ids);
                break;
            case FBFX:
                // 间接费、税金放在清单表 -> gcdp_dwd_bqitem
                makeupDetailList = dwsIndexMakeupRepositoryImpl.selectBqItemById(ids);
                break;
            default:
                break;
        }

        return makeupDetailList;
    }
}
