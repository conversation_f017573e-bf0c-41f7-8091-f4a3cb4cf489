package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.ZbwProjectDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwdProjectInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DwdProjectInfoQueryMapper extends BaseMapper<DwdProjectInfo> {
    DwdProjectInfo selectByPrimaryKey(Long id);

    /**
     * 获取全部项目列表，包含项目信息
     */
    List<DwdProjectInfo> listDetailProjectInfo(@Param("enterpriseId") String enterpriseId,
                                               @Param("orgIds") List<String> orgIds,
                                               @Param("projectCodes") List<String> projectCodes,
                                               @Param("projectName") String projectName,
                                               @Param("areaId") List<String> areaId,
                                               @Param("isVirtualOrg") String isVirtualOrg,
                                               @Param("sharedEnterpriseId") List<String> sharedEnterpriseId,
                                               @Param("includeSelfFlg") String includeSelfFlg,
                                               @Param("authControlProjectCodeList") List<String> authControlProjectCodeList);

    List<ZbwProjectDto> selectListByEnterpriseId(@Param("enterpriseId") String enterpriseId);

    DwdProjectInfo selectByProjectCode(String enterpriseId, String projectCode);

    List<DwdProjectInfo> selectByProjectCodes(@Param("enterpriseId")String enterpriseId, @Param("projectCodes")List<String> projectCodes);

}