package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "特征详情", description = "列头信息")
public class AttrDataDto {
    @ApiModelProperty(value = "特征名称", required = false, example = "xxx")
    private String attrName;
    @ApiModelProperty(value = "数据类型", required = false, example = "xxx")
    private String type;
    @ApiModelProperty(value = "枚举列表", required = false, example = "xxx")
    private String selectList;
}
