package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dimservice.domain.dao.enums.IndexCategoryDictEnum;
import com.glodon.gcdpindexsearch.common.enums.IndexTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.IndexDataDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ProjectIndexDataDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterIndexDataVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.SingleProjectIndexReqVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/*
 * @Class com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl DwsIndexEconomicsDataRepositoryImpl
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 经济指标查询
 * @Date 10:55 2022/11/3
 **/
@Service("indexEconomics")
public class DwsIndexEconomicsDataDetailServiceImpl extends IndexDataDetailService implements ProjectIndexDataDetailService {

    @Autowired
    IDwsIndexDataRepository indexDataRepository;

    @Override
    public IndexData getIndexDataDetail(FilterIndexDataVO filterIndexDataVO) {
        List<ProjectIndexData> result = indexDataRepository.selectEconomicsIndexList(filterIndexDataVO);
        makePid(result);
        // note信息
        List<DwsIndexProjectNote> noteList = indexDataRepository.getTotal(filterIndexDataVO.getIds());

        List<String> ids = noteList.stream().map(x -> x.getId().toString()).collect(Collectors.toList());
        calcIndexValue(result);
        if (filterIndexDataVO.getShowAll() == null || filterIndexDataVO.getShowAll() == 0) {
            result = filterInvalidData(result);
        }
        makeCode(result);
        return new IndexData(ids, result);
    }

    /**
     * 计算经济技术指标的指标值
     * @param result
     */
    private void calcIndexValue(List<ProjectIndexData> result) {
        result.forEach(item -> {
            GcdpDwsIndexEconomics gcdpDwsIndexEconomics = (GcdpDwsIndexEconomics) item;
            gcdpDwsIndexEconomics.setIndexValue(MathUtil.divStr(gcdpDwsIndexEconomics.getFzValue(), gcdpDwsIndexEconomics.getFmValue()));
            gcdpDwsIndexEconomics.setProjectUnLdMergeHash(gcdpDwsIndexEconomics.getProjectUnLdMergeHash());
        });
    }

    @Override
    public IndexData getItemIndexDataDetail(FilterIndexDataVO filterIndexDataVO) throws Exception {
        //判断ids 是否为空，如果为空则根据筛选条件过滤出ids
        List<DwsIndexProjectNote> dwsIndexProjectNotes = getIdsByFilter(filterIndexDataVO);
        if (CollectionUtils.isEmpty(dwsIndexProjectNotes)) {
            return new IndexData();
        }
        List<String> ids = dwsIndexProjectNotes.stream().map(x -> x.getId().toString()).collect(Collectors.toList());
        //根据id 查询指标科目
        List<ProjectIndexData> result = indexDataRepository.selectEconomicItemIndexList(ids, filterIndexDataVO.getShowAll());
        makePid(result);
        makeCode(result);
        result.stream().forEach(x -> {
            GcdpDwsItemIndexEconomic economic = (GcdpDwsItemIndexEconomic) x;
            economic.setMaxAndMin(MathUtil.parseStandardMaxAndMin(economic.getMaxAndMin()));
            economic.setDictCode(IndexCategoryDictEnum.BZZB.getCode());
            economic.setItemIds(x.getIds());
        });
        return new IndexData(ids, result);
    }

    @Override
    public ItemIndex buildItemIndex(SingleProjectIndexReqVO reqVO, SingleProjectMakeUp makeUp){
        FilterIndexDataVO vo = new FilterIndexDataVO();
        vo.setIds(reqVO.getTempNodeIds());
        List<ProjectIndexData> result = indexDataRepository.selectEconomicsIndexList(vo);
        makePid(result);

        calcIndexValue(result);
        if (reqVO.getShowAll() == null || reqVO.getShowAll() == 0) {
            result = filterInvalidData(result);
        }
        makeCode(result);
        return buildReturnValue(IndexTypeEnums.INDEX_ECONOMIC.getName(), result);
    }

    /**
     * 转换经济技术指标数据为单指标接口返回的数据类型
     * @param indexType
     * @param indexData
     * @return
     */
    private ItemIndex buildReturnValue(String indexType, List<ProjectIndexData> indexData) {
        List<SingleProjectEconomicsValue> itemValueList = Lists.newArrayList();
        indexData.forEach(item -> {
            // 处理指标值
            SingleEconomicsValueDto indexValueDto = new SingleEconomicsValueDto();
            BeanUtils.copyProperties(item, indexValueDto);
            Map<String, SingleEconomicsValueDto> indexes = Maps.newHashMap();
            indexes.put(indexType, indexValueDto);

            // 指标节点
            SingleProjectEconomicsValue itemValue = new SingleProjectEconomicsValue();
            BeanUtils.copyProperties(item, itemValue);
            itemValue.setIndexes(indexes);
            itemValueList.add(itemValue);
        });

        return new ItemIndex(Collections.singletonList(indexType), itemValueList, null);
    }
}

