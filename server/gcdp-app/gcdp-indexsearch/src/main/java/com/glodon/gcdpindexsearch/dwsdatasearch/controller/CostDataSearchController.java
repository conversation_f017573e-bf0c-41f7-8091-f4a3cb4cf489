package com.glodon.gcdpindexsearch.dwsdatasearch.controller;


import com.glodon.gcdpindexsearch.common.BaseController;
import com.glodon.gcdpindexsearch.dwsdatasearch.application.CostDataSearchFacade;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexConditionDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.ConditionQueryVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexListVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexMakeupVO;
import com.glodon.gcdpindexsearch.infrastructure.annotation.TakeTime;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description:建造做法指标库、全成本指标库查询数据集合接口层
 * <AUTHOR>
 * @date 2023/5/15 14:15
 */
@Api(tags = "建造做法指标库、全成本指标库查询接口层")
@Slf4j
@RestController
@RequestMapping("/cost-data")
public class CostDataSearchController extends BaseController {
    @Autowired
    CostDataSearchFacade costDataSearchFacade;

    @ApiOperation(value = "查询指标区间组成")
    @TakeTime
    @PostMapping("/cost-index-makeup/{enterpriseId}")
    public List<CostIndexMakeupBaseDto> costIndexMakeup(@PathVariable String enterpriseId,  @RequestBody CostIndexMakeupVO costIndexMakeupVO){
        costIndexMakeupVO.setEnterpriseId(enterpriseId);
        return costDataSearchFacade.costIndexMakeup(costIndexMakeupVO);
    }
    @ApiOperation(value = "查看列表(全成本指标和建造做法指标)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")
    })
    @TakeTime
    @PostMapping("/cost-index-list/{enterpriseId}")
    public List<CostIndexBaseDto> costIndexList(@PathVariable String enterpriseId, @RequestBody CostIndexListVO costIndexListVO)throws Exception{
        costIndexListVO.setEnterpriseId(enterpriseId);
        costIndexListVO.setOrgIds(getOrgIds());
        return costDataSearchFacade.costIndexList(costIndexListVO);
    }
    @ApiOperation(value = "条件反推(全成本指标和建造做法指标)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047"),
            @ApiImplicitParam(name = "type", value = "qcbzb:全成本指标库，jzzfzb：建造做法指标库", dataType = "String", defaultValue = "qcbzb")
    })
    @TakeTime
    @GetMapping("/get-condition/{enterpriseId}")
    public CostIndexConditionDto getCondition(@PathVariable String enterpriseId, @RequestParam String type, @RequestParam(required = false) List<String> authControlProjectCodeList){
        return costDataSearchFacade.getCondition(enterpriseId,type,getOrgIds(), authControlProjectCodeList);
    }

    @ApiOperation(value = "条件反推(全成本指标和建造做法指标)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")
    })
    @TakeTime
    @PostMapping("/get-conditionNew/{enterpriseId}")
    public CostIndexConditionDto getConditionNew(@PathVariable String enterpriseId, @RequestBody ConditionQueryVO conditionQueryVO){
        return costDataSearchFacade.getCondition(enterpriseId, conditionQueryVO.getType(), getOrgIds(), conditionQueryVO.getAuthControlProjectCodeList());
    }
}
