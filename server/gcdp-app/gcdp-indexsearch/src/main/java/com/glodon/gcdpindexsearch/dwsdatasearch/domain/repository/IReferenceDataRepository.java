package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYJZBZDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYJZCBDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYXEZBDto;

import java.util.List;
import java.util.Map;

public interface IReferenceDataRepository {


    /**
     * @description: 查询项目信息根部OrgIds和EnterprisedId
     * @date: 2023/5/19 14:08
     */
    List<ReferenceDataBaseDto> selectProjectInfoByOrgIdsAndEnterprisedId(String enterpriseId,
                                                                         List<String> orgIds, List<String> projectNameList, List<String> dataSourceList, String isVirtualOrg, List<String> authControlProjectCodeList);

    /**
     * @description: 查询 企业限额指标 参考历史数据
     * @date: 2023/5/19 14:08
     */
    List<ReferenceDataQYXEZBDto> selectReferenceDataToQYXEZBLib(Map<String, ReferenceDataBaseDto> projectNoteIdsMap, String name, Integer itemCostType);

    /**
     * @description: 查询 企业建造标准 参考历史数据
     * @date: 2023/5/19 14:08
     */
    List<ReferenceDataQYJZBZDto> selectReferenceDataToQYJZBZLib(Map<String, ReferenceDataBaseDto> projectNoteIdsMap, String name);

    /**
     * @description: 查询 企业建造成本 参考历史数据
     * @date: 2023/5/19 14:08
     */
    List<ReferenceDataQYJZCBDto> selectReferenceDataToQYJZCBLib(Map<String, ReferenceDataBaseDto> projectNoteIdsMap, String name, Integer itemCostType);

}
