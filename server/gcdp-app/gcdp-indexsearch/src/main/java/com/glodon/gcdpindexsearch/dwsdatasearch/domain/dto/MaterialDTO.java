package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import io.swagger.models.auth.In;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @program ebq
 * @description TODO
 * @create 2023/8/17 14:56
 */

@Data
public class MaterialDTO {
    private String id;
    private String pid;
    private Long normItemId;
    private Long bqItemId;
    private String code;
    private Integer type;
    private String name;
    private String spec;
    private String unit;
    private BigDecimal usage;
    /** 市场价 */
    private BigDecimal marketRate;
    /** 含税是市场价 */
    private BigDecimal marketTaxRate;
    /** 预算价 */
    private BigDecimal budgetRate;
    /** 含税预算价 */
    private BigDecimal budgetTaxRate;
    /** 供货方式 */
    private Integer supplyType ;
    /** 品牌 */
    private String brand;
    /** 数量 */
    private BigDecimal quantity;
    /** 工料机拓展字段 （consumption为消耗量）*/
    private String lmmExtendDataJson;
    /** 材料表扩展越秀字段 */
    private String resourceExtendDataJson;
    /** 子集 */
    private List<MaterialDTO> children;
}
