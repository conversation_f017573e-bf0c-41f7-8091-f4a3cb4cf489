package com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor;

import cn.hutool.core.util.StrUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ItemData;
import lombok.Data;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @className: AbstractIndexProcessor
 * @author: lml
 * @description: 处理器抽象类
 */
@Data
public abstract class AbstractIndexProcessor<T extends ItemData> {

    protected AbstractIndexProcessor<T> nextProcessor;

    /**
     * 数据层面处理dws侧的指标数据
     *
     * @param dataList 数据list
     * @return 处理之后的数据list
     */
    public abstract List<T> process(List<T> dataList, String indexType);

    protected List<T> processNext(List<T> dataList, String indexType) {
        AbstractIndexProcessor<T> nextProcessor = getNextProcessor();
        if (nextProcessor != null) {
            return nextProcessor.process(dataList, indexType);
        }
        return dataList;
    }

    public String orderedIds(String ids) {
        if (StrUtil.isNotEmpty(ids)) {
            List<String> resultIds = StrUtil.split(ids, StrUtil.COMMA, true, true);
            List<String> list = resultIds.stream().sorted(Comparator.comparingLong(Long::valueOf)).collect(Collectors.toList());
            return StrUtil.join(StrUtil.COMMA, list);
        }
        return ids;
    }
}
