package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwsCostStanderdQueryMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYJZBZDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYJZCBDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYXEZBDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IReferenceDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class DwsReferenceDataRepositoryImpl implements IReferenceDataRepository {
    @Autowired
    private DwsCostStanderdQueryMapper dwsCostStanderdQueryMapper;

    @Override
    public List<ReferenceDataBaseDto> selectProjectInfoByOrgIdsAndEnterprisedId(String enterpriseId, List<String> orgIds, List<String> projectNameList, List<String> dataSourceList, String isVirtualOrg, List<String> authControlProjectCodeList) {
        return dwsCostStanderdQueryMapper.selectProjectInfoByOrgIdsAndEnterprisedId(enterpriseId, orgIds, projectNameList, dataSourceList, isVirtualOrg, authControlProjectCodeList);
    }

    @Override
    public List<ReferenceDataQYXEZBDto> selectReferenceDataToQYXEZBLib(Map<String, ReferenceDataBaseDto> projectNoteIdsMap, String name, Integer itemCostType) {
        return dwsCostStanderdQueryMapper.selectReferenceDataToQYXEZBLib(projectNoteIdsMap, name);
    }

    @Override
    public List<ReferenceDataQYJZBZDto> selectReferenceDataToQYJZBZLib(Map<String, ReferenceDataBaseDto> projectNoteIdsMap, String name) {
        return dwsCostStanderdQueryMapper.selectReferenceDataToQYJZBZLib(projectNoteIdsMap, name);
    }

    @Override
    public List<ReferenceDataQYJZCBDto> selectReferenceDataToQYJZCBLib(Map<String, ReferenceDataBaseDto> projectNoteIdsMap, String name, Integer itemCostType) {
        return dwsCostStanderdQueryMapper.selectReferenceDataToQYJZCBLib(projectNoteIdsMap, name, itemCostType);
    }
}
