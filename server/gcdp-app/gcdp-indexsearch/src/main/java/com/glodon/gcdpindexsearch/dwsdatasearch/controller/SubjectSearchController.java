package com.glodon.gcdpindexsearch.dwsdatasearch.controller;

import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdpindexsearch.common.BaseController;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.BqItemDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.application.SubjectSearchFacde;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ItemIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ProjectIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.*;
import com.glodon.gcdpindexsearch.infrastructure.annotation.TakeTime;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @description: 科目数据接口层
 * @date 2022/11/18 11:09
 */
@Api(tags = "科目数据接口层")
@Slf4j
@RestController
@RequestMapping("/gcdp-index-dws")
public class SubjectSearchController extends BaseController {

    @Autowired
    SubjectSearchFacde subjectSearchFacde;

    /**
     * 样本量接口指标库不用了，现在调用的按单体的列表；
     * 样本量接口只是给契约用，用的是原始未合并的数据；
     */
    @ApiOperation(value = "样本量查询")
    @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")
    @TakeTime
    @PostMapping("/ld-note-list/{enterpriseId}")
    public List<SampleNoteDto> ldNoteList(@PathVariable String enterpriseId, @RequestBody SampleNoteIndexDataVO sampleNoteIds) {
        return subjectSearchFacde.ldNoteList(enterpriseId, sampleNoteIds);
    }

    @ApiOperation(value = "清单组成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")})
    @TakeTime
    @PostMapping("/makeup-detail-list/{enterpriseId}")
    public List<MakeupDetailBaseVO> makeupDetail(@PathVariable String enterpriseId, @Valid @RequestBody MakeupQueryVO makeupQueryVO) {
        return subjectSearchFacde.makeupDetail(makeupQueryVO);
    }

    @ApiOperation(value = "楼栋维度清单明细组成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")})
    @TakeTime
    @PostMapping("/ld-makeup-detail-list/{enterpriseId}")
    public List<BqItemDto> ldMakeupDetail(@PathVariable String enterpriseId, @Valid @RequestBody MakeupQueryVO makeupQueryVO) {
        return subjectSearchFacde.ldMakeupDetail(makeupQueryVO);
    }

    @ApiOperation(value = "指标组成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")})
    @TakeTime
    @PostMapping("/index-detail-list/{enterpriseId}")
    public MakeupIndexDto makeupIndexList(@PathVariable String enterpriseId, @RequestBody MakeUpIndexVo makeUpIndexVo) {
        return subjectSearchFacde.makeupIndexList(makeUpIndexVo);
    }

    @ApiOperation(value = "人材机组成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "清单id", dataType = "string"),
            @ApiImplicitParam(name = "type", value = "清单类别", dataType = "int")
    })
    @TakeTime
    @GetMapping("/material-detail-list/{enterpriseId}")
    public JSONObject makeupMaterialList(@PathVariable String enterpriseId, @RequestParam String id, @RequestParam Integer type) {
        return subjectSearchFacde.makeupMaterialList(id, type);
    }

    @ApiOperation(value = "查询所有指标类型的科目列表与科目组成单体详情")
    @TakeTime
    @PostMapping("/index-data-details/{enterpriseId}")
    public List<ProjectIndexData> indexDataDetail(@PathVariable String enterpriseId,
                                                  @RequestBody FilterIndexDataVO filterIndexDataVO) throws Exception {
        log.info("查询查询所有指标类型的科目列表与科目组成单体详情：{}-{}", enterpriseId, filterIndexDataVO);
        filterIndexDataVO.setEnterpriseId(enterpriseId);
        filterIndexDataVO.setOrgIds(getOrgIds());
        log.info("enterpriseId={},orgId={}", enterpriseId, getOrgIds());
        return subjectSearchFacde.getIndexDetailList(filterIndexDataVO);
    }


    @ApiOperation(value = "科目维度查询指标详情")
    @TakeTime
    @PostMapping("/item-index-data/{enterpriseId}")
    public ItemIndexData itemIndexData(@PathVariable String enterpriseId, @Valid @RequestBody ItemIndexQueryReqVO itemIndexQueryReqVO) {
        log.info("科目维度查询指标详情参数：{}-{}", enterpriseId, itemIndexQueryReqVO);
        return subjectSearchFacde.getItemIndexData(enterpriseId, getOrgIds(), itemIndexQueryReqVO);
    }

    @ApiOperation(value = "查询所有指标类型的科目列表与科目组成单体详情-单指标(标准指标库拉取数据用，只适用于成本含量指标，主要工料与经济技术指标走老接口)")
    @TakeTime
    @PostMapping("/item-index-data-details/{enterpriseId}")
    public ItemIndexData itemIndexDataDetails(@PathVariable String enterpriseId, @Valid @RequestBody ItemIndexQueryReqVO itemIndexQueryReqVO) {
        log.info("查询所有指标类型的科目列表与科目组成单体详情-单指标：{}-{}", enterpriseId, itemIndexQueryReqVO);
        return subjectSearchFacde.getItemIndexDataDetails(enterpriseId, getOrgIds(), itemIndexQueryReqVO);
    }

    @ApiOperation(value = "成本指标库科目下钻明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")})
    @TakeTime
    @PostMapping("/cost-index-detail/{enterpriseId}")
    public List<CBZBKMakeupDetailDto> costIndexDetail(@PathVariable String enterpriseId, @Valid @RequestBody MakeupQueryVO makeupQueryVO) {
        return subjectSearchFacde.costIndexDetail(makeupQueryVO);
    }

}
