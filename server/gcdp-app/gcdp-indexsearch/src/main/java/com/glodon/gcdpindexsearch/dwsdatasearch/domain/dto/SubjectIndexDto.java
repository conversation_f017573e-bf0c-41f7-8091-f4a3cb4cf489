package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.glodon.gcdp.common.domain.model.IndexParent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: yhl
 * @DateTime: 2023/7/20 14:21
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("squid:S1068") // 忽略sonar规则： Unused "private" fields should be removed
public class SubjectIndexDto extends IndexParent implements Serializable {
    private Long id;
    private Long pid;
    private Long indexProjectNoteId;
    private Long contractProjectId;
    private Long bidnodeId;
    private String code;
    private String name;
    private String namePath;
    private String unit;
    private String originalTemplateUuid;
    private String calculateName;
    private BigDecimal calculateValue;
    private String swlCalculateName;
    private BigDecimal swlCalculateValue;
    private BigDecimal swlIndexValue;
    private BigDecimal swlIndexValueIncludeTax;
    private String swlIndexUnit;
    private BigDecimal amount;
    private BigDecimal amountIncludeTax;
    private BigDecimal quantities;
    private String dfCalculateName;
    private BigDecimal dfCalculateValue;
    private BigDecimal dfIndexValue;
    private BigDecimal dfIndexValueIncludeTax;
    private String dfIndexUnit;
    private String itemHash;
    private String zylCalculateName;
    private BigDecimal zylCalculateValue;
    private String zylIndexUnit;
    private BigDecimal zylIndexValue;
    private Date archiveDate;
    private Integer itemCostType;

    @ApiModelProperty(value = "原始科目id集合")
    private List<Long> originalIdList;
    @ApiModelProperty(value = "原始科目id集合")
    private List<String> originalCodeList;
    @ApiModelProperty(value = "科目模板集合")
    private List<String> templateUuids;
    @ApiModelProperty(value = "科目计算口径编码")
    private String caliberCode;
    @ApiModelProperty(value = "科目计算口径名称")
    private String caliberName;
    @ApiModelProperty(value = "科目计算口径值")
    private BigDecimal caliberValue;
    @ApiModelProperty(value = "单方含量(不含税)")
    private BigDecimal unitContent;
    @ApiModelProperty(value = "单方造价(不含税)")
    private BigDecimal unitCost;
    @ApiModelProperty(value = "综合单价(不含税)")
    private BigDecimal compUnitPrice;
    @ApiModelProperty(value = "单方造价(含税)")
    private BigDecimal unitCostIncludeTax;
    @ApiModelProperty(value = "综合单价(含税)")
    private BigDecimal compUnitPriceIncludeTax;
    @ApiModelProperty(value = "总价")
    private BigDecimal totalCost;
    @ApiModelProperty(value = "总价(含税)")
    private BigDecimal totalCostIncludeTax;
    @ApiModelProperty(value = "排序")
    private Integer ord;
    @ApiModelProperty(value = "是否叶子结点")
    private boolean isLeaf;
    @ApiModelProperty(value = "层级")
    private Integer level;
    @ApiModelProperty(value = "子级科目个数")
    private Integer childCount;

    private String ids;

}
