package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository;

import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMakeup;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/11/18 15:56
 */
public interface IDwsIndexMakeupRepository {
    List<DwsIndexMakeup> selectCostMakeupByNoteIdAndSubjectId(List<Long> ids, List<Long> itemIds);
    List<DwsIndexMakeup> selectUsageMakeupByNoteIdAndSubjectId(List<Long> ids, List<Long> itemIds);
    List<MakeupDetailDto> selectNormItemById(List<Long> ids);
    /**
     * 查询清单下的定额
     * @param bqIds: 清单id
     * @return
     */
    List<MakeupDetailDto> selectNormItemByBqItemId(List<Long> bqIds);
    List<MakeupDetailDto> selectResourceItemById(List<Long> ids);

    /**
     * 查询清单的消耗量明细,针对企标工程:清单挂耗量的场景
     * @return
     */
    List selectBqLmmDetailItems();

    /**
     * 查询定额下的工料消耗
     * @return
     */
    List<MaterialDTO> selectNormItemLmmDetailItems(List<Long> ids);

    List<MakeupDetailDto> selectBqitemById(List<Long> ids);
    List<CostDetailDTO> selectCostDetailByBqItemId(Long id);
    List<MaterialDTO> selectMaterialByBqItemId(Long id);
    List<MaterialDTO> selectMaterialByBqItemId(List<Long> ids);
    List<MaterialDTO> selectMaterialByNormItemId(List<Long> ids);
    List<Long> selectNormItemIdsByBqItemId(Long bqItemId);

    List<DwsIndexMakeup> selectMakeupByNoteIdAndSubjectId(List<Long> noteIds, List<Long> itemIds);

    List<DwdContractProject> selectContractProjectInfo(List<Long> ids);

    List<CBZBKMakeupDetailDto> selectExpendSubBqItemById(Set<Long> ids);
    List<CBZBKMakeupDetailDto> selectResourceById(Set<Long> ids);
    List<CBZBKMakeupDetailDto> selectBqItemById(Set<Long> ids);
    List<DwsIndexMakeup> selectMainResMakeupByNoteIdAndSubjectId(List<Long> noteIds, List<Long> itemIds);
}
