package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.IndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterIndexDataVO;

/**
 * @description: 查询指标Service
 * <AUTHOR>
 * @date 2022/10/24 10:58
 */
public interface ProjectIndexDataService {


    /**
     * @description: 查询4类指标
     * @param filterIndexDataVO
     * @return com.glodon.gcdpindexsearch.dwsdatasearch.application.dto.ConditionDto
     * <AUTHOR>
     * @date 2022/10/25 18:35
     */
    IndexData getIndexData(FilterIndexDataVO filterIndexDataVO)throws Exception;

}
