package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/10/26 18:29
 */
@Data
@ApiModel(value = "详情页面页签范围", description = "详情页面页签范围")
public class ProjectDetailRangeDto {

    @ApiModelProperty(value = "是否包含工程特征")
    private Boolean includeProjectAttr = false;
    @ApiModelProperty(value = "是否包含建造标准")
    private Boolean includeBuildStandard = false;
    @ApiModelProperty(value = "是否包含设计指标")
    private Boolean includeDesignIndex = false;

    @ApiModelProperty(value = "是否包含建面单方指标")
    private Boolean includeJMDF = false;
    @ApiModelProperty(value = "是否包含实物量单方指标")
    private Boolean includeSWLDF = false;
    @ApiModelProperty(value = "是否包含单方指标")
    private Boolean includeDFZB = false;
    @ApiModelProperty(value = "是否包含主要量指标")
    private Boolean includeZYLZB = false;
    @ApiModelProperty(value = "是否包含建面含量指标")
    private Boolean includeJMHLZB = false;
    @ApiModelProperty(value = "是否包含主要工料指标")
    private Boolean includeIndexMainRes = false;
    @ApiModelProperty(value = "是否包含经济技术指标")
    private Boolean includeIndexEconomics = false;

    @ApiModelProperty(value = "是否包含非建安指标")
    private Boolean includeFJAIndex = false;
    @ApiModelProperty(value = "建安组成")
    private List<MakeUp> JAIndex;

    @Data
    public static class MakeUp{
        private String groupType;
        private String groupKey;
        private String displayName;
        @JsonIgnore
        private Set<String> displayNameSet = new LinkedHashSet<>();
        @JsonIgnore
        private Integer typeOrd;
        private Integer basicInfoOrd;
        @JsonIgnore
        private Integer archiveOrd;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date archiveDate;

        private List<MakeUpNode> mergeInfos = new ArrayList<>();
    }

    @Data
    public static class MakeUpNode{
        private Long tempNoteId;
        private String originalType;
        private String groupType;
        private String groupKey;
        private String displayName;
        // 楼栋、虚拟楼栋特有
        private String sameLdGroupKey;
        private String identifyName;
        private String originalName;

        @JsonIgnore
        private Integer typeOrd;
        private Integer basicInfoOrd;
        @JsonIgnore
        private Integer archiveOrd;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date archiveDate;
    }
}
