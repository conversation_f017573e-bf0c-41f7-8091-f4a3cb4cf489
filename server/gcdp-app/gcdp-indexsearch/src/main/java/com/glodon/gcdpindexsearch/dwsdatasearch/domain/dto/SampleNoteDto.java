package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/11/3 9:29
 */
@Data
@ApiModel(value = "样本量note", description = "样本量note")
public class SampleNoteDto {

    @ApiModelProperty(value = "单体唯一标识id", example = "10446")
    private String id;
    @ApiModelProperty(value = "项目编码", example = "XMHA2210240002")
    private String projectCode;
    @ApiModelProperty(value = "项目名称", example = "bai审核")
    private String projectName;
    @ApiModelProperty(value = "单体工程", example = "高层、底层")
    private String name;
    @ApiModelProperty(value = "单体工程-优化后的楼栋名称", example = "1号楼、2号楼")
    private String ldNameIdentity;
    @ApiModelProperty(value = "总项目规模/建筑面积", example = "0")
    private BigDecimal buildArea;
    @ApiModelProperty(value = "总造价（不含税）", example = "0")
    private BigDecimal amount;
    @ApiModelProperty(value = "总造价（含税）", example = "0")
    private BigDecimal amountIncludeTax;
    @ApiModelProperty(value = "单方造价（不含税）", example = "0")
    private BigDecimal dfIndexValue;
    @ApiModelProperty(value = "单方造价（含税）", example = "0")
    private BigDecimal dfIndexValueIncludeTax;

    @ApiModelProperty(value = "科目费用类型，1：全费 2非全费 3 非全费->全费", example = "0")
    private Integer itemCostType;

    @ApiModelProperty(value = "全费总造价（不含税）", required = false, example = "0")
    private BigDecimal fullCostAmount;
    @ApiModelProperty(value = "全费总造价（含税）", required = false, example = "0")
    private BigDecimal fullCostAmountIncludeTax;
    @ApiModelProperty(value = "全费单方造价（不含税）", required = false, example = "0")
    private BigDecimal fullCostDfIndexValue;
    @ApiModelProperty(value = "全费单方造价（含税）", required = false, example = "0")
    private BigDecimal fullCostDfIndexValueIncludeTax;

    @ApiModelProperty(value = "非全费总造价（不含税）", required = false, example = "0")
    private BigDecimal nonFullCostAmount;
    @ApiModelProperty(value = "非全费总造价（含税）", required = false, example = "0")
    private BigDecimal nonFullCostAmountIncludeTax;
    @ApiModelProperty(value = "非全费单方造价（不含税）", required = false, example = "0")
    private BigDecimal nonFullCostDfIndexValue;
    @ApiModelProperty(value = "非全费单方造价（含税）", required = false, example = "0")
    private BigDecimal nonFullCostDfIndexValueIncludeTax;

    @ApiModelProperty(value = "项目创建时间")
    private Date createDate;
    @ApiModelProperty(value = "归档时间")
    private Date archiveDate;
    @ApiModelProperty(value = "业态名称", example = "居住建筑/办公建筑")
    private String categoryName;
    @ApiModelProperty(value = "造价类型", example = "招标控制价")
    private String phaseName;
    @ApiModelProperty(value = "文件名称", example = "超算中心工程")
    private String fileName;
    @ApiModelProperty(value = "数据源", example = "单体数据源")
    private String productSource;
}
