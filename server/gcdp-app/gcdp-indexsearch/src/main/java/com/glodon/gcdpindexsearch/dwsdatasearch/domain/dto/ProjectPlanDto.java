package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 设计指标实体类
 */
@Data
@ApiModel(value = "设计指标列表", description = "设计指标列表")
public class ProjectPlanDto{
    @ApiModelProperty(value = "id", required = false, example = "xxx")
    private Long id;
    @ApiModelProperty(value = "父id", required = false, example = "xxx")
    private Long pid;
    @ApiModelProperty(value = "编码", required = false, example = "xxx")
    private String code;
    @ApiModelProperty(value = "指标名称", required = false, example = "xxx")
    private String name;
    @ApiModelProperty(value = "单位", required = false, example = "xxx")
    private String unit;
    @ApiModelProperty(value = "公式", required = false, example = "xxx")
    private String formula;
    @ApiModelProperty(value = "值", required = false, example = "xxx")
    private String value;
    @ApiModelProperty(value = "备注", required = false, example = "xxx")
    private String remark;
    @ApiModelProperty(value = "值类型 1 text 2 number 3 percent", required = false, example = "xxx")
    private String valueType;
    @ApiModelProperty(value = "唯一码", required = false, example = "xxx")
    private String pkCode;
}
