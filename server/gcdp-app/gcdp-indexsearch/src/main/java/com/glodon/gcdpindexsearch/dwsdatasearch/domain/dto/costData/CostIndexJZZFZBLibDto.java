package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 全成本指标库返回数据类
 * <AUTHOR>
 * @date 2023-5-16
 */
@Data
public class CostIndexJZZFZBLibDto extends CostIndexBaseDto{
    @ApiModelProperty(value = "标准说明及建造做法", required = false,example = ":[{\n" +
            "        \"description\":\"标准说明\",\n" +
            "        \"standardValue\":\"建造做法3\"\n" +
            "        },\n" +
            "        \"description\":\"标准说明\",\n" +
            "        \"standardValue\":\"建造做法3\"\n" +
            "        }" )
    private List<StandardDescription> standardDescription;
    @ApiModelProperty(value = "单方造价-区间范围（不含税）", required = false,example = "1.3~3.4")
    private String dfMinAndMax;
    @ApiModelProperty(value = "单方造价-区间范围（含税）", required = false,example = "1.3~3.4")
    private String dfMinAndMaxIncludeTax;
    @ApiModelProperty(value = "单方造价-均值（不含税）", required = false,example = "1.3")
    private String dfAvg;
    @ApiModelProperty(value = "单方造价-均值（含税）", required = false,example = "1.3")
    private String dfAvgIncludeTax;
    @ApiModelProperty(value = "综合单价-区间范围（不含税）", required = false,example = "1.3~3.4")
    private String zhdjMinAndMax;
    @ApiModelProperty(value = "综合单价-区间范围（含税）", required = false,example = "1.3~3.4")
    private String zhdjMinAndMaxIncludeTax;
    @ApiModelProperty(value = "综合单价-均值（不含税）", required = false,example = "1.3")
    private String zhdjAvg;
    @ApiModelProperty(value = "综合单价-均值（含税）", required = false,example = "1.3")
    private String zhdjAvgIncludeTax;
    @JsonIgnore
    private String stdDescription;


}
