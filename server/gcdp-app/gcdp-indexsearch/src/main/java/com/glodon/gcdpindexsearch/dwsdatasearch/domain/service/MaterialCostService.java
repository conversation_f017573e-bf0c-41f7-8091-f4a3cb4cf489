package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MaterialDTO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.MaterialVO;

import java.util.List;

/**
 * <AUTHOR>
 * @program ebq
 * @description 清单组价服务
 * @date 2023/8/17 10:00
 */

public interface MaterialCostService {
    /**
     * @Description 清单人材机组价
     * @param id
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2023/8/17
     **/
    JSONObject bqItemMaterialList(String id);

    /**
     * @Description 定额人材机组价
     * @param id
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2023/8/17
     **/
    JSONObject normItemMaterialList(String id);

    List<MaterialVO> convertMaterials(List<MaterialDTO> materialDTOS);
}
