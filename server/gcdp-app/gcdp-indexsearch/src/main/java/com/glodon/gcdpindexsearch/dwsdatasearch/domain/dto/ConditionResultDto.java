package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: yhl
 * @DateTime: 2023/8/4 10:57
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConditionResultDto extends DwsIndexProjectNote {

    private String productSource;

    /**
     * 项目地点-省id
     */
    private String provinceId;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 项目地点-省编码
     */
    private String provinceCode;

    /**
     * 项目地点-市编码
     */
    private String cityCode;
    /**
     * 项目地点-市id
     */
    private String cityId;

    /**
     * 项目地点-市名称
     */
    private String cityName;

    /**
     * 项目地点-区id
     */
    private String districtId;

    /**
     * 项目地点-区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;
}
