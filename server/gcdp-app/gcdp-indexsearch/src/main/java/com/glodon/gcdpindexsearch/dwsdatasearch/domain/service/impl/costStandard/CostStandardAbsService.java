package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.costStandard;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdp.common.domain.enums.StageEnum;
import com.glodon.gcdp.dwdservice.domain.service.project.IDwdProjectAuthEntInfoService;
import com.glodon.gcdp.dwdservice.domain.vo.OrgAuthVo;
import com.glodon.gcdpindexsearch.common.constant.BusinessConstants;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.StandardDescription;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IReferenceDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.CostStandardService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.ReferenceDataVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class CostStandardAbsService implements CostStandardService {
    @Autowired
    protected IReferenceDataRepository referenceDataRepository;

    @Autowired
    private IDwdProjectAuthEntInfoService projectAuthEntInfoService;

    abstract <T extends ReferenceDataBaseDto> List<T> doSelectReferenceDataByNoteIdsAndName(Map<String, ReferenceDataBaseDto> projectNoteIdsMap, String name, Integer itemCostType);

    @Override
    public <T extends ReferenceDataBaseDto> List<T> referenceData(ReferenceDataVO referenceDataVO) {
        log.info("1、获取项目信息-开始:[{}]", referenceDataVO);
        /** 1、获取项目信息 **/
        List<ReferenceDataBaseDto> projectInfoList = getProjectInfo(referenceDataVO);
        if (CollectionUtils.isEmpty(projectInfoList)) {
            log.info(" 2、获取项目信息-异常");
            return new ArrayList<>();
        }

        /** 2、获取项目下科目信息 **/
        Map<String, ReferenceDataBaseDto> projectNoteIdMap = getProjectNoteIdsMap(projectInfoList);
        log.info(" 2、获取项目信息-[{}],projectNoteId为[{}]", referenceDataVO, projectNoteIdMap.keySet());
        List<T> referenceDataList = doSelectReferenceDataByNoteIdsAndName(projectNoteIdMap, referenceDataVO.getName(), referenceDataVO.getItemCostType());

        /** 3、将项目记录数据相关字段数据 拷贝给 科目的对应字段上 **/
        mergeProjectInfoToReferenceData(projectInfoList, referenceDataList);

        /** 4、处理特殊字段：StandardSescription、CalculateName **/
        dealStandardDescriptionAndCalculateName(referenceDataList);

        /** 5、根据归档时间倒序 **/
        referenceDataList.sort(Comparator.comparing(ReferenceDataBaseDto::getArchiveDate, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
        log.info("3、 获取项目信息-结束");
        return referenceDataList;
    }

    private List<ReferenceDataBaseDto> getProjectInfo(ReferenceDataVO referenceDataVO) {
        OrgAuthVo orgAuthVo = projectAuthEntInfoService.countByEnterpriseId(referenceDataVO.getEnterpriseId(), referenceDataVO.getOrgIds());

        List<ReferenceDataBaseDto> projectInfoList = referenceDataRepository.selectProjectInfoByOrgIdsAndEnterprisedId(referenceDataVO.getEnterpriseId(), orgAuthVo.getOrgIds(),
                referenceDataVO.getProjectNameList(), referenceDataVO.getDataSourceList(), orgAuthVo.getIsVirtualOrg(), referenceDataVO.getAuthControlProjectCodeList());

        // 如果是目标成本数据来源的，需要根据阶段过滤出最后阶段、且归档时间最新的一份；
        List<ReferenceDataBaseDto> resNoMbcbProjectInfoList = projectInfoList.stream().filter(item ->
                !BusinessConstants.DATASOURCE_MBCB.equals(item.getDataSoure())).collect(Collectors.toList());

        List<ReferenceDataBaseDto> mbcbReferenceDataBaseDtoList = getMbcbLastReferenceDataList(projectInfoList);
        resNoMbcbProjectInfoList.addAll(mbcbReferenceDataBaseDtoList);

        return resNoMbcbProjectInfoList;
    }

    /**
     * @param projectInfoList:
     * @return java.util.List<com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto>
     * @description: 如果是目标成本数据来源的，需要根据阶段过滤出最后阶段、且归档时间最新的一份
     * @date: 2023/5/25 7:56
     */
    private List<ReferenceDataBaseDto> getMbcbLastReferenceDataList(List<ReferenceDataBaseDto> projectInfoList) {
        List<ReferenceDataBaseDto> mbcbProjectInfoList = projectInfoList.stream().filter(item ->
                BusinessConstants.DATASOURCE_MBCB.equals(item.getDataSoure())).peek(item -> item.setPhaseWeight(getPhaseWeight(item.getPhase()))).collect(Collectors.toList());


        // 获取同一 projectCode下 最新的记录数据
        Map<String, ReferenceDataBaseDto> result = mbcbProjectInfoList.stream()
                .collect(Collectors.toMap(ReferenceDataBaseDto::getProjectCode, Function.identity(), (a, b) -> a.getPhaseWeight() > b.getPhaseWeight() ? a : b));

        // 根据 projectCode 对 mbcbProjectInfoList 分组
        Map<String, List<ReferenceDataBaseDto>> indexProjectNotes = mbcbProjectInfoList.stream()
                .collect(Collectors.groupingBy(x -> x.getProjectCode() + CharConst.DOUBLE_AT + x.getContractProjectId()));

        // 根据 projectCode 和 contractProjectId 在list 中过滤出指定的数据
        List<ReferenceDataBaseDto> mapping = new LinkedList<>();
        for (Map.Entry<String, ReferenceDataBaseDto> node : result.entrySet()) {
            List<ReferenceDataBaseDto> referenceDataBaseDtos = indexProjectNotes.get(node.getKey() + CharConst.DOUBLE_AT + node.getValue().getContractProjectId());
            if (CollUtil.isNotEmpty(referenceDataBaseDtos)) {
                mapping.addAll(referenceDataBaseDtos);
            }
        }
        return mapping;
    }

    private Integer getPhaseWeight(String phase) {
        Integer code = StageEnum.getCodeByName(phase);
        Assert.notNull(code, "未知测算阶段");
        return Objects.requireNonNull(code);
    }

    private Map<String, ReferenceDataBaseDto> getProjectNoteIdsMap(List<ReferenceDataBaseDto> referenceDataProjectInfoList) {
        return referenceDataProjectInfoList.stream().collect(Collectors.toMap(item -> item.getProjectNoteId(), Function.identity(), (x, y) -> x));
    }

    /**
     * @param indexDtos:
     * @return void
     * @description: 处理特殊字段：展开 StandardValue、赋值 calculate_name
     * @date: 2023/5/19 18:18
     */
    private <T extends ReferenceDataBaseDto> void dealStandardDescriptionAndCalculateName(List<T> indexDtos) {
        indexDtos.forEach(item -> {
            /** 展开 StandardValue 为对象 **/
            String standardJson = item.getStandardValue();
            if (!StringUtils.isEmpty(standardJson)) {
                List<StandardDescription> standardDescriptions = JSON.parseArray(standardJson, StandardDescription.class);
                item.setStandardDescription(standardDescriptions);
            }

            /**非建安计算口径取 zyl_calculate_name的，建安读 swl_calculate_name 或 df_calculate_name **/
            if (item.getNonConstruction() != null && item.getNonConstruction()) {
                item.setCaculateName(item.getZylCalculateName());
            }
        });
    }

    private <T extends ReferenceDataBaseDto> void fillReferenceData(T referenceDataBase, ReferenceDataBaseDto projectInfo) {
        referenceDataBase.setArchiveDate(projectInfo.getArchiveDate());
        referenceDataBase.setNonConstruction(projectInfo.getNonConstruction());
        referenceDataBase.setCityName(projectInfo.getCityName());
        referenceDataBase.setDistrictName(projectInfo.getDistrictName());
        referenceDataBase.setProvinceName(projectInfo.getProvinceName());
        referenceDataBase.setCityId(projectInfo.getCityId());
        referenceDataBase.setProvinceId(projectInfo.getProvinceId());
        referenceDataBase.setDistrictId(projectInfo.getDistrictId());
        referenceDataBase.setProjectName(projectInfo.getProjectName());
        referenceDataBase.setNodeName(projectInfo.getNodeName());
        referenceDataBase.setProjectCategoryName(projectInfo.getProjectCategoryName());
        referenceDataBase.setFileName(projectInfo.getFileName());
        referenceDataBase.setPhase(projectInfo.getPhase());
        referenceDataBase.setProductPosition(projectInfo.getProductPosition());
    }

    private <T extends ReferenceDataBaseDto> void mergeProjectInfoToReferenceData(List<ReferenceDataBaseDto> projectInfoList, List<T> referenceDataList) {
        Map<String, ReferenceDataBaseDto> projectInfoMap = projectInfoList.stream().collect(Collectors.toMap(item -> item.getProjectNoteId(), item -> item, (x, y) -> x));
        referenceDataList.forEach(item -> fillReferenceData(item, projectInfoMap.get(item.getProjectNoteId())));
    }
}
