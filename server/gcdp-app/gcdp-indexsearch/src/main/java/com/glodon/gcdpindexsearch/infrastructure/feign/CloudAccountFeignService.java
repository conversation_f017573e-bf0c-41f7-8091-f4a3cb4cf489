package com.glodon.gcdpindexsearch.infrastructure.feign;

import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdpindexsearch.infrastructure.config.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @className: CloudFeignService
 * @description: 广联云接口调用层
 * @author: zhaoyj-g
 * @date: 2020/9/23
 **/
@FeignClient(name = "cloud-account",url = "https://account.glodon.com",configuration = FeignConfiguration.class)
public interface CloudAccountFeignService {
    @RequestMapping(method = RequestMethod.POST, value = "/v3/oauth2/token", consumes = {MediaType.APPLICATION_FORM_URLENCODED_VALUE})
    JSONObject searchToken(@RequestBody Map<String, ?> entityBody, @RequestHeader("Authorization") String accessToken);
}
