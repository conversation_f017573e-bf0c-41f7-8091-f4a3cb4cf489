package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.common.utils.UnitUtil;
import com.glodon.gcdp.dimservice.domain.dao.enums.IndexTypeIdEnum;
import com.glodon.gcdp.dwdservice.infrastructure.utils.UnitUtils;
import com.glodon.gcdpindexsearch.common.enums.IndexTypeCalcEnums;
import com.glodon.gcdpindexsearch.common.util.TreeUtils;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ItemData;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024-02-23 11:06
 * @email <EMAIL>
 */
@Service("CommonPostProcess")
public class CommonIndexPostProcessService<T extends ItemData> {

    public void processParentIndex(List<T> dataList, String indexType) {
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        Map<String,  Consumer<List<T>>> postProcessStrategyMap = getPostProcessStrategy();
        if (!postProcessStrategyMap.containsKey(indexType)) {
            return;
        }
        postProcessStrategyMap.get(indexType).accept(dataList);
    }

    private Map<String, Consumer<List<T>>> getPostProcessStrategy() {
        Map<String, Consumer<List<T>>> postProcessStrategyMap  = new HashMap<>();
        postProcessStrategyMap.put(IndexTypeCalcEnums.SWLDF.getType(), this::processSwldf);
        return postProcessStrategyMap;
    }

    private void processSwldf(List<T> originalData) {
        List<T> treeData = TreeUtils.buildIndexTree(originalData);
        treeData.forEach(this::processData);
    }

    @SuppressWarnings("unchecked")
    private void processData(T element) {
            List<T> children = (List<T>) element.getChildren();
        if (CollUtil.isEmpty(children)) {
            return;
        }
        // 从下向上遍历
        children.forEach(this::processData);
        List<T> matchChildrenData = children.stream()
                .filter(e -> Objects.nonNull(element.getUnit()) && Objects.equals(element.getUnit(), e.getUnit()) && MathUtil.notNullAndNotZero(e.getQuantity()))
                .collect(Collectors.toList());
        dealCurrentElement(element);
        if (CollUtil.isNotEmpty(matchChildrenData)) {
            BigDecimal calValueAmount = matchChildrenData.stream()
                    .map(ItemData::getQuantity)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            element.setCalcValue(calValueAmount);
            extendProcess(element);
            element.setQuantity(calValueAmount);
            // 重新构建指标单位
            element.setIndexUnit(UnitUtils.getUnit(IndexTypeIdEnum.SWLDF, UnitUtil.unitConvert(element.getUnit()), element.getCalcUnit()));
        } else {
            element.setCalcValue(null);
            extendInvalidProcess(element);
            element.setQuantity(null);
            element.setIndexUnit(null);
        }

    }

    protected void dealCurrentElement(T element) {
        // 处理当前节点
    }

    protected void extendInvalidProcess(T element) {
        // 无效数据扩展处理
    }

    protected  void extendProcess(T element) {
        // 扩展处理
    }

}
