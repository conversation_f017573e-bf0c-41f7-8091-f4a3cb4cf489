package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.costStandard;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: 企业基准成本-查询数据
 * <AUTHOR>
 * @date 2023/5/16 9:15
 */
@Service("CostStandardService-qyjzcb")
@Slf4j
public class CostStandardQYJZCBServiceImpl extends CostStandardAbsService {

    @Override
    public <T extends ReferenceDataBaseDto> List<T> doSelectReferenceDataByNoteIdsAndName(Map<String, ReferenceDataBaseDto> projectNoteIdsMap, String name, Integer itemCostType) {
        return (List<T>) referenceDataRepository.selectReferenceDataToQYJZCBLib(projectNoteIdsMap, name, itemCostType);
    }
}
