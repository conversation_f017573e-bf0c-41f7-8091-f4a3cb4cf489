package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.glodon.gcdp.common.domain.enums.ArchiveTypeEnum;
import com.glodon.gcdp.common.domain.model.ColsInfo;
import com.glodon.gcdp.common.domain.model.StandardBuilderData;
import com.glodon.gcdp.common.domain.model.StandardBuilderData.standardDesc;
import com.glodon.gcdp.common.domain.model.StandardBuilderData.value;
import com.glodon.gcdp.common.domain.model.StandardBuilderDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsNewArchiveData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwsNewArchiveDataMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ProjectPlanDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.NewArchiveDataService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.BuildStandardVO;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设计指标服务实现类
 *
 * <AUTHOR>
 * @date 2022-11-1
 */
@Service
public class NewArchiveDataServiceImpl implements NewArchiveDataService {
    @Autowired
    private DwsNewArchiveDataMapper newArchiveDataMapper;

    @Autowired
    private IDwsDataRepository dwsDataRepository;

    @Override
    public List<ProjectPlanDto> getProjectPlan(String enterpriseId, String projectCode, String phase) {
        List<DwsNewArchiveData> list = newArchiveDataMapper.getDataJsonByProjectCode(enterpriseId, projectCode, phase);
        if (list == null || list.isEmpty()) {
            return Lists.newArrayList();
        }
        List<ProjectPlanDto> result = Lists.newArrayList();
        for (DwsNewArchiveData dwsNewArchiveData : list) {
            String dataJson = dwsNewArchiveData.getDataJson();
            if (!StringUtil.isBlank(dataJson)) {
                List<ProjectPlanDto> dwsNewArchiveDataList = JSONObject.parseArray(dataJson, ProjectPlanDto.class);
                result.addAll(dwsNewArchiveDataList);
            }
        }
        return result;
    }

    @Override
    public DwsNewArchiveData getDwsNewArchiveDataByProjectCode(String enterpriseId, String projectCode) {
        LambdaQueryWrapper<DwsNewArchiveData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(enterpriseId), DwsNewArchiveData::getEnterpriseId, enterpriseId)
                .eq(StringUtils.isNotBlank(projectCode), DwsNewArchiveData::getProjectCode, projectCode)
                .eq(DwsNewArchiveData::getType, ArchiveTypeEnum.BUILD_STANDARD.getType());
        return newArchiveDataMapper.selectOne(queryWrapper);
    }

    @Override
    public StandardBuilderDto getBuildStandard(BuildStandardVO buildStandardVO) {
        StandardBuilderDto standardBuilderDto = null;
        String enterpriseId = buildStandardVO.getEnterpriseId();
        String projectCode = buildStandardVO.getProjectCode();
        List<Long> projectNoteIds = buildStandardVO.getProjectNoteIds();
        // 查询建造标准数据
        DwsNewArchiveData standardBuild = getDwsNewArchiveDataByProjectCode(enterpriseId, projectCode);
        if (standardBuild != null) {
            // 查询业态
            List<String> categoryCodes = dwsDataRepository.selectCategoryCode(projectNoteIds);
            String dataJson = standardBuild.getDataJson();
            if (StringUtils.isNotBlank(dataJson)) {
                standardBuilderDto = JSON.parseObject(dataJson, StandardBuilderDto.class);
                // 过滤业态数组
                filterColsInfo(standardBuilderDto, categoryCodes);
                // 过滤科目动态列
                filterStandard(standardBuilderDto, categoryCodes);
            }
        }
        return standardBuilderDto;
    }

    private void filterStandard(StandardBuilderDto standardBuilderDto, List<String> categoryCodes) {
        List<StandardBuilderData> standardBuilderData = standardBuilderDto.getData();
        for (StandardBuilderData data : standardBuilderData) {
            List<standardDesc> standardDesc = data.getStandardDesc();
            if (CollectionUtils.isEmpty(standardDesc)) {
                continue;
            }
            Iterator<StandardBuilderData.standardDesc> iterator = standardDesc.iterator();
            while (iterator.hasNext()) {
                StandardBuilderData.standardDesc desc = iterator.next();
                List<value> values = desc.getValue();
                // 根据业态
                List<StandardBuilderData.value> newValues = values.stream().filter(
                        c -> new ArrayList<>(categoryCodes).contains(c.getCategoryCode())
                ).collect(Collectors.toList());
                if (newValues.isEmpty() && StringUtils.isBlank(desc.getName())) {
                    iterator.remove();
                } else {
                    desc.setValue(newValues);
                }
            }
        }
    }

    private void filterColsInfo(StandardBuilderDto standardBuilderDto, List<String> categoryCodes) {
        List<ColsInfo> colsInfos = standardBuilderDto.getColsInfo();
        List<ColsInfo> newColsInfos = colsInfos.stream().filter(
                c -> new ArrayList<>(categoryCodes).contains(c.getTargetCode())
        ).collect(Collectors.toList());
        standardBuilderDto.setColsInfo(newColsInfos);
    }
}
