package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.CostDetailDTO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MakeupDetailDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MaterialDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @program ebq
 * @description 清单人材机组价
 * @date 2023/8/17 10:33
 */

@Mapper
public interface DwdBqitemCostMapper {
    /**
     * 查询清单下的组价明细
     * @param id
     * @return
     */
    List<CostDetailDTO> selectCostDetailByBqItemId(Long id);

    /**
     * 查询清单下的定额
     * @param ids
     * @return
     */
    List<MakeupDetailDto> selectNormItemByBqItemId(@Param("ids") List<Long> ids);

    /**
     * 查询清单下的消耗量明细,企标清单场景下:清单+耗量模式
     * @param id
     * @return
     */
    List<MaterialDTO> selectMaterialByBqItemId(Long id);

    /**
     * 查询清单下的工料明细:企标清单直接挂耗量的场景
     * @param ids
     * @return
     */
    List<MaterialDTO> selectMaterialByBqItemIds(@Param("ids") List<Long> ids);

    /**
     * 查询定额下的消耗量明细
     * @param ids
     * @return
     */
    List<MaterialDTO> selectMaterialByNormItemId(@Param("ids") List<Long> ids);

    /**
     * 查询清单下的所有定额id
     * @param bqItemId
     * @return
     */
    List<Long> selectNormItemIdsByBqItemId(Long bqItemId);
}
