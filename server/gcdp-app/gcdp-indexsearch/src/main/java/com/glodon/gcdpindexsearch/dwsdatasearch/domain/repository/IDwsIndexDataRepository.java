package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterIndexDataVO;

import java.util.List;
/*
 * @Class com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository IDwsIndexDataRepository
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 指标查询父类
 * @Date 10:55 2022/11/3
 **/
public interface IDwsIndexDataRepository {

    public List<ProjectIndexData> selectCostIndexList(FilterIndexDataVO filterIndexDataVO);
    public List<ProjectIndexData> selectEconomicsIndexList(FilterIndexDataVO filterIndexDataVO);
    public List<ProjectIndexData> selectMainIndexList(FilterIndexDataVO filterIndexDataVO);
    public List<ProjectIndexData> selectUsageIndexList(FilterIndexDataVO filterIndexDataVO);
    public List<DwsIndexProjectNote>  getTotal(List<String> ids);

    /**
     * 查询科目维度成本指标结果数据
     *
     * @param ids
     * @return
     */
    List<GcdpDwsItemIndexCost> selectCostItemIndexList(List<String> ids, Integer showAll);

    /**
     * 查询科目维度含量指标结果
     * @param ids
     * @return
     */
    List<GcdpDwsItemIndexUsage> selectUsageItemIndexList(List<String> ids, Integer showAll);

    /**
     * 查询科目维度经济技术指标数据
     * @param ids
     * @return
     */
    List<ProjectIndexData> selectEconomicItemIndexList(List<String> ids, Integer showAll);

    /**
     * 查询科目维度主要工料指标数据
     * @param ids
     * @return
     */
    List<ProjectIndexData> selectMainResItemIndexList(List<String> ids, Integer showAll);

    /**
     * 按itemHash合并数据，查询成本指标数据
     * @param ids
     * @return
     */
    List<CostIndexBaseDto> selectCostIndexMergeByItemHash(List<Long> ids);

    /**
     * 按itemHash合并，查询含量指标数据
     * @param ids
     * @return
     */
    List<CostIndexBaseDto> selectUsageIndexMergeByItemHash(List<Long> ids);

    /**
     * 查询建造标准指标数据
     * @param ids
     * @return
     */
    List<CostIndexBaseDto> selectBuildStandardIndex(List<Long> ids);

    List<GcdpDwsIndex> selectItemIndexByNoteId(String indexTypePrefix, String calcType, List<Long> ids, Integer itemCostType);

    List<SingleProjectDbIndex> selectDwsIndexByNoteId(String indexTypePrefix,
                                                      String calcType,
                                                      List<Long> ids,
                                                      Integer itemCostType,
                                                      Boolean onlyXNLDFlag,
                                                      String baseTradeName);
}
