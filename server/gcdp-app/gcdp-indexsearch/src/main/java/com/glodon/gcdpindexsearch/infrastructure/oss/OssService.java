package com.glodon.gcdpindexsearch.infrastructure.oss;

import com.glodon.dcost.oss.service.DCostObjectStorageService;
import com.glodon.gcdp.common.utils.GzipUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.InputStream;

@Service
@Slf4j
@ConditionalOnProperty(value = "object-storage.enable", havingValue = "true")
public class OssService {
    @Autowired
    private DCostObjectStorageService dCostObjectStorageService;

    public boolean ossPush(String ossPath, Object obj) {
        try {
            InputStream is = GzipUtil.getZipInputStream(obj);
            return upload(ossPath, is);
        }
        catch (Exception e) {
            log.error("push to OSS failed !!! [Oss Path:{}]", ossPath);
            return Boolean.FALSE;
        }
    }

    private boolean upload(String ossPath, InputStream is) {
        return dCostObjectStorageService.upload(ossPath, is);
    }

    public class OssConstant {
        public static final String SLANT = "/";
        public static final String ZBGX_OSS_PATH = "gcdp-data/zbgx";
    }

}
