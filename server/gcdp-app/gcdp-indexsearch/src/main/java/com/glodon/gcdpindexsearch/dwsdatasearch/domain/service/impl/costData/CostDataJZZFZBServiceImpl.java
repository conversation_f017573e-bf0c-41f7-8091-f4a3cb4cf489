package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.costData;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdpindexsearch.common.constant.CostDataConstants;
import com.glodon.gcdpindexsearch.common.enums.IndexTypeEnums;
import com.glodon.gcdpindexsearch.common.util.EmptyUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.utils.StringToListUtils;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexListVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexMakeupVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 建造做法库-查询数据
 * @date 2023/5/16 9:15
 */
@Service(CostDataConstants.COST_DATA_SERVICE_PREFIX + "jzzfzb")
@Slf4j
public class CostDataJZZFZBServiceImpl extends CostDataAbsService {

    @SuppressWarnings("all")
    protected List<CostIndexMakeupBaseDto> getCostIndex(CostIndexMakeupVO costIndexMakeupVO) {
        List<Long> itemIds = StringToListUtils.convertToLongList(costIndexMakeupVO.getItemIds());
        if (EmptyUtil.isEmpty(itemIds)) {
            return Collections.emptyList();
        }

        List<? extends CostIndexMakeupBaseDto> indexDtos = null;
        IndexTypeEnums indexTypeEnum = IndexTypeEnums.fromIndex(costIndexMakeupVO.getIndexType());
        switch (indexTypeEnum) {
            // 单方造价
            case DFZJ: {
                indexDtos = dwsCostDataRepository.selectJZZFZBlibDfzjCostIndexByIds(itemIds);
                break;
            }
            // 综合单价
            case ZHDJ: {
                indexDtos = dwsCostDataRepository.selectJZZFZZBlibtZhdjCostIndexByIds(itemIds);
                break;
            }
            default:
                return new ArrayList<>();
        }
        return (List<CostIndexMakeupBaseDto>) indexDtos;
    }

    @Override
    public List<CostIndexBaseDto> costIndexList(CostIndexListVO costIndexListVO) {
        //根据组织权限和数据来源，查询条件筛选样本量
        List<DwsIndexProjectNote> indexProjectNotes = filterProjectNoteByCondition(costIndexListVO);
        if(CollUtil.isEmpty(indexProjectNotes)){
            return Lists.newArrayList();
        }
        List<Long> ids = indexProjectNotes.stream().map(DwsIndexProjectNote::getId).collect(Collectors.toList());
        //1、根据样本量查询建造做法列表
        List<CostIndexBaseDto> jzbzIndex = indexDataRepository.selectBuildStandardIndex(ids);
        //2、重新设置建造标准
        resetStandardDescription(jzbzIndex);
        //3、重新设置父子关系
        makePid(jzbzIndex);
        return jzbzIndex;
    }

    /**
     * 重新设置建造标准
     * @param jzbzIndex
     */
    private void resetStandardDescription(List<CostIndexBaseDto> jzbzIndex) {
        if(CollUtil.isEmpty(jzbzIndex)){
            return;
        }
        Map<Long,List<CostIndexBaseDto>> groupMap = jzbzIndex.stream().collect(Collectors.groupingBy(CostIndexBaseDto::getPid));
        jzbzIndex.stream().forEach(item->{
            CostIndexJZZFZBLibDto costIndexJZZFZBLibDto = (CostIndexJZZFZBLibDto)item;
            String stdDescription = costIndexJZZFZBLibDto.getStdDescription();
            List<CostIndexBaseDto> childList =  groupMap.get(costIndexJZZFZBLibDto.getId());
            //只有叶子节点才给建造做法赋值
            if(StringUtils.isNotBlank(stdDescription)&&CollUtil.isEmpty(childList)){
                List<StandardDescription> standardDescription = JSONObject.parseArray(stdDescription,StandardDescription.class);
                costIndexJZZFZBLibDto.setStandardDescription(standardDescription);
            }
        });
    }

}
