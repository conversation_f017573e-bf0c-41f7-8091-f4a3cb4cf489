package com.glodon.gcdpindexsearch.infrastructure.thread;

import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池管理类
 * <AUTHOR>
 *
 */
@Configuration
@EnableAsync
@Log4j2
public class ThreadExecutorPool{
    private static final String ASYNC_SERVICE = "async-service";

    @Bean("copyDataServiceExecutor")
    public ThreadPoolTaskExecutor asyncServiceExecutor() {
        log.info("start copyDataServiceExecutor");
        //使用自定义的VisiableThreadPoolTaskExecutor
        log.info("核心线程数:{}",6);
        ThreadPoolTaskExecutor executor = new VisiableThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(6);
        //配置最大线程数
        executor.setMaxPoolSize(8);
        //配置队列大小
        executor.setQueueCapacity(999999);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix(ASYNC_SERVICE);
        executor.setKeepAliveSeconds(180);
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean("calDataServiceExecutor")
    public ThreadPoolTaskExecutor calDataServiceExecutor() {
        log.info("start calDataServiceExecutor");
        //使用自定义的VisiableThreadPoolTaskExecutor
        log.info("核心线程数:{}",6);
        ThreadPoolTaskExecutor executor = new VisiableThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(6);
        //配置最大线程数
        executor.setMaxPoolSize(6);
        //配置队列大小
        executor.setQueueCapacity(999999);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix(ASYNC_SERVICE);
        executor.setKeepAliveSeconds(180);
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        //执行初始化
        executor.initialize();
        return executor;
    }
    @Bean("delDataServiceExecutor")
    public ThreadPoolTaskExecutor delDataServiceExecutor() {
        log.info("start delDataServiceExecutor");
        //使用自定义的VisiableThreadPoolTaskExecutor
        log.info("核心线程数:{}",6);
        ThreadPoolTaskExecutor executor = new VisiableThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(6);
        //配置最大线程数
        executor.setMaxPoolSize(8);
        //配置队列大小
        executor.setQueueCapacity(999999);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix(ASYNC_SERVICE);
        executor.setKeepAliveSeconds(180);
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean("selectDataServiceExecutor")
    public ThreadPoolTaskExecutor selectDataServiceExecutor() {
        log.info("start selectDataServiceExecutor");
        //使用自定义的VisiableThreadPoolTaskExecutor
        log.info("核心线程数:{}",20);
        ThreadPoolTaskExecutor executor = new VisiableThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(20);
        //配置最大线程数
        executor.setMaxPoolSize(20);
        //配置队列大小
        executor.setQueueCapacity(999999);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix(ASYNC_SERVICE);
        executor.setKeepAliveSeconds(180);
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean("queryDataServiceExecutor")
    public ThreadPoolTaskExecutor queryDataServiceExecutor() {
        log.info("start selectDataServiceExecutor");
        //使用自定义的VisiableThreadPoolTaskExecutor
        log.info("核心线程数:{}",20);
        ThreadPoolTaskExecutor executor = new VisiableThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(20);
        //配置最大线程数
        executor.setMaxPoolSize(20);
        //配置队列大小
        executor.setQueueCapacity(999999);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix(ASYNC_SERVICE);
        executor.setKeepAliveSeconds(180);
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean("decompressionServiceExecutor")
    public ThreadPoolTaskExecutor decompressionServiceExecutor() {
        log.info("start decompressionServiceExecutor");
        //使用自定义的VisiableThreadPoolTaskExecutor
        log.info("核心线程数:{}",6);
        ThreadPoolTaskExecutor executor = new VisiableThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(6);
        //配置最大线程数
        executor.setMaxPoolSize(8);
        //配置队列大小
        executor.setQueueCapacity(999999);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("async-service");
        executor.setKeepAliveSeconds(180);
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean("indexCalculateServiceExecutor")
    public ThreadPoolTaskExecutor indexCalculateServiceExecutor() {
        log.info("start indexCalculateServiceExecutor");
        //使用自定义的VisiableThreadPoolTaskExecutor
        log.info("核心线程数:{}",3);
        ThreadPoolTaskExecutor executor = new VisiableThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(3);
        //配置最大线程数
        executor.setMaxPoolSize(3);
        //配置队列大小
        executor.setQueueCapacity(999999);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("async-service indexCalculate");
        executor.setKeepAliveSeconds(180);
        //执行初始化
        executor.initialize();
        return executor;
    }
}
