package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexCost;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexCost;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ProjectIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexBaseDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GcdpDwsIndexCostMapper {
    List<ProjectIndexData> getIndex(@Param(value = "ids") List<String> ids);

    /**
     * 查询科目维度指标结果数据
     *
     * @param ids 单体集合
     * @return 所有成本维度的数据
     */
    List<GcdpDwsItemIndexCost> getItemIndex(@Param(value = "ids") List<String> ids, Integer showAll);

    List<DwsIndexCost> selectByItemIds(@Param("itemIdList") List<Long> itemIdList);

    List<CostIndexBaseDto> selectCostIndexMergeByItemHash(@Param(value = "ids")List<Long> ids);
}
