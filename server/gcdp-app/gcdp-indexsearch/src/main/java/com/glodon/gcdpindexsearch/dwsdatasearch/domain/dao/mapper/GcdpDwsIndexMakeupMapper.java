package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMakeup;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.CBZBKMakeupDetailDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MakeupDetailDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: 指标科目组成
 * @date 2022/11/18 14:38
 */
@Mapper
public interface GcdpDwsIndexMakeupMapper {
    List<DwsIndexMakeup> selectCostMakeupByNoteIdAndSubjectId(@Param(value = "ids")List<Long> ids, @Param(value = "itemIds")List<Long> itemIds);
    List<DwsIndexMakeup> selectUsageMakeupByNoteIdAndSubjectId(@Param(value = "ids")List<Long> ids, @Param(value = "itemIds")List<Long> itemIds);
    List<MakeupDetailDto> selectBqItemByIds(@Param(value = "ids")List<Long> ids);
    List<MakeupDetailDto> selectNormItemByIds(@Param(value = "ids")List<Long> ids);
    List<MakeupDetailDto> selectResourceItemById(@Param(value = "ids")List<Long> ids);
    List<DwsIndexMakeup> selectMakeupByNoteIdAndSubjectId(@Param(value = "ids")List<Long> noteIds, @Param(value = "itemIds")List<Long> itemIds);
    List<DwdContractProject> selectContractProjectInfo(@Param("ids") List<Long> ids);
    List<CBZBKMakeupDetailDto> selectExpendSubBqItemById(@Param("ids") Set<Long> ids);
    List<CBZBKMakeupDetailDto> selectResourceById(@Param("ids") Set<Long> ids);
    List<CBZBKMakeupDetailDto> selectBqItemById(@Param("ids") Set<Long> ids);
    List<DwsIndexMakeup> selectMainResMakeupByNoteIdAndSubjectId(@Param(value = "ids")List<Long> noteIds, @Param(value = "itemIds")List<Long> itemIds);
}
