package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

@Data
@ApiModel(value = "工程特征列表", description = "工程特征列表")
public class ProjectAttrDto {
    @ApiModelProperty(value = "专业名称", required = false, example = "xxx")
    private String tradeName;
    @ApiModelProperty(value = "专业编码", required = false, example = "xxx")
    private String tradeCode;
    @SuppressWarnings("rawtypes")
    @ApiModelProperty(value = "特征详情", required = false, example = "xxx")
    private List<LinkedHashMap> data;
    @JsonIgnore
    private Date archiveDate;
    @JsonIgnore
    private String caption;
    @JsonIgnore
    private BigDecimal buildArea;
    @ApiModelProperty(value = "列头信息", required = false, example = "xxx")
    private List<ColsDto> cols;

}

