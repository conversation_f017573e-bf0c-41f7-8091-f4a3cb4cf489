package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.BqItemDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.CBZBKMakeupDetailDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.MakeupDetailBaseVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.MakeupQueryVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 清单组成
 * @date 2022/11/18 13:57
 */
public interface MakeupDetailService {
    List<MakeupDetailBaseVO> makeupDetail(MakeupQueryVO makeupQueryVO);

    List<BqItemDto> ldMakeupDetail(MakeupQueryVO itemConditionVO);

    List<CBZBKMakeupDetailDto> costIndexDetail(MakeupQueryVO makeupQueryVO);
}
