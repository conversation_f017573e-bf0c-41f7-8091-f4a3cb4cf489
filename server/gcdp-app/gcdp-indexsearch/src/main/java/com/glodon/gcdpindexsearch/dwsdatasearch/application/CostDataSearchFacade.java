package com.glodon.gcdpindexsearch.dwsdatasearch.application;


import com.glodon.gcdp.common.domain.enums.ProductSource;
import com.glodon.gcdp.common.utils.SpringUtil;
import com.glodon.gcdpindexsearch.common.constant.CostDataConstants;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexConditionDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.CostIndexMakeupBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.CostDataService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexListVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexMakeupVO;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 建造做法指标库、全成本指标库查询数据Facade
 * @date 2023/5/16 14:27
 */
@Component
public class CostDataSearchFacade {
    private final String costDataServicePrefix = CostDataConstants.COST_DATA_SERVICE_PREFIX;
    public List<CostIndexMakeupBaseDto> costIndexMakeup(CostIndexMakeupVO costIndexMakeupVO) {
        CostDataService costDataService = SpringUtil.getBean(costDataServicePrefix + costIndexMakeupVO.getType());
        return costDataService.costIndexMakeup(costIndexMakeupVO);
    }

    /**
     * 反推查询条件
     *
     * @param enterpriseId
     * @param type
     * @param orgIds
     * @param authControlProjectCodeList
     * @return
     */
    public CostIndexConditionDto getCondition(String enterpriseId, String type, List<String> orgIds, List<String> authControlProjectCodeList) {
        CostDataService costDataService = SpringUtil.getBean(costDataServicePrefix + type);
        List<String> productSource = Lists.newArrayList(ProductSource.MBCB.getIndex());
        if ("qcbzb".equals(type)) {
            productSource.add(ProductSource.TZGSBZ.getIndex());
        }
        return costDataService.getCondition(enterpriseId, orgIds, productSource, authControlProjectCodeList);
    }

    public List<CostIndexBaseDto> costIndexList(CostIndexListVO costIndexListVO)throws Exception {
        CostDataService costDataService = SpringUtil.getBean(costDataServicePrefix + costIndexListVO.getType());
        return costDataService.costIndexList(costIndexListVO);
    }
}
