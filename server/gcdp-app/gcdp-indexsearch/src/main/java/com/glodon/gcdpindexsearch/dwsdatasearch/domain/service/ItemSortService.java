package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ItemData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.ItemIndexQueryReqVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 科目/工程维度查询结果排序服务类
 * @date 2023-12-18 14:38
 * @email <EMAIL>
 */
public interface ItemSortService {

    /**
     * 科目维度查询结果排序
     *
     * @param res                 查询结果
     * @param itemIndexQueryReqVO 企业id
     * @param indexType
     * <AUTHOR>
     * @date 2023-12-18 14:54:17
     */
    <T extends ItemData> List<T> subjectQueryItemSort(List<T> res, ItemIndexQueryReqVO itemIndexQueryReqVO, String indexType);

}
