package com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdpindexsearch.common.enums.IndexTypePrefixEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsIndex;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: IndexCalcMergeProcessor
 * @author: lml
 * @description: 计算口径维度维度处理器
 */
@SuppressWarnings("all")
@Slf4j
public class IndexCalcMergeProcessor extends AbstractIndexProcessor<GcdpDwsIndex> {
    @Override
    public List<GcdpDwsIndex> process(List<GcdpDwsIndex> dataList, String indexType) {
        //给单条数据设置指标值
        setIndexValue(dataList);
        Map<String, List<GcdpDwsIndex>> hashIndexMap = dataList.parallelStream().collect(Collectors.groupingBy(GcdpDwsIndex::getIndexWithCalcMergeHash));
        log.info("CalcMergeProcessor处理dataList的数据长度为:[{}] hashIndexMap的长度为:[{}]", dataList.size(), hashIndexMap.size());
        if (dataList.size() == hashIndexMap.size()) {
            return processNext(dataList, indexType);
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<GcdpDwsIndex> indexes = Collections.synchronizedList(new LinkedList<>());
        hashIndexMap.entrySet().parallelStream().forEach(node -> {
            List<GcdpDwsIndex> index = node.getValue();
            GcdpDwsIndex source = CollUtil.getFirst(index);
            if (index.size() == 1) {
                indexes.add(source);
                return;
            }
            GcdpDwsIndex dest = new GcdpDwsIndex();
            BeanUtil.copyProperties(source, dest);
            indexes.add(dest);
            combineReturnField(index, dest, indexType);
        });
        stopWatch.stop();
        log.info("CalcMergeProcessor维度数据组装耗时为:[{}]秒", stopWatch.getLastTaskInfo().getTimeSeconds());
        return processNext(indexes, indexType);
    }

    /**
     * 将数据库的处理逻辑改成多线程处理了  有可能会存在同一栋楼被划分在多个组里, 所以需要在在数据汇总的时候 进行一次手动计算
     *
     * @param indexes 指标数据
     * @param dest    组装的数据
     */
    private void combineReturnField(List<GcdpDwsIndex> indexes, GcdpDwsIndex dest, String type) {
        List<String> tempNoteIds = new ArrayList<>(indexes.size());
        List<String> itemIds = new ArrayList<>(indexes.size());

        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal amountIncludeTax = BigDecimal.ZERO;
        BigDecimal calculateValue = BigDecimal.ZERO;
        BigDecimal indexValue = BigDecimal.ZERO;
        BigDecimal indexValueIncludeTax = BigDecimal.ZERO;
        // 计算口径取最新
        calculateValue = indexes.stream()
                .sorted(Comparator.comparing(GcdpDwsIndex::getArchiveDate, Comparator.nullsLast(Comparator.reverseOrder())))
                .map(GcdpDwsIndex::getCalcValue)
                .filter(Objects::nonNull)
                .findFirst().orElse(null);

        for (GcdpDwsIndex index : indexes) {
            if (StringUtils.isNotBlank(index.getItemIds())) {
                itemIds.add(index.getItemIds());
            }
            if (StringUtils.isNotBlank(index.getTempNoteIds())) {
                tempNoteIds.add(index.getTempNoteIds());
            }
            // 实物量单方计算口径取合计
            if (IndexTypePrefixEnums.SWLDF.getType().equals(type) && MathUtil.notNullAndNotZero(index.getCalcValue())) {
                calculateValue = NumberUtil.add(calculateValue, index.getCalcValue());
            }
            if (MathUtil.notNullAndNotZero(index.getAmount())) {
                amount = NumberUtil.add(amount, index.getAmount());
            }
            if (MathUtil.notNullAndNotZero(index.getAmountIncludeTax())) {
                amountIncludeTax = NumberUtil.add(amountIncludeTax, index.getAmountIncludeTax());
            }
        }

        //计算指标值
        if (MathUtil.notNullAndNotZero(amount)) {
            indexValue = MathUtil.div(amount, calculateValue);
        }
        if (MathUtil.notNullAndNotZero(amountIncludeTax)) {
            indexValueIncludeTax = MathUtil.div(amountIncludeTax, calculateValue);
        }

        dest.setTotal(indexes.stream().map(GcdpDwsIndex::getTotal).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        dest.setTotalIncludeTax(indexes.stream().map(GcdpDwsIndex::getTotalIncludeTax).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        dest.setIndexValue(indexValue);
        dest.setIndexValueIncludeTax(indexValueIncludeTax);
        dest.setAmount(amount);
        dest.setAmountIncludeTax(amountIncludeTax);
        dest.setCalcValue(calculateValue);
        dest.setItemIds(StrUtil.join(StrUtil.COMMA, itemIds));
        dest.setTempNoteIds(StrUtil.join(StrUtil.COMMA, tempNoteIds));
    }

    /**
     * @description: 按合并之后计算口径重新算指标值
     * @author: luoml-b
     * @date: 2023/11/27 10:16
     * @param: dataList
     **/
    private void setIndexValue(List<GcdpDwsIndex> dataList) {
        dataList.forEach(item -> {
            if (MathUtil.notNullAndNotZero(item.getAmount())) {
                item.setIndexValue(MathUtil.div(item.getAmount(), item.getCalcValue()));
            }
            if (MathUtil.notNullAndNotZero(item.getAmountIncludeTax())) {
                item.setIndexValueIncludeTax(MathUtil.div(item.getAmountIncludeTax(), item.getCalcValue()));
            }
        });
    }
}
