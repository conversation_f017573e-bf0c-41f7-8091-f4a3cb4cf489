package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsIndex;
import com.glodon.gcdpindexsearch.common.enums.FilterTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.CostJmMakeupIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.MakeupIndexDetailService;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("DuplicatedCode")
@Service("makeupIndexDetailServiceJMDF")
@Slf4j
public class CostJmMakeupIndexDetailServiceImpl implements MakeupIndexDetailService {
    @Autowired
    private GcdpDwsIndexMapper gcdpDwsIndexMapper;

    @Override
    public List<MakeUpIndexData> listMakeupIndexDetail(List<MakeUpIndexData> indexDataList, String itemIds, FilterTypeEnums filterType) {
        if (CollUtil.isEmpty(indexDataList)) {
            return Lists.newArrayList();
        }
        //查询科目信息
        List<DwsIndex> dwsIndices = assembleIndexes(itemIds, filterType);
        if (CollUtil.isEmpty(dwsIndices)) {
            return Lists.newArrayList();
        }
        log.info("cost={}", dwsIndices.size());
        return getIndexData(indexDataList, dwsIndices);
    }

    /**
     * 查询指标数据
     * @param itemIds
     * @param filterType
     * @return
     */
    List<DwsIndex> assembleIndexes(String itemIds, FilterTypeEnums filterType){
        List<Long> itemIdList = Arrays.stream(itemIds.split(Constant.COMMA)).map(Long::new).collect(Collectors.toList());
        List<DwsIndex> dwsIndices = gcdpDwsIndexMapper.selectByItemIds(itemIdList);

        if (CollUtil.isEmpty(dwsIndices)){
            return Lists.newArrayList();
        }

        switch (filterType){
            case NO_TAX_VALID:
                return dwsIndices.stream()
                        .filter(x -> (x.getJmdfIndexValue() != null && x.getJmdfIndexValue().compareTo(BigDecimal.ZERO) != 0)).collect(Collectors.toList());
            case INCLUDE_TAX_VALID:
                return dwsIndices.stream()
                        .filter(x -> (x.getJmdfIndexValueIncludeTax() != null && x.getJmdfIndexValueIncludeTax().compareTo(BigDecimal.ZERO) != 0)).collect(Collectors.toList());
            default:
                return dwsIndices.stream()
                        .filter(x -> (x.getJmdfIndexValueIncludeTax() != null && x.getJmdfIndexValueIncludeTax().compareTo(BigDecimal.ZERO) != 0)
                                || (x.getJmdfIndexValue() != null && x.getJmdfIndexValue().compareTo(BigDecimal.ZERO) != 0)).collect(Collectors.toList());
        }
    }

    private List<MakeUpIndexData> getIndexData(List<MakeUpIndexData> indexDataList, List<DwsIndex> dwsIndices) {
        List<MakeUpIndexData> data = new ArrayList<>();
        Map<String, List<DwsIndex>> calculateValueMap = dwsIndices.stream().collect(Collectors.groupingBy(DwsIndex::getJmdfIndexWithCalcMergeHash));
        for (Map.Entry<String, List<DwsIndex>> entry : calculateValueMap.entrySet()) {
            List<DwsIndex> dwsIndexList = entry.getValue();
            CostJmMakeupIndexData costJmMakeupIndexData = new CostJmMakeupIndexData();
            MakeUpIndexData makeUpIndexData = this.setSharedFields(indexDataList, entry);
            if (makeUpIndexData == null) {
                continue;
            }
            BeanUtils.copyProperties(makeUpIndexData, costJmMakeupIndexData);
            setIndexDataField(costJmMakeupIndexData, dwsIndexList);
            data.add(costJmMakeupIndexData);
        }
        return data;
    }

    private void setIndexDataField(CostJmMakeupIndexData costJmMakeupIndexData, List<DwsIndex> dwsIndices) {
        DwsIndex dwsIndex = dwsIndices.get(0);
        costJmMakeupIndexData.setName(dwsIndex.getName());
        costJmMakeupIndexData.setUnit(dwsIndex.getJmdfIndexUnit());
        //计算口径取最新归档的
        BigDecimal calValue = dwsIndices.stream().filter(item -> MathUtil.notNullAndNotZero(item.getJmdfIndexValue())
                        || MathUtil.notNullAndNotZero(item.getJmdfIndexValueIncludeTax()))
                .sorted(Comparator.comparing(DwsIndex::getArchiveDate).reversed())
                .map(DwsIndex::getJmCalculateValue).findFirst().orElse(null);
        setValue(costJmMakeupIndexData, dwsIndices, calValue);
    }

    private void setValue(CostJmMakeupIndexData costJmMakeupIndexData, List<DwsIndex> dwsIndices, BigDecimal jmCalculateValue) {
        BigDecimal amount = dwsIndices.stream().filter(x -> x.getJmCalculateValue() != null && x.getJmdfIndexValue() != null)
                .map(item -> MathUtil.isGreaterZero(item.getAmount()) ? item.getAmount() : item.getJmCalculateValue().multiply(item.getJmdfIndexValue()).setScale(6, RoundingMode.HALF_DOWN))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal amountIncludeTax = dwsIndices.stream().filter(x -> x.getJmCalculateValue() != null && x.getJmdfIndexValueIncludeTax() != null)
                .map(item -> MathUtil.isGreaterZero(item.getAmountIncludeTax()) ? item.getAmountIncludeTax() : item.getJmCalculateValue().multiply(item.getJmdfIndexValueIncludeTax()).setScale(6, RoundingMode.HALF_DOWN))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        costJmMakeupIndexData.setAmount(amount.toString());
        costJmMakeupIndexData.setAmountIncludeTax(amountIncludeTax.toString());

        if (jmCalculateValue == null) {
            costJmMakeupIndexData.setBuildArea(Constant.LINE);
            costJmMakeupIndexData.setJmIndexValue(Constant.LINE);
        } else if (BigDecimal.ZERO.compareTo(jmCalculateValue) == 0) {
            costJmMakeupIndexData.setJmIndexValue(Constant.LINE);
            costJmMakeupIndexData.setBuildArea(jmCalculateValue.toString());
        } else {
            costJmMakeupIndexData.setJmIndexValue(amount.divide(jmCalculateValue, 6, RoundingMode.HALF_DOWN).toString());
            costJmMakeupIndexData.setJmIndexValueIncludeTax(amountIncludeTax.divide(jmCalculateValue, 6, RoundingMode.HALF_DOWN).toString());
            costJmMakeupIndexData.setBuildArea(jmCalculateValue.toString());
        }
    }

}
