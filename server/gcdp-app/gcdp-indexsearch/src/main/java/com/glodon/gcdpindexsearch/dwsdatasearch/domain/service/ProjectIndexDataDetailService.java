package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.IndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ItemIndex;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.SingleProjectMakeUp;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterIndexDataVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.SingleProjectIndexReqVO;

/**
 * @description: 查询指标Service
 * <AUTHOR>
 * @date 2022/10/24 10:58
 */
public interface ProjectIndexDataDetailService {


    /**
     * @description: 查询4类指标
     * @param filterIndexDataVO
     * @return com.glodon.gcdpindexsearch.dwsdatasearch.application.dto.ConditionDto
     * <AUTHOR>
     * @date 2022/10/25 18:35
     */
    IndexData getIndexDataDetail(FilterIndexDataVO filterIndexDataVO);
    /**
     * @description: 详情查询4类指标
     * @param filterIndexDataVO
     * @return com.glodon.gcdpindexsearch.dwsdatasearch.application.dto.ConditionDto
     * <AUTHOR>
     * @date 2022/10/25 18:35
     */
    IndexData getItemIndexDataDetail(FilterIndexDataVO filterIndexDataVO)throws Exception;

    /**
     * 工程维度查询:按单指标结构返回
     *
     * @param reqVO
     * @param makeUp
     * @return
     */
    ItemIndex buildItemIndex(SingleProjectIndexReqVO reqVO, SingleProjectMakeUp makeUp) throws Exception;

}
