package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/26 18:29
 */
@Data
@ApiModel(value = "单体信息", description = "单体信息")
public class DtInfoDto {

    @ApiModelProperty(value = "单体唯一标识id", required = false, example = "10446,10447")
    private Long tempNoteId;
    private Long contractProjectId;
    @ApiModelProperty(value = "原单体唯一标识id", required = false, example = "10446,10447")
    private Long zbProjectNoteResId;
    @ApiModelProperty(value = "数据来源", required = false, example = "zbsq")
    private String productSource;
    @ApiModelProperty(value = "原始楼栋名称", required = false, example = "1#-23")
    private String originalDtName;
    @ApiModelProperty(value = "科目费用类型，1全费 2非全费", required = false, example = "1")
    private Integer itemCostType;
}
