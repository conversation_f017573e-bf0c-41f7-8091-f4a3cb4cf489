package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/26 18:29
 */
@Data
@ApiModel(value = "工程维度列表", description = "工程维度列表")
public class ProjectSearchDto {

    @ApiModelProperty(value = "单体唯一标识id集合", required = false, example = "10446,10447")
    private List<Long> ids;
    @ApiModelProperty(value = "项目编码", required = false, example = "XMHA2210240002")
    private String projectCode;
    @ApiModelProperty(value = "项目名称", required = false, example = "bai审核")
    private String projectName;
    @ApiModelProperty(value = "业态", required = false, example = "超高层办公楼-1")
    private String category;
    @ApiModelProperty(value = "单体工程", required = false, example = "高层、底层")
    private String ldNameIdentify;
    @ApiModelProperty(value = "省编码", required = false, example = "11")
    private String provinceId;
    @ApiModelProperty(value = "省名称", required = false, example = "河南")
    private String provinceName;
    @ApiModelProperty(value = "市编码", required = false, example = "149")
    private String cityId;
    @ApiModelProperty(value = "市名称", required = false, example = "郑州")
    private String cityName;
    @ApiModelProperty(value = "区编码", required = false, example = "1251")
    private String districtId;
    @ApiModelProperty(value = "区名称", required = false, example = "金水区")
    private String districtName;
    @ApiModelProperty(value = "总项目规模/建筑面积", required = false, example = "0")
    private BigDecimal buildArea;
    @ApiModelProperty(value = "总造价（不含税）", required = false, example = "0")
    private BigDecimal amount;
    @ApiModelProperty(value = "总造价（含税）", required = false, example = "0")
    private BigDecimal amountIncludeTax;
    @ApiModelProperty(value = "单方造价（不含税）", required = false, example = "0")
    private BigDecimal dfIndexValue;
    @ApiModelProperty(value = "单方造价（含税）", required = false, example = "0")
    private BigDecimal dfIndexValueIncludeTax;
    @ApiModelProperty(value = "造价类型", required = false, example = "结算价")
    private String phase;
    @ApiModelProperty(value = "项目创建时间", required = false)
    private Date createDate;
    @ApiModelProperty(value = "工程归档时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date archiveDate;
    @ApiModelProperty(value = "标识", required = false, example = "0")
    private Integer initFlag;
    @ApiModelProperty(value = "企业编码", required = false, example = "0")
    private String enterpriseId;

    @ApiModelProperty(value = "全费总造价（不含税）", required = false, example = "0")
    private BigDecimal fullCostAmount;
    @ApiModelProperty(value = "全费总造价（含税）", required = false, example = "0")
    private BigDecimal fullCostAmountIncludeTax;
    @ApiModelProperty(value = "全费单方造价（不含税）", required = false, example = "0")
    private BigDecimal fullCostDfIndexValue;
    @ApiModelProperty(value = "全费单方造价（含税）", required = false, example = "0")
    private BigDecimal fullCostDfIndexValueIncludeTax;

    @ApiModelProperty(value = "非全费总造价（不含税）", required = false, example = "0")
    private BigDecimal nonFullCostAmount;
    @ApiModelProperty(value = "非全费总造价（含税）", required = false, example = "0")
    private BigDecimal nonFullCostAmountIncludeTax;
    @ApiModelProperty(value = "非全费单方造价（不含税）", required = false, example = "0")
    private BigDecimal nonFullCostDfIndexValue;
    @ApiModelProperty(value = "非全费单方造价（含税）", required = false, example = "0")
    private BigDecimal nonFullCostDfIndexValueIncludeTax;

    /**
     * 永久唯一标识
     */
    @ApiModelProperty(value = "单体唯一标识", required = false, example = "0")
    private String dtIdentity;
    /**
     * 数据来源
     */
    private List<String> productSource;
    /**
     * 单体信息
     */
    private List<DtInfoDto> dtInfoDtoList = Lists.newArrayList();


}
