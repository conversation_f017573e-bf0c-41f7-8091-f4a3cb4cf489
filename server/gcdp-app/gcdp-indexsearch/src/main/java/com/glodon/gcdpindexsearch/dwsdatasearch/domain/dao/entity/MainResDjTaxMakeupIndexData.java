package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "主要工料单价指标（含税）-指标组成 ",description = "主要工料单价指标（含税）-指标组成")
public class MainResDjTaxMakeupIndexData extends MakeUpIndexData{
    @ApiModelProperty(value = "主要工料单价指标（含税）")
    private String djTaxIndexValue;
    @ApiModelProperty(value = "科目单位")
    private String unit;
    @ApiModelProperty(value = "含税合价")
    private String taxMarketAmount;
    @ApiModelProperty(value = "消耗量")
    private String quantity;
}
