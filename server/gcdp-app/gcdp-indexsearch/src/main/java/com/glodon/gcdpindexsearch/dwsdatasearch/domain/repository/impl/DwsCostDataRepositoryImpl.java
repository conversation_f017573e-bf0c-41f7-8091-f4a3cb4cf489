package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwsCostDataSearchMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsCostDataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public class DwsCostDataRepositoryImpl implements IDwsCostDataRepository {
    @Autowired
    private DwsCostDataSearchMapper dwsCostDataSearchMapper;
    @Override
    public List<CostIndexMakeupQCBZBLibJMDFDto> selectQCBZBLibJmdfCostIndexByIds(List<Long> itemIds) {
        return dwsCostDataSearchMapper.selectQCBZBLibJmdfCostIndexByIds(itemIds);
    }

    @Override
    public List<CostIndexMakeupQCBZBLibDFHLDto> selectQCBZBLibDfhlCostIndexByIds(List<Long> itemIds) {
        return dwsCostDataSearchMapper.selectQCBZBLibDfhlCostIndexByIds(itemIds);
    }

    @Override
    public List<CostIndexMakeupQCBZBLibDFZJDto> selectQCBZBlibDfzjCostIndexByIds(List<Long> itemIds) {
        return dwsCostDataSearchMapper.selectQCBZBlibDfzjCostIndexByIds(itemIds);
    }

    @Override
    public List<CostIndexMakeupQCBZBLibZHDJDto> selectQCBZBlibZhdjCostIndexByIds(List<Long> itemIds) {
        return dwsCostDataSearchMapper.selectQCBZBlibZhdjCostIndexByIds(itemIds);
    }

    @Override
    public List<CostIndexMakeupJZZFZBLibDFZJDto> selectJZZFZBlibDfzjCostIndexByIds(List<Long> itemIds) {
        return dwsCostDataSearchMapper.selectJZZFZBLibDfzjCostIndexByIds(itemIds);
    }

    @Override
    public List<CostIndexMakeupJZZFZBLibZHDJDto> selectJZZFZZBlibtZhdjCostIndexByIds(List<Long> itemIds) {
        return dwsCostDataSearchMapper.selectJZZFZBLibZhdjCostIndexByIds(itemIds);
    }

    @Override
    public List<CostIndexMakeupBaseDto> selectProjectInfoByNoteIds(Set<Long> indexProjectNoteIds) {
        return dwsCostDataSearchMapper.selectProjectInfoByNoteIds(indexProjectNoteIds);
    }
}
