package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.domain.enums.ProductSource;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dimservice.domain.dao.enums.IndexCategoryDictEnum;
import com.glodon.gcdpindexsearch.common.enums.IndexTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.IndexDataDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ProjectIndexDataDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterIndexDataVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.SingleProjectIndexReqVO;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * @Class com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl DwsIndexMainDataRepositoryImpl
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 主要量指标查询
 * @Date 10:56 2022/11/3
 **/
@Service("indexMain")
public class DwsIndexMainDataDetailServiceImpl extends IndexDataDetailService implements ProjectIndexDataDetailService {

    @Autowired
    IDwsIndexDataRepository indexDataRepository;

    @Override
    public IndexData getIndexDataDetail(FilterIndexDataVO filterIndexDataVO) {
        List<ProjectIndexData> result = indexDataRepository.selectMainIndexList(filterIndexDataVO);
        makePid(result);

        // note信息
        List<DwsIndexProjectNote> noteList = indexDataRepository.getTotal(filterIndexDataVO.getIds());
        List<String> ids = noteList.stream().map(x -> x.getId().toString()).collect(Collectors.toList());

        calcIndexValue(noteList, result);
        if (filterIndexDataVO.getShowAll() == null || filterIndexDataVO.getShowAll() == 0) {
            result = filterInvalidData(result);
        }
        makeCode(result);
        return new IndexData(ids, result);
    }

    private void calcIndexValue(List<DwsIndexProjectNote> noteList, List<ProjectIndexData> result) {
        BigDecimal totalIncludeTax = getTotalIncludeTax(noteList);
        result.forEach(item -> {
            GcdpDwsIndexMainRes gcdpDwsIndexMainRes = (GcdpDwsIndexMainRes) item;
            gcdpDwsIndexMainRes.setDjIndexValueIncludeTax(MathUtil.divStr(gcdpDwsIndexMainRes.getDjTotalTax(), gcdpDwsIndexMainRes.getDjCalculateValue()));
            gcdpDwsIndexMainRes.setDjIndexValue(MathUtil.divStr(gcdpDwsIndexMainRes.getDjTotal(), gcdpDwsIndexMainRes.getDjCalculateValue()));
            gcdpDwsIndexMainRes.setHlIndexValue(MathUtil.divStr(gcdpDwsIndexMainRes.getHlTotal(), gcdpDwsIndexMainRes.getHlCalculateValue()));
            gcdpDwsIndexMainRes.setProjectUnLdMergeHash(gcdpDwsIndexMainRes.getProjectUnLdMergeHash());
            gcdpDwsIndexMainRes.setPercent(MathUtil.toPercent(MathUtil.divStr(gcdpDwsIndexMainRes.getDjTotalTax(), totalIncludeTax)));

            gcdpDwsIndexMainRes.setCalculateValue(gcdpDwsIndexMainRes.getHlCalculateValue());
            // 处理计算口径单位
            gcdpDwsIndexMainRes.setCalculateUnit(gcdpDwsIndexMainRes.getUnit());
            gcdpDwsIndexMainRes.setTotal(gcdpDwsIndexMainRes.getDjTotal());
            gcdpDwsIndexMainRes.setTotalIncludeTax(gcdpDwsIndexMainRes.getDjTotalTax());
            gcdpDwsIndexMainRes.setQuantity(gcdpDwsIndexMainRes.getHlTotal());

        });
    }

    public String getCalculateUnitFromIndexUnit(String indexUnit) {
        if (StringUtils.isBlank(indexUnit)) {
            return null;
        }
        String[] units = indexUnit.split("/");
        if (units.length > 1 && !Objects.equals("-", units[1])) {
            return units[1];
        }
        return null;
    }

    @Override
    public IndexData getItemIndexDataDetail(FilterIndexDataVO filterIndexDataVO) throws Exception {
        //判断ids 是否为空，如果为空则根据筛选条件过滤出ids
        List<DwsIndexProjectNote> dwsIndexProjectNotes = getIdsByFilter(filterIndexDataVO);
        if (CollectionUtils.isEmpty(dwsIndexProjectNotes)) {
            return new IndexData();
        }
        List<String> ids = dwsIndexProjectNotes.stream().map(x -> x.getId().toString()).collect(Collectors.toList());
        //根据id 查询指标科目
        List<ProjectIndexData> result = indexDataRepository.selectMainResItemIndexList(ids, filterIndexDataVO.getShowAll());
        makePid(result);
        makeCode(result);
        for (ProjectIndexData indexData : result) {
            GcdpDwsItemIndexMainRes itemIndexMainRes = (GcdpDwsItemIndexMainRes) indexData;
            setDjUnit(itemIndexMainRes);
            setDjTaxUnit(itemIndexMainRes);
            setGlhlUnit(itemIndexMainRes);
            setMaxAndMin(itemIndexMainRes);
            itemIndexMainRes.setDjDictCode(IndexCategoryDictEnum.ZYGLDJZB.getCode());
            itemIndexMainRes.setDjTaxDictCode(IndexCategoryDictEnum.ZYGLDJZB_TAX.getCode());
            itemIndexMainRes.setGlhlDictCode(IndexCategoryDictEnum.ZYGLHLZB.getCode());
            itemIndexMainRes.setItemIds(itemIndexMainRes.getIds());
        }
        return new IndexData(ids, result);
    }

    /**
     * 按单指标接口形式封装主要工料指标的返回结构
     * @param reqVO
     * @param makeUp
     * @return
     */
    @Override
    public ItemIndex buildItemIndex(SingleProjectIndexReqVO reqVO, SingleProjectMakeUp makeUp) {
        FilterIndexDataVO vo = new FilterIndexDataVO();
        vo.setIds(reqVO.getTempNodeIds());
        List<ProjectIndexData> result = indexDataRepository.selectMainIndexList(vo);
        makePid(result);

        List<DwsIndexProjectNote> noteList = indexDataRepository.getTotal(vo.getIds());
        calcIndexValue(noteList, result);

        if (reqVO.getShowAll() == null || reqVO.getShowAll() == 0) {
            result = filterInvalidData(result);
        }
        makeCode(result);
        return buildReturnValue(IndexTypeEnums.INDEX_MAIN.getName(), result);
    }

    /**
     * 转换主要工料指标数据为单指标接口返回的数据类型
     * @param indexType
     * @param indexData
     * @return
     */
    private ItemIndex buildReturnValue(String indexType, List<ProjectIndexData> indexData) {
        List<SingleProjectIndexMainResValue> itemValueList = Lists.newArrayList();
        indexData.forEach(item -> {
            SingleIndexMainResValueDto indexValueDto = new SingleIndexMainResValueDto();
            BeanUtils.copyProperties(item, indexValueDto);
            Map<String, SingleIndexMainResValueDto> indexes = Maps.newHashMap();
            indexes.put(indexType, indexValueDto);

            // 指标节点
            SingleProjectIndexMainResValue itemValue = new SingleProjectIndexMainResValue();
            BeanUtils.copyProperties(item, itemValue);
            itemValue.setIndexes(indexes);
            itemValueList.add(itemValue);
        });

        return new ItemIndex(Collections.singletonList(indexType), itemValueList, null);
    }

    private void setMaxAndMin(GcdpDwsItemIndexMainRes itemIndexMainRes) {
        itemIndexMainRes.setDjMaxAndMin(MathUtil.parseStandardMaxAndMin(itemIndexMainRes.getDjMaxAndMin()));
        itemIndexMainRes.setDjTaxMaxAndMin(MathUtil.parseStandardMaxAndMin(itemIndexMainRes.getDjTaxMaxAndMin()));
        itemIndexMainRes.setGlhlMaxAndMin(MathUtil.parseStandardMaxAndMin(itemIndexMainRes.getGlhlMaxAndMin()));
    }


    private void setDjUnit(GcdpDwsItemIndexMainRes itemIndexMainRes) {
        String unit = StringUtils.isNotBlank(itemIndexMainRes.getUnit()) ? itemIndexMainRes.getUnit() : Constant.LINE;
        itemIndexMainRes.setDjUnit(Constant.YUAN + Constant.Slash + unit);
    }

    private void setDjTaxUnit(GcdpDwsItemIndexMainRes itemIndexMainRes) {
        String unit = StringUtils.isNotBlank(itemIndexMainRes.getUnit()) ? itemIndexMainRes.getUnit() : Constant.LINE;
        itemIndexMainRes.setDjTaxUnit(Constant.YUAN + Constant.Slash + unit);
    }

    private void setGlhlUnit(GcdpDwsItemIndexMainRes itemIndexMainRes) {
        String hlCalName = itemIndexMainRes.getHlCalculateName();
        if (StringUtils.isNotBlank(hlCalName) && hlCalName.contains("(")) {
            hlCalName = StringUtils.isNotBlank(hlCalName) ? hlCalName.substring(hlCalName.indexOf("(") + 1, hlCalName.indexOf(")")) : Constant.LINE;
        } else {
            hlCalName = Constant.M2;
        }
        String unit = StringUtils.isNotBlank(itemIndexMainRes.getUnit()) ? itemIndexMainRes.getUnit() : Constant.LINE;
        itemIndexMainRes.setGlhlUnit(unit + Constant.Slash + hlCalName);
    }

    private BigDecimal getTotalIncludeTax(List<DwsIndexProjectNote> noteList) {

        if (CollUtil.isEmpty(noteList)) {
            return BigDecimal.ZERO;
        }

        return noteList.parallelStream()
                .filter(x -> ProductSource.ZBSQ.getIndex().equals(x.getProductSource()) ||
                        ProductSource.ZBW.getIndex().equals(x.getProductSource()) ||
                        ProductSource.HGW.getIndex().equals(x.getProductSource()) ||
                        ProductSource.GBQD.getIndex().equals(x.getProductSource()) ||
                        ProductSource.ZBGX.getIndex().equals(x.getProductSource()))
                .map(DwsIndexProjectNote::getTotalIncludeTax).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }
}

