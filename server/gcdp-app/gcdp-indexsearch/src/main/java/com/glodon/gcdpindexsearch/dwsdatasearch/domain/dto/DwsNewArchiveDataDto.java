package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DwsNewArchiveDataDto implements Serializable {


    private Long id;

    /**
     * 类别： 1=设计指标 2=建造标准
     */
    private Integer type;

    /**
     * 归档时间
     */
    private Date archiveDate;

    /**
     * 工程来源
     */
    private String productSource;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 企业id
     */
    private String enterpriseId;

    /**
     * 合约工程id
     */
    private Long contractProjectId;

    private Date tblCreateDate;

    /**
     * JSON数据
     */
    private String dataJson;
}