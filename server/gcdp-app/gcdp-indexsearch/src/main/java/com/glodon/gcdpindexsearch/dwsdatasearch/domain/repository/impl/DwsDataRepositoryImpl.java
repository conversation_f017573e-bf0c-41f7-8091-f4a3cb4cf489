package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdp.dwdservice.domain.dao.mapper.GcdpDwdProjectAuthEntInfoMapper;
import com.glodon.gcdp.dwdservice.domain.vo.DwdProjectInfoDynamicQueryVo;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ZbgxDwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwsIndexProjectNoteMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexUsageMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ConditionResultDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.SampleNoteDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.SubjectIndexDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.Constants;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.SumTypeEnum;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.*;
import com.glodon.gcdpindexsearch.dynamic.domain.entity.SimpleProjectNode;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
public class DwsDataRepositoryImpl implements IDwsDataRepository {

    @Autowired
    @Qualifier("indexSearchDwsIndexProjectNoteMapper")
    DwsIndexProjectNoteMapper indexSearchDwsIndexProjectNoteMapper;
    @Autowired
    GcdpDwsIndexUsageMapper indexDataMapper;

    @Autowired
    GcdpDwdProjectAuthEntInfoMapper projectAuthEntInfoMapper;

    @Autowired
    GcdpDwsIndexMapper gcdpDwsIndexMapper;

    /**
     * 查询工程分类和造价类型
     */
    @Override
    public List<ConditionResultDto> selectCondition(String enterpriseId, List<Integer> typeList, List<String> orgIds, String isVirtualOrg,
                                                    List<String> sharedEnterpriseIds, List<String> productSource, List<String> authControlProjectCodeList) {
        return indexSearchDwsIndexProjectNoteMapper.selectCondition(enterpriseId, typeList, orgIds, isVirtualOrg, sharedEnterpriseIds, productSource, authControlProjectCodeList);
    }

    @Override
    public List<DwsIndexProjectNote> selectList(String enterpriseId, List<Integer> typeList, FilterConditionVO filterConditionVO) {
        return indexSearchDwsIndexProjectNoteMapper.selectNoteList(enterpriseId, typeList, filterConditionVO);
    }

    @Override
    public List<DwsIndexProjectNote> selectNoteBySingleProjectReqVO(SingleProjectReqVO reqVO) {
        return indexSearchDwsIndexProjectNoteMapper.selectNoteBySingleProjectReqVO(reqVO);
    }

    @Override
    public List<DwsIndexProjectNote> selectProjectAttrByIds(List<Long> ids, List<Long> contractProjectIds, String enterpriseId) {
        return indexSearchDwsIndexProjectNoteMapper.selectProjectAttrByIds(ids, contractProjectIds, enterpriseId);
    }

    @Override
    public List<SampleNoteDto> selectNoteAndProjByIds(String enterpriseId, List<Long> noteIds) {
        return indexSearchDwsIndexProjectNoteMapper.selectNoteAndProjByIds(enterpriseId, noteIds);
    }

    @Override
    public List<MakeUpIndexData> selectNoteByIdsGroupByPhaseAndName(List<Long> ids) {
        return indexSearchDwsIndexProjectNoteMapper.selectNoteByIdsGroupByPhaseAndName(ids);
    }

    @Override
    public List<MakeUpIndexData> selectNoteInfoByIds(List<Long> ids) {
        return indexSearchDwsIndexProjectNoteMapper.selectNoteInfoByIds(ids);
    }

    @Override
    public List<DwsIndexProjectNote> selectNoteByIds(String enterpriseId, List<Long> ids) {
        return indexSearchDwsIndexProjectNoteMapper.selectNoteByIds(enterpriseId, ids);
    }

    @Override
    public List<String> selectCategoryCode(List<Long> projectNoteIds) {
        if (CollectionUtils.isNotEmpty(projectNoteIds)) {
            LambdaQueryWrapper<DwsIndexProjectNote> queryWrapper = new LambdaQueryWrapper<DwsIndexProjectNote>()
                    .select(DwsIndexProjectNote::getProjectCategoryCode)
                    .in(DwsIndexProjectNote::getId, projectNoteIds)
                    .eq(DwsIndexProjectNote::getNonConstruction, false)
                    .isNotNull(DwsIndexProjectNote::getProjectCategoryCode);
            List<DwsIndexProjectNote> dwsIndexProjectNotes = indexSearchDwsIndexProjectNoteMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(dwsIndexProjectNotes)) {
                return dwsIndexProjectNotes.stream().map(DwsIndexProjectNote::getProjectCategoryCode).collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<Long> selectNoteByContractProjectIds(String enterpriseId, List<Long> contractProjectIds) {
        return indexSearchDwsIndexProjectNoteMapper.selectNoteByContractProjectIds(enterpriseId, contractProjectIds);
    }

    @Override
    public List<DwsIndexProjectNote> selectNoteByBidNodeIds(String enterpriseId, List<Long> bidNodeIds) {
        return indexSearchDwsIndexProjectNoteMapper.selectNoteByBidNodeIds(enterpriseId, bidNodeIds);
    }

    @Override
    public List<SimpleProjectNode> queryAllSimple(DwdProjectInfoDynamicQueryVo queryVo) {
        return indexSearchDwsIndexProjectNoteMapper.queryAllSimple(queryVo);
    }

    @Override
    public NoteExistVO existNoteJudge(String enterpriseId, NoteExistVO noteExistVO) {
        if (CollectionUtils.isEmpty(noteExistVO.getIds())) {
            return noteExistVO;
        }
        // 对传入的单项id进行去重
        Set<Long> idSet = Sets.newHashSet(noteExistVO.getIds());
        LambdaQueryWrapper<DwsIndexProjectNote> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DwsIndexProjectNote::getId)
                .eq(DwsIndexProjectNote::getEnterpriseId, enterpriseId)
                .in(DwsIndexProjectNote::getId, idSet);
        List<Long> idsExist = indexSearchDwsIndexProjectNoteMapper.selectList(queryWrapper)
                .stream().map(DwsIndexProjectNote::getId).collect(Collectors.toList());
        noteExistVO.setIds(idsExist);
        return noteExistVO;
    }

    @Override
    public List<DwsIndexProjectNote> selectCategoryCodeAndPhaseAndPosition(String enterpriseId, List<String> productSource, List<String> orgIds, String isVirtualOrg, List<String> authControlProjectCodeList) {
        return indexSearchDwsIndexProjectNoteMapper.selectCategoryCodeAndPhaseAndPosition(enterpriseId, productSource, orgIds, isVirtualOrg, authControlProjectCodeList);
    }

    @Override
    public List<DwsIndexProjectNote> selectNoteByCondition(CostIndexListVO costIndexListVO) {
        return indexSearchDwsIndexProjectNoteMapper.selectNoteByCondition(costIndexListVO);
    }

    @Override
    public List<DwsIndexProjectNote> selectSharedListWithBlobField(List<SharedEnterpriseVo> sharedEnterpriseList,
                                                                   List<Integer> typeList,
                                                                   FilterConditionVO filterConditionVO,
                                                                   List<String> phaseList,
                                                                   boolean isNeedContractInfo,
                                                                   boolean isNeedFeatureInfo) {
        return indexSearchDwsIndexProjectNoteMapper.selectSharedListWithBlobField(sharedEnterpriseList, typeList, filterConditionVO, phaseList, isNeedContractInfo, isNeedFeatureInfo);
    }

    @Override
    public List<DwsIndexProjectNote> selectNotesByIds(List<Long> ids) {
        return indexSearchDwsIndexProjectNoteMapper.selectNotesByIds(ids);
    }

    @Override
    public List<SubjectIndexDto> selectCostAndUsageIndexByNoteId(List<Long> noteIds, Integer itemCostType) {
        return gcdpDwsIndexMapper.selectCostAndUsageIndexByNoteId(noteIds, itemCostType);
    }


    @Override
    public List<DwsIndexProjectNote> selectByContractProjectIds(String enterpriseId, List<Long> contractProjectIds, List<Long> bidnodeIds) {
        return indexSearchDwsIndexProjectNoteMapper.selectByContractProjectIds(enterpriseId, contractProjectIds, bidnodeIds);
    }

    @Override
    public List<ZbgxDwsIndexProjectNote> selectZbgxNotesByIds(List<Long> ids) {
        return indexSearchDwsIndexProjectNoteMapper.selectZbgxNotesByIds(ids);
    }

    @Override
    public List<DwsIndexProjectNote> selectAllNotesByIds(List<Long> ids) {
        return indexSearchDwsIndexProjectNoteMapper.selectAllNotesByIds(ids);
    }
    @Override
    public List<DwsIndexProjectNote> selectWholeNoteByHash(Integer sumCondition, List<DwsIndexProjectNote> validNotes, List<String> productSource) {
        if (CollUtil.isEmpty(validNotes)){
            return validNotes;
        }

        // 获取表字段名
        String hashFieldName = SumTypeEnum.HASH_FIELD_NAME.get(sumCondition);
        if (hashFieldName == null) {
            return validNotes;
        }

        // 获取对象属性
        Function<DwsIndexProjectNote, String> fieldExtractor = SumTypeEnum.HASH_FIELD_EXTRACTORS.get(sumCondition);
        if (fieldExtractor == null) {
            return validNotes;
        }

        // 获取hash值和id
        Set<String> hashes = validNotes.stream().map(fieldExtractor).filter(x -> !Constants.DEFAULT_HASH.equals(x)).collect(Collectors.toSet());
        Set<Long> ids = validNotes.stream().map(DwsIndexProjectNote::getId).collect(Collectors.toSet());
        if (hashes.isEmpty() || ids.isEmpty()) {
            return validNotes;
        }
        // 防止哈希碰撞，查出的数据必须落到这些key里
        Set<String> keySet = validNotes.stream().map(x -> generateKey(x, fieldExtractor)).collect(Collectors.toSet());

        // 是否需要blob字段
        boolean isNeedBlobField = SumTypeEnum.IS_NEED_BLOB_FIELD.getOrDefault(sumCondition, Boolean.TRUE);
        List<DwsIndexProjectNote> noteList = indexSearchDwsIndexProjectNoteMapper.selectWholeNoteByHash(hashFieldName, hashes, ids, isNeedBlobField, productSource);

        return noteList.stream().filter(x -> keySet.contains(generateKey(x, fieldExtractor))).collect(Collectors.toList());
    }

    private String generateKey(DwsIndexProjectNote x, Function<DwsIndexProjectNote, String> fieldExtractor) {
        return x.getEnterpriseId() +
                CharConst.DOUBLE_AT +
                x.getProjectCode() +
                CharConst.DOUBLE_AT +
                x.getPhase() +
                CharConst.DOUBLE_AT +
                fieldExtractor.apply(x);
    }
}

