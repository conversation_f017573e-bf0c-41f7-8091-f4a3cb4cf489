package com.glodon.gcdpindexsearch.dwsdatasearch.controller;

import com.alibaba.fastjson.JSON;
import com.glodon.gcdp.common.domain.Page;
import com.glodon.gcdp.common.domain.model.StandardBuilderDto;
import com.glodon.gcdp.common.domain.model.StandardBuilderNewDto;
import com.glodon.gcdpindexsearch.common.BaseController;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.dto.SubjectIndexDetailDto;
import com.glodon.gcdpindexsearch.dwddatasearch.domain.vo.IndexQueryVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.application.ProjectSearchFacde;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.IndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.SingleProjectIndexDataDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.*;
import com.glodon.gcdpindexsearch.infrastructure.annotation.TakeTime;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @description: 工程数据接口层
 * <AUTHOR>
 * @date 2022/10/24 11:10
 */
@Api(tags = "工程数据接口层")
@Slf4j
@RestController
@RequestMapping("/gcdp-index-dws")
public class ProjectSearchController extends BaseController {
    @Autowired
    ProjectSearchFacde projectSearchFacde;

    @ApiOperation(value = "反推筛选条件-工程分类、造价类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047"),
            @ApiImplicitParam(name = "sumCondition", value = "汇算类型（查询时）：1按项目，2按业态，3按楼栋", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "productSource", value = "数据来源", dataType = "String", defaultValue = "zbsq,mbcb,qyqd...")})
    @TakeTime
    @GetMapping("/get-condition/{enterpriseId}")
    public ConditionDto getCondition(@PathVariable String enterpriseId,
                                     @RequestParam Integer sumCondition,
                                     @RequestParam List<String> productSource,
                                     @RequestParam(required = false) List<String> authControlProjectCodeList){
        return projectSearchFacde.getCondition(enterpriseId, sumCondition, getOrgIds(), getSharedEnterpriseIds(), productSource, authControlProjectCodeList);
    }

    @ApiOperation(value = "反推筛选条件-工程分类、造价类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")})
    @TakeTime
    @PostMapping("/get-conditionNew/{enterpriseId}")
    public ConditionDto getConditionNew(@PathVariable String enterpriseId,
                                       @RequestBody ConditionQueryVO conditionQueryVO){
        return projectSearchFacde.getCondition(enterpriseId,
                conditionQueryVO.getSumCondition(),
                getOrgIds(),
                getSharedEnterpriseIds(),
                conditionQueryVO.getProductSource(),
                conditionQueryVO.getAuthControlProjectCodeList());
    }

    // todo 和飞总确认是否有在使用，考虑废弃
    @ApiOperation(value = "工程维度列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")})
    @TakeTime
    @PostMapping("/project-analyze-list/{enterpriseId}")
    public Page<ProjectSearchDto> projectAnalyzeList(@PathVariable String enterpriseId, @Valid @RequestBody FilterConditionVO filterConditionVO) throws Exception {
        log.info("工程维度列表参数：{}-{}", enterpriseId, filterConditionVO);
        filterConditionVO.setSharedEnterpriseId(getSharedEnterpriseIds());
        filterConditionVO.setIncludeSelfFlag(getIncludeSelfFlg());
        return projectSearchFacde.projectAnalyzeList(enterpriseId, filterConditionVO, getOrgIds());
    }

    @ApiOperation(value = "工程列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")})
    @TakeTime
    @PostMapping("/contract-project-list/{enterpriseId}")
    public ContractProjectDto contractProjectList(@PathVariable String enterpriseId, @Valid @RequestBody FilterConditionReqVO reqVO) throws Exception {
        log.info("工程列表查询：{}-{}", enterpriseId, reqVO);
        FilterConditionVO filterCondition = reqVO.getFilterConditionVO();
        filterCondition.setSumCondition(reqVO.getSumCondition());
        filterCondition.setProductSource(reqVO.getProductSource());
        filterCondition.setAuthControlProjectCodeList(reqVO.getAuthControlProjectCodeList());
        return projectSearchFacde.contractProjectList(enterpriseId, filterCondition, getOrgIds());
    }

    @ApiOperation(value = "结构化项目列表")
    @ApiImplicitParams({
    @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")})
    @TakeTime
    @PostMapping("/struct-project-list/{enterpriseId}")
    public StructContractProjectDto structureProjectList(@PathVariable String enterpriseId, @Valid @RequestBody FilterConditionReqVO reqVO) throws Exception{
        FilterConditionVO filterCondition = reqVO.getFilterConditionVO();
        filterCondition.setSumCondition(reqVO.getSumCondition());
        filterCondition.setProductSource(reqVO.getProductSource());
        filterCondition.setAuthControlProjectCodeList(reqVO.getAuthControlProjectCodeList());
        return projectSearchFacde.structProjectList(enterpriseId, filterCondition, reqVO.getStandardCondition(), getOrgIds());
    }

    @ApiOperation(value = "明细包含数据范围")
    @TakeTime
    @PostMapping("/detail-range/{enterpriseId}")
    public ProjectDetailRangeDto detailRange(@PathVariable String enterpriseId, @Valid @RequestBody SingleProjectReqVO reqVO) {
        log.info("明细包含数据范围入参：{}-{}", enterpriseId, JSON.toJSONString(reqVO));
        return projectSearchFacde.detailRange(reqVO);
    }
    @ApiOperation(value = "查询指标详情")
    @TakeTime
    @PostMapping("/index-data/{enterpriseId}")
    public IndexData indexData(@PathVariable String enterpriseId,
                               @RequestBody FilterIndexDataVO filterIndexDataVO)throws Exception {
        log.info("查询指标详情：{}-{}", enterpriseId, JSON.toJSONString(filterIndexDataVO));
        filterIndexDataVO.setEnterpriseId(enterpriseId);
        filterIndexDataVO.setOrgIds(getOrgIds());
        log.info("enterpriseId={},orgId={}",enterpriseId,getOrgIds());
        return projectSearchFacde.getIndexData(filterIndexDataVO);
    }

    @ApiOperation(value = "查询单个项目指标详情")
    @TakeTime
    @PostMapping("/single-project-index-data/{enterpriseId}")
    public SingleProjectIndexDataDto indexData(@PathVariable String enterpriseId,
                                               @Valid @RequestBody SingleProjectIndexReqVO reqVO) {
        log.info("查询指标详情：{}-{}", enterpriseId, JSON.toJSONString(reqVO));
        return projectSearchFacde.getSingleProjectIndex(reqVO);
    }

    @ApiOperation(value = "查看设计规划指标数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047"),
            @ApiImplicitParam(name = "projectCode", value = "项目户口簿编码", dataType = "java.lang.String", defaultValue = "XMHB2209260001")})
    @TakeTime
    @GetMapping("/project-plan/{enterpriseId}")
    public List<ProjectPlanDto> getProjectPlan(@PathVariable String enterpriseId, @RequestParam() String projectCode, @RequestParam(required = false) String phase) {
        return projectSearchFacde.getProjectPlan(enterpriseId, projectCode, phase);
    }

    @ApiOperation(value = "查看建造标准数据")
    @TakeTime
    @PostMapping("/build_standard/{enterpriseId}")
    public StandardBuilderDto getBuildStandard(@PathVariable String enterpriseId, @RequestBody BuildStandardVO buildStandardVO) {
        if (buildStandardVO == null) {
            buildStandardVO = new BuildStandardVO(enterpriseId);
        }
        return projectSearchFacde.getBuildStandard(buildStandardVO);
    }

    @ApiOperation(value = "查看建造标准数据")
    @TakeTime
    @PostMapping("/build_standard/v2/{enterpriseId}")
    public StandardBuilderNewDto getBuildStandardV2(@PathVariable String enterpriseId,
                                                    @RequestBody BuildStandardVO buildStandardVO) {
        if (buildStandardVO == null) {
            buildStandardVO = new BuildStandardVO(enterpriseId);
        }
        return projectSearchFacde.getBuildStandardV2(buildStandardVO);
    }

//    @ApiOperation(value = "查看建造标准数据")
//    @TakeTime
//    @PostMapping("/build_standard_new/{enterpriseId}")
//    public StandardBuilderNewDto getBuildStandardV2(@PathVariable String enterpriseId,
//                                                    @RequestBody BuildStandardVO buildStandardVO) {
//        if (buildStandardVO == null) {
//            buildStandardVO = new BuildStandardVO(enterpriseId);
//        }
//        return new StandardBuilderNewDto();
//    }

    @ApiOperation(value = "查看工程特征")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047",example = "6821004528704418047"),
            @ApiImplicitParam(name = "ids", value = "工程信息表的id", dataType = "java.util.List", defaultValue = "[6,1680001]" ,example = "[6,1680001]")})
    @TakeTime
    @PostMapping("/project-attr/{enterpriseId}")
    public List<ProjectAttrDto> getProjectAttr(@PathVariable String enterpriseId, @RequestBody List<Long> ids){
        return projectSearchFacde.getProjectAttr(enterpriseId, ids,null);
    }

    @ApiOperation(value = "查看工程特征")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047",example = "6821004528704418047"),
            @ApiImplicitParam(name = "ids", value = "工程信息表的id", dataType = "java.util.List", defaultValue = "[6,1680001]" ,example = "[6,1680001]")})
    @TakeTime
    @PostMapping("/project-attr-open/{enterpriseId}")
    public List<ProjectAttrDto> getProjectAttrs(@PathVariable String enterpriseId,
                                               @RequestBody ProjectAttrQueryVO queryVO){
        return projectSearchFacde.getProjectAttr(enterpriseId, queryVO.getContractProjectIds(), queryVO.getOriginalIds());
    }

    @ApiOperation(value = "根据传入的单项id集合 判断单项是否存在")
    @TakeTime
    @PostMapping("/exist/{enterpriseId}")
    public List<NoteExistVO> noteExist(@PathVariable("enterpriseId") String enterpriseId,
                                       @RequestBody List<NoteExistVO> noteExistVOS){
        log.info("开始根据传入的单项id集合进行查询, 查询参数[enterpriseId={}-{}]", enterpriseId, noteExistVOS);
        return projectSearchFacde.noteExist(enterpriseId, noteExistVOS);
    }


    @ApiOperation(value = "单项科目查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseId", value = "企业标识", dataType = "path", defaultValue = "6821004528704418047")})
    @TakeTime
    @PostMapping("/index-detail/{enterpriseId}")
    public List<SubjectIndexDetailDto> subjectIndexDetail(@PathVariable String enterpriseId,
                                                          @RequestParam(required = false) Integer itemCostType,
                                                          @Valid @RequestBody List<IndexQueryVO> indexQueryVO) {
        return projectSearchFacde.subjectIndexDetail(indexQueryVO, itemCostType);
    }

}
