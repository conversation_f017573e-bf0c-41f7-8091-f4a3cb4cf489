package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.StopWatch;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dimservice.domain.dao.entity.DimZbStandardsMainQuantity;
import com.glodon.gcdp.dimservice.domain.dao.service.CustomCodeService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.ProcessFactory;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDimZbStandardsRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.IndexDataDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ProjectIndexDataDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterIndexDataVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.SingleProjectIndexReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/*
 * @Class com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl DwsIndexUsageDataRepositoryImpl
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 含量指标查询
 * @Date 10:56 2022/11/3
 **/
@Service("indexUsage")
@Slf4j
public class DwsIndexUsageDataDetailServiceImpl extends IndexDataDetailService implements ProjectIndexDataDetailService {

    @Autowired
    IDwsIndexDataRepository indexDataRepository;

    @Autowired
    private IDimZbStandardsRepository zbStandardsRepository;

    @Autowired
    private CustomCodeService customCodeService;

    @Override
    public IndexData getIndexDataDetail(FilterIndexDataVO filterIndexDataVO) {
        List<ProjectIndexData> result = indexDataRepository.selectUsageIndexList(filterIndexDataVO);
        makePid(result);
        // note信息
        List<DwsIndexProjectNote> noteList = indexDataRepository.getTotal(filterIndexDataVO.getIds());

        List<String> ids = noteList.stream().map(x -> x.getId().toString()).collect(Collectors.toList());
        result.forEach(item -> {
            GcdpDwsIndexUsage gcdpDwsIndexUsage = (GcdpDwsIndexUsage) item;
            gcdpDwsIndexUsage.setJmhlIndexValue(MathUtil.divStr(gcdpDwsIndexUsage.getJmhlIndexTotal(), gcdpDwsIndexUsage.getJmhlCalculateValue()));
            gcdpDwsIndexUsage.setZylIndexValue(MathUtil.divStr(gcdpDwsIndexUsage.getZylIndexTotal(), gcdpDwsIndexUsage.getZylCalculateValue()));
            gcdpDwsIndexUsage.setItemIds(item.getIds());
        });
        if (filterIndexDataVO.getShowAll() == null || filterIndexDataVO.getShowAll() == 0) {
            result = filterInvalidData(result);
        }
        //按照基础信息库排序
        sortIndex(result, filterIndexDataVO.getEnterpriseId());
        makeCode(result);
        return new IndexData(ids, result);
    }

    @Override
    public IndexData getItemIndexDataDetail(FilterIndexDataVO filterIndexDataVO) throws Exception {
        //判断ids 是否为空，如果为空则根据筛选条件过滤出ids
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("1. 过滤含量维度查询条件");
        List<DwsIndexProjectNote> dwsIndexProjectNotes = getIdsByFilter(filterIndexDataVO);
        stopWatch.stop();
        log.info("1. 含量维度查询过滤耗时:[{}]秒", stopWatch.getLastTaskInfo().getTimeSeconds());
        if (CollectionUtils.isEmpty(dwsIndexProjectNotes)) {
            return new IndexData();
        }
        stopWatch.start("2. 查询含量维度数据库");
        List<String> ids = dwsIndexProjectNotes.stream().map(x -> x.getId().toString()).collect(Collectors.toList());
        log.info("2. 查询含量维度数据库的入参为:[{}]", ids);
        List<GcdpDwsItemIndexUsage> result = concurrentGetIndexUsages(filterIndexDataVO, ids);
        stopWatch.stop();
        log.info("2. 查询含量维度数据库耗时:[{}]秒", stopWatch.getLastTaskInfo().getTimeSeconds());
        // 制作数据之前进行数据组装
        stopWatch.start("3. 数据组装耗时");
        result = sorted(ProcessFactory.indexUsageProcessor(result));
        stopWatch.stop();
        log.info("3. 数据组装耗时为:[{}]秒", stopWatch.getLastTaskInfo().getTimeSeconds());
        makePid(result);
        //按照基础信息库排序
        sortIndex(result, filterIndexDataVO.getEnterpriseId());
        makeCode(result);
        return new IndexData(ids, result);
    }

    private List<GcdpDwsItemIndexUsage> concurrentGetIndexUsages(FilterIndexDataVO filterIndexDataVO, List<String> ids) throws InterruptedException {
        //根据id 查询指标科目
        List<List<String>> batchIds = ListUtil.partition(ids, DEFAULT_NOTE_BATCH_SIZE);
        List<GcdpDwsItemIndexUsage> result = Collections.synchronizedList(new ArrayList<>(ids.size() * DEFAULT_NOTE_BATCH_SIZE));
        CountDownLatch countDownLatch = new CountDownLatch(batchIds.size());
        for (List<String> bIds : batchIds) {
            selectDataServiceExecutor.execute(() -> {
                List<GcdpDwsItemIndexUsage> usageList = indexDataRepository.selectUsageItemIndexList(bIds, filterIndexDataVO.getShowAll());
                result.addAll(usageList);
                countDownLatch.countDown();
            });
        }
        countDownLatch.await();
        return result;
    }

    private <T extends ProjectIndexData> void sortIndex(List<T> query, String entId) {
        if (CollectionUtils.isEmpty(query)) {
            return;
        }
        String customerCode = customCodeService.getBasicInfoNewQyCode(entId);
        log.info("该企业主要量数据：customerCode:{},query:{}", customerCode, query);
        List<DimZbStandardsMainQuantity> dimZbStandardsMainQuantities = zbStandardsRepository.selectByCustomerCode(customerCode);
        if (CollectionUtils.isEmpty(dimZbStandardsMainQuantities)) {
            return;
        }

        //拿出是基础信息库的科目，基础信息库的科目code是雪花id，19位，非基础信息库的code是以3为倍数
        List<T> basicList = query.stream().filter(item -> StringUtils.isNotBlank(item.getCode()) && item.getCode().length() == 19).collect(Collectors.toList());
        log.info("该企业基础信息库的科目主要量数据：customerCode:{},basicList:{}", customerCode, basicList);
        if (CollectionUtils.isEmpty(basicList)) {
            return;
        }
        query.removeAll(basicList);

        //把基础信息库查出来的科目用description和对应下标生成一个map
        Map<String, Integer> description2IndexMap = IntStream.range(0, dimZbStandardsMainQuantities.size()).boxed()
                .collect(Collectors.toMap(i -> dimZbStandardsMainQuantities.get(i).getDescription(), Function.identity(), (existing, replacement) -> existing));
        log.info("该企业description2IndexMap：customerCode:{},description2IndexMap:{}", customerCode, description2IndexMap);
        /*
        当对 ProjectIndexData 对象列表进行排序时，comparator 将根据科目的 name字段对应的 description 来确定排序顺序。具体排序规则如下：
        1. 如果 ProjectIndexData 对象的 name 字段对应的 description description2IndexMap 中存在，则按照 description 在 dimZbStandardsMainQuantities 列表中的下标进行排序。
        2. 如果 ProjectIndexData 对象的 name字段对应的 description 在 description2IndexMap 中不存在（即无法找到匹配的 key），则将该对象排在列表的末尾，以保持原始顺序。
         */
        Comparator<ProjectIndexData> comparator = Comparator.comparing(data -> description2IndexMap.getOrDefault(data.getName(), Integer.MAX_VALUE));

        // 对 query 列表使用 comparator 进行排序
        basicList.sort(comparator);
        query.addAll(basicList);
    }

    @Override
    public ItemIndex buildItemIndex(SingleProjectIndexReqVO reqVO, SingleProjectMakeUp makeUp) throws Exception {
        log.error("含量指标请通过single-project-index-data接口查询");
        throw new Exception("请通过单指标接口获取数据");
    }

}

