package com.glodon.gcdpindexsearch.infrastructure.util.json;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * BigDecimal类型Json序列化，用于返回给前端使用
 * */
public class BigDecimalStringSerializer extends StdSerializer<BigDecimal> {

    public static final BigDecimalStringSerializer INSTANCE = new BigDecimalStringSerializer();

    public BigDecimalStringSerializer() {
        super(BigDecimal.class);
    }

    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if(bigDecimal != null){
            String val = bigDecimal.stripTrailingZeros().toPlainString();
            jsonGenerator.writeString(val);
        }
    }
}
