package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

/**
 * @description: 标准说明及建造做法对象
 * @date 2023-05-16 15:01
 * <AUTHOR>
 */
@Data
public class StandardDescription {
    /**
     * 建造标准参数
     */
    private String description;
    /**
     * 建造做法
     */
    private String standardValue;

    /**
     * 归档时间，同单项合并建造标准取非空最新时使用
     */
    @JsonIgnore
    private Date archiveDate;
    @JsonIgnore
    private int ord;
    @JsonIgnore
    private String baseNoteMergeKey;
}
