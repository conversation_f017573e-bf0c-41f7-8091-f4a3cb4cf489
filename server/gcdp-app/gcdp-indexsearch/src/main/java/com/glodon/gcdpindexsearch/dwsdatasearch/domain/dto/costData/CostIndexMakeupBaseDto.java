package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 * @description: 建造做法指标库、全成本指标库-指标区间组成 BaseDto
 * @date 2023-05-16 15:01
 */
@Data
public class CostIndexMakeupBaseDto {
    /**
     * 科目id
     */
    @JsonIgnore
    private Long id;
    /**
     * indexProjectNoteId
     */
    @JsonIgnore
    private Long indexProjectNoteId;
    /**
     * standardValue
     */
    @JsonIgnore
    private String standardValue;
    /**
     * 建造做法
     */
    private List<StandardDescription> standardDescription;
    /**
     * 科目名称
     */
    private String name;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 项目编码
     */
    private String projectCode;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 业务阶段
     */
    private String phase;
    /**
     * 业态
     */
    private String categoryName;
    /**
     * 产品定位
     */
    private String position;
    /**
     * 省
     */
    private String provinceName;
    /**
     * 省Id
     */
    private String provinceId;
    /**
     * 市
     */
    private String cityName;
    /**
     * 市Id
     */
    private String cityId;
    /**
     * 区
     */
    private String districtName;
    /**
     * 区Id
     */
    private String districtId;
}
