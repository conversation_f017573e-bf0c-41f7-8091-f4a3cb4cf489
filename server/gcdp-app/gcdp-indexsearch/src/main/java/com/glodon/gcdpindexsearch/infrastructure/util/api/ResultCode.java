package com.glodon.gcdpindexsearch.infrastructure.util.api;

/**
 * 枚举了一些常用API操作码
 * Created by Chenc on 2020/7/29.
 */
public enum ResultCode implements IErrorCode {
    SUCCESS(200, "操作成功"),
    FAILED(500, "操作失败"),
    VALIDATE_FAILED(400, "参数检验失败"),
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    FORBIDDEN(403, "没有相关权限"),

    DUPLICATE(10000, "数据已存在"),
    NOTFOUND(20000, "数据不存在"),
    NOT_VALID_DATA(30000, "请在指标神器客户端将工程重新计算升级后再进行指标更新");


    private long code;
    private String message;

    private ResultCode(long code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public long getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
