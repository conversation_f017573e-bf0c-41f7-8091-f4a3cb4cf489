package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdp.common.domain.enums.ProductSource;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.ProcessFactory;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwdDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.IndexDataDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ProjectIndexDataDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterIndexDataVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.SingleProjectIndexReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Service("indexCost")
@Slf4j
public class DwsIndexCostDataDetailServiceImpl extends IndexDataDetailService implements ProjectIndexDataDetailService {

    @Autowired
    IDwsIndexDataRepository indexDataRepository;
    @Autowired
    IDwdDataRepository dwdDataRepository;

    @Override
    public IndexData getIndexDataDetail(FilterIndexDataVO filterIndexDataVO) {
        List<ProjectIndexData> result = indexDataRepository.selectCostIndexList(filterIndexDataVO);

        if (CollUtil.isEmpty(result)) {
            return null;
        }
        makePid(result);
        // note信息
        List<DwsIndexProjectNote> noteList = indexDataRepository.getTotal(filterIndexDataVO.getIds());
        boolean initFlag = filterIndexDataVO.getInitFlag() != null && filterIndexDataVO.getInitFlag() == 1;
        // 总造价
        BigDecimal total = getTotal(noteList, initFlag, false);
        BigDecimal totalIncludeTax = getTotal(noteList, initFlag, true);

        List<String> ids = noteList.stream().map(x -> x.getId().toString()).collect(Collectors.toList());
        // 项目的建造面积
        BigDecimal projectScale = getProjectScale(noteList, initFlag, filterIndexDataVO.getEnterpriseId());
        // 父项合价
        Map<Long, GcdpDwsIndexCost> idAndAmount = new HashMap<>(result.size());
        result.forEach(item -> {
            GcdpDwsIndexCost gcdpDwsIndexCost = (GcdpDwsIndexCost) item;
            idAndAmount.put(gcdpDwsIndexCost.getId(), gcdpDwsIndexCost);
        });

        result.forEach(item -> {
            GcdpDwsIndexCost gcdpDwsIndexCost = (GcdpDwsIndexCost) item;
            // 建面单方 = 科目合价/建筑面积
            BigDecimal jmValue = initFlag ? projectScale : gcdpDwsIndexCost.getJmValue();
            gcdpDwsIndexCost.setJmDfValue(MathUtil.divStr(gcdpDwsIndexCost.getJmAmount(), jmValue));
            gcdpDwsIndexCost.setJmDfValueIncludeTax(MathUtil.divStr(gcdpDwsIndexCost.getJmAmountIncludeTax(), jmValue));
            // 实物量单方 科目合价/科目工程量
            gcdpDwsIndexCost.setSwlDfValue(MathUtil.divStr(gcdpDwsIndexCost.getSwlAmount(), gcdpDwsIndexCost.getSwlValue()));
            gcdpDwsIndexCost.setSwlDfValueIncludeTax(MathUtil.divStr(gcdpDwsIndexCost.getSwlAmountIncludeTax(), gcdpDwsIndexCost.getSwlValue()));
            // 单方指标 = 科目合价/计算口径值
            BigDecimal dfValue = getDfValue(gcdpDwsIndexCost, initFlag, projectScale);
            gcdpDwsIndexCost.setDfZbValue(MathUtil.divStr(gcdpDwsIndexCost.getDfAmount(), dfValue));
            gcdpDwsIndexCost.setDfZbValueIncludeTax(MathUtil.divStr(gcdpDwsIndexCost.getDfAmountIncludeTax(), dfValue));

            // 父级占比指标 科目合价/父项合价
            BigDecimal parentAmount;
            BigDecimal parentAmountIncludeTax;
            if (gcdpDwsIndexCost.getPid().equals(-1L)) {
                parentAmount = gcdpDwsIndexCost.getAmount();
                parentAmountIncludeTax = gcdpDwsIndexCost.getAmountIncludeTax();
            } else {
                GcdpDwsIndexCost parent = idAndAmount.get(gcdpDwsIndexCost.getPid());
                parentAmount = parent.getAmount();
                parentAmountIncludeTax = parent.getAmountIncludeTax();
            }
            gcdpDwsIndexCost.setParentPercent(MathUtil.toPercent(MathUtil.divStr(gcdpDwsIndexCost.getAmount(), parentAmount)));
            gcdpDwsIndexCost.setParentPercentIncludeTax(MathUtil.toPercent(MathUtil.divStr(gcdpDwsIndexCost.getAmountIncludeTax(), parentAmountIncludeTax)));

            // 总造价占比指标 科目合价/总造价
            gcdpDwsIndexCost.setTotalPercent(MathUtil.toPercent(MathUtil.divStr(gcdpDwsIndexCost.getAmount(), total)));
            gcdpDwsIndexCost.setTotalPercentIncludeTax(MathUtil.toPercent(MathUtil.divStr(gcdpDwsIndexCost.getAmountIncludeTax(), totalIncludeTax)));

            //樣本Id
            gcdpDwsIndexCost.setItemIds(item.getIds());
        });
        if (filterIndexDataVO.getShowAll() == null || filterIndexDataVO.getShowAll() == 0) {
            result = filterInvalidData(result);
        }
        makeCode(result);
        return new IndexData(ids, result);
    }

    /**
     * 获取当前科目造价单方（其他单方）的计算口径值
     *      规则：
     *          若 initFlag 为真（表示建筑面积需要从项目读取） 且 计算口径是“建筑面积”的需要读取项目的 建筑面积
     *          否则 读取 当前科目对应的计算口径值
     * @param gcdpDwsIndexCost 成本指标
     * @param initFlag  是否读取项目上的建筑面积
     * @param projectScale  项目的建筑面积
     */
    private BigDecimal getDfValue(GcdpDwsIndexCost gcdpDwsIndexCost, boolean initFlag, BigDecimal projectScale) {
        return (CharConst.BUILD_AREA.equals(gcdpDwsIndexCost.getDfCalculateName()) && initFlag) ? projectScale : gcdpDwsIndexCost.getDfValue();
    }

    @Override
    public IndexData getItemIndexDataDetail(FilterIndexDataVO filterIndexDataVO) throws Exception {
        long startTime = System.nanoTime();
        StopWatch st = new StopWatch("科目维度成本指标耗时统计");
        st.start("条件过滤");
        //判断ids 是否为空，如果为空则根据筛选条件过滤出ids
        List<DwsIndexProjectNote> dwsIndexProjectNotes = getIdsByFilter(filterIndexDataVO);
        st.stop();
        log.info("1. 条件过滤 ,耗时为:[{}]", st.getLastTaskInfo().getTimeSeconds());
        if (CollectionUtils.isEmpty(dwsIndexProjectNotes)) {
            return new IndexData(filterIndexDataVO.getIds(), null);
        }
        List<String> ids = dwsIndexProjectNotes.stream().map(x -> x.getId().toString()).collect(Collectors.toList());
        st.start("数据库查询");
        //根据id 查询指标科目
        List<GcdpDwsItemIndexCost> result = concurrentGetIndexCosts(filterIndexDataVO, ids);
        st.stop();
        log.info("2. 数据库查询的入参为:[{}],耗时为:[{}]秒", ids, st.getLastTaskInfo().getTimeSeconds());
        st.start("3. 数据组装");
        // 调用成本指标责任链进行数据组装
        result = sorted(ProcessFactory.indexCostProcessor(result));
        makePid(result);
        makeCode(result);

        st.stop();
        log.info("3. 数据组装的耗时为:[{}] 毫秒", st.getLastTaskInfo().getTimeMillis());
        log.info("科目维度数据查询整体耗时为:[{}]秒", DateUtil.nanosToSeconds(System.nanoTime() - startTime));
        return new IndexData(ids, result);
    }

    private List<GcdpDwsItemIndexCost> concurrentGetIndexCosts(FilterIndexDataVO filterIndexDataVO, List<String> ids) throws InterruptedException {
        List<List<String>> batchIds = ListUtil.partition(ids, DEFAULT_NOTE_BATCH_SIZE);
        List<GcdpDwsItemIndexCost> result = Collections.synchronizedList(new ArrayList<>(ids.size() * DEFAULT_NOTE_BATCH_SIZE));
        CountDownLatch countDownLatch = new CountDownLatch(batchIds.size());
        for (List<String> bIds : batchIds) {
            selectDataServiceExecutor.execute(() -> {
                List<GcdpDwsItemIndexCost> costList = indexDataRepository.selectCostItemIndexList(bIds, filterIndexDataVO.getShowAll());
                result.addAll(costList);
                countDownLatch.countDown();
            });
        }
        countDownLatch.await();
        return result;
    }


    private BigDecimal getTotal(List<DwsIndexProjectNote> noteList, boolean initFlag, boolean includeTaxFlag) {
        if (CollUtil.isEmpty(noteList)) {
            return BigDecimal.ZERO;
        }

        if (initFlag) {
            BigDecimal totalSum = BigDecimal.ZERO;

            Set<Long> contractProjectIdSet = new HashSet<>();

            for (DwsIndexProjectNote note : noteList) {
                String productSource = note.getProductSource();
                if (ProductSource.QYQD.getIndex().equals(productSource) || ProductSource.YSTZ.getIndex().equals(productSource) || ProductSource.JSTZ.getIndex().equals(productSource)) {
                    // 一个合约计一次合约工程的含税总造价
                    if (contractProjectIdSet.add(note.getContractProjectId())) {
                        totalSum = MathUtil.add(totalSum, includeTaxFlag ? note.getContractProjectTotalIncludeTax() : note.getContractProjectTotal());
                    }
                } else {
                    totalSum = MathUtil.add(totalSum, includeTaxFlag ? note.getTotalIncludeTax() : note.getTotal());
                }
            }

            return totalSum;
        }

        return noteList.parallelStream().map(x -> includeTaxFlag ? x.getTotalIncludeTax() : x.getTotal()).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    /**
     * @param noteList
     * @param initFlag
     * @param enterpriseId
     * @return java.math.BigDecimal
     * @description: 获取项目的项目规模
     * <AUTHOR>
     * @date 2023/3/13 13:50
     */
    private BigDecimal getProjectScale(List<DwsIndexProjectNote> noteList, boolean initFlag, String enterpriseId) {
        if (initFlag && CollUtil.isNotEmpty(noteList)) {
            String projectCode = noteList.get(0).getProjectCode();
            DwdProjectInfo projectInfo = dwdDataRepository.selectByProjectCode(enterpriseId, projectCode);
            if (projectInfo != null) {
                return projectInfo.getProjectScale();
            }
        }
        return null;
    }

    @Override
    public ItemIndex buildItemIndex(SingleProjectIndexReqVO reqVO, SingleProjectMakeUp makeUp) throws Exception {
        log.error("成本指标查询通过单指标接口查询");
        throw new Exception("请通过单指标接口获取数据");
    }
}

