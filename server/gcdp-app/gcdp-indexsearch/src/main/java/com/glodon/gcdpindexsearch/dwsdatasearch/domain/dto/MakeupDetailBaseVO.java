package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 指标下钻明细
 */
@Data
@ApiModel(value = "清单组成", description = "清单组成")
@SuppressWarnings("squid:S1068") // 忽略sonar规则： Unused "private" fields should be removed
public class MakeupDetailBaseVO {
    @ApiModelProperty(value = "id", example = "id")
    private Long id;
    @ApiModelProperty(value = "编码", example = "1")
    private String code;
    @ApiModelProperty(value = "名称", example = "挖一般土方")
    private String name;
    @ApiModelProperty(value = "单位", example = "m3")
    private String unit;
    @ApiModelProperty(value = "量", example = "168")
    private BigDecimal quantity;
    @ApiModelProperty(value = "类别", example = "1分部分项清单2措施清单3材料4定额")
    private Integer type;
    @ApiModelProperty(value = "是否计量 1 是 0 否", example = "1")
    private Integer isCalcQuantity = 1;
    @ApiModelProperty(value = "是否计价 1是 0否", example = "1")
    private Integer isCalcAmount = 1;
    @ApiModelProperty(value = "转换系数", example = "1")
    private BigDecimal factor;
}
