package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository;

import com.glodon.gcdp.dwdservice.domain.vo.DwdProjectInfoDynamicQueryVo;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ZbgxDwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ConditionResultDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.SampleNoteDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.SubjectIndexDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.*;
import com.glodon.gcdpindexsearch.dynamic.domain.entity.SimpleProjectNode;

import java.util.List;
import java.util.Set;

public interface IDwsDataRepository {
    List<ConditionResultDto> selectCondition(String enterpriseId,
                                             List<Integer> typeList,
                                             List<String> orgIds,
                                             String isVirtualOrg,
                                             List<String> sharedEnterpriseIds,
                                             List<String> productSource,
                                             List<String> authControlProjectCodeList);

    List<DwsIndexProjectNote> selectNoteBySingleProjectReqVO(SingleProjectReqVO reqVO);

    List<DwsIndexProjectNote> selectList(String enterpriseId, List<Integer> typeList, FilterConditionVO filterConditionVO);

    /**
     * 根据ids获取业态code
     *
     * @param projectNoteIds
     * @return
     */
    List<String> selectCategoryCode(List<Long> projectNoteIds);

    List<DwsIndexProjectNote> selectProjectAttrByIds(List<Long> ids, List<Long> contractProjectIds, String enterpriseId);

    List<SampleNoteDto> selectNoteAndProjByIds(String enterpriseId, List<Long> noteIds);

    /**
     * 根据id查询列表
     *
     * @param ids
     * @return
     */
    List<MakeUpIndexData> selectNoteByIdsGroupByPhaseAndName(List<Long> ids);

    /**
     * 根据id查询列表,不做合并
     *
     * @param ids
     * @return
     */
    List<MakeUpIndexData> selectNoteInfoByIds(List<Long> ids);

    /**
     * 根据id查询数据
     *
     * @param enterpriseId
     * @param collect
     * @return
     */
    List<DwsIndexProjectNote> selectNoteByIds(String enterpriseId, List<Long> collect);

    List<Long> selectNoteByContractProjectIds(String enterpriseId, List<Long> contractProjectIds);

    List<DwsIndexProjectNote> selectNoteByBidNodeIds(String enterpriseId, List<Long> bidNodeIds);

    List<SimpleProjectNode> queryAllSimple(DwdProjectInfoDynamicQueryVo queryVo);

    /**
     * 根据传入的查询参数 判断单项工程是否存在
     *
     * @param enterpriseId
     * @param noteExistVO
     * @return com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.NoteExistVO
     * <AUTHOR>
     * @date 2023-03-08 14:11:02
     */
    NoteExistVO existNoteJudge(String enterpriseId, NoteExistVO noteExistVO);

    /**
     * 查询工程分类，造价类型，产品业态
     *
     * @param enterpriseId
     * @param productSource
     * @param orgIds
     * @param isVirtualOrg
     * @param authControlProjectCodeList
     * @return
     */
    List<DwsIndexProjectNote> selectCategoryCodeAndPhaseAndPosition(String enterpriseId, List<String> productSource, List<String> orgIds, String isVirtualOrg, List<String> authControlProjectCodeList);

    List<DwsIndexProjectNote> selectNoteByCondition(CostIndexListVO costIndexListVO);

    /**
     * 获取互信企业数据列表
     * @param sharedEnterpriseList
     * @param typeList
     * @param filterConditionVO
     * @return
     */
    List<DwsIndexProjectNote> selectSharedListWithBlobField(List<SharedEnterpriseVo> sharedEnterpriseList,
                                                            List<Integer> typeList,
                                                            FilterConditionVO filterConditionVO,
                                                            List<String> phaseList,
                                                            boolean isNeedContractInfo,
                                                            boolean isNeedFeatureInfo);

    List<DwsIndexProjectNote> selectNotesByIds(List<Long> ids);

    List<SubjectIndexDto> selectCostAndUsageIndexByNoteId(List<Long> noteIds, Integer itemCostType);

    List<DwsIndexProjectNote> selectByContractProjectIds(String enterpriseId, List<Long> contractProjectIds, List<Long> bidnodeIds);

    List<ZbgxDwsIndexProjectNote> selectZbgxNotesByIds(List<Long> ids);

    List<DwsIndexProjectNote> selectAllNotesByIds(List<Long> ids);

    List<DwsIndexProjectNote> selectWholeNoteByHash(Integer sumCondition, List<DwsIndexProjectNote> validNotes, List<String> productSource);

}
