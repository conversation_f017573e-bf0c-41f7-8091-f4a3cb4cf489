package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYJZBZDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYJZCBDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataQYXEZBDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @description: 企业成本标准 相关库的查询
 * @date: 2023/5/17 14:33
 */
@Mapper
public interface DwsCostStanderdQueryMapper {
    /**
     * @param contractProjectIds:
     * @param name:               科目名称
     * @return java.util.List<com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto>
     * @description: 查询参考的历史数据-企业限额指标库
     * @date: 2023/5/17 16:06
     */
    List<ReferenceDataQYXEZBDto> selectReferenceDataToQYXEZBLib(@Param(value = "projectNoteIdsMap") Map<String, ReferenceDataBaseDto> projectNoteIdsMap,
                                                                @Param(value = "name") String name);

    List<ReferenceDataQYJZBZDto> selectReferenceDataToQYJZBZLib(@Param(value = "projectNoteIdsMap") Map<String, ReferenceDataBaseDto> projectNoteIdsMap,
                                                                @Param(value = "name") String name);

    List<ReferenceDataQYJZCBDto> selectReferenceDataToQYJZCBLib(@Param(value = "projectNoteIdsMap") Map<String, ReferenceDataBaseDto> projectNoteIdsMap,
                                                                @Param(value = "name") String name,
                                                                @Param(value = "itemCostType") Integer itemCostType);

    List<ReferenceDataBaseDto> selectProjectInfoByOrgIdsAndEnterprisedId(@Param(value = "enterpriseId") String enterpriseId,
                                                                         @Param(value = "orgIds") List<String> orgIds,
                                                                         @Param(value = "projectNameList") List<String> projectNameList,
                                                                         @Param(value = "dataSourceList") List<String> dataSourceList,
                                                                         @Param(value = "isVirtualOrg") String isVirtualOrg,
                                                                         @Param(value = "authControlProjectCodeList") List<String> authControlProjectCodeList);
}
