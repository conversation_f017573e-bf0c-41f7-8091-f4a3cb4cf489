package com.glodon.gcdpindexsearch.infrastructure.advice;

import com.glodon.gcdpindexsearch.infrastructure.exception.BusinessException;
import com.glodon.gcdpindexsearch.infrastructure.util.api.ApiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 统一异常处理
 * */
@Slf4j
@ControllerAdvice
public class GExceptionAdvice {

    /**
     * 处理参数校验异常
     * */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public ApiResult argExceptionHandler(MethodArgumentNotValidException e, HttpServletResponse response) {
        Map<String, String> map = new HashMap<>();
        BindingResult bindingResult = e.getBindingResult();
        bindingResult.getFieldErrors().forEach(fieldError -> map.put(fieldError.getField(), fieldError.getDefaultMessage()));

        log.warn("参数传递错误: {}", map);
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return ApiResult.validateFailed("请求参数错误: " + map);
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public ApiResult exceptionHandler(Exception e, HttpServletResponse response) {
        log.error("服务运行时异常：", e);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return ApiResult.failed("服务器内部错误: " + e.getMessage());
    }

    @ExceptionHandler(value = BusinessException.class)
    @ResponseBody
    public ApiResult businessExceptionHandler(BusinessException e, HttpServletResponse response) {
        log.error("业务异常：", e);
        return ApiResult.failed(e.getResultCode());
    }
}
