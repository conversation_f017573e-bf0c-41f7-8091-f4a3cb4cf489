package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.SingleProjectDbIndex;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description:
 * @date 2024-02-24 14:59
 * @email <EMAIL>
 */
@Service("SingleProjectDb")
public class SingleProjectDbIndexPostProcessService extends CommonIndexPostProcessService<SingleProjectDbIndex> {
    @Override
    protected void extendProcess(SingleProjectDbIndex element) {
        element.setIndexFZ(element.getAmount());
        element.setIndexFZIncludeTax(element.getAmountIncludeTax());
    }
}
