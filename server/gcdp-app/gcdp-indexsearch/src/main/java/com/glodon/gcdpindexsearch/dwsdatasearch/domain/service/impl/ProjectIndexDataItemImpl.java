package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import com.glodon.gcdp.common.utils.SpringUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.IndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ProjectIndexDataDetailService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ProjectIndexDataService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterIndexDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description: 查询科目维度指标Service
 * <AUTHOR>
 * @date 2022/10/24 11:01
 */
@Service("projectIndexDataService-item")
@Slf4j
public class ProjectIndexDataItemImpl implements ProjectIndexDataService {
    @Override
    public IndexData getIndexData(FilterIndexDataVO filterIndexDataVO) throws Exception{
        ProjectIndexDataDetailService projectIndexDataDetailService = SpringUtil.getBean(filterIndexDataVO.getIndexType());
        return projectIndexDataDetailService.getItemIndexDataDetail(filterIndexDataVO);
    }
}
