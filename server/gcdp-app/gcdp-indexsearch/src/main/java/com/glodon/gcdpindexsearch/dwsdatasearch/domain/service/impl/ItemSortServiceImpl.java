package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdp.common.domain.consts.ValueConst;
import com.glodon.gcdp.common.domain.enums.ProductSource;
import com.glodon.gcdp.common.utils.HashUtil;
import com.glodon.gcdp.dimservice.domain.dao.entity.DimItemComponentCategoryItem;
import com.glodon.gcdp.dimservice.domain.dao.entity.DimProjectCategory;
import com.glodon.gcdp.dimservice.domain.dao.entity.DimZbStandardsMainQuantity;
import com.glodon.gcdp.dimservice.domain.dao.mapper.DimItemComponentMapper;
import com.glodon.gcdp.dimservice.domain.dao.service.CustomCodeService;
import com.glodon.gcdp.dimservice.domain.dao.service.DimProjectCategoryService;
import com.glodon.gcdpindexsearch.common.enums.IndexTypePrefixEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ItemData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwsIndexProjectNoteMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDimZbStandardsRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ItemSortService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.ItemIndexQueryReqVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @description:
 * @date 2023-12-18 14:39
 * @email <EMAIL>
 */
@Service
@Slf4j
public class ItemSortServiceImpl implements ItemSortService {


    private static final String DEFAULT_ENTERPRISE_ID = "-101";
    private static final String VIRTUAL_LEVEL_ONE_NAME = "工程总造价";
    @Autowired
    private CustomCodeService customCodeService;

    @Autowired
    private IDimZbStandardsRepository zbStandardsRepository;

    @Autowired
    @Qualifier("indexSearchDwsIndexProjectNoteMapper")
    private DwsIndexProjectNoteMapper indexProjectNoteMapper;

    @Autowired
    private DimItemComponentMapper dimItemComponentMapper;

    @Autowired
    private DimProjectCategoryService dimProjectCategoryService;

    private static final Set<String> ITEM_COMPONENT_SET = Sets.newHashSet(ProductSource.GCDP_ZBSQ_WEB.getIndex(), ProductSource.HG_PLATFORM.getIndex(), ProductSource.GCDP_ZBSQ_WEB_GBQD.getIndex(), ProductSource.GCDP_ZBSQ_WEB.getIndex());
    @Override
    public <T extends ItemData> List<T> subjectQueryItemSort(List<T> res, ItemIndexQueryReqVO itemIndexQueryReqVO, String indexType) {
        if (CollectionUtils.isEmpty(res)) {
            return res;
        }
        // 1. 预处理原始数据
        List<T> preHandleRes = preHandle(res, itemIndexQueryReqVO);

        // 2. 按项目划分排序
        Comparator<T> sortByItemComponent = sortByItemComponent();

        // 3. 主要量排序
        Comparator<T> sortZylIndex = sortZylIndex(preHandleRes, itemIndexQueryReqVO.getEnterpriseId(), indexType);

        // 4. 默认排序
        Comparator<T> commonSort = sorted();

        return  preHandleRes.stream().sorted(sortByItemComponent
                .thenComparing(sortZylIndex)
                .thenComparing(commonSort))
                .collect(Collectors.toList());
    }

    private <T extends ItemData> List<T> preHandle(List<T> res, ItemIndexQueryReqVO itemIndexQueryReqVO) {
        String enterpriseId = itemIndexQueryReqVO.getEnterpriseId();
        if (!needSortByItemComponent(enterpriseId)) {
            log.info("当前企业[entId:{}]无已经接入项目划分的归档文件, 不需要进行项目划分排序", enterpriseId);
            return res;
        }

        // 1. 重新生成namPath并生成hash 解决不同数据源的科目全路径分隔符问题 同时方便后续扩展
        List<T> generateNamePathHashList = generateNamePathHashList(res);
        Set<String> namePathHashList = generateNamePathHashList
                .stream().map(ItemData::getNamePathHash)
                .collect(Collectors.toSet());


        // 2. 找出项目划分中匹配的所有科目 并处理相关信息
        List<DimItemComponentCategoryItem> matchItemComponentList = getMatchItemComponentList(namePathHashList, itemIndexQueryReqVO);
        if (CollectionUtils.isEmpty(matchItemComponentList)) {
            log.info("无匹配科目");
            return res;
        }

        Map<String, Integer> itemHashOrdMap = IntStream.range(0, matchItemComponentList.size()).boxed()
                .collect(Collectors.toMap(i -> matchItemComponentList.get(i).getItemHash(), Function.identity()));

        // 3. 处理res 项目划分中匹配得到的科目信息
        List<T> firstOrdList = generateNamePathHashList.stream()
                .peek(e -> e.setRealOrd(itemHashOrdMap.getOrDefault(e.getNamePathHash(), Integer.MAX_VALUE)))
                .sorted(Comparator.comparing(ItemData::getRealOrd))
                .collect(Collectors.toList());

        Map<Long, T> virtualLevelOneMap = firstOrdList.stream().filter(e -> StringUtils.isNotBlank(e.getName())
                        && e.getName().equals(VIRTUAL_LEVEL_ONE_NAME))
                .collect(Collectors.toMap(ItemData::getId, Function.identity()));

        // 4. 处理res 项目划分中 虚拟节点的顺序问题
        firstOrdList.forEach(e -> {
            Long pid = e.getPid();
            if (Objects.nonNull(pid) && virtualLevelOneMap.containsKey(pid)) {
                T data = virtualLevelOneMap.get(pid);
                data.setRealOrd(e.getRealOrd());
                virtualLevelOneMap.remove(pid);
            }
        });
        return firstOrdList;
    }

    private <T extends ItemData> Comparator<T> sortByItemComponent() {
        return Comparator.comparing(ItemData::getRealOrd, Comparator.nullsFirst(Comparator.naturalOrder()));
    }

    @SuppressWarnings("all")
    private List<DimItemComponentCategoryItem> getMatchItemComponentList(Set<String> namePathHashList,
                                                                         ItemIndexQueryReqVO itemIndexQueryReqVO) {

        List<String> enterpriseIds = Lists.newArrayList(DEFAULT_ENTERPRISE_ID, itemIndexQueryReqVO.getEnterpriseId());
        List<DimProjectCategory> dimProjectCategories = dimProjectCategoryService.listCategoryLevelOne(itemIndexQueryReqVO.getEnterpriseId());
        List<String> categoryCodes = dimProjectCategories.stream().map(DimProjectCategory::getCategoryId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(categoryCodes)) {
            log.error("无法获取当前企业:{}的一级业态编码，请检查数据同步服务是否正常");
            return Collections.emptyList();
        }
        // 1. 找到所有的项目划分科目
        List<DimItemComponentCategoryItem> dimItemComponentCategoryItems =
                dimItemComponentMapper.listByEnterpriseIdInAndNamePathHash(enterpriseIds, namePathHashList, categoryCodes);

        // 2. 优先拿样本量匹配的业态作为排序顺序 并重排业态顺序
        Integer maxOrd = dimProjectCategories.get(dimProjectCategories.size() -1).getOrd();
        Set<String> matchCategory = itemIndexQueryReqVO.getMatchCategory();
        Map<String, Integer> categoryOrdMap = dimProjectCategories.stream().peek(e -> {
                    if (matchCategory.contains(e.getCategoryId())) {
                        e.setOrd(e.getOrd() - maxOrd);
                    }
                })
                .collect(Collectors.toMap(DimProjectCategory::getCategoryId, DimProjectCategory::getOrd, (o1,o2) -> o1));
        return dimItemComponentCategoryItems.stream()
                .peek(e -> {
                    e.setCategoryOrd(categoryOrdMap.getOrDefault(e.getCategoryCode(), maxOrd +1 ));
                })
                .collect(Collectors.groupingBy(DimItemComponentCategoryItem::getItemHash))
                .values()
                .stream()
                // 分组之后 多条同层级科目 优先取用户自定义的 业态顺序在前 科目顺序在前的 科目
                .map(group -> group.stream()
                        .min(Comparator.comparing(DimItemComponentCategoryItem::getEnterpriseId).reversed()
                                .thenComparing(DimItemComponentCategoryItem::getCategoryOrd)
                                .thenComparing(DimItemComponentCategoryItem::getItemOrd)
                        ).get())
                // 多条不同层级匹配科目 之间排序
                .sorted(Comparator.comparing(DimItemComponentCategoryItem::getCategoryOrd)
                        .thenComparing(DimItemComponentCategoryItem::getItemOrd))
                .collect(Collectors.toList());

    }

    private <T extends ItemData> List<T> generateNamePathHashList(List<T> res) {
        Map<Long, String> idNameMap = res.stream()
                .filter(e -> Objects.nonNull(e.getName()))
                .collect(Collectors.toMap(ItemData::getId, ItemData::getName));
        Map<Long, Long> idPidMap = res.stream()
                .filter(e -> Objects.nonNull(e.getPid()))
                .collect(Collectors.toMap(ItemData::getId, ItemData::getPid));
       return res.stream()
                .map(index -> getNamePathAndItemHash(idNameMap, idPidMap, index))
                .collect(Collectors.toList());
    }


    private <T extends ItemData> T getNamePathAndItemHash(Map<Long, String> idNameMap, Map<Long, Long> idPidMap, T index) {
        Long id = index.getId();
        List<String > namePath = Lists.newArrayList();
        getPidName(idNameMap, idPidMap, id, namePath);
        Collections.reverse(namePath);
        index.setNamePathHash(HashUtil.hash64WithFnv1a(String.join(CharConst.DOUBLE_AT, namePath)));
        return index;
    }


    private void getPidName(Map<Long, String> idNameMap, Map<Long, Long> idPidMap, Long id, List<String> namePath) {
        String name = idNameMap.get(id);
        if (VIRTUAL_LEVEL_ONE_NAME.equals(name)) {
            return;
        }
        namePath.add(name);

        if (CollectionUtils.isEmpty(idNameMap)
                || CollectionUtils.isEmpty(idPidMap)
                || !idPidMap.containsKey(id)) {
            return;
        }
        Long pid = idPidMap.get(id);
        // 防止出现环形结构 导致一直走不出去
        if (pid.equals(ValueConst.NON_PID) || id.equals(pid)) {
            return;
        }
        getPidName(idNameMap, idPidMap, pid, namePath);
    }

    private boolean needSortByItemComponent(String enterpriseId) {
        return indexProjectNoteMapper.selectCountByEnterpriseIdAndProductSource(enterpriseId, ITEM_COMPONENT_SET) > 0;
    }


    private <T extends ItemData> Comparator<T> sortZylIndex(List<T> res, String entId, String indexType) {
        Comparator<T> constantComparator = (obj1, obj2) -> 0;
        if (!IndexTypePrefixEnums.ZYLZB.getType().equals(indexType)) {
            return constantComparator;
        }
        String customerCode = customCodeService.getBasicInfoNewQyCode(entId);
        log.info("该企业主要量数据：customerCode:{},query:{}", customerCode, res);
        List<DimZbStandardsMainQuantity> dimZbStandardsMainQuantities = zbStandardsRepository.selectByCustomerCode(customerCode);
        if ( CollectionUtils.isEmpty(dimZbStandardsMainQuantities)) {
            return constantComparator;
        }

        //拿出是基础信息库的科目，基础信息库的科目code是雪花id，19位，非基础信息库的code是以3为倍数
        List<T> basicList = res.stream().filter(item -> StringUtils.isNotBlank(item.getCode()) && item.getCode().length() == 19).collect(Collectors.toList());
        log.info("该企业基础信息库的科目主要量数据：customerCode:{},basicList:{}", customerCode, basicList);
        if (CollectionUtils.isEmpty(basicList)) {
            return constantComparator;
        }
        //把基础信息库查出来的科目用description和对应下标生成一个map
        Map<String, Integer> description2IndexMap = IntStream.range(0, dimZbStandardsMainQuantities.size()).boxed()
                .collect(Collectors.toMap(i -> dimZbStandardsMainQuantities.get(i).getDescription(), Function.identity(), (existing, replacement) -> existing));
        log.info("该企业description2IndexMap：customerCode:{},description2IndexMap:{}", customerCode, description2IndexMap);
        /*
        当对 ItemData 对象列表进行排序时，comparator 将根据科目的 name字段对应的 description 来确定排序顺序。具体排序规则如下：
        1. 如果 ItemData 对象的 name 字段对应的 description description2IndexMap 中存在，则按照 description 在 dimZbStandardsMainQuantities 列表中的下标进行排序。
        2. 如果 ItemData 对象的 name字段对应的 description 在 description2IndexMap 中不存在（即无法找到匹配的 key），则将该对象排在列表的末尾，以保持原始顺序。
         */
        return Comparator.comparing(data -> description2IndexMap.getOrDefault(data.getName(), Integer.MAX_VALUE));
    }


    protected <T extends ItemData> Comparator<T> sorted() {
        return Comparator.comparing(T::getCode, Comparator.nullsFirst(Comparator.naturalOrder())).thenComparing(T::getName, Comparator.nullsLast(Comparator.naturalOrder()));
    }
}
