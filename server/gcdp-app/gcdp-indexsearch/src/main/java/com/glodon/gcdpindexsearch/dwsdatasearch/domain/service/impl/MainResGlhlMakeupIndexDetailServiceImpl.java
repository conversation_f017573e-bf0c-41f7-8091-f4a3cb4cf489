package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsIndex;
import com.glodon.gcdpindexsearch.common.enums.FilterTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMainRes;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MainResGlhlMakeupIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMainMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.MakeupIndexDetailService;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service("makeupIndexDetailServiceZYGLHLZB")
public class MainResGlhlMakeupIndexDetailServiceImpl implements MakeupIndexDetailService {
    @Autowired
    private GcdpDwsIndexMainMapper dwsIndexMainMapper;

    @Override
    public List<MakeUpIndexData> listMakeupIndexDetail(List<MakeUpIndexData> indexDataList, String itemIds, FilterTypeEnums filterType) {
        if (CollUtil.isEmpty(indexDataList)) {
            return Lists.newArrayList();
        }
        //查询科目信息
        List<DwsIndexMainRes> mainRes = assembleIndexes(itemIds);
        if (CollUtil.isEmpty(mainRes)) {
            return Lists.newArrayList();
        }
        return getIndexData(indexDataList, mainRes);
    }

    /**
     * 查询指标数据
     * @param itemIds: 科目ids
     * @return
     */
    List<DwsIndexMainRes> assembleIndexes(String itemIds){
        List<Long> itemIdList = Arrays.stream(itemIds.split(Constant.COMMA)).map(Long::new).collect(Collectors.toList());
        List<DwsIndexMainRes> mainRes = dwsIndexMainMapper.selectByItemIds(itemIdList);
        if (CollUtil.isEmpty(mainRes)){
            return Lists.newArrayList();
        }

        return mainRes.stream()
                .filter(x -> x.getHlIndexValue() != null && x.getHlIndexValue().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
    }

    private List<MakeUpIndexData> getIndexData(List<MakeUpIndexData> indexDataList, List<DwsIndexMainRes> mainRes) {
        List<MakeUpIndexData> data = new ArrayList<>();
        Map<String, List<DwsIndexMainRes>> calculateValueMap = mainRes.stream().collect(Collectors.groupingBy(item -> item.getSubjectLdMergeHash()));
        for (Map.Entry<String,List<DwsIndexMainRes>> entry : calculateValueMap.entrySet()){
            List<DwsIndexMainRes> dwsIndexMainReses = entry.getValue();
            DwsIndexMainRes dwsIndexMainRes = dwsIndexMainReses.get(0);
            Optional<MakeUpIndexData> optional = indexDataList.stream().filter(item->item.getIds().contains(String.valueOf(dwsIndexMainRes.getIndexProjectNoteId()))).findFirst();
            if(!optional.isPresent()){
                continue;
            }
            String ids = dwsIndexMainReses.stream().map(item-> String.valueOf(item.getIndexProjectNoteId())).collect(Collectors.joining(","));
            MakeUpIndexData makeUpIndexData = optional.get();
            MainResGlhlMakeupIndexData glhlMakeupIndexData = new MainResGlhlMakeupIndexData();
            BeanUtils.copyProperties(makeUpIndexData, glhlMakeupIndexData);
            glhlMakeupIndexData.setIds(ids);
            setIndexDataField(glhlMakeupIndexData, dwsIndexMainReses);
            data.add(glhlMakeupIndexData);
        }
        return data;
    }

    private void setIndexDataField(MainResGlhlMakeupIndexData glhlMakeupIndexData, List<DwsIndexMainRes> dwsIndexMainReses) {
        Optional<DwsIndexMainRes> latestMainRes = dwsIndexMainReses.stream().
                max(Comparator.comparing(DwsIndexMainRes::getArchiveDate, Comparator.nullsFirst(Comparator.naturalOrder())));
        if (!latestMainRes.isPresent()) {
            return;
        }
        DwsIndexMainRes dwsIndexMainRes = latestMainRes.get();
        String name = dwsIndexMainRes.getName();
        BigDecimal hlCalculateValue = dwsIndexMainRes.getHlCalculateValue();
        String calculateName = dwsIndexMainRes.getHlCalculateName();
        BigDecimal quantity = dwsIndexMainReses.stream().filter(x -> x.getHlIndexValue() != null && x.getHlCalculateValue() != null)
                .map(x -> x.getHlIndexValue().multiply(x.getHlCalculateValue())).reduce(BigDecimal.ZERO, BigDecimal::add);
        glhlMakeupIndexData.setName(name);
        glhlMakeupIndexData.setQuantity(quantity == null ? Constant.LINE : quantity.toString());
        if (quantity != null && hlCalculateValue != null && BigDecimal.ZERO.compareTo(hlCalculateValue) != 0) {
            glhlMakeupIndexData.setGlhlIndexValue(quantity.divide(hlCalculateValue, 6, RoundingMode.HALF_DOWN).toString());
        } else {
            glhlMakeupIndexData.setGlhlIndexValue(Constant.LINE);
        }
        glhlMakeupIndexData.setCaliberValue(hlCalculateValue == null ? Constant.LINE : hlCalculateValue.toString());
        glhlMakeupIndexData.setCaliber(calculateName);
        if (StringUtils.isNotBlank(calculateName)&&calculateName.contains("(")) {
            calculateName = StringUtils.isNotBlank(calculateName) ? calculateName.substring(calculateName.indexOf("(") + 1, calculateName.indexOf(")")) : Constant.LINE;
        } else {
            calculateName = Constant.M2;
        }
        String unit = StringUtils.isNotBlank(dwsIndexMainRes.getUnit()) ? dwsIndexMainRes.getUnit() : Constant.LINE;
        glhlMakeupIndexData.setUnit(unit + Constant.Slash + calculateName);
    }
}
