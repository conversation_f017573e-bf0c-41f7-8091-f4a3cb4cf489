package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @Description: 全成本指标与建造做法库区间组成mapper
 * @Author: zhangj-cl
 * @Date: 2023/5/17 14:42
 */
@Mapper
public interface DwsCostDataSearchMapper {

    List<CostIndexMakeupQCBZBLibJMDFDto> selectQCBZBLibJmdfCostIndexByIds(@Param("itemIds") List<Long> itemIds);

    List<CostIndexMakeupQCBZBLibDFHLDto> selectQCBZBLibDfhlCostIndexByIds(@Param("itemIds") List<Long> itemIds);

    List<CostIndexMakeupQCBZBLibDFZJDto> selectQCBZBlibDfzjCostIndexByIds(@Param("itemIds") List<Long> itemIds);

    List<CostIndexMakeupQCBZBLibZHDJDto> selectQCBZBlibZhdjCostIndexByIds(@Param("itemIds") List<Long> itemIds);

    List<CostIndexMakeupJZZFZBLibDFZJDto> selectJZZFZBLibDfzjCostIndexByIds(@Param("itemIds") List<Long> itemIds);

    List<CostIndexMakeupJZZFZBLibZHDJDto> selectJZZFZBLibZhdjCostIndexByIds(@Param("itemIds") List<Long> itemIds);

    List<CostIndexMakeupBaseDto> selectProjectInfoByNoteIds(@Param("indexProjectNoteIds") Set<Long> indexProjectNoteIds);
}
