package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 组成
 * @date 2022/11/18 11:27
 */
@Data
@ApiModel(value = "组成", description = "组成")
@SuppressWarnings("squid:S1068") // 忽略sonar规则： Unused "private" fields should be removed
public class MakeupDetailDto extends MakeupDetailBaseVO {

    /**
     * 科目Id (用于科目跟清单关联关系的id)
     */
    private String indexId;

    @ApiModelProperty(value = "id", example = "id")
    private Long id;
    private Long pid;
    private Long bqtemId;
    @ApiModelProperty(value = "编码", example = "001001002001")
    private String code;
    @ApiModelProperty(value = "名称", example = "挖一般土方")
    private String name;
    @ApiModelProperty(value = "特征/规格型号", example = "土质要求：泥浆外运")
    private String spec;
    @ApiModelProperty(value = "单位", example = "m3")
    private String unit;
    @ApiModelProperty(value = "工程量", example = "16867.700")
    private BigDecimal quantity;
    @ApiModelProperty(value = "清单价（不含税）", example = "6.30")
    private BigDecimal rate;
    @ApiModelProperty(value = "清单价（含税）", example = "6.30")
    private BigDecimal rateIncludeTax;

    @ApiModelProperty(value = "价合计（不含税）", example = "106230.80")
    private BigDecimal amount;
    @ApiModelProperty(value = "价合计（含税）", example = "106230.80")
    private BigDecimal amountIncludeTax;
    @ApiModelProperty(value = "类别", example = "1分部分项清单2措施清单4定额")
    private Integer type;

    /**
     * 0=项1=补项2=借项3=暂
     */
    @ApiModelProperty(value = "清单类型", example = " 0=项1=补项2=借项3=暂")
    private Integer bqItemType;
    @ApiModelProperty(value = "定额类型", example = "0=定 1=补 2=换 3=借 4=综 5=借换 100=安 200=降 300=子目级资源 1000=零材通机")
    private Integer normItemType;
    /**
     * 工料法，工料法的都是不含税
     */
    private BigDecimal glRate;
    private BigDecimal glAmount;
    /**
     * 仿清单法
     */
    private BigDecimal fqdRate;
    private BigDecimal fqdRateIncludeTax;
    private BigDecimal fqdAmount;
    private BigDecimal fqdAmountIncludeTax;

    /**
     * 1=清单计价2=定额计价工料法3=定额计价仿清单法
     */
    private Integer estiType;

    // 清单下关联的子目
    private List<MakeupDetailDto> normItems;

    // 子目下关联的工料消耗
    private List<MaterialDTO> lmmDetailItems;

    @JsonIgnore
    @ApiModelProperty(value = "材料类型", example = "6")
    private Integer resourceType;
    @JsonIgnore
    @ApiModelProperty(value = "材料类型名称", example = "人")
    private String resourceTypeName;
    @JsonIgnore
    @ApiModelProperty(value = "材料类型排序", example = "1")
    private Integer resourceTypeOrd;
    @JsonIgnore
    @ApiModelProperty(value = "市场价（含税）", example = "6.30")
    private BigDecimal marketRateIncludeTax;
    @JsonIgnore
    @ApiModelProperty(value = "市场价合计（含税）", example = "6.30")
    private BigDecimal marketAmountIncludeTax;
    @JsonIgnore
    @ApiModelProperty(value = "市场价（不含税）", example = "106230.80")
    private BigDecimal marketRate;
    @JsonIgnore
    @ApiModelProperty(value = "市场价合计（不含税）", example = "106230.80")
    private BigDecimal marketAmount;
}
