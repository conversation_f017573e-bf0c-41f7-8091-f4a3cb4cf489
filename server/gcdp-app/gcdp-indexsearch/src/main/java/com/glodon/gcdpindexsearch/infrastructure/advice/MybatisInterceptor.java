package com.glodon.gcdpindexsearch.infrastructure.advice;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Statement;
import java.util.Properties;

/**
 * MyBatis拦截器，用于监控SQL执行时间
 * */
@Slf4j
@Component
@Intercepts({
        @Signature(
                type = StatementHandler.class,
                method = "query",
                args = {Statement.class, ResultHandler.class})
})
public class MybatisInterceptor implements Interceptor {

    // 是否记录超时SQL
    @Value("${mybatis.log-timeout-sql:true}")
    private boolean logTimeoutSQL;
    // 超时时间
    @Value("${mybatis.timeout:50}")
    private Long timeout;
    // 是否需要format sql
    @Value("${mybatis.format-sql:false}")
    private boolean formatSQL;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object target = invocation.getTarget();
        long begin = System.currentTimeMillis();
        StatementHandler statementHandler = (StatementHandler) target;
        try {
            return invocation.proceed();
        } finally {
            long end = System.currentTimeMillis();
            // 判断超时
            if (logTimeoutSQL && (end - begin) > timeout) {
                // 控制台打印日志
                BoundSql boundSql = statementHandler.getBoundSql();
                String sql = boundSql.getSql();
                log.warn("SQL执行时间：{} ms，执行 SQL：[ {} ]", (end - begin), sql);
            }
        }
    }


    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
