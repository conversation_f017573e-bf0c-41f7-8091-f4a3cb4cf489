package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.gcdp.dwdservice.domain.model.refresh.RefreshEnterprise;
import com.glodon.gcdp.dwdservice.domain.vo.DwdProjectInfoDynamicQueryVo;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.ZbgxDwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.ConditionResultDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.SampleNoteDto;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.CostIndexListVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterConditionVO;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.SharedEnterpriseVo;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.SingleProjectReqVO;
import com.glodon.gcdpindexsearch.dynamic.domain.entity.SimpleProjectNode;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository("indexSearchDwsIndexProjectNoteMapper")
public interface DwsIndexProjectNoteMapper extends BaseMapper<DwsIndexProjectNote> {

    List<ConditionResultDto> selectCondition(@Param(value = "enterpriseId") String enterpriseId,
                                             @Param(value = "typeList") List<Integer> typeList,
                                             @Param(value = "orgIds") List<String> orgIds,
                                             @Param(value = "isVirtualOrg") String isVirtualOrg,
                                             @Param(value = "sharedEnterpriseIds") List<String> sharedEnterpriseIds,
                                             @Param(value = "productSource") List<String> productSource,
                                             @Param(value = "authControlProjectCodeList") List<String> authControlProjectCodeList);

    List<DwsIndexProjectNote> selectNoteList(@Param(value = "enterpriseId") String enterpriseId,
                                             @Param(value = "typeList") List<Integer> typeList,
                                             FilterConditionVO condition);

    List<DwsIndexProjectNote> selectProjectAttrByIds(@Param("ids") List<Long> ids,
                                                     @Param("contractProjectIds") List<Long> contractProjectIds,
                                                     @Param("enterpriseId") String enterpriseId);

    List<DwsIndexProjectNote> selectNoteBySingleProjectReqVO(SingleProjectReqVO reqVO);

    List<SampleNoteDto> selectNoteAndProjByIds(@Param("enterpriseId") String enterpriseId, @Param(value = "noteIds") List<Long> noteIds);

    List<DwsIndexProjectNote> getTotal(@Param(value = "ids") List<String> ids);

    List<MakeUpIndexData> selectNoteByIdsGroupByPhaseAndName(@Param(value = "ids") List<Long> ids);

    List<MakeUpIndexData> selectNoteInfoByIds(@Param(value = "ids") List<Long> ids);

    List<DwsIndexProjectNote> selectNoteByIds(@Param("enterpriseId") String enterpriseId, @Param(value = "noteIds") List<Long> ids);

    List<Long> selectNoteByContractProjectIds(@Param("enterpriseId") String enterpriseId, @Param(value = "contractProjectId") List<Long> contractProjectId);

    List<DwsIndexProjectNote> selectNoteByBidNodeIds(@Param("enterpriseId") String enterpriseId, @Param(value = "bidNodeIds") List<Long> bidNodeIds);

    List<SimpleProjectNode> queryAllSimple(DwdProjectInfoDynamicQueryVo queryVo);

    List<DwsIndexProjectNote> selectCategoryCodeAndPhaseAndPosition(@Param("enterpriseId") String enterpriseId,
                                                                    @Param(value = "productSource") List<String> productSource,
                                                                    @Param(value = "orgIds") List<String> orgIds,
                                                                    @Param(value = "isVirtualOrg") String isVirtualOrg,
                                                                    @Param(value = "authControlProjectCodeList") List<String> authControlProjectCodeList);

    List<DwsIndexProjectNote> selectNoteByCondition(CostIndexListVO costIndexListVO);

    List<DwsIndexProjectNote> selectByContractProjectIds(@Param("enterpriseId") String enterpriseId,
                                                         @Param("contractProjectIds") List<Long> contractProjectIds,
                                                         @Param("bidnodeIds") List<Long> bidnodeIds);

    List<DwsIndexProjectNote> selectSharedListWithBlobField(@Param("sharedEnterpriseList") List<SharedEnterpriseVo> sharedEnterpriseList,
                                                            @Param("typeList") List<Integer> typeList,
                                                            FilterConditionVO condition,
                                                            @Param("phaseList") List<String> phaseList,
                                                            @Param("isNeedContractInfo") boolean isNeedContractInfo,
                                                            @Param("isNeedFeatureInfo") boolean isNeedFeatureInfo);

    List<DwsIndexProjectNote> selectNotesByIds(@Param(value = "ids") List<Long> ids);


    int selectCountByEnterpriseIdAndProductSource(String enterpriseId, @Param("set") Set<String> itemComponentSet);

    List<DwsIndexProjectNote> selectNotesByContractProjectIds(@Param(value = "contractProjectId") List<Long> contractProjectId);

    List<ZbgxDwsIndexProjectNote> selectZbgxNotesByIds(@Param(value = "ids") List<Long> ids);

    List<DwsIndexProjectNote> selectAllNotesByIds(@Param(value = "ids") List<Long> ids);
    List<DwsIndexProjectNote> selectWholeNoteByHash(@Param("hashFieldName") String hashFieldName,
                                                    @Param("hashSet") Set<String> hashSet,
                                                    @Param("idSet") Set<Long> idSet,
                                                    @Param("isNeedBlobField") boolean isNeedBlobField,
                                                    @Param("productSources") List<String> productSources);

    void batchUpdateJson(@Param(value = "list") List<DwsIndexProjectNote> noteList);
    List<RefreshEnterprise> selectRefreshEnterprise(@Param("enterpriseIds") List<String> enterpriseIdList);
}
