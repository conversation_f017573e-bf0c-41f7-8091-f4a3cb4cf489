package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @description: 工程分类dto
 * @date 2022/10/25 16:37
 */
@Data
public class CategoryDto {

    @ApiModelProperty(value = "编码", required = true, example = "001002")
    private String code;
    @ApiModelProperty(value = "名称", required = true, example = "小高层")
    private String name;
    @ApiModelProperty(value = "全路径名称", required = true, example = "居住建筑@@小高层")
    private String namePath;
    @ApiModelProperty(value = "子节点", required = true)
    private List<CategoryDto> children = new ArrayList<>();

    public CategoryDto() {}

    public CategoryDto(String code, String name, String namePath) {
        this.code = code;
        this.name = name;
        this.namePath = namePath;
    }

    public static List<CategoryDto> buildTree(List<DwsIndexProjectNote> dwsIndexProjectNoteList) {
        List<CategoryDto> result = new ArrayList<>();
        Map<String, CategoryDto> namePathMap = new HashMap<>();

        for (DwsIndexProjectNote dwsIndexProjectNote : dwsIndexProjectNoteList) {
            String projectCategoryName = dwsIndexProjectNote.getProjectCategoryName();
            String projectCategoryCode = dwsIndexProjectNote.getProjectCategoryCode();

            if(StringUtils.isEmpty(projectCategoryName) || StringUtils.isEmpty(projectCategoryCode)){
                continue;
            }

            String[] categoryNameArr = projectCategoryName.split(CharConst.DOUBLE_AT);
            StringJoiner namePathJoiner = new StringJoiner(CharConst.DOUBLE_AT);

            for (int i = 0; i < categoryNameArr.length; i++) {
                String name = categoryNameArr[i];

                String parentNamePath = namePathJoiner.toString();
                namePathJoiner.add(name);
                String namePath = namePathJoiner.toString();

                if (!namePathMap.containsKey(namePath)){
                    String code = "";
                    if (categoryNameArr.length == projectCategoryCode.length()/3){
                        code = projectCategoryCode.substring(0, (i + 1) * 3);
                    }

                    CategoryDto categoryDto = new CategoryDto(code, name, namePath);

                    namePathMap.put(namePath, categoryDto);

                    if (i == 0){
                        result.add(categoryDto);
                    }

                    if (StringUtils.isNotEmpty(parentNamePath)){
                        CategoryDto parentCategoryDto = namePathMap.get(parentNamePath);
                        parentCategoryDto.getChildren().add(categoryDto);
                    }
                }
            }
        }

        return result;
    }

    public static void main(String[] args) {
        List<DwsIndexProjectNote> dwsIndexProjectNoteList = new ArrayList<>();
        DwsIndexProjectNote note1 = new DwsIndexProjectNote();
        note1.setProjectCategoryCode("001001001");
        note1.setProjectCategoryName("001/001001/001001001");
        DwsIndexProjectNote note2 = new DwsIndexProjectNote();
        note2.setProjectCategoryCode("001001002");
        note2.setProjectCategoryName("001/001001/001001002");
        DwsIndexProjectNote note3 = new DwsIndexProjectNote();
        note3.setProjectCategoryCode("002001001");
        note3.setProjectCategoryName("002/002001/002001001");
        DwsIndexProjectNote note4 = new DwsIndexProjectNote();
        note4.setProjectCategoryCode("002/00200");
        note4.setProjectCategoryName("002/002002");

        dwsIndexProjectNoteList.add(note1);
        dwsIndexProjectNoteList.add(note2);
        dwsIndexProjectNoteList.add(note3);
        dwsIndexProjectNoteList.add(note4);

    }

}
