package com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums;

import com.glodon.gcdp.common.domain.enums.DwsNoteTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import lombok.Getter;

import java.util.*;
import java.util.function.Function;

/**
 * @description: 查询维度枚举类: 按项目=1；按业态=2；按单体=3
 * <AUTHOR>
 * @date 2022/10/24 18:44
 */
@Getter
public enum SumTypeEnum {
    /** 按项目=1 */
    PROJECT(1,"按项目", null),
    /** 按业态=2 */
    CATEGORY(2,"按业态", Arrays.asList(DwsNoteTypeEnums.CATEGORY.getIndex(), DwsNoteTypeEnums.LD.getIndex(), DwsNoteTypeEnums.XN_LD.getIndex())),
    /** 按单体=3 */
    DT(3,"按单体", Arrays.asList(DwsNoteTypeEnums.LD.getIndex(), DwsNoteTypeEnums.XN_LD.getIndex())),
    /** 按专业 */
    LD_TRADE(4,"按真实楼栋专业", Collections.singletonList(DwsNoteTypeEnums.LD.getIndex())),
    XNLD_TRADE(5,"虚拟楼栋专业", Collections.singletonList(DwsNoteTypeEnums.XN_LD.getIndex())),
    /** 虚拟节点 */
    VIRTUAL_NODE(6,"虚拟节点", Collections.singletonList(DwsNoteTypeEnums.VIRTUAL.getIndex())),
    /** 审核结构化列表，反查完整数据的依据是dt_hash */
    STRUCT_PROJECT(7,"审核结构化列表", Arrays.asList(DwsNoteTypeEnums.LD.getIndex(), DwsNoteTypeEnums.XN_LD.getIndex(), DwsNoteTypeEnums.VIRTUAL.getIndex())),
    ;

    SumTypeEnum(Integer code, String description, List<Integer> typeList){
        this.code = code;
        this.description = description;
        this.typeList = typeList;
    }
    private Integer code;
    private String description;
    private List<Integer> typeList;

    /**
     * @description: 根据页面类型获取type
     *               按项目:    type不用加
     *               按业态:    type=业态+楼栋    (末级业态，或者业态下的楼栋）
     *               按单体:    type=楼栋
     * @param code
     * @return java.util.List<java.lang.Integer>
     * <AUTHOR>
     * @date 2022/10/24 19:17
     */
    public static List<Integer> getTypeListByCode(Integer code){
        if (code != null){
            SumTypeEnum[] values = SumTypeEnum.values();
            for (SumTypeEnum sumTypeEnum : values) {
                if(code.equals(sumTypeEnum.getCode())){
                    return sumTypeEnum.getTypeList();
                }
            }
        }
        return null;
    }

    public static final Map<Integer, String> HASH_FIELD_NAME = new HashMap<>();
    public static final Map<Integer, Function<DwsIndexProjectNote, String>> HASH_FIELD_EXTRACTORS = new HashMap<>();
    public static final Map<Integer, Boolean> IS_NEED_BLOB_FIELD = new HashMap<>();

    /**
     * HASH_FIELD_NAME 对应的表字段
     * HASH_FIELD_EXTRACTORS 对应的对象字段
     * IS_NEED_BLOB_FIELD 是否需要blob字段（工程特征、合同信息）
     */
    static {
        HASH_FIELD_NAME.put(PROJECT.getCode(), "project_hash");
        HASH_FIELD_NAME.put(CATEGORY.getCode(), "dt_hash");
        HASH_FIELD_NAME.put(DT.getCode(), "dt_hash");
        HASH_FIELD_NAME.put(STRUCT_PROJECT.getCode(), "dt_hash");
        HASH_FIELD_EXTRACTORS.put(PROJECT.getCode(), DwsIndexProjectNote::getProjectHash);
        HASH_FIELD_EXTRACTORS.put(CATEGORY.getCode(), DwsIndexProjectNote::getDtHash);
        HASH_FIELD_EXTRACTORS.put(DT.getCode(), DwsIndexProjectNote::getDtHash);
        HASH_FIELD_EXTRACTORS.put(STRUCT_PROJECT.getCode(), DwsIndexProjectNote::getDtHash);
        IS_NEED_BLOB_FIELD.put(PROJECT.getCode(), Boolean.TRUE);
        IS_NEED_BLOB_FIELD.put(CATEGORY.getCode(), Boolean.TRUE);
        IS_NEED_BLOB_FIELD.put(DT.getCode(), Boolean.TRUE);
        IS_NEED_BLOB_FIELD.put(STRUCT_PROJECT.getCode(), Boolean.FALSE);
    }
}
