package com.glodon.gcdpindexsearch.infrastructure.cache;

/**
 * @packageName: com.glodon.gcdpindexsearch.infrastructure.cache
 * @className: MemoryCache
 * @author: yany<PERSON><PERSON> <EMAIL>
 * @date: 2023/1/4 11:08
 * @description:
 */
public class MemoryCache {

    public static volatile String ACCESS_TOKEN = null;


    public static void setAccessToken(String accessToken) {
        ACCESS_TOKEN = accessToken;
    }


    public static String getAccessToken() {
        return ACCESS_TOKEN;
    }
}
