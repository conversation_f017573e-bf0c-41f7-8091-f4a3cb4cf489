package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl.costStandard;

import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard.ReferenceDataBaseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: 企业限额指标-查询数据
 * <AUTHOR>
 * @date 2023/5/16 9:15
 */
@Service("CostStandardService-qyxezb")
@Slf4j
public class CostStandardQYXEZBServiceImpl extends CostStandardAbsService {

    @Override
    public <T extends ReferenceDataBaseDto> List<T> doSelectReferenceDataByNoteIdsAndName(Map<String, ReferenceDataBaseDto> projectNoteIdsMap, String name, Integer itemCostType){
        return (List<T>) referenceDataRepository.selectReferenceDataToQYXEZBLib(projectNoteIdsMap, name, itemCostType);
    }
}
