package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costStandard;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.StandardDescription;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 三库-参考历史数据 BaseDto
 * @date 2023-05-15 15:01
 */
@Data
public class ReferenceDataBaseDto {
    @ApiModelProperty(value = "科目名称/标准名称", required = true)
    protected String name;

    @ApiModelProperty(value = "标准说明（list）， 只有目标成本来源才有此字段")
    private List<StandardDescription> standardDescription;

    @JsonIgnore
    @ApiModelProperty(value = "标准说明（原始jsonString）， 只有目标成本来源才有此字段")
    private String standardValue;

    @ApiModelProperty(value = "科目单位", required = true)
    protected String unit;

    @ApiModelProperty(value = "项目名称", required = true)
    protected String projectName;

    @ApiModelProperty(value = "单项名称", required = true)
    protected String nodeName;

    @ApiModelProperty(value = "业务阶段", required = true)
    protected String phase;

    @ApiModelProperty(value = "产品业态", required = true)
    protected String projectCategoryName;

    @ApiModelProperty(value = "产品定位", required = true)
    protected String productPosition;

    @ApiModelProperty(value = "省", required = true)
    protected String provinceName;

    @ApiModelProperty(value = "省Id", required = true)
    protected String provinceId;

    @ApiModelProperty(value = "市", required = true)
    protected String cityName;

    @ApiModelProperty(value = "市Id", required = true)
    protected String cityId;

    @ApiModelProperty(value = "区", required = true)
    protected String districtName;

    @ApiModelProperty(value = "区Id", required = true)
    protected String districtId;

    @ApiModelProperty(value = "归档时间", required = true)
    protected String archiveDate;

    @ApiModelProperty(value = "文件名称", required = true)
    protected String fileName;

    @JsonIgnore
    @ApiModelProperty(value = "contractProjectId")
    protected String contractProjectId;

    @JsonIgnore
    @ApiModelProperty(value = "projectNoteId")
    private String projectNoteId;

    @JsonIgnore
    @ApiModelProperty(value = "项目来源：mbcb、zbsq等")
    private String dataSoure;

    @JsonIgnore
    @ApiModelProperty(value = "projectCode")
    private String projectCode;

    @JsonIgnore
    @ApiModelProperty(value = "项目上的阶段的权重，用来区分那个阶段是最新的")
    private Integer stage;

    @JsonIgnore
    @ApiModelProperty(value = "phaseWeight, 项目上的权重数据")
    private Integer phaseWeight;

    @JsonIgnore
    @ApiModelProperty(value = "是否非建安数据, true 是、false 不是")
    private Boolean nonConstruction;

    @ApiModelProperty(value = "计算口径")
    private String caculateName;

    @JsonIgnore
    @ApiModelProperty(value = "主要量 计算口径： 非建安计算口径取 主要量的，建安读 swl_calculate_name 或 df_calculate_name")
    private String zylCalculateName;
}

