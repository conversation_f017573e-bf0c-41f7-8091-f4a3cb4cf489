package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@ApiModel(value = "指标组成动态表头",description = "指标组成动态表头")
@Data
public class MakeupIndexDynamicColDto {
    @ApiModelProperty(value = "排序",example = "0")
    private Integer ord;
    @ApiModelProperty(value = "列属性key",example = "jmIndexValue")
    private String fieldName;
    @ApiModelProperty(value = "列名",example = "建面单方")
    private String caption;
    @ApiModelProperty(value = "类型 number:数字,text:文本",example = "text")
    private String type;
}
