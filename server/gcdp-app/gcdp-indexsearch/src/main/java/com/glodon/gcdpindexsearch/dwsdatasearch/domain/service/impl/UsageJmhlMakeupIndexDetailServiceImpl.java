package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsIndex;
import com.glodon.gcdpindexsearch.common.enums.FilterTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.MakeUpIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.UsageJmhlMakeupIndexData;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.MakeupIndexDetailService;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.google.common.collect.Lists;
import io.swagger.models.auth.In;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("DuplicatedCode")
@Service("makeupIndexDetailServiceJMHLZB")
public class UsageJmhlMakeupIndexDetailServiceImpl implements MakeupIndexDetailService {

    @Autowired
    private GcdpDwsIndexMapper gcdpDwsIndexMapper;

    @Override
    public List<MakeUpIndexData> listMakeupIndexDetail(List<MakeUpIndexData> indexDataList, String itemIds, FilterTypeEnums filterType) {
        if (CollUtil.isEmpty(indexDataList)) {
            return Lists.newArrayList();
        }
        //查询科目信息
        List<DwsIndex> dwsIndices = assembleIndexes(itemIds);
        if (CollUtil.isEmpty(dwsIndices)) {
            return Lists.newArrayList();
        }
        return getIndexData(indexDataList, dwsIndices);
    }

    /**
     * 查询指标数据
     * @param itemIds: 科目ids
     * @return
     */
    List<DwsIndex> assembleIndexes(String itemIds){
        List<Long> itemIdList = Arrays.stream(itemIds.split(Constant.COMMA)).map(Long::new).collect(Collectors.toList());
        List<DwsIndex> dwsIndices = gcdpDwsIndexMapper.selectByItemIds(itemIdList);
        if (CollUtil.isEmpty(dwsIndices)){
            return Lists.newArrayList();
        }

        return dwsIndices.stream()
                .filter(x -> x.getJmhlIndexValue() != null &&
                        x.getJmhlIndexValue().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
    }

    private List<MakeUpIndexData> getIndexData(List<MakeUpIndexData> indexDataList, List<DwsIndex> dwsIndices) {
        List<MakeUpIndexData> data = new ArrayList<>();
        Map<String, List<DwsIndex>> calculateValueMap = dwsIndices.stream()
                .filter(x -> x.getJmhlIndexValue() != null &&
                        x.getJmhlIndexValue().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.groupingBy(DwsIndex::getJmhlIndexWithCalcMergeHash));
        for (Map.Entry<String, List<DwsIndex>> entry : calculateValueMap.entrySet()) {
            List<DwsIndex> dwsIndexList = entry.getValue();
            UsageJmhlMakeupIndexData usageJmhlMakeupIndexData = new UsageJmhlMakeupIndexData();
            MakeUpIndexData makeUpIndexData = this.setSharedFields(indexDataList, entry);
            if (makeUpIndexData == null) {
                continue;
            }
            BeanUtils.copyProperties(makeUpIndexData, usageJmhlMakeupIndexData);
            setIndexDataField(usageJmhlMakeupIndexData, dwsIndexList);
            data.add(usageJmhlMakeupIndexData);
        }
        return data;
    }

    private void setIndexDataField(UsageJmhlMakeupIndexData usageJmhlMakeupIndexData, List<DwsIndex> dwsIndices) {
        DwsIndex dwsIndex = dwsIndices.get(0);
        String name = dwsIndex.getName();
        //计算口径去最新归档的
        BigDecimal jmhlCalculateValue = dwsIndices.stream().filter(item -> MathUtil.notNullAndNotZero(item.getJmhlIndexValue()))
                .sorted(Comparator.comparing(DwsIndex::getArchiveDate).reversed())
                .map(DwsIndex::getJmCalculateValue).findFirst().orElse(null);
        BigDecimal quantity = dwsIndices.stream().filter(x -> x.getJmhlIndexValue() != null && x.getJmCalculateValue() != null)
                .map(x -> MathUtil.isGreaterZero(x.getQuantity()) ? x.getQuantity() : x.getJmhlIndexValue().multiply(x.getJmCalculateValue())).reduce(BigDecimal.ZERO, BigDecimal::add);
        usageJmhlMakeupIndexData.setName(name);
        usageJmhlMakeupIndexData.setUnit(dwsIndex.getJmhlIndexUnit());
        if (jmhlCalculateValue != null && BigDecimal.ZERO.compareTo(jmhlCalculateValue) != 0) {
            usageJmhlMakeupIndexData.setJmhlIndexValue(quantity.divide(jmhlCalculateValue, 6, RoundingMode.HALF_DOWN).toString());
        } else {
            usageJmhlMakeupIndexData.setJmhlIndexValue(Constant.LINE);
        }
        usageJmhlMakeupIndexData.setQuantity(quantity.toString());
        usageJmhlMakeupIndexData.setBuildArea(jmhlCalculateValue == null ? Constant.LINE : jmhlCalculateValue.toString());
    }

}
