package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdp.common.domain.consts.ValueConst;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.SumTypeEnum;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.glodon.gcdp.common.domain.enums.DwsNoteTypeEnums.*;
import static com.glodon.gcdp.common.domain.enums.ProductSource.*;
import static com.glodon.gcdpindexsearch.common.constant.ProjectConst.STOP_SIGN;

@SuppressWarnings("DuplicatedCode")
public class CommonHandler {

    /**
     * 根据汇算类型分组合并
     */
    public static Map<String, List<DwsIndexProjectNote>> noteGroupBySumCondition(Integer sumCondition, List<DwsIndexProjectNote> noteList) {
        Map<String, List<DwsIndexProjectNote>> groupNote = new LinkedHashMap<>();

        if (CollUtil.isEmpty(noteList)) {
            return groupNote;
        }

        //如果造价阶段为空，则单独一组，平铺展开
        if (SumTypeEnum.PROJECT.getCode().equals(sumCondition)) {
            // 按项目 【项目+阶段】 按照项目创建时间排序
            groupNote = noteList.stream()
                    .collect(Collectors.groupingBy(CommonHandler::generateSameXmKey, LinkedHashMap::new, Collectors.toList()));
        } else if (SumTypeEnum.CATEGORY.getCode().equals(sumCondition)) {
            // 业态  【项目+阶段+业态】 按照项目创建时间排序，项目下按工程分类的编码
            groupNote = noteList.stream()
                    .filter(x -> StringUtils.isNotEmpty(x.getProjectCategoryName()))
                    .sorted(Comparator.comparing(DwsIndexProjectNote::getProjectCategoryCode, Comparator.nullsFirst(Comparator.naturalOrder())))
                    .collect(Collectors.groupingBy(CommonHandler::generateSameYtKey, LinkedHashMap::new, Collectors.toList()));
        } else if (SumTypeEnum.DT.getCode().equals(sumCondition)) {
            // 真实单体 【项目+阶段+楼栋+建筑面积】  按照项目创建时间排序，项目下楼栋名称排序，【楼栋名相同，面积相同，才算同一个楼】
            // 虚拟单体 【项目+阶段+楼栋+专业】  按照项目创建时间排序，项目下楼栋名称排序，【虚拟楼栋名，专业相同，才算同一个楼】
            groupNote = noteList.stream()
                    .sorted(Comparator.comparing(DwsIndexProjectNote::getLdNameIdentify, Comparator.nullsFirst(Comparator.naturalOrder())))
                    .collect(Collectors.groupingBy(
                            note -> {
                                if (LD.getIndex().equals(note.getType())) {
                                    return generateSameLdKey(note);
                                } else if (XN_LD.getIndex().equals(note.getType())) {
                                    return generateSameXnldKey(note);
                                } else if (VIRTUAL.getIndex().equals(note.getType())) {
                                    return generateSameXnjdKey(note);
                                }
                                return "";
                            },
                            LinkedHashMap::new,
                            Collectors.toList()
                    ));
        }

        return groupNote;
    }

    /**
     * 计算建筑面积
     * @param noteList 节点List
     * @return 返回计算后的建筑面积
     */
    public static BigDecimal calcBuildArea(List<DwsIndexProjectNote> noteList) {
        // 计算建筑面积的时候，只算虚拟楼栋和真实楼栋的。其他类型都不要。
        Map<Integer, List<DwsIndexProjectNote>> typeAndNoteMap = noteList.stream()
                .filter(x -> LD.getIndex().equals(x.getType()) || XN_LD.getIndex().equals(x.getType()) || VIRTUAL.getIndex().equals(x.getType()))
                .collect(Collectors.groupingBy(DwsIndexProjectNote::getType));

        BigDecimal buildArea = BigDecimal.ZERO;
        Set<String> processedKeys = new HashSet<>();

        // 取标签为“真实楼栋”的建筑面积汇总值；如只有“虚拟楼栋”，先做专业标签的聚合，再取归档时间最晚、结构第一个的专业对应的建筑面积值
        if (typeAndNoteMap.containsKey(LD.getIndex())) {
            buildArea = typeAndNoteMap.get(LD.getIndex()).stream()
                    .sorted(Comparator.comparing(DwsIndexProjectNote::getArchiveDate).reversed())
                    .filter(note -> processedKeys.add(CommonHandler.generateSameLdKey(note)))
                    .map(note -> Optional.ofNullable(note.getBuildArea()).orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else if (typeAndNoteMap.containsKey(XN_LD.getIndex())) {
            String tradeName = CollUtil.getFirst(typeAndNoteMap.get(XN_LD.getIndex())).getTradeName();
            buildArea = typeAndNoteMap.get(XN_LD.getIndex()).stream()
                    .sorted(Comparator.comparing(DwsIndexProjectNote::getArchiveDate).reversed())
                    .filter(note -> Objects.equals(tradeName, note.getTradeName()) && processedKeys.add(CommonHandler.generateSameXnldKey(note)))
                    .map(note -> Optional.ofNullable(note.getBuildArea()).orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 虚拟节点的建筑面积取按归档时间倒排 取非空最新
        } else if (typeAndNoteMap.containsKey(VIRTUAL.getIndex())) {
            buildArea = typeAndNoteMap.get(VIRTUAL.getIndex()).stream()
                    .sorted(Comparator.comparing(DwsIndexProjectNote::getArchiveDate).reversed())
                    .filter(note -> processedKeys.add(CommonHandler.generateSameLdKey(note)))
                    .map(note -> Optional.ofNullable(note.getBuildArea()).orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return buildArea;
    }

    /**
     * 计算专业建筑面积: 先按专业标签聚合,然后再累加
     * 注：仅限项目下虚拟楼栋合并或业态下虚拟楼栋合并的场景
     * @param noteList 节点List
     * @return 返回计算后的建筑面积
     */
    public static BigDecimal calcTradeBuildArea(List<DwsIndexProjectNote> noteList) {
        return calcBuildArea(noteList);
    }

    /**
     * 计算不同产品来源的项目节点的工程总造价
     * @param simpleNoteSumFlag
     * @param noteList
     * @return
     */
    public static BigDecimal[] calculateTotal(int simpleNoteSumFlag, List<DwsIndexProjectNote> noteList) {
        // 初始化时,①项目规模取户口簿的项目规模；②市场化计价的总造价直接取合约总造价；③计算是需要加非建安
        BigDecimal amountTotal = BigDecimal.valueOf(0);
        BigDecimal amountTotalIncludeTax = BigDecimal.valueOf(0);
        BigDecimal fullCostTotal = BigDecimal.valueOf(0);
        BigDecimal fullCostTotalIncludeTax = BigDecimal.valueOf(0);
        BigDecimal nonFullCostTotal = BigDecimal.valueOf(0);
        BigDecimal nonFullCostTotalIncludeTax = BigDecimal.valueOf(0);

        BigDecimal totalAmount = BigDecimal.valueOf(0);
        BigDecimal totalAmountIncludeTax = BigDecimal.valueOf(0);

        if (simpleNoteSumFlag == ValueConst.FALSE) {
            Set<Long> contractProjectIdSet = new HashSet<>();

            for (DwsIndexProjectNote note : noteList) {
                if (QYQD.getIndex().equals(note.getProductSource())
                        || YSTZ.getIndex().equals(note.getProductSource())
                        || JSTZ.getIndex().equals(note.getProductSource())) {
                    if (contractProjectIdSet.add(note.getContractProjectId())) {
                        amountTotal = MathUtil.add(amountTotal, note.getContractProjectTotal());
                        amountTotalIncludeTax = MathUtil.add(amountTotalIncludeTax, note.getContractProjectTotalIncludeTax());

                        fullCostTotal = MathUtil.add(fullCostTotal, note.getContractProjectTotal());
                        fullCostTotalIncludeTax = MathUtil.add(fullCostTotalIncludeTax, note.getContractProjectTotalIncludeTax());

                        // 不含税
                        totalAmount = MathUtil.add(totalAmount, note.getContractProjectTotal());
                        // 计算含税
                        totalAmountIncludeTax = MathUtil.add(totalAmountIncludeTax, note.getContractProjectTotalIncludeTax());
                    }
                } else {
                    amountTotal = MathUtil.add(amountTotal, note.getTotal());
                    amountTotalIncludeTax = MathUtil.add(amountTotalIncludeTax, note.getTotalIncludeTax());

                    fullCostTotal = MathUtil.add(fullCostTotal, note.getFullCostTotal());
                    fullCostTotalIncludeTax = MathUtil.add(fullCostTotalIncludeTax, note.getFullCostTotalIncludeTax());
                    nonFullCostTotal = MathUtil.add(nonFullCostTotal, note.getNonFullCostTotal());
                    nonFullCostTotalIncludeTax = MathUtil.add(nonFullCostTotalIncludeTax, note.getNonFullCostTotalIncludeTax());

                    // 不含税
                    totalAmount = MathUtil.notNullAndNotZero(note.getFullCostTotal()) ?
                            MathUtil.add(totalAmount, note.getFullCostTotal()) : MathUtil.add(totalAmount, note.getNonFullCostTotal());
                    // 计算含税
                    totalAmountIncludeTax = MathUtil.notNullAndNotZero(note.getFullCostTotalIncludeTax()) ?
                            MathUtil.add(totalAmountIncludeTax, note.getFullCostTotalIncludeTax()) : MathUtil.add(totalAmountIncludeTax, note.getNonFullCostTotalIncludeTax());
                }
            }
        } else {
            for (DwsIndexProjectNote item : noteList) {
                // 金额累加
                amountTotal = MathUtil.add(amountTotal, item.getTotal());
                amountTotalIncludeTax = MathUtil.add(amountTotalIncludeTax, item.getTotalIncludeTax());

                fullCostTotal = MathUtil.add(fullCostTotal, item.getFullCostTotal());
                fullCostTotalIncludeTax = MathUtil.add(fullCostTotalIncludeTax, item.getFullCostTotalIncludeTax());
                nonFullCostTotal = MathUtil.add(nonFullCostTotal, item.getNonFullCostTotal());
                nonFullCostTotalIncludeTax = MathUtil.add(nonFullCostTotalIncludeTax, item.getNonFullCostTotalIncludeTax());

                // 不含税
                totalAmount = MathUtil.notNullAndNotZero(item.getFullCostTotal()) ?
                        MathUtil.add(totalAmount, item.getFullCostTotal()) : MathUtil.add(totalAmount, item.getNonFullCostTotal());
                // 计算含税
                totalAmountIncludeTax = MathUtil.notNullAndNotZero(item.getFullCostTotalIncludeTax()) ?
                        MathUtil.add(totalAmountIncludeTax, item.getFullCostTotalIncludeTax()) : MathUtil.add(totalAmountIncludeTax, item.getNonFullCostTotalIncludeTax());
            }
        }
        return new BigDecimal[]{amountTotal, amountTotalIncludeTax, fullCostTotal, fullCostTotalIncludeTax,
                nonFullCostTotal, nonFullCostTotalIncludeTax, totalAmount, totalAmountIncludeTax};
    }

    private static String generateSameXmKey(DwsIndexProjectNote note) {
        StringJoiner stringJoiner = new StringJoiner(CharConst.DOUBLE_AT);
        stringJoiner.add(note.getEnterpriseId())
                .add(note.getProjectCode())
                .add(note.getPhase());

        return stringJoiner.toString();
    }

    private static String generateSameYtKey(DwsIndexProjectNote note) {
        StringJoiner stringJoiner = new StringJoiner(CharConst.DOUBLE_AT);
        stringJoiner.add(note.getEnterpriseId())
                .add(note.getProjectCode())
                .add(note.getPhase())
                .add(note.getProjectCategoryName());

        return stringJoiner.toString();
    }

    public static String generateSameLdKey(DwsIndexProjectNote note) {
        StringJoiner stringJoiner = new StringJoiner(CharConst.DOUBLE_AT);
        stringJoiner.add(note.getEnterpriseId())
                .add(note.getProjectCode())
                .add(note.getPhase())
                .add(note.getLdNameIdentify())
                .add(String.valueOf(note.getBuildArea()));

        return stringJoiner.toString();
    }

    public static String generateSameXnldKey(DwsIndexProjectNote note) {
        StringJoiner stringJoiner = new StringJoiner(CharConst.DOUBLE_AT);
        stringJoiner.add(note.getEnterpriseId())
                .add(note.getProjectCode())
                .add(note.getPhase())
                .add(note.getLdNameIdentify())
                .add(note.getTradeName());

        return stringJoiner.toString();
    }

    public static String generateSameXnjdKey(DwsIndexProjectNote note) {
        StringJoiner stringJoiner = new StringJoiner(CharConst.DOUBLE_AT);
        stringJoiner.add(note.getEnterpriseId())
                .add(note.getProjectCode())
                .add(note.getPhase());

        return stringJoiner.toString();
    }

    /**
     * 真实楼栋下的专业唯一标识
     * @param note
     * @return
     */
    public static String generateSameLdTradeKey(DwsIndexProjectNote note) {
        StringJoiner stringJoiner = new StringJoiner(CharConst.DOUBLE_AT);
        stringJoiner.add(note.getEnterpriseId())
                .add(note.getProjectCode())
                .add(note.getPhase())
                .add(note.getLdNameIdentify())
                .add(note.getTradeName())
                .add(String.valueOf(note.getBuildArea()));

        return stringJoiner.toString();
    }

    /**
     * 虚拟楼栋专业
     * 注：不可直接使用,必须是按业态或按项目分组筛选出后的虚拟楼栋(去除了业态标识)
     * @param note
     * @return
     */
    public static String generateSameXnldTrade(DwsIndexProjectNote note) {
        StringJoiner stringJoiner = new StringJoiner(CharConst.DOUBLE_AT);
        stringJoiner.add(note.getEnterpriseId())
                .add(note.getProjectCode())
                .add(note.getPhase())
                .add(note.getTradeName());

        return stringJoiner.toString();
    }

    /**
     * 项目下相同专业
     * @param note
     * @return
     */
    public static String generateSameXmTradeKey(DwsIndexProjectNote note) {
        StringJoiner stringJoiner = new StringJoiner(CharConst.DOUBLE_AT);
        stringJoiner.add(note.getEnterpriseId())
                .add(note.getProjectCode())
                .add(note.getPhase())
                .add(note.getTradeName());

        return stringJoiner.toString();
    }

    public static String getLastCategoryName(String categoryNamePath){
        if (StringUtils.isEmpty(categoryNamePath)){
            return null;
        }
        int index = categoryNamePath.lastIndexOf(CharConst.DOUBLE_AT);
        return index == -1 ? categoryNamePath : categoryNamePath.substring(index + CharConst.DOUBLE_AT.length());
    }

    public static void sameLdCategorySync(List<DwsIndexProjectNote> validNotes) {
        // 同步相同楼栋的工程分类，取非空最新
        Map<String, DwsIndexProjectNote> ldCategoryNonNullAndNewestMap = validNotes.stream()
                .filter(x -> Objects.equals(x.getType(), LD.getIndex()) && StringUtils.isNotEmpty(x.getProjectCategoryName()))
                .collect(Collectors.toMap(CommonHandler::generateSameLdKey, Function.identity(), (v1, v2) -> v1));

        Map<String, DwsIndexProjectNote> xnldCategoryNonNullAndNewestMap = validNotes.stream()
                .filter(x -> Objects.equals(x.getType(), XN_LD.getIndex()) && StringUtils.isNotEmpty(x.getProjectCategoryName()))
                .collect(Collectors.toMap(CommonHandler::generateSameXnldKey, Function.identity(), (v1, v2) -> v1));

        for (DwsIndexProjectNote note : validNotes) {
            if (Objects.equals(note.getType(), LD.getIndex()) && ldCategoryNonNullAndNewestMap.containsKey(CommonHandler.generateSameLdKey(note))){
                DwsIndexProjectNote dwsIndexProjectNote = ldCategoryNonNullAndNewestMap.get(CommonHandler.generateSameLdKey(note));
                note.setProjectCategoryName(dwsIndexProjectNote.getProjectCategoryName());
                note.setProjectCategoryCode(dwsIndexProjectNote.getProjectCategoryCode());
            }else if(Objects.equals(note.getType(), XN_LD.getIndex()) && xnldCategoryNonNullAndNewestMap.containsKey(CommonHandler.generateSameXnldKey(note))){
                DwsIndexProjectNote dwsIndexProjectNote = xnldCategoryNonNullAndNewestMap.get(CommonHandler.generateSameXnldKey(note));
                note.setProjectCategoryName(dwsIndexProjectNote.getProjectCategoryName());
                note.setProjectCategoryCode(dwsIndexProjectNote.getProjectCategoryCode());
            }
        }
    }

    public static Map<String, String> getLdNameIdentifyAndNameMap(List<DwsIndexProjectNote> projectNotes) {
        Map<String, String> ldNameIdentifyAndNameMap = Maps.newHashMap();
        for (DwsIndexProjectNote projectNote : projectNotes) {
            if (StrUtil.isEmpty(projectNote.getName())){
                continue;
            }
            String key = CommonHandler.generateSameLdKey(projectNote);
            if (ldNameIdentifyAndNameMap.containsKey(key)){
                String value = ldNameIdentifyAndNameMap.get(key);
                if (Arrays.stream(value.split(STOP_SIGN)).anyMatch(x -> Objects.equals(x, projectNote.getName()))) {
                    continue;
                }
                StringJoiner stringJoiner = new StringJoiner(STOP_SIGN);
                stringJoiner.add(value).add(projectNote.getName());
                ldNameIdentifyAndNameMap.put(key, stringJoiner.toString());
            }else {
                ldNameIdentifyAndNameMap.put(key, projectNote.getName());
            }
        }
        return ldNameIdentifyAndNameMap;
    }
}
