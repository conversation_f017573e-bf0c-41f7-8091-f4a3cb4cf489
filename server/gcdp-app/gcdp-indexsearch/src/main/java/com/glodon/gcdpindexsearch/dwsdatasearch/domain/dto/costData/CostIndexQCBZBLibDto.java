package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 全成本指标库返回数据类
 * <AUTHOR>
 * @date 2023-5-16
 */
@Data
public class CostIndexQCBZBLibDto extends CostIndexBaseDto{
    @ApiModelProperty(value = "合并为一条数据时的id列表-成本指标", required = true)
    private String costIds;
    @ApiModelProperty(value = "合并为一条数据时的id列表-含量指标", required = true)
    private String usageIds;
    @ApiModelProperty(value = "科目单位", required = true,example = "m2")
    private String unit;
    @ApiModelProperty(value = "建面单方-区间范围（不含税）", required = false,example = "1.3~3.4")
    private String jmMinAndMax;
    @ApiModelProperty(value = "建面单方-区间范围（含税）", required = false,example = "1.3~3.4")
    private String jmMinAndMaxIncludeTax;
    @ApiModelProperty(value = "建面单方-均值（不含税）", required = false,example = "1.3")
    private String jmAvg;
    @ApiModelProperty(value = "建面单方-均值（含税）", required = false,example = "1.3")
    private String jmAvgIncludeTax;
    @ApiModelProperty(value = "单方含量-区间范围", required = false,example = "1.3~3.4")
    private String dfhlMinAndMax;
    @ApiModelProperty(value = "单方含量-均值", required = false,example = "1.3")
    private String dfhlAvg;
    @ApiModelProperty(value = "单方造价-区间范围（不含税）", required = false,example = "1.3~3.4")
    private String dfMinAndMax;
    @ApiModelProperty(value = "单方造价-区间范围（含税）", required = false,example = "1.3~3.4")
    private String dfMinAndMaxIncludeTax;
    @ApiModelProperty(value = "单方造价-均值（不含税）", required = false,example = "1.3")
    private String dfAvg;
    @ApiModelProperty(value = "单方造价-均值（含税）", required = false,example = "1.3")
    private String dfAvgIncludeTax;
    @ApiModelProperty(value = "综合单价-区间范围（不含税）", required = false,example = "1.3~3.4")
    private String zhdjMinAndMax;
    @ApiModelProperty(value = "综合单价-区间范围（含税）", required = false,example = "1.3~3.4")
    private String zhdjMinAndMaxIncludeTax;
    @ApiModelProperty(value = "综合单价-均值（不含税）", required = false,example = "1.3")
    private String zhdjAvg;
    @ApiModelProperty(value = "综合单价-均值（含税）", required = false,example = "1.3")
    private String zhdjAvgIncludeTax;
    /**
     * 科目hash
     */
    private String itemHash;

    private String namePath;
}
