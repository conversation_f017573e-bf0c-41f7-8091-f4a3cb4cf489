package com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.usage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.glodon.gcdp.common.utils.MathUtil;
import com.glodon.gcdp.dimservice.domain.dao.enums.IndexCategoryDictEnum;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.GcdpDwsItemIndexUsage;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.AbstractProcessor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @packageName: com.glodon.gcdpindexsearch.dwsdatasearch.domain.processor.usage
 * @className: IndexUsageUnLdProcessor
 * @author: yanyuh<PERSON> <EMAIL>
 * @date: 2023/4/18 15:42
 * @description:
 */
@SuppressWarnings("all")
@Slf4j
public class IndexUsageUnLdProcessor extends AbstractProcessor<GcdpDwsItemIndexUsage> {
    @Override
    public List<GcdpDwsItemIndexUsage> process(List<GcdpDwsItemIndexUsage> dataList) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<GcdpDwsItemIndexUsage> returnResult = Collections.synchronizedList(new LinkedList<>());
        Map<String, List<GcdpDwsItemIndexUsage>> hashUsageMap = dataList.parallelStream().collect(Collectors.groupingBy(GcdpDwsItemIndexUsage::getSubjectUnLdMergeHash));
        hashUsageMap.entrySet().parallelStream().forEach(node -> {
            List<GcdpDwsItemIndexUsage> usages = node.getValue();
            GcdpDwsItemIndexUsage source = CollUtil.getFirst(usages);
            GcdpDwsItemIndexUsage dest = new GcdpDwsItemIndexUsage();
            BeanUtil.copyProperties(source, dest);
            combineReturnField(usages, dest);
            returnResult.add(dest);
        });
        stopWatch.stop();
        log.info("含量非楼栋数据组装耗时为:[{}], 非楼栋数据量为:[{}] ,map的数量为:[{}]", stopWatch.getLastTaskInfo().getTimeSeconds(), dataList.size(), hashUsageMap.size());
        return processNext(returnResult);
    }


    private void combineReturnField(List<GcdpDwsItemIndexUsage> usages, GcdpDwsItemIndexUsage dest) {
        int zylCount = 0;
        int jmhlCount = 0;
        String zylUnit = StrUtil.EMPTY;
        String jmhlUnit = StrUtil.EMPTY;
        BigDecimal zylSum = BigDecimal.ZERO;
        BigDecimal jmhlSum = BigDecimal.ZERO;
        BigDecimal zylAmount = BigDecimal.ZERO;
        BigDecimal jmhlAmount = BigDecimal.ZERO;
        BigDecimal zylSumCalculateValue = BigDecimal.ZERO;
        BigDecimal jmhlSumCalculateValue = BigDecimal.ZERO;

        List<String> zylSampleIds = new ArrayList<>(usages.size());
        List<String> jmhlSamples = new ArrayList<>(usages.size());
        List<String> ids = new ArrayList<>(usages.size());

        for (GcdpDwsItemIndexUsage usage : usages) {
            ids.add(usage.getIds());
            jmhlSamples.add(usage.getJmhlSampleIds());
            zylSampleIds.add(usage.getZylSampleIds());
            if (StrUtil.isEmpty(zylUnit) && StrUtil.isNotEmpty(usage.getZylUnit())) {
                zylUnit = usage.getZylUnit();
            }
            if (StrUtil.isEmpty(jmhlUnit) && StrUtil.isNotEmpty(usage.getJmhlUnit())) {
                jmhlUnit = usage.getJmhlUnit();
            }

            if (usage.getZylIndexValue() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, usage.getZylIndexValue())) {
                zylCount++;
                zylSum = NumberUtil.add(zylSum, usage.getZylIndexValue());
            }
            if (usage.getZylQuantity() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, usage.getZylQuantity())) {
                zylAmount = NumberUtil.add(zylAmount, usage.getZylQuantity());
            }
            if (usage.getZylCalculateValue() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, usage.getZylCalculateValue())) {
                zylSumCalculateValue = NumberUtil.add(zylSumCalculateValue, usage.getZylCalculateValue());
            }

            if (usage.getJmhlIndexValue() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, usage.getJmhlIndexValue())) {
                jmhlCount++;
                jmhlSum = NumberUtil.add(jmhlSum, usage.getJmhlIndexValue());
            }
            if (usage.getJmhlQuantity() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, usage.getJmhlQuantity())) {
                jmhlAmount = NumberUtil.add(jmhlAmount, usage.getJmhlQuantity());
            }
            if (usage.getJmhlCalculateValue() != null && !MathUtil.isEqualFive(BigDecimal.ZERO, usage.getJmhlCalculateValue())) {
                jmhlSumCalculateValue = NumberUtil.add(jmhlSumCalculateValue, usage.getJmhlCalculateValue());
            }
        }

        List<BigDecimal> jmhlIndexIncludeTaxs = usages.stream().map(GcdpDwsItemIndexUsage::getJmhlIndexValue).filter(Objects::nonNull).collect(Collectors.toList());
        List<BigDecimal> zylIndexIncludeTaxs = usages.stream().map(GcdpDwsItemIndexUsage::getZylIndexValue).filter(Objects::nonNull).collect(Collectors.toList());
        String jmhlMaxAndMin = MathUtil.combineMinAndMax(MathUtil.min(jmhlIndexIncludeTaxs), MathUtil.max(jmhlIndexIncludeTaxs));
        String zylMaxAndMin = MathUtil.combineMinAndMax(MathUtil.min(zylIndexIncludeTaxs), MathUtil.max(zylIndexIncludeTaxs));

        dest.setIds(StrUtil.join(StrUtil.COMMA, ids));
        dest.setJmhlSampleIds(StrUtil.join(StrUtil.COMMA, jmhlSamples));
        dest.setZylSampleIds(StrUtil.join(StrUtil.COMMA, zylSampleIds));
        dest.setZylUnit(zylUnit);
        dest.setJmhlUnit(jmhlUnit);
        dest.setZylMaxAndMin(zylMaxAndMin);
        dest.setJmhlMaxAndMin(jmhlMaxAndMin);

        dest.setZylAvg(MathUtil.divStrZeroDashed(zylSum, new BigDecimal(zylCount)));
        dest.setZylWeightedAvg(MathUtil.divStrZeroDashed(zylAmount, zylSumCalculateValue));
        dest.setZylSampleCount(zylCount);

        dest.setJmhlAvg(MathUtil.divStrZeroDashed(jmhlSum, new BigDecimal(jmhlCount)));
        dest.setJmhlWeightedAvg(MathUtil.divStrZeroDashed(jmhlAmount, jmhlSumCalculateValue));
        dest.setJmhlSampleCount(jmhlCount);

        dest.setZylDictCode(IndexCategoryDictEnum.ZYLZB.getCode());
        dest.setJmhlDictCode(IndexCategoryDictEnum.JMHLZB.getCode());

        String itemIds = orderedIds(dest.getIds());
        dest.setItemIds(itemIds);
        dest.setIds(itemIds);
    }
}
