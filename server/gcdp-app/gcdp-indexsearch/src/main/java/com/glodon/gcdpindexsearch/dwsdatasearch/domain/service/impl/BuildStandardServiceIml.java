package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdp.common.domain.consts.CharConst;
import com.glodon.gcdp.common.domain.enums.DwsNoteTypeEnums;
import com.glodon.gcdp.common.domain.model.BuildStandardColsInfo;
import com.glodon.gcdp.common.domain.model.StandardBuilderNewData;
import com.glodon.gcdp.common.domain.model.StandardBuilderNewDto;
import com.glodon.gcdp.common.utils.HashType;
import com.glodon.gcdp.dwsindexservice.domain.dao.entity.DwsBuildStandardIndex;
import com.glodon.gcdp.dwsindexservice.domain.dao.mapper.DwsBuildStandardIndexMapper;
import com.glodon.gcdp.dwsindexservice.domain.dao.mapper.DwsIndexBuildStandardsRelationshipMapper;
import com.glodon.gcdp.dwsindexservice.infrastructure.util.IndexUtils;
import com.glodon.gcdpindexsearch.common.util.EmptyUtil;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexProjectNote;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwsIndexProjectNoteMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData.StandardDescription;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.enums.SumTypeEnum;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.BuildStandardService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.CommonHandler;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.BuildStandardVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class BuildStandardServiceIml implements BuildStandardService {
    @Autowired
    private DwsIndexProjectNoteMapper dwsIndexProjectNoteMapper;
    @Autowired
    private DwsBuildStandardIndexMapper dwsBuildStandardIndexMapper;
    @Autowired
    private DwsIndexBuildStandardsRelationshipMapper dwsIndexBuildStandardsRelationshipMapper;

    @Override
    public StandardBuilderNewDto getBuildStandard(BuildStandardVO buildStandardVO) {
        List<DwsBuildStandardIndex> dwsBuildStandardIndexes = dwsBuildStandardIndexMapper.selectByIndexProjectNoteIds(buildStandardVO.getProjectNoteIds());
        // 按照单体id分组
        Map<Long, List<DwsBuildStandardIndex>> dwsBuildStandardIndexMap = dwsBuildStandardIndexes.stream().collect((Collectors.groupingBy(DwsBuildStandardIndex::getIndexProjectNoteId)));

        List<DwsIndexProjectNote> projectNotes = dwsIndexProjectNoteMapper.selectAllNotesByIds(buildStandardVO.getProjectNoteIds());
        projectNotes = projectNotes.stream().filter(note -> !DwsNoteTypeEnums.NONE.getIndex().equals(note.getType()) && dwsBuildStandardIndexMap.containsKey(note.getId())).collect(Collectors.toList());
        if (EmptyUtil.isEmpty(projectNotes)) {
            return new StandardBuilderNewDto();
        }

        Map<Long, String> id2BaseNoteMergeKeyMap = this.getId2BaseNoteMergeKeyMap(projectNotes);

        dwsBuildStandardIndexMap.forEach((projectNoteId, dwsBuildStandards) -> {
            // 构建树并计算标准名称hash
            List<DwsBuildStandardIndex> buildStandardTree = IndexUtils.buildIndexTree(dwsBuildStandards);
            Map<Long, DwsBuildStandardIndex> allDataIdMap = dwsBuildStandards.stream().collect(Collectors.toMap(DwsBuildStandardIndex::getId, Function.identity()));
            IndexUtils.foreachTree(buildStandardTree, (buildStandard, hasChild) -> {
                String levelAndNameHash = IndexUtils.buildSonWithFatherHash(buildStandard, allDataIdMap, HashType.ITEM_HASH, () -> getItemHashKey(buildStandard));
                buildStandard.setLevelAndNameHash(levelAndNameHash);
                DwsBuildStandardIndex parent = allDataIdMap.get(buildStandard.getPid());
                buildStandard.setLevelAndNameParentHash(parent == null ? String.valueOf(buildStandard.getPid()) : parent.getLevelAndNameHash());
                buildStandard.setBaseNoteMergeKey(id2BaseNoteMergeKeyMap.get(projectNoteId));
            });
        });

        // 同单体合并及不同单体合并
        return mergeDwsBuildStandards(dwsBuildStandardIndexes, projectNotes);
    }

    private StandardBuilderNewDto mergeDwsBuildStandards(List<DwsBuildStandardIndex> dwsBuildStandardIndexes, List<DwsIndexProjectNote> projectNotes) {
        Map<Long, DwsIndexProjectNote> idNotesMap = projectNotes.stream().collect(Collectors.toMap(DwsIndexProjectNote::getId, Function.identity()));
        Map<String, List<DwsBuildStandardIndex>> nameHashMap = dwsBuildStandardIndexes.stream().collect(Collectors.groupingBy(DwsBuildStandardIndex::getLevelAndNameHash));

        StandardBuilderNewDto result = new StandardBuilderNewDto();
        result.setColsInfo(assembleColsInfo(projectNotes));
        result.setData(assembleData(nameHashMap, idNotesMap));

        return result;
    }

    private List<BuildStandardColsInfo> assembleColsInfo(List<DwsIndexProjectNote> projectNotes) {
        Map<String, List<DwsIndexProjectNote>> groupNote = CommonHandler.noteGroupBySumCondition(SumTypeEnum.DT.getCode(), projectNotes);

        List<BuildStandardColsInfo> colsInfoList = new ArrayList<>(groupNote.size());

        groupNote.forEach((key, value) -> {
            BuildStandardColsInfo colsInfo = new BuildStandardColsInfo();
            colsInfo.setIdentifyName(value.get(0).getLdNameIdentify());
            colsInfo.setIdentifyId(key);
            colsInfo.setMergeNoteIds(value.stream().map(DwsIndexProjectNote::getId).collect(Collectors.toList()));
            colsInfoList.add(colsInfo);

        });
        return colsInfoList;
    }

    private List<StandardBuilderNewData> assembleData(Map<String, List<DwsBuildStandardIndex>> nameHashMap, Map<Long, DwsIndexProjectNote> idNotesMap) {
        List<StandardBuilderNewData> standardList = new ArrayList<>(nameHashMap.size());
        nameHashMap.forEach((key, value) -> {
            StandardBuilderNewData standardBuilderNewData = new StandardBuilderNewData();
            standardBuilderNewData.setId(key);
            standardBuilderNewData.setPid(value.get(0).getLevelAndNameParentHash());
            standardBuilderNewData.setName(value.get(0).getName());
            standardBuilderNewData.setCode(value.get(0).getCode());
            standardBuilderNewData.setStandardDesc(mergeStandardDesc(value, idNotesMap));
            standardList.add(standardBuilderNewData);
        });
        return standardList.stream()
                .sorted((o1, o2) -> StrUtil.compareVersion(o1.getCode(), o2.getCode()))
                .collect(Collectors.toList());
    }

    /**
     * 不同单项的相同建造标准(建造标准名称一致)合并
     */
    private List<StandardBuilderNewData.StandardDesc> mergeStandardDesc(List<DwsBuildStandardIndex> dwsBuildStandardIndexes, Map<Long, DwsIndexProjectNote> idNotesMap) {
        Map<String, List<DwsBuildStandardIndex>> sameNotesMergeMap = dwsBuildStandardIndexes.stream().collect(Collectors.groupingBy(DwsBuildStandardIndex::getBaseNoteMergeKey));

        List<StandardDescription> standardDescriptions = getDescriptions(idNotesMap, sameNotesMergeMap);

        Map<String, List<StandardDescription>> descMap = standardDescriptions.stream()
                .peek(a -> {
                    if (a.getDescription() == null) {
                        a.setDescription("");
                    }
                })
                .collect(Collectors.groupingBy(StandardDescription::getDescription));

        List<StandardBuilderNewData.StandardDesc> standardDescList = new ArrayList<>();

        descMap.forEach((key, value) -> {
            StandardBuilderNewData.StandardDesc standardDesc = new StandardBuilderNewData.StandardDesc();
            standardDesc.setDesc(key);
            standardDesc.setStandardValues(assembleStandardValues(value));
            standardDesc.setOrd(value.get(0).getOrd());
            standardDescList.add(standardDesc);
        });
        return standardDescList.stream().sorted(Comparator.comparingInt(StandardBuilderNewData.StandardDesc::getOrd)).collect(Collectors.toList());
    }

    private List<StandardDescription> getDescriptions(Map<Long, DwsIndexProjectNote> idNotesMap, Map<String, List<DwsBuildStandardIndex>> sameNotesMergeMap) {
        List<StandardDescription> standardDescriptions = new ArrayList<>();

        sameNotesMergeMap.forEach((key, value) -> {
            // 同单项建造标准说明及建造做法合并，按照归档时间取非空最新
            // value的size大于1，说明同单项下有多个该条标准名称下的标准说明及做法需要合并
            List<StandardDescription> needMergeDesc = new ArrayList<>();
            value.forEach(dwsBuildStandardIndex -> {
                String standardDesc = dwsBuildStandardIndex.getStandardDescription();
                List<StandardDescription> standardDescription = JSONObject.parseArray(standardDesc, StandardDescription.class);
                int i = 0;
                for (StandardDescription desc : standardDescription) {
                    Long noteId = dwsBuildStandardIndex.getIndexProjectNoteId();
                    desc.setArchiveDate(idNotesMap.get(noteId).getArchiveDate());
                    desc.setOrd(i++);
                    desc.setBaseNoteMergeKey(key);
                }
                needMergeDesc.addAll(standardDescription);
            });
            standardDescriptions.addAll(mergeDescAndValueOnSameNote(needMergeDesc));
        });
        return standardDescriptions;
    }

    private List<StandardBuilderNewData.StandardValue> assembleStandardValues(List<StandardDescription> standardDescriptions) {
        List<StandardBuilderNewData.StandardValue> standardValues = new ArrayList<>();
        for (StandardDescription standardDescription : standardDescriptions) {
            StandardBuilderNewData.StandardValue standardValue = new StandardBuilderNewData.StandardValue();
            standardValue.setIdentifyId(standardDescription.getBaseNoteMergeKey());
            standardValue.setValue(standardDescription.getStandardValue());
            standardValues.add(standardValue);
        }
        return standardValues;
    }

    private List<StandardDescription> mergeDescAndValueOnSameNote(List<StandardDescription> standardDescription) {
        if (CollectionUtils.isEmpty(standardDescription)) {
            return standardDescription;
        }
        Map<String, StandardDescription> mergedMap = standardDescription.stream().collect(Collectors.toMap(StandardDescription::getDescription, Function.identity(), (desc1, desc2) -> {
            // todo 取standardValue有值的的且归档时间archiveDate最新的
            if (StringUtils.isNotEmpty(desc1.getStandardValue()) && StringUtils.isNotEmpty(desc2.getStandardValue())) {
                return desc1.getArchiveDate().compareTo(desc2.getArchiveDate()) >= 0 ? desc1 : desc2;
            } else if (StringUtils.isNotEmpty(desc1.getStandardValue())) {
                return desc1;
            } else if (StringUtils.isNotEmpty(desc2.getStandardValue())) {
                return desc2;
            }
            return desc1;
        }));

        return mergedMap.values().stream().sorted(Comparator.comparing(StandardDescription::getOrd)).collect(Collectors.toList());
    }

    private Map<Long, String> getId2BaseNoteMergeKeyMap(List<DwsIndexProjectNote> projectNotes) {
        Map<Long, String> id2BaseNoteMergeKeyMap = new HashMap<>();
        projectNotes.forEach(note -> {
            if (DwsNoteTypeEnums.LD.getIndex().equals(note.getType())) {
                id2BaseNoteMergeKeyMap.put(note.getId(), CommonHandler.generateSameLdKey(note));
            } else if (DwsNoteTypeEnums.XN_LD.getIndex().equals(note.getType())) {
                id2BaseNoteMergeKeyMap.put(note.getId(), CommonHandler.generateSameXnldKey(note));
            } else if (DwsNoteTypeEnums.VIRTUAL.getIndex().equals(note.getType())) {
                id2BaseNoteMergeKeyMap.put(note.getId(), CommonHandler.generateSameXnjdKey(note));
            }
        });
        return id2BaseNoteMergeKeyMap;
    }

    private String getItemHashKey(DwsBuildStandardIndex dwsBuildStandardIndex) {
        //标准名称+层级hash
        List<String> keys = new ArrayList<>();
        keys.add(StrUtil.blankToDefault(dwsBuildStandardIndex.getName(), CharConst.LINE));
        return StrUtil.join(CharConst.DOUBLE_AT, keys);
    }
}
