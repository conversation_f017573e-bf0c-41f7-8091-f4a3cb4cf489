package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl;

import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdContractProject;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwsIndexMakeup;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.DwdBqitemCostMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMakeupMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwsIndexMakeupRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: 指标科目组成
 * @date 2022/11/18 15:57
 */
@Repository
public class DwsIndexMakeupRepositoryImpl implements IDwsIndexMakeupRepository {
    @Autowired
    GcdpDwsIndexMakeupMapper gcdpDwsIndexMakeupMapper;

    @Autowired
    DwdBqitemCostMapper dwdBqitemCostMapper;

    @Override
    public List<DwsIndexMakeup> selectCostMakeupByNoteIdAndSubjectId(List<Long> ids, List<Long> itemIds){
        return gcdpDwsIndexMakeupMapper.selectCostMakeupByNoteIdAndSubjectId(ids, itemIds);
    }

    @Override
    public List<DwsIndexMakeup> selectUsageMakeupByNoteIdAndSubjectId(List<Long> ids, List<Long> itemIds) {
        return gcdpDwsIndexMakeupMapper.selectUsageMakeupByNoteIdAndSubjectId(ids, itemIds);
    }

    @Override
    public List<MakeupDetailDto> selectNormItemById(List<Long> ids) {
        return gcdpDwsIndexMakeupMapper.selectNormItemByIds(ids);
    }

    @Override
    public List<MakeupDetailDto> selectNormItemByBqItemId(List<Long> bqIds){
        return dwdBqitemCostMapper.selectNormItemByBqItemId(bqIds);
    }

    @Override
    public List<MakeupDetailDto> selectResourceItemById(List<Long> ids){
        return gcdpDwsIndexMakeupMapper.selectResourceItemById(ids);
    }

    @Override
    public List selectBqLmmDetailItems() {
        return null;
    }

    @Override
    public List<MaterialDTO> selectNormItemLmmDetailItems(List<Long> ids) {
        return dwdBqitemCostMapper.selectMaterialByNormItemId(ids);
    }

    @Override
    public List<MakeupDetailDto> selectBqitemById(List<Long> ids) {
        return gcdpDwsIndexMakeupMapper.selectBqItemByIds(ids);
    }

    @Override
    public List<CostDetailDTO> selectCostDetailByBqItemId(Long id) {
        return dwdBqitemCostMapper.selectCostDetailByBqItemId(id);
    }

    @Override
    public List<MaterialDTO> selectMaterialByBqItemId(Long id) {
        return dwdBqitemCostMapper.selectMaterialByBqItemId(id);
    }

    @Override
    public List<MaterialDTO> selectMaterialByBqItemId(List<Long> ids) {
        return dwdBqitemCostMapper.selectMaterialByBqItemIds(ids);
    }

    @Override
    public List<MaterialDTO> selectMaterialByNormItemId(List<Long> ids) {
        return dwdBqitemCostMapper.selectMaterialByNormItemId(ids);
    }

    @Override
    public List<Long> selectNormItemIdsByBqItemId(Long bqItemId) {
        return dwdBqitemCostMapper.selectNormItemIdsByBqItemId(bqItemId);
    }

    @Override
    public List<DwsIndexMakeup> selectMakeupByNoteIdAndSubjectId(List<Long> noteIds, List<Long> itemIds){
        return gcdpDwsIndexMakeupMapper.selectMakeupByNoteIdAndSubjectId(noteIds, itemIds);
    }

    @Override
    public List<DwdContractProject> selectContractProjectInfo(List<Long> ids){
        return gcdpDwsIndexMakeupMapper.selectContractProjectInfo(ids);
    }

    @Override
    public List<CBZBKMakeupDetailDto> selectExpendSubBqItemById(Set<Long> ids) {
        return gcdpDwsIndexMakeupMapper.selectExpendSubBqItemById(ids);
    }

    @Override
    public List<CBZBKMakeupDetailDto> selectResourceById(Set<Long> ids) {
        return gcdpDwsIndexMakeupMapper.selectResourceById(ids);
    }
    @Override
    public List<CBZBKMakeupDetailDto> selectBqItemById(Set<Long> ids) {
        return gcdpDwsIndexMakeupMapper.selectBqItemById(ids);
    }
    @Override
    public List<DwsIndexMakeup> selectMainResMakeupByNoteIdAndSubjectId(List<Long> noteIds, List<Long> itemIds){
        return gcdpDwsIndexMakeupMapper.selectMainResMakeupByNoteIdAndSubjectId(noteIds, itemIds);
    }
}
