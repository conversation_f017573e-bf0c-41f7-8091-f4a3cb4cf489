package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto.costData;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 全成本指标库-综合单价-指标区间组成-Dto
 * @date 2023-05-16 15:01
 */
@Data
@SuppressWarnings("squid:S1068") // 忽略sonar规则： Unused "private" fields should be removed
public class CostIndexMakeupQCBZBLibZHDJDto extends CostIndexMakeupBaseDto {
    /**
     * 科目单位
     */
    private String unit;
    /**
     * 综合单价-对应中台的实物量单方（不含税）
     */
    private BigDecimal swlIndexValue;
    /**
     * 综合单价-对应中台的实物量单方（含税）
     */
    private BigDecimal swlIndexValueIncludeTax;
    /**
     * 造价金额（不含税）
     */
    private BigDecimal amount;
    /**
     * 造价金额（含税）
     */
    private BigDecimal amountIncludeTax;
    /**
     * 工程量
     */
    private BigDecimal quantity;
}
