package com.glodon.gcdpindexsearch.dwsdatasearch.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 成本指标库-科目下钻明细
 */
@Data
public class CBZBKMakeupDetailDto {
    /** noteId */
    private Long noteId;
    /** id */
    private Long id;
    /** 编码 */
    private String code;
    /** 类别 */
    private String type;
    /** 名称 */
    private String name;
    /** 单位 */
    private String unit;
    /** 工程量 */
    private BigDecimal quantity;
    /** 项目特征 */
    private String characteristic;
    /** 工作内容 */
    private String workScope;
    /** 规格型号 */
    private String spec;
    /** 项目特征/工作内容：按规则拼接好的值 */
    private String specWork;
    /** 除税单价 */
    private BigDecimal rate;
    /** 含税单价 */
    private BigDecimal rateIncludeTax;
    /** 除税合价 */
    private BigDecimal amount;
    /** 含税合价 */
    private BigDecimal amountIncludeTax;
    /** 税率 */
    private BigDecimal taxRatio;
    /** 品牌 */
    private String brand;
    /** 费用组成 */
    private String costDetail;
    /** 甲供材料 */
    private String ownerSupply;
    /** 计量规则 */
    private String rule;
    /** 工程量是否计入 */
    private Integer isCalcQuantity;
    /** 合价是否计入 */
    private Integer isCalcAmount;
    /** 单位转换系数 */
    private BigDecimal factor;

    /** 类别ORD */
    @JsonIgnore
    private Integer typeOrd;
    /** 工程归档时间 */
    @JsonIgnore
    private Date archiveDate;

    private String tableName;
}
