package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.gcdpindexsearch.common.enums.FilterTypeEnums;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.*;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.mapper.GcdpDwsIndexMainMapper;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.MakeupIndexDetailService;
import com.glodon.gcdpindexsearch.dynamic.domain.common.Constant;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service("makeupIndexDetailServiceZYGLDJZB")
public class MainResDjMakeupIndexDetailServiceImpl implements MakeupIndexDetailService {
    @Autowired
    private GcdpDwsIndexMainMapper dwsIndexMainMapper;

    @Override
    public List<MakeUpIndexData> listMakeupIndexDetail(List<MakeUpIndexData> indexDataList, String itemIds, FilterTypeEnums filterType) {
        if (CollUtil.isEmpty(indexDataList)) {
            return Lists.newArrayList();
        }
        //查询科目信息
        List<DwsIndexMainRes> mainRes = assembleIndexes(itemIds);
        if (CollUtil.isEmpty(mainRes)) {
            return Lists.newArrayList();
        }
        return getIndexData(indexDataList, mainRes);
    }

    /**
     * 查询指标数据
     * @param itemIds
     * @return
     */
    List<DwsIndexMainRes> assembleIndexes(String itemIds){
        List<Long> itemIdList = Arrays.stream(itemIds.split(Constant.COMMA)).map(Long::new).collect(Collectors.toList());
        List<DwsIndexMainRes> mainRes = dwsIndexMainMapper.selectByItemIds(itemIdList);
        if (CollUtil.isEmpty(mainRes)){
            return Lists.newArrayList();
        }

        return mainRes.stream()
                .filter(x -> x.getDjIndexValue() != null && x.getDjIndexValue().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
    }

    private List<MakeUpIndexData> getIndexData(List<MakeUpIndexData> indexDataList, List<DwsIndexMainRes> mainRes) {
        List<MakeUpIndexData> data = new ArrayList<>();
        Map<String, List<DwsIndexMainRes>> calculateValueMap = mainRes.stream().collect(Collectors.groupingBy(item -> item.getSubjectLdMergeHash()));
        for (Map.Entry<String,List<DwsIndexMainRes>> entry : calculateValueMap.entrySet()){
            List<DwsIndexMainRes> dwsIndexMainReses = entry.getValue();
            DwsIndexMainRes dwsIndexMainRes = dwsIndexMainReses.get(0);
            Optional<MakeUpIndexData> optional = indexDataList.stream().filter(item->item.getIds().contains(String.valueOf(dwsIndexMainRes.getIndexProjectNoteId()))).findFirst();
            if(!optional.isPresent()){
                continue;
            }
            String ids = dwsIndexMainReses.stream().map(item-> String.valueOf(item.getIndexProjectNoteId())).collect(Collectors.joining(","));
            MakeUpIndexData makeUpIndexData = optional.get();
            MainResDjMakeupIndexData djMakeupIndexData = new MainResDjMakeupIndexData();
            BeanUtils.copyProperties(makeUpIndexData, djMakeupIndexData);
            djMakeupIndexData.setIds(ids);
            setIndexDataField(djMakeupIndexData, dwsIndexMainReses);
            data.add(djMakeupIndexData);
        }
        return data;
    }

    private void setIndexDataField(MainResDjMakeupIndexData djMakeupIndexData, List<DwsIndexMainRes> dwsIndexMainRes) {
        DwsIndexMainRes indexMainRes = dwsIndexMainRes.get(0);
        String name = indexMainRes.getName();
        BigDecimal djCalculateValue = dwsIndexMainRes.stream().map(DwsIndexMainRes::getDjCalculateValue).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(6, RoundingMode.HALF_DOWN);
        BigDecimal marketAmount = dwsIndexMainRes.stream().filter(x -> x.getDjCalculateValue() != null && x.getDjIndexValue() != null)
                .map(x -> x.getDjCalculateValue().multiply(x.getDjIndexValue())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(6, RoundingMode.HALF_DOWN);
        djMakeupIndexData.setName(name);
        djMakeupIndexData.setQuantity(djCalculateValue == null ? Constant.LINE : djCalculateValue.toString());
        djMakeupIndexData.setMarketAmount(marketAmount == null ? Constant.LINE : marketAmount.toString());
        if (marketAmount != null && djCalculateValue != null && BigDecimal.ZERO.compareTo(djCalculateValue) != 0) {
            djMakeupIndexData.setDjIndexValue(marketAmount.divide(djCalculateValue, 6, RoundingMode.HALF_DOWN).toString());
        } else {
            djMakeupIndexData.setDjIndexValue(Constant.LINE);
        }
        String unit = StringUtils.isNotBlank(indexMainRes.getUnit()) ? indexMainRes.getUnit() : Constant.LINE;
        djMakeupIndexData.setUnit(Constant.YUAN + Constant.Slash + unit);
    }

}
