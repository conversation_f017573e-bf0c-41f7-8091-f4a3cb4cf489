package com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.impl;

import com.glodon.gcdpindexsearch.GcdpIndexSearchApplication;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.dao.entity.DwdProjectInfo;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.repository.IDwdDataRepository;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.service.ProjectNoteService;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterConditionVO;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(classes = GcdpIndexSearchApplication.class)
public class DwdDataRepositoryImplTest {

    @Autowired
    private IDwdDataRepository dwdDataRepository;

    @Autowired
    private ProjectNoteService projectNoteService;

    @Test
    public void testListProject() {
        List<DwdProjectInfo> dwdProjectInfos =
                dwdDataRepository.listDetailProjectInfo("7034349766112777149", Lists.newArrayList("737317156860416"), Lists.newArrayList(), null, null, "true",null,"", null);
        System.out.println(dwdProjectInfos);
    }

    @Test
    public void testGetCondition() throws Exception {
        FilterConditionVO filterConditionVO = new FilterConditionVO();
        List<DwdProjectInfo> projectsByDetailCondition = projectNoteService.getProjectsByDetailCondition(filterConditionVO, "70343497661127771491", Lists.newArrayList("737317156860416"));
        System.out.println(projectsByDetailCondition);
    }

}