//package com.glodon.gcdpindexsearch;
//
//import com.glodon.gateway.sdk.httpclient.RestClient;
//import com.glodon.gateway.sdk.httpclient.core.HttpRequestConfig;
//import com.glodon.gateway.sdk.httpclient.core.HttpRequestResult;
//
//import java.io.IOException;
//
///**
// * TODO
// *
// * <AUTHOR>
// * @date 2022/4/1
// */
//public class GcdpIndexSearchApplicationTests {
//
//    private static final String ACCESS_KEY = "464bc505551c475d9687b753ad5c5b7e";
//    private static final String APPSECRET = "e6Gdt7Y6nC8ua20s";
//
//    public static void testGet1() throws IOException {
//        HttpRequestConfig request = HttpRequestConfig.create(ACCESS_KEY, APPSECRET)
//                .url("http://geip-pre.glodon.com:8088/dcost-1/gcdp-index-search/api/v1/filters/6890201680626230044/project-info");
//        HttpRequestResult response = RestClient.get(request);
//        String responseResult = response.getResponseText();
//        System.out.println("返回内容:" + responseResult);
//    }
//
//    public static void testPost1() throws IOException {
//        String jsonData = "{" +
//                "    \"enterpriseId\": \"6890201680626230044\",\n" +
//                "    \"customerCode\": \"6890201680626230044\"\n" +
//                "}";
//        HttpRequestConfig request = HttpRequestConfig.create(ACCESS_KEY, APPSECRET)
//                .url("http://geip-pre.glodon.com:8088/dcost-1/gcdp-index-search/api/v1/index/project-category")
//                .json(jsonData);
//        HttpRequestResult response = RestClient.post(request);
//        String responseResult = response.getResponseText();
//        System.out.println("返回内容:" + responseResult);
//    }
//
//    public static void main(String[] args) throws IOException {
//        testPost1();
//    }
//}
