package com.glodon.gcdpindexsearch.dwsdatasearch.domain.service;

import com.glodon.gcdp.dwdservice.domain.dao.entity.DwdProjectAuthEntInfo;
import com.glodon.gcdp.dwdservice.domain.service.project.IDwdProjectAuthEntInfoService;
import com.glodon.gcdpindexsearch.GcdpIndexSearchApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(classes = GcdpIndexSearchApplication.class)
public class DwdProjectAuthEntInfoServiceTest {

    @Autowired
    IDwdProjectAuthEntInfoService dwdProjectAuthEntInfoService;

    @Test
    public void testSelect() {
        List<DwdProjectAuthEntInfo> list = dwdProjectAuthEntInfoService.list();
        System.out.println(list);
    }

}