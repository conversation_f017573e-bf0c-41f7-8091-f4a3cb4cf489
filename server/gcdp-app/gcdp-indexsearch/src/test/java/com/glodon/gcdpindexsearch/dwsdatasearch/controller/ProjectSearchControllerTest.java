package com.glodon.gcdpindexsearch.dwsdatasearch.controller;

import com.glodon.gcdp.dimservice.domain.dao.enums.TemplateMatchTargeType;
import com.glodon.gcdp.dimservice.domain.service.DimZbTemplateItemMatchService;
import com.glodon.gcdpindexsearch.GcdpIndexSearchApplication;
import com.glodon.gcdpindexsearch.dwsdatasearch.domain.vo.FilterConditionVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @author: yhl
 * @DateTime: 2022/12/6 10:26
 * @Description:
 */

@SpringBootTest(classes = GcdpIndexSearchApplication.class)
public class ProjectSearchControllerTest {


    @Autowired
    private ProjectSearchController projectSearchController;


    @Autowired
    private DimZbTemplateItemMatchService templateItemMatchService;


    @Test
    public void getDimZbTemplateItemMatch() {
        templateItemMatchService.getDimZbTemplateItemMatch("1", "2", TemplateMatchTargeType.SUBJECT_TEMPLATE.getIndex());
    }

    @Test
    public void testProjectAnalyzeList() throws Exception {

        FilterConditionVO filterConditionVO = new FilterConditionVO();
        filterConditionVO.setSumCondition(3);
        projectSearchController.projectAnalyzeList("6902854225782862496", filterConditionVO);
    }
}