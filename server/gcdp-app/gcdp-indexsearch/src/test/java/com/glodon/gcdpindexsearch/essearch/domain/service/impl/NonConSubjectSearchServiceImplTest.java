package com.glodon.gcdpindexsearch.essearch.domain.service.impl;

import com.glodon.gcdpindexsearch.essearch.MockTestBase;
import com.glodon.gcdpindexsearch.essearch.domain.model.PageInfo;
import com.glodon.gcdpindexsearch.essearch.domain.ro.AddrConditionRo;
import com.glodon.gcdpindexsearch.essearch.domain.ro.SubjectQueryRo;
import com.glodon.gcdpindexsearch.essearch.domain.vo.CategoryVo;
import com.glodon.gcdpindexsearch.essearch.domain.vo.NonConstructionInstallationIndexVo;
import com.glodon.gcdpindexsearch.essearch.domain.vo.SubjectAutoVo;
import com.glodon.gcdpindexsearch.essearch.domain.vo.SubjectManualVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class NonConSubjectSearchServiceImplTest extends MockTestBase {

    private NonConSubjectSearchServiceImpl nonConSubjectSearchServiceImplUnderTest;

    @BeforeEach
    public void setUp() {
        nonConSubjectSearchServiceImplUnderTest = new NonConSubjectSearchServiceImpl();
    }

    @Test
    public void testManualSearchNonConstructionInstallationIndex() {
        // Setup
        final SubjectQueryRo subjectQueryRo = new SubjectQueryRo();
        subjectQueryRo.setContractProjectIds(Arrays.asList(0L));
        subjectQueryRo.setEnterpriseId("enterpriseId");
        subjectQueryRo.setSubjectName("subjectName");
        subjectQueryRo.setTemplateUuid("templateUuid");
        subjectQueryRo.setProjectCategoryCode(Arrays.asList("value"));
        final AddrConditionRo addrConditionRo = new AddrConditionRo();
        addrConditionRo.setProvinceId(Arrays.asList(0));
        addrConditionRo.setCityId(Arrays.asList(0));
        addrConditionRo.setDistrictId(Arrays.asList(0));
        subjectQueryRo.setAddrConditionRo(addrConditionRo);
        subjectQueryRo.setPhase("phase");
        subjectQueryRo.setProductPositioning("productPositioning");
        subjectQueryRo.setPageSize(0);
        subjectQueryRo.setPageNum(0);

        final SubjectManualVo nonConstructionInstallationIndexVoSubjectAutoVo = new SubjectManualVo();
        nonConstructionInstallationIndexVoSubjectAutoVo.setSubjectList(Arrays.asList("value"));
        // Run the test
        // Verify the results
    }

    @Test
    public void testAutoSearchNonConstructionInstallationIndex() {
        // Setup
        final SubjectQueryRo subjectQueryRo = new SubjectQueryRo();
        subjectQueryRo.setContractProjectIds(Arrays.asList(0L));
        subjectQueryRo.setEnterpriseId("enterpriseId");
        subjectQueryRo.setSubjectName("subjectName");
        subjectQueryRo.setTemplateUuid("templateUuid");
        subjectQueryRo.setProjectCategoryCode(Arrays.asList("value"));
        final AddrConditionRo addrConditionRo = new AddrConditionRo();
        addrConditionRo.setProvinceId(Arrays.asList(0));
        addrConditionRo.setCityId(Arrays.asList(0));
        addrConditionRo.setDistrictId(Arrays.asList(0));
        subjectQueryRo.setAddrConditionRo(addrConditionRo);
        subjectQueryRo.setPhase("phase");
        subjectQueryRo.setProductPositioning("productPositioning");
        subjectQueryRo.setPageSize(0);
        subjectQueryRo.setPageNum(0);

        final SubjectAutoVo<NonConstructionInstallationIndexVo> nonConstructionInstallationIndexVoSubjectAutoVo = new SubjectAutoVo<>();
        nonConstructionInstallationIndexVoSubjectAutoVo.setSubjectIndexList(Arrays.asList());
        nonConstructionInstallationIndexVoSubjectAutoVo.setCategoryVoList(Arrays.asList(new CategoryVo("code", "name", "namePath")));
        nonConstructionInstallationIndexVoSubjectAutoVo.setProductPositioningList(Arrays.asList("value"));
        nonConstructionInstallationIndexVoSubjectAutoVo.setPhaseList(Arrays.asList("value"));
        final PageInfo<SubjectAutoVo<NonConstructionInstallationIndexVo>> expectedResult = new PageInfo<>(nonConstructionInstallationIndexVoSubjectAutoVo);

        // Run the test
        final PageInfo<SubjectAutoVo<NonConstructionInstallationIndexVo>> result = nonConSubjectSearchServiceImplUnderTest.autoSearchNonConstructionInstallationIndex(subjectQueryRo);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
