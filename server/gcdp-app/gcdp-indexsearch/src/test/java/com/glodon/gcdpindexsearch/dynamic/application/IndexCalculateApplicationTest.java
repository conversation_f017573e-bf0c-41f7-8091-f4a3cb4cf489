package com.glodon.gcdpindexsearch.dynamic.application;

import com.glodon.gcdp.calculate.dynamic.domain.entity.ZbgxResourceDetail;
import com.glodon.gcdpindexsearch.GcdpIndexSearchApplication;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(classes = GcdpIndexSearchApplication.class)
public class IndexCalculateApplicationTest {

    @Autowired
    private IndexCalculateApplication indexSearchApplication;

    @Autowired
    private ResourceApplication resourceApplication;

    @Test
    public void indexCalculate() {
        List<Long> noteId = Lists.newArrayList(23573032L);
        List<ZbgxResourceDetail> zbgxResourceDetails = resourceApplication.resData("6956185797691630239", noteId, null);

        //材料 处理
        for (ZbgxResourceDetail resource : zbgxResourceDetails) {

        }
    }
}