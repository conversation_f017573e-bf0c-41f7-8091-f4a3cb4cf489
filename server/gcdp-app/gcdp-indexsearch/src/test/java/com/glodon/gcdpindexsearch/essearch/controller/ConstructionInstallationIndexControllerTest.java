package com.glodon.gcdpindexsearch.essearch.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.glodon.gcdpindexsearch.essearch.domain.model.PageInfo;
import com.glodon.gcdpindexsearch.essearch.domain.ro.SubjectQueryRo;
import com.glodon.gcdpindexsearch.essearch.domain.service.IConSubjectSearchService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@ContextConfiguration(classes = {ConstructionInstallationIndexController.class})
@ExtendWith(SpringExtension.class)
class ConstructionInstallationIndexControllerTest {
    @Autowired
    private ConstructionInstallationIndexController constructionInstallationIndexController;

    @MockBean
    private IConSubjectSearchService iConSubjectSearchService;

    /**
     * Method under test: {@link ConstructionInstallationIndexController#getSubjectIndexAuto(SubjectQueryRo)}
     */
    @Test
    void testGetSubjectIndexAuto() throws Exception {
        when(iConSubjectSearchService.autoSearchConstructionInstallationIndex((SubjectQueryRo) any()))
                .thenReturn(new PageInfo<>());

        SubjectQueryRo subjectQueryRo = new SubjectQueryRo();
        subjectQueryRo.setContractProjectIds(new ArrayList<>());
        subjectQueryRo.setEnterpriseId("");
        subjectQueryRo.setMbcbSubjectInfo(new ArrayList<>());
        subjectQueryRo.setPageNum(10);
        subjectQueryRo.setPageSize(3);
        subjectQueryRo.setPhase("Phase");
        subjectQueryRo.setProductPositioning("Product Positioning");
        subjectQueryRo.setProjectCategoryCode(new ArrayList<>());
        subjectQueryRo.setSubjectName("Hello from the Dreaming Spires");
        subjectQueryRo.setTemplateUuid("01234567-89AB-CDEF-FEDC-BA9876543210");
        String content = (new ObjectMapper()).writeValueAsString(subjectQueryRo);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/subject-search/api/constructionInstallation/search/auto")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        MockMvcBuilders.standaloneSetup(constructionInstallationIndexController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("{\"pageNum\":0,\"pageSize\":0,\"data\":null}"));
    }

    /**
     * Method under test: {@link ConstructionInstallationIndexController#getSubjectIndexManual(SubjectQueryRo)}
     */
    @Test
    void testGetSubjectIndexManual() throws Exception {
        when(iConSubjectSearchService.autoSearchConstructionInstallationIndex((SubjectQueryRo) any()))
                .thenReturn(new PageInfo<>());

        SubjectQueryRo subjectQueryRo = new SubjectQueryRo();
        subjectQueryRo.setContractProjectIds(new ArrayList<>());
        subjectQueryRo.setEnterpriseId("");
        subjectQueryRo.setMbcbSubjectInfo(new ArrayList<>());
        subjectQueryRo.setPageNum(10);
        subjectQueryRo.setPageSize(3);
        subjectQueryRo.setPhase("Phase");
        subjectQueryRo.setProductPositioning("Product Positioning");
        subjectQueryRo.setProjectCategoryCode(new ArrayList<>());
        subjectQueryRo.setSubjectName("Hello from the Dreaming Spires");
        subjectQueryRo.setTemplateUuid("01234567-89AB-CDEF-FEDC-BA9876543210");
        String content = (new ObjectMapper()).writeValueAsString(subjectQueryRo);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/subject-search/api/constructionInstallation/search/manual")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        MockMvcBuilders.standaloneSetup(constructionInstallationIndexController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("{\"pageNum\":0,\"pageSize\":0,\"data\":null}"));
    }
}

