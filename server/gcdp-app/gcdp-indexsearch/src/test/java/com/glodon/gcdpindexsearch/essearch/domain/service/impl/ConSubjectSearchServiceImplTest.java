package com.glodon.gcdpindexsearch.essearch.domain.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.gcdp.dimservice.domain.dao.entity.DimZbTemplateItemMatch;
import com.glodon.gcdp.es.dto.ConstructionInstallationIndexDto;
import com.glodon.gcdpindexsearch.GcdpIndexSearchApplication;
import com.glodon.gcdpindexsearch.essearch.domain.ro.SubjectQueryRo;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: yhl
 * @DateTime: 2023/8/24 10:26
 * @Description:
 */
@SpringBootTest(classes = GcdpIndexSearchApplication.class)
public class ConSubjectSearchServiceImplTest {

    @Autowired
    private ConSubjectSearchServiceImpl searchService;

    @Test
    public void subjectSettle() {
        String query = IoUtil.readUtf8(FileUtil.getInputStream(new File("E:\\data\\request_data\\query.json")));
        String template = IoUtil.readUtf8(FileUtil.getInputStream(new File("E:\\data\\request_data\\template.json")));
        String indexStr = IoUtil.readUtf8(FileUtil.getInputStream(new File("E:\\data\\request_data\\index.json")));
        JSONObject indexJson = JSON.parseObject(indexStr);
        List<ConstructionInstallationIndexDto> indexDtoList = Lists.newArrayList();
        Map<String, List<ConstructionInstallationIndexDto>> indexMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : indexJson.entrySet()) {
            indexDtoList.addAll(JSON.parseArray(entry.getValue().toString(), ConstructionInstallationIndexDto.class));
        }

        searchService.subjectMergeAndSettle(JSON.parseObject(query, SubjectQueryRo.class), indexDtoList,
                JSONArray.parseArray(template, DimZbTemplateItemMatch.class));

    }
}