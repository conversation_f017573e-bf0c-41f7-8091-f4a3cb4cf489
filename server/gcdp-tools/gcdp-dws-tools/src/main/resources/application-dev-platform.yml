ECS_REDIS_HOST: ***********
ECS_REDIS_PORT: 6379
ECS_REDIS_PASSWORD: '!yJ0Gx!+mxRq5'
ECS_REDIS_DB: 11
ECS_REDIS_PREFIX: gcdp
spring:
  config:
    import:
      - classpath:application-gcdp-common.yml
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      druid: #以下是全局默认值，可以全局更改
        #监控统计拦截的filters
        filters: stat
        #配置初始化大小/最小/最大
        initial-size: 1
        min-idle: 1
        max-active: 200
        #获取连接等待超时时间
        max-wait: 60000
        #间隔多久进行一次检测，检测需要关闭的空闲连接
        time-between-eviction-runs-millis: 60000
        #一个连接在池中最小生存的时间
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 'x'
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        #打开PSCache，并指定每个连接上PSCache的大小。oracle设为true，mysql设为false。分库分表较多推荐设置为false
        pool-prepared-statements: false
        max-pool-prepared-statement-per-connection-size: 20
        stat:
          merge-sql: true
          log-slow-sql: true
          slow-sql-millis: 2000
          primary: master
      datasource:
        master:
          url: ***********************************************************************************************************************************************
          username: gcdp_admin
          password: T97^TmZGgy9fQOE
          driver-class-name: com.mysql.cj.jdbc.Driver # 3.2.0开始支持SPI可省略此配置
          minIdle: 5
          maxActive: 200
          initial-size: 5
  kafka:
    bootstrap-servers: 10.0.68.17:6667,10.0.68.19:6667,10.0.68.43:6667   # kafka集群信息,可以多个，之间用逗号分隔
    consumer:
      group-id: gcdp-dws-etl-consumer #指定消费者组
      enable-auto-commit: false #是否自动提交
      auto-commit-interval: 100
      auto-offset-reset: latest #当各分区下有已提交的offset时，从提交的offset开始消费；无提交的offset时，从头开始消费  earliest latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 100
    properties:
      max:
        poll:
          interval:
            ms: 300000
      request:
        size: 5242880 # 发送大小为5m
    listener:
      ack-mode: manual_immediate
      concurrency: 1
mybatis:
  mapper-locations: classpath:mapper/dwd/*Mapper.xml,classpath:mapper/dim/*Mapper.xml,classpath:mapper/state/*Mapper.xml,classpath*:mapper/*Mapper.xml

topic-Message:
  topic-resource-push: topic-gcdp-resource-push  #人材机推送topic
  topic-bqitem-push: topic-gcdp-bqitem-push  #人材机推送topic
  topic-gcdp-dws-etl: topic-gcdp-dws-etl


object-storage:
  enable: true
  protocol: S3_HW  #可选项 OSS、S3_HW、S3_COS、S3_AWS、S3_AWS_V4、S3_MINIO、S3_CEPH、S3_QY、S3
  endPoint: https://obs.cn-north-4.myhuaweicloud.com
  #endPoint: https://oss-cn-beijing-internal.aliyuncs.com #内网地址
  externalEndpoint: https://obs.cn-north-4.myhuaweicloud.com #外网地址
  accessKeyId: LIAVC36ROIM0YUIUYUXX
  accessKeySecret: pEtQS6XVokEz1aBB67IOuBPhseE1sI8wjeWucRfD
  bucketName: gldzb-test
  region: cn-north-4
  pathStyleAccess: false # 是否强制开启路径形式的资源访问
  maxConnections: 1000
  connectionTimeout: 3000
  socketTimeout: 3000

oss:
  bqitem-push:
    path-prefix: gcdp-data/itemlib

# 授权中心服务
apiAuth:
  url: https://api-auth-aetest.glodon.com
  appKey: kqIUvqfcH7obwsZRx4U3L3yG5M3sLuRL
  g-signature: F864378123E9BA92E14E6C7862257FCC

# 部件服务
dcost-sub:
  url: https://dcost-sub-test-sprint.glodon.com