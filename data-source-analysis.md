# 数据源分析报告 - GCDP 数据平台

## 概述

本文档分析了 GCDP (Glodon Cost Data Platform) 数据平台中不同数据源的数据模型、摄取流程和转换逻辑。系统通过 `ProductSource` 枚举定义了 80+ 种不同的数据源，涵盖了从传统成本计算系统到现代项目管理平台的各种业务场景。

## 数据源分类

### 1. 成本计算系统类
- **QYQD** (qyqd) - 市场化计价：2,800 条记录
- **SGFBQD** (sgfbqd) - 施工分包清单产品
- **MBCB** (mbcb) - 目标成本：1,069 条记录
- **TZGSBZ** (tzgsbz) - 投资估算：311 条记录
- **SGCBCS2** (sgcbcs2) - 施工成本测算(新模型)：9,934 条记录 (最多)
- **SGCBHS2** (sgcbhs2) - 施工成本核算(新模型)：2,169 条记录

### 2. 指标分析系统类
- **ZBSQ** (zbsq) - 指标神器：6,656 条记录
- **ZBGX** (zbgx) - 指标更新：491 条记录
- **ZBW** (zbw) - 指标网：148 条记录
- **ZBSQ_WEB** (zbsq-web) - 指标神器-WEB端：2,006 条记录

### 3. 项目管理系统类
#### GEPS 系统 (20+ 个数据源)
- **GCDP_GEPS_PROJECT_INFO** - geps项目信息
- **GCDP_GEPS_LWFBHT** - geps-劳务分包合同
- **GCDP_GEPS_CLCGHT** - geps-材料采购合同
- **GCDP_GEPS_JXGZHT** - geps-机械购置合同

#### PMLead 系统 (30+ 个数据源)
- **GCDP_PMLEAD_CLCGHT** - pmlead材料采购合同
- **GCDP_PMLEAD_LWFBHT** - PMLead劳务分包合同
- **GCDP_PMLEAD_ZYFBHT** - PMLead专业分包合同

#### PMCore 系统 (10+ 个数据源)
- **GCDP_PMCORE_CLCGHT** - pmcore材料采购合同
- **GCDP_PMCORE_LWFBHT** - pmcore劳务分包合同

### 4. 文件导入系统类
- **EXCEL** (gcdp-excel) - Excel导入新成本项目数据
- **EXCEL_HISTORY** (gcdp-excel-history) - Excel导入历史项目数据
- **PDF_INQUIRY** (gcdp-pdf-inquiry) - PDF市场询价数据
- **WORD_INQUIRY** (gcdp-word-inquiry) - Word市场询价数据

## 数据库架构

### 数据分层架构
系统采用经典的数据仓库分层架构：

1. **ODS层** (Operational Data Store)
   - `gcdp_ods_contract_project` - 原始合同项目数据

2. **DWD层** (Data Warehouse Detail)
   - `gcdp_dwd_project_info` - 项目基础信息 (使用 data_source 字段)
   - `gcdp_dwd_contract_project` - 合同项目详情 (使用 product_source 字段)
   - `gcdp_dwd_index` - 指标数据
   - `gcdp_dwd_resource` - 资源数据 (16,357,633 条记录)
   - `gcdp_dwd_sub_project` - 子项目数据

3. **DWS层** (Data Warehouse Summary)
   - `gcdp_dws_index` - 指标汇总
   - `gcdp_dws_business_summary` - 业务汇总
   - `gcdp_dws_sub_bqitem_index` - 子项清单指标

4. **DIM层** (Dimension)
   - `gcdp_dim_cost_subject` - 成本科目字典
   - `gcdp_dim_resource_category_dict` - 资源分类字典

## 核心数据模型

### 项目信息模型 (gcdp_dwd_project_info)
```sql
主要字段：
- id (bigint) - 主键
- project_code (varchar) - 项目编码
- project_name (varchar) - 项目名称
- data_source (varchar) - 数据源标识
- enterprise_id (varchar) - 企业ID
- province_name, city_name, district_name - 地理信息
- project_category_code/name - 项目分类
- create_date, update_date - 时间戳
```

### 合同项目模型 (gcdp_dwd_contract_project)
```sql
主要字段：
- id (bigint) - 主键
- project_id (bigint) - 关联项目ID
- product_source (varchar) - 产品数据源
- enterprise_id (varchar) - 企业ID
- name (varchar) - 合同名称
- total, total_include_tax - 金额信息
- project_attr_json (mediumtext) - 项目属性JSON
- extend_data_json (json) - 扩展数据JSON
```

### 指标数据模型 (gcdp_dwd_index)
```sql
主要字段：
- id (bigint) - 主键
- contract_project_id (bigint) - 关联合同项目
- code, name - 指标编码和名称
- quantity, rate, amount - 数量、单价、金额
- index_data_json (longtext) - 指标详细数据
- extend_data_json (json) - 扩展数据
```

## 数据摄取流程

### 1. 实时数据摄取
- **Kafka消息队列**: 使用多个Topic处理不同类型的数据
  - `topic-gcdp-resource-push` - 人材机推送
  - `topic-gcdp-bqitem-push` - 清单项推送
  - `topic-gcdp-dws-etl` - 数据仓库ETL

### 2. 批量数据导入
- **文件导入**: 支持Excel、PDF、Word格式
- **系统同步**: 从GEPS、PMLead、PMCore等系统定期同步

### 3. 数据转换规则
- **产品源映射**: ProductSource枚举值映射到数据库product_source字段
- **数据标准化**: 统一的字段命名和数据格式
- **JSON扩展**: 使用JSON字段存储灵活的扩展属性

## 不同数据源的特点

### 高频使用数据源
1. **sgcbcs2** (施工成本测算新模型) - 9,934条记录
   - 特点：新版成本测算模型，数据结构完整
   - 主要用途：施工阶段成本预测和控制

2. **zbsq** (指标神器) - 6,656条记录
   - 特点：指标分析和对比功能
   - 主要用途：成本指标分析和基准对比

3. **qyqd** (市场化计价) - 2,800条记录
   - 特点：市场价格数据
   - 主要用途：市场价格参考和询价

### 项目管理系统集成
- **GEPS系统**: 涵盖项目全生命周期管理
- **PMLead系统**: 专注于项目执行阶段管理
- **PMCore系统**: 核心项目管理功能

### 文件导入系统
- 支持多种文件格式 (Excel, PDF, Word)
- 区分新项目数据和历史数据
- 支持市场询价数据导入

## 业务规则和约束

### 数据源优先级
系统通过ProductSource枚举的getIndexListByProjectSource方法定义了数据源优先级：
- AGREEMENT -> ProductSource.AGREEMENT
- ARCHIVEDB -> ProductSource.SGFBQD

### 数据验证规则
- 企业隔离：通过enterprise_id确保数据安全
- 数据完整性：必填字段验证
- 业务逻辑：金额计算、税率处理等

### 扩展性设计
- JSON字段支持灵活的扩展属性
- 枚举设计支持新数据源的添加
- 分层架构支持数据处理流程的扩展

## 技术实现细节

### 数据库连接配置
```properties
# 主数据库配置 (测试环境)
mysql.host=*************
mysql.username=manager
mysql.password=123qwe!@#
mysql.database=db_cost_data_platform

# PMLead数据库
mysql.pmlead.database=cda_dwd_pmlead_test
mysql.pmlead.url=**************:3306

# PMCore数据库 (StarRocks)
mysql.pmcore.database=cda_dwd_pmcore_test
mysql.pmcore.url=starrockscluster-test4.sg-data-te-bj4-vpc.glodon.com:9030
```

### 关键表结构分析

#### 产品源字段分布
```sql
-- 包含product_source字段的表统计
表名                                    记录数
gcdp_dwd_resource                      16,357,633  (资源数据)
gcdp_dwd_contract_project              29,402      (合同项目)
gcdp_dwd_sub_project                   47          (子项目)
```

#### 数据源使用频率分析
```sql
-- 合同项目表中产品源分布 (Top 10)
sgcbcs2     (施工成本测算新模型)    9,934 条
zbsq        (指标神器)             6,656 条
qyqd        (市场化计价)           2,800 条
sgcbhs2     (施工成本核算新模型)    2,169 条
zbsq-web    (指标神器WEB端)        2,006 条
gcdp-zbsq   (指标神器平台版)       1,900 条
mbcb        (目标成本)             1,069 条
zbgx        (指标更新)             491 条
gbqd        (国标清单)             402 条
ystz        (预算台账)             335 条
```

### 数据处理流程

#### ETL处理流程
1. **数据抽取 (Extract)**
   - 从源系统通过API或文件导入获取数据
   - 支持实时和批量两种模式

2. **数据转换 (Transform)**
   - ProductSource枚举值标准化
   - 数据格式统一化处理
   - 业务规则验证和清洗

3. **数据加载 (Load)**
   - 分层加载：ODS -> DWD -> DWS -> ADS
   - 增量更新和全量刷新策略

#### 消息队列处理
```yaml
# Kafka配置
kafka:
  bootstrap-servers: 10.125.2.6:9094,10.125.2.6:9095,10.125.2.6:9096
  topics:
    - topic-gcdp-resource-push    # 人材机推送
    - topic-gcdp-bqitem-push      # 清单项推送
    - topic-gcdp-dws-etl          # 数据仓库ETL
```

## 数据源映射关系

### ProductSource枚举到数据库映射
| 枚举值 | 数据库值 | 中文名称 | 主要用途 |
|--------|----------|----------|----------|
| QYQD | qyqd | 市场化计价 | 市场价格参考 |
| SGFBQD | sgfbqd | 施工分包清单产品 | 分包管理 |
| MBCB | mbcb | 目标成本 | 成本控制 |
| AGREEMENT | gcdp-agreement | 定制版-履约合同 | 合同管理 |
| ZBSQ | zbsq | 指标神器 | 指标分析 |
| EXCEL | gcdp-excel | Excel导入新成本项目数据 | 数据导入 |

### 系统集成架构
```
外部系统           数据平台层           存储层
┌─────────────┐   ┌─────────────┐   ┌─────────────┐
│ GEPS系统    │──▶│ ODS层       │──▶│ MySQL       │
├─────────────┤   ├─────────────┤   ├─────────────┤
│ PMLead系统  │──▶│ DWD层       │──▶│ StarRocks   │
├─────────────┤   ├─────────────┤   ├─────────────┤
│ PMCore系统  │──▶│ DWS层       │──▶│ MongoDB     │
├─────────────┤   ├─────────────┤   ├─────────────┤
│ 文件导入    │──▶│ ADS层       │──▶│ Redis       │
└─────────────┘   └─────────────┘   └─────────────┘
```

## 性能优化建议

### 索引优化
- `gcdp_dwd_project_info.data_source` - 添加索引提升查询性能
- `gcdp_dwd_contract_project.product_source` - 复合索引优化
- `enterprise_id` 字段 - 企业隔离查询优化

### 分区策略
- 按时间分区：`create_date`, `update_date`
- 按企业分区：`enterprise_id`
- 按数据源分区：`product_source`

### 缓存策略
- Redis缓存热点数据源配置
- 枚举值映射关系缓存
- 企业权限数据缓存

## 总结

GCDP数据平台通过统一的ProductSource枚举管理80+种数据源，采用分层数据仓库架构，支持实时和批量数据摄取。系统设计具有良好的扩展性和灵活性，能够适应不断变化的业务需求。主要的数据源包括成本计算系统、指标分析系统、项目管理系统和文件导入系统，每种数据源都有其特定的业务场景和数据特点。

通过本次分析，我们可以看到：
1. **数据源多样性**：80+种数据源覆盖了建筑行业的各个业务场景
2. **架构合理性**：分层架构支持复杂的数据处理需求
3. **扩展性良好**：JSON字段和枚举设计支持业务扩展
4. **性能可控**：通过合理的索引和分区策略保证查询性能
